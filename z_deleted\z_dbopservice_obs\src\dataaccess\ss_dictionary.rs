use anyhow::Result;
use rbatis::{crud, py_sql};
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub struct SsDictionary {
  pub id: i32,
  pub ss_typeid: i32,
  pub ss_pid: i32,
  pub ss_short: String,
  pub ss_name: String,
  pub ss_showorder: i32,
  pub ss_memo: String,
}
crud!(SsDictionary {}, "ss_dictionary");
rbatis::impl_select!(SsDictionary{query(tid:&i32, pid:&i32) -> Option => "`where ss_typeid = #{tid} and ss_pid = #{pid} limit 1 `"});

impl SsDictionary {
  #[py_sql(
    "`select * from ss_dictionary where id > 0 `
      if tid > 0:
        ` and ss_typeid=#{tid}`
      if pid > 0:
        ` and ss_pid = #{pid}`
    "
  )]
  async fn query_many(rb: &mut rbatis::RBatis, tid: i32, pid: i32) -> Result<Vec<SsDictionary>, rbatis::Error> {
    impled!()
  }
}
