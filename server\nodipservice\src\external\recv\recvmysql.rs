use anyhow::{anyhow, Result};
use dataservice::entities::prelude::*;
use futures_util::stream::TryStreamExt;
use sqlx::Row;

// "server=tcp:localhost,1433;IntegratedSecurity=true;TrustServerCertificate=true".to_owned()

#[derive(Debug)]
pub struct RecvMysql;

impl RecvMysql {
  pub async fn query_from_external_labresult(conn: &sqlx::MySqlPool, tjbh: &str) -> Result<Vec<VTjLisresult>> {
    let sql = format!(
      "select tjbh, brxm, xmxh, xmmc, xmdw, xmjg, gdbj, ckdz, ckgz, ckfw, jyys, bgrq, bgys,sfyc from v_tj_lisresult where tjbh = '{}'",
      tjbh
    );
    // info!("开始从mysql获取结果数据，体检号：{}", tjbh);
    let mut results: Vec<VTjLisresult> = vec![];

    let mut rows = sqlx::query(&sql).fetch(conn);
    while let Some(row) = rows.try_next().await? {
      // map the row into a user-defined domain type
      //   let email: &str = row.try_get("email")?;

      //
      let tjbh: Option<&str> = row.try_get(0)?; // index by 0-based position
      let brxm: Option<&str> = row.try_get(1)?; // nullable column must be get as Option<...> to avoid panic.
      let xmxh: Option<&str> = row.try_get(2)?; //
      let xmmc: Option<&str> = row.try_get(3)?; //
      let xmdw: Option<&str> = row.try_get(4)?; //
      let xmjg: Option<&str> = row.try_get(5)?; //
      let gdbj: Option<&str> = row.try_get(6)?; //
      let ckdz: Option<&str> = row.try_get(7)?; //
      let ckgz: Option<&str> = row.try_get(8)?; //
      let ckfw: Option<&str> = row.try_get(9)?; //
      let jyys: Option<&str> = row.try_get(10)?; //
      let bgrq: Option<&str> = row.try_get(11)?; //
      let bgys: Option<&str> = row.try_get(12)?; //
      let sfyc: Option<i32> = row.try_get(13)?; //
      let labret = VTjLisresult {
        id: 0,
        tjbh: tjbh.unwrap_or_default().to_string(),
        brxm: brxm.unwrap_or_default().to_string(),
        xmxh: xmxh.unwrap_or_default().to_string(),
        xmmc: xmmc.unwrap_or_default().to_string(),
        xmdw: xmdw.unwrap_or_default().to_string(),
        xmjg: xmjg.unwrap_or_default().to_string(),
        sfyc: sfyc.unwrap_or_default(),
        gdbj: gdbj.unwrap_or_default().to_string(),
        ckdz: ckdz.unwrap_or_default().to_string(),
        ckgz: ckgz.unwrap_or_default().to_string(),
        ckfw: ckfw.unwrap_or_default().to_string(),
        jyys: jyys.unwrap_or_default().to_string(),
        bgrq: bgrq.unwrap_or_default().to_string(),
        bgys: bgys.unwrap_or_default().to_string(),
      };
      results.push(labret);
    }
    if results.len() <= 0 {
      return Err(anyhow!("lis系统没有结果数据"));
    }

    Ok(results)
  }

  pub async fn query_from_external_pacsresult(conn: &sqlx::MySqlPool, tjbh: &str, hassfyc: i32, version: i32) -> Result<Vec<VTjPacsresult>> {
    let mut results: Vec<VTjPacsresult> = vec![];
    let sql: String;
    if hassfyc == 1 {
      sql = format!(
        "select tjbh,brxm,jclx,jcxm,jcmc,imagesight,imagediagnosis,jcys,sxys,bgys,bgrq,sfyc from v_tj_pacsresult where tjbh = '{}'",
        tjbh
      );
    } else {
      sql = format!(
        "select tjbh,brxm,jclx,jcxm,jcmc,imagesight,imagediagnosis,jcys,sxys,bgys,bgrq from v_tj_pacsresult where tjbh = '{}'",
        tjbh
      );
    }
    let mut rows = sqlx::query(&sql).fetch(conn);
    while let Some(row) = rows.try_next().await? {
      let tjbh: Option<String> = row.try_get(0)?; // index by 0-based position
      let brxm: Option<String> = row.try_get(1)?; // nullable column must be get as Option<...> to avoid panic.
      let jclx: Option<String> = row.try_get(2)?; //
      let jcxm: Option<String> = row.try_get(3)?; //
      let jcmc: Option<String> = row.try_get(4)?; //
      let imagesight: Option<String> = row.try_get(5)?; //
      let imagediagnosis: Option<String> = row.try_get(6)?; //
      let jcys: Option<String> = row.try_get(7)?; //

      let sxys: Option<String> = row.try_get(8)?; //
      let bgys: Option<String> = row.try_get(9)?; //
      let bgrq: Option<String> = row.try_get(10)?; //
      let mut sfyc = 0;
      if hassfyc == 1 {
        if version == 1 {
          let sfyci: Option<i32> = row.try_get(11)?; //
          sfyc = sfyci.unwrap_or_default();
        } else {
          let sfs: Option<String> = row.try_get(11)?;
          if sfs.is_some() {
            sfyc = sfs.unwrap().parse().unwrap_or_default();
          }
        }
      }
      let imagepath = Some("".to_string());
      let datas = Some("".to_string());
      let pret: VTjPacsresult = VTjPacsresult {
        id: 0,
        tjbh: tjbh.unwrap_or_default().to_string(),
        brxm: brxm.unwrap_or_default().to_string(),
        jclx: jclx.unwrap_or_default().to_string(),
        jcxm: jcxm.unwrap_or_default().to_string(),
        jcmc: jcmc.unwrap_or_default().to_string(),
        imagesight: imagesight.unwrap_or_default().to_string(),
        imagediagnosis: imagediagnosis.unwrap_or_default().to_string(),
        jcys: jcys.unwrap_or_default().to_string(),
        sxys: sxys.unwrap_or_default().to_string(),
        bgys: bgys.unwrap_or_default().to_string(),
        bgrq: bgrq.unwrap_or_default().to_string(),
        sfyc: sfyc,
        imagepath,
        datas,
      };
      results.push(pret);
    }
    Ok(results)
  }

  pub async fn query_lis_xmxx(conn: &sqlx::MySqlPool) -> Result<Vec<VLisXmxx>> {
    let sql = "select itemid, chinesename, englishab, unit from v_lis_xmxx";
    let mut results: Vec<VLisXmxx> = vec![];

    // let stream = client.query(sql, &[]).await?;
    // let mut stream = stream.into_row_stream();

    let mut rows = sqlx::query(&sql).fetch(conn);
    while let Some(row) = rows.try_next().await? {
      //
      let itemid: Option<&str> = row.try_get(0)?; // index by 0-based position
      let chinesename: Option<&str> = row.try_get(1)?; // nullable column must be get as Option<...> to avoid panic.
      let englishab: Option<&str> = row.try_get(2)?; //
      let unit: Option<&str> = row.try_get(3)?; //

      let labret = VLisXmxx {
        itemid: itemid.unwrap_or_default().to_string(),
        chinesename: chinesename.unwrap_or_default().to_string(),
        englishab: englishab.unwrap_or_default().to_string(),
        unit: unit.unwrap_or_default().to_string(),
      };
      results.push(labret);
    }

    Ok(results)
  }
}
