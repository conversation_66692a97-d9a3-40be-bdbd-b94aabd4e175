//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Default, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_iteminfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  #[sea_orm(unique)]
  pub tj_itemid: String,
  pub tj_itemtype: i32,
  pub tj_itemname: String,
  pub tj_itemunit: String,
  pub tj_showorder: i32,
  pub tj_sex: i32,
  pub tj_itemprice: f32,
  pub tj_valuetype: String,
  pub tj_defaultresult: String,
  pub tj_reftype: String,
  pub tj_uppervalue: String,
  pub tj_lowvalue: String,
  pub tj_highoffset: String,
  pub tj_lowoffset: String,
  pub tj_okflag: i32,
  pub tj_barflag: i32,
  pub tj_combineflag: i32,
  pub tj_autodiagflag: i32,
  pub tj_lisnum: String,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_memo: String,
  pub tj_operator: i32,
  pub tj_moddate: i64,
  pub sample_code: String,
  pub tj_extcode: String,
  pub tj_extitemtype: i32,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
