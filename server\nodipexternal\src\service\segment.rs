use uuid::Uuid;

pub struct SegmentHelper;

impl SegmentHelper {
  pub fn get_msh_segment(msgdt: &str, msg: &str, event: &str) -> (String, String) {
    let uuid = Uuid::new_v4().to_string();
    (format!("MSH|^~\\&|nodip|JoesongSoft|TECH|WinningSoft|{msgdt}||{msg}^{event}|{uuid}|P|2.4|||||CHN\r"), uuid)
  }

  pub fn get_evn_segment(event: &str, eventdt: &str) -> String {
    format!("EVN|{}|{}\r", event, eventdt)
  }

  // pub fn get_simple_pid_segment(patid: &str, sex: i32) -> String {
  //   let sexstr = match sex {
  //     1 => "M",
  //     2 => "F",
  //     _ => "U",
  //   };
  //   format!("PID|1||{patid}^^^&PATID|||||{sexstr}||||||||S\r")
  // }

  //详细地址&乡镇或街道^[公司(单位)名称]^城市^省^邮编^国家^地址类型^^县或区
  pub fn get_pid_segment(patid: &str, testid: &str, idcard: &str, ptname: &str, birthdate: &str, phone: &str, sex: i32, address: &str, corpname: &str) -> String {
    let sexstr = match sex {
      1 => "M",
      2 => "F",
      _ => "U",
    };
    let newbirthdate: String;
    if birthdate.is_empty() {
      newbirthdate = "".to_string();
    } else {
      let bt: Vec<&str> = birthdate.split('-').collect();
      if bt.len() < 3 {
        newbirthdate = birthdate.to_string();
      } else {
        newbirthdate = format!("{}{}{}000000", bt[0], bt[1], bt[2]);
      }
    }
    format!(
      "PID|1|{patid}^^^&PATID|{patid}^^^&PATID~{testid}^^^&BLH~{testid}^^^&BRKH~^^^&SFZH~{idcard}^^^&YEXH||{ptname}||{newbirthdate}|{sexstr}|||{address}^{corpname}^^^^^^^|CHN|^^^^^^{phone}||||||||||23|||||40^中国\r"
    )
  }

  // PID|1||11311737^^^&HISPATID|||||M||||||||S
  pub fn get_dft_pid_segment(patid: &str) -> String {
    // let sexstr = match sex {
    //   1 => "M",
    //   2 => "F",
    //   _ => "U",
    // };

    format!(
      "PID|1||{patid}^^^&HISPATID\r" // "PID|1|{patid}^^^&PATID|{patid}^^^&PATID~{testid}^^^&BLH~{testid}^^^&BRKH~^^^&SFZH~{idcard}^^^&YEXH||{ptname}||{newbirthdate}|{sexstr}|||{address}^{corpname}^^^^^^^|CHN|^^^^^^{phone}||||||||||23|||||40^中国\r"
    )
  }

  ///porc:0:个人体检 1：团体体检
  pub fn get_pv1_segment(patid: &str, doctorid: &str, doctorname: &str, porc: i32, eventdt: &str) -> String {
    //101~自费 19， visit number
    format!("PV1|1|P|3022^体检中心||||{doctorid}^{doctorname}|||||||||||{porc}|{patid}|101~体检费||||||||||||||||||||||||{eventdt}||||||{patid}\r")
  }

  pub fn get_orc_segment(orcid: &str, eventdt: &str, op: i32, doctorid: &str, doctorname: &str) -> String {
    //NW:新增;RP:修改;CA:取消
    let method = match op {
      x if x == nodipservice::common::constant::ExtOpType::ADD as i32 => "NW",
      // x if x == nodipservice::common::constant::ExtOpType::EDIT as i32 => "RP",
      x if x == nodipservice::common::constant::ExtOpType::DEL as i32 => "CA",
      _ => "NW",
    };

    //15:EVENT DATE
    format!("ORC|{method}|{orcid}|||0|||||^^^^^^^0||{doctorid}^^{doctorname}|||{eventdt}||||^^^^^^^^^P~^^^^^^^^^D\r")
  }

  //OBR||||11050000100_01^体检费^^0|0|20230605082007|||1|||||||||3022|体检中心|001|体检中心||&251^&&&1||||||||||||||||||||||^^^zk061104760|WCT^^^2
  //OBR||||2073^体检尿常规^^0|0|20230605082007|||1|||||||||3022|体检中心||检验科||&19^&&&1|||||||||||||20230605082007|||体检尿常规^^0^^申请单体检尿常规^0||||||^^^zk061104756|WCT^^^2
  pub fn get_obr_segment(itemid: &str, itemname: &str, price: &str, eventdt: &str, orcid: &str, execdept: &str, itemtype: i32) -> String {
    // format!("OBR||||{itemid}^{itemname}^^0|0|{eventdt}|||1|||||||||3022|体检中心|001|体检中心||&286^&&&1||||||||||||||||||||||^^^{orcid}|WCT^^^2\r")
    let mut newitem = "".to_string();
    if execdept.eq_ignore_ascii_case("检验科") {
      newitem = format!("{itemname}^^0^^{itemname}^0");
    }
    format!("OBR|1|||{itemid}^{itemname}^^{itemtype}|0|{eventdt}|||1|||||||||3022|体检中心||{execdept}||&{price}^&&&1|||||||||||||{eventdt}|||{newitem}||||||^^^{orcid}{itemid}|WCT^^^2\r")
  }

  //FT1|1|Sw100001||20230605082007||0|11050000100_01^体检费|0|收费项目|1|251|251|3022^健康管理中心||||0|0||9209^
  pub fn get_ft1_segment(txid: &str, amount: &str, eventdt: &str, doctorid: &str) -> String {
    format!("FT1|1|{txid}||{eventdt}||0|11050000100_01^体检费|0|收费项目|1|{amount}|{amount}|3022^健康管理中心||||0|0||{doctorid}\r")
  }
}
