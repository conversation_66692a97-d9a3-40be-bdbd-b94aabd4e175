// use std::collections::HashSet;

use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
// use rust_decimal::prelude::ToPrimitive;
use tracing::*;

use crate::{
  common::constant::{ValueType, YesOrNo},
  dto::CheckitemsQueryDto,
};

pub struct CheckitemSvc;

impl CheckitemSvc {
  pub async fn query_checkitems(dto: &CheckitemsQueryDto, db: &DbConnection) -> Result<Vec<TjCheckiteminfo>> {
    // let testids: Vec<String> = dto.testids.iter().map(|v| v.as_str()).collect();
    // let deptids: Vec<String> = dto.deptids.iter().map(|v| v.as_str()).collect();
    let ret = TjCheckiteminfo::query_many(&dto.testids, &dto.deptids, dto.flag, dto.combined, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut datas = ret.unwrap();
    datas.sort_by(|a, b| a.tj_synid.cmp(&b.tj_synid).then(a.tj_showorder.cmp(&b.tj_showorder)));
    Ok(datas)
  }

  // pub async fn update_checkitems(testid: &String, items: &Vec<TjIteminfo>, db: &DbConnection) -> Result<u64> {
  //   //1）从数据库查找该用户的所有检查项目
  //   //2）新增的insert
  //   //2）不存在的delete
  //   if testid.is_empty() {
  //     return Err(anyhow!("testid is empty..."));
  //   }
  //   if items.len() <= 0 {
  //     // return Err(anyhow!("items is empty"));
  //     let ret = TjCheckiteminfo::delete_many(&testid, &vec![], &db.get_connection()).await;
  //     return Ok(ret.unwrap().to_u64().unwrap_or_default());
  //   }
  //   //所有新项目的组合编号
  //   let new_itemids: Vec<String> = items
  //     .iter()
  //     .filter(|f| f.tj_combineflag == YesOrNo::Yes as i32)
  //     .map(|v| v.tj_itemid.clone())
  //     .collect::<HashSet<String>>()
  //     .into_iter()
  //     .collect();
  //   info!("new combine ids:{:?}", &new_itemids);
  //   // let new_itemids: Vec<&str> = new_itemids.iter().map(|v| v.as_str()).collect();
  //   let ret = TjDeptitem::query_many(&new_itemids, &vec![], &db.get_connection()).await;
  //   if ret.as_ref().is_err() {
  //     error!("{}", ret.as_ref().unwrap_err().to_string());
  //     return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   let deptids: Vec<String> = ret.unwrap().into_iter().map(|v| v.tj_deptid.to_owned()).collect::<HashSet<String>>().into_iter().collect();
  //   // let deptids: Vec<&str> = deptids.iter().map(|v| v.as_str()).collect();
  //   let ret = TjDepartinfo::query_many(&vec![], &deptids, -1, &db.get_connection()).await;
  //   if ret.as_ref().is_err() {
  //     error!("{}", ret.as_ref().unwrap_err().to_string());
  //     return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   let deptinfos = ret.unwrap();
  //   let new_deptids: Vec<String> = deptinfos.iter().map(|v| v.tj_deptid.clone()).collect::<HashSet<_>>().into_iter().collect();
  //   info!("new deptids:{:?}", &new_deptids);
  //   //先处理项目信息
  //   let ret = TjCheckiteminfo::query(&testid, &db.get_connection()).await;
  //   if ret.as_ref().is_err() {
  //     error!("{}", ret.as_ref().unwrap_err().to_string());
  //     return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   let exist_ckitems = ret.unwrap();
  //   let exist_itemids: Vec<String> = exist_ckitems.iter().map(|v| v.tj_synid.to_owned()).collect::<HashSet<_>>().into_iter().collect();
  //   //新项目没有，原项目有，则删除
  //   let del_itemids: Vec<String> = exist_itemids
  //     .iter()
  //     .filter(|f| new_itemids.iter().find(|v| v.eq_ignore_ascii_case(&f)).is_none())
  //     .map(|m| m.to_owned())
  //     .collect();
  //   info!("to del itemids:{:?}", &del_itemids);
  //   let ret = TjCheckiteminfo::delete_many(&testid, &del_itemids, &db.get_connection()).await;
  //   if ret.as_ref().is_err() {
  //     error!("{}", ret.as_ref().unwrap_err().to_string());
  //     return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   //新项目有，原项目没有，则增加
  //   let add_itemids: Vec<String> = new_itemids
  //     .iter()
  //     .filter(|f| exist_itemids.iter().find(|v| v.eq_ignore_ascii_case(&f)).is_none())
  //     .map(|v| v.clone())
  //     .collect();
  //   info!("to add itemids:{:?}", &add_itemids);
  //   let ret = TjCombineinfo::query_many(&add_itemids, &db.get_connection()).await;
  //   if ret.as_ref().is_err() {
  //     error!("{}", ret.as_ref().unwrap_err().to_string());
  //     return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   let combine_infos = ret.unwrap();
  //   info!("new combine items:{:?}", &combine_infos);
  //   let new_item_ids: Vec<String> = combine_infos.into_iter().map(|v| v.tj_itemid.to_owned()).collect::<HashSet<_>>().into_iter().collect();
  //   let mut new_item_ids: Vec<String> = new_item_ids.iter().map(|f| f.clone()).collect();
  //   info!("需要添加的项目编号:{:?}", &new_item_ids);
  //   new_item_ids.extend(add_itemids);
  //   info!("new itemids to be inserted:{:?}", &new_item_ids);
  //   let ret = TjIteminfo::query_many(&new_item_ids, YesOrNo::Yes as i32, -1, -1, &vec![], &db.get_connection()).await;
  //   if ret.as_ref().is_err() {
  //     error!("{}", ret.as_ref().unwrap_err().to_string());
  //     return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   let new_items = ret.unwrap();
  //   let mut new_checkitems: Vec<TjCheckiteminfo> = Vec::new();
  //   for val in new_items {
  //     info!("val is:{:?}", &val);
  //     let ci = TjCheckiteminfo { ..Default::default() };
  //     new_checkitems.push(ci);
  //   }
  //   //处理科室小结信息
  //   let mut new_summaries: Vec<TjTestsummary> = Vec::new();
  //   for val in deptinfos.iter() {
  //     let summary = TjTestsummary {
  //       tj_testid: testid.clone(),
  //       tj_deptid: val.tj_deptid.clone(),
  //       ..Default::default()
  //     };
  //     new_summaries.push(summary);
  //   }
  //   let ret = TjTestsummary::query(&testid, &vec![], &db.get_connection()).await;
  //   if ret.as_ref().is_err() {
  //     error!("{}", ret.as_ref().unwrap_err().to_string());
  //     return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   let exist_summaries = ret.unwrap();
  //   info!("exist summaries:{:?}", &exist_summaries);
  //   Ok(0)
  // }

  pub async fn query_ext_checktitems(testid: &String, db: &DbConnection) -> Result<Vec<ExtCheckiteminfo>> {
    let ret = ExtCheckiteminfo::query_many(&testid, 0, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn update_checktiem_result_flag(tr: &mut TjCheckiteminfo, age: i32, sex: i32, db: &DbConnection) {
    if tr.tj_result.trim().is_empty()
      || tr.tj_result.eq_ignore_ascii_case("阴性(-)")
      || tr.tj_result.eq_ignore_ascii_case("阴性")
      || tr.tj_result.eq_ignore_ascii_case("正常")
      || tr.tj_result.eq_ignore_ascii_case("未见异常")
    {
      tr.tj_abnormalflag = 0;
      tr.tj_abnormalshow = "".to_string();
      return;
    }
    let mut iteminfo = crate::SYSCACHE.get().unwrap().get_iteminfo(&tr.tj_itemid, &db).await;
    if tr.tj_result.eq_ignore_ascii_case(&iteminfo.tj_defaultresult) {
      tr.tj_abnormalflag = 0;
      tr.tj_abnormalshow = "".to_string();
      return;
    }

    let mut age = age;
    let mut sex = sex;
    if age <= 0 || sex <= 0 {
      let testid = tr.tj_testid.clone();
      let ret = TjMedexaminfo::query(&testid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return;
      }
      let medinfo = ret.unwrap();
      if medinfo.is_none() {
        return;
      }
      let medinfo = medinfo.unwrap();
      age = medinfo.tj_age;

      let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return;
      }
      let ptinfo = ret.unwrap();
      if ptinfo.is_some() {
        sex = ptinfo.unwrap().tj_psex;
      }
    }

    let itemrangeinfos = crate::SYSCACHE.get().unwrap().get_item_rangeinfo(&tr.tj_itemid, age, sex).await;
    if itemrangeinfos.len() > 0 {
      iteminfo.tj_lowvalue = itemrangeinfos[0].tj_lowvalue.to_owned();
      iteminfo.tj_uppervalue = itemrangeinfos[0].tj_uppervalue.to_owned();
    }

    if iteminfo.tj_valuetype.eq_ignore_ascii_case(&ValueType::DingLiang.to_string()) {
      let isval_ok = tr.tj_result.parse::<f64>();
      if isval_ok.is_err() {
        // error!("定量类型，但是非数字结果......:{}", &tr.tj_result);
        tr.tj_abnormalflag = 1;
        return;
      }
      let ret_val = isval_ok.unwrap();
      tr.tj_abnormalflag = 0;
      tr.tj_abnormalshow = "".to_string();
      match iteminfo.tj_reftype.to_uppercase().as_str() {
        "X-Y" | "X_Y" => {
          if !iteminfo.tj_lowvalue.is_empty() {
            if let Ok(lowval) = iteminfo.tj_lowvalue.parse::<f64>() {
              if ret_val < lowval {
                tr.tj_abnormalflag = 1;
                tr.tj_abnormalshow = iteminfo.tj_lowoffset.to_owned();
              }
            }
          }
          if !iteminfo.tj_uppervalue.is_empty() {
            if let Ok(highval) = iteminfo.tj_uppervalue.parse::<f64>() {
              if ret_val > highval {
                tr.tj_abnormalflag = 1;
                tr.tj_abnormalshow = iteminfo.tj_highoffset.to_owned();
              }
            }
          }
        }
        "<Y" => {
          if !iteminfo.tj_uppervalue.is_empty() {
            if let Ok(highval) = iteminfo.tj_uppervalue.parse::<f64>() {
              if ret_val >= highval {
                tr.tj_abnormalflag = 1;
                tr.tj_abnormalshow = iteminfo.tj_highoffset.to_owned();
              }
            }
          }
        }
        "<=Y" => {
          if !iteminfo.tj_uppervalue.is_empty() {
            if let Ok(highval) = iteminfo.tj_uppervalue.parse::<f64>() {
              if ret_val > highval {
                tr.tj_abnormalflag = 1;
                tr.tj_abnormalshow = iteminfo.tj_highoffset.to_owned();
              }
            }
          }
        }
        ">X" => {
          if !iteminfo.tj_lowvalue.is_empty() {
            if let Ok(lowval) = iteminfo.tj_lowvalue.parse::<f64>() {
              if ret_val <= lowval {
                tr.tj_abnormalflag = 1;
                tr.tj_abnormalshow = iteminfo.tj_lowoffset.to_owned();
              }
            }
          }
        }
        ">=X" => {
          if !iteminfo.tj_lowvalue.is_empty() {
            if let Ok(lowval) = iteminfo.tj_lowvalue.parse::<f64>() {
              if ret_val < lowval {
                tr.tj_abnormalflag = 1;
                tr.tj_abnormalshow = iteminfo.tj_lowoffset.to_owned();
              }
            }
          }
        }
        _ => {
          let itemresults = crate::SYSCACHE.get().unwrap().get_itemresultinfos(&tr.tj_itemid, db).await;
          for pv in itemresults.into_iter() {
            if pv.tj_itemresult.is_empty() {
              continue;
            }
            if tr.tj_result.contains(&pv.tj_itemresult) && pv.tj_sumflag == YesOrNo::Yes as i32 {
              tr.tj_abnormalflag = 1;
              break;
            }
          }
        }
      }
    } else {
      // info!("非定量项目，检查参考结果......");
      let itemresults = crate::SYSCACHE.get().unwrap().get_itemresultinfos(&tr.tj_itemid, db).await;
      for pv in itemresults.into_iter() {
        if pv.tj_itemresult.is_empty() {
          continue;
        }

        // info!("item result info:{:?}", &pv);
        if tr.tj_result.contains(&pv.tj_itemresult) && pv.tj_sumflag == YesOrNo::Yes as i32 {
          // info!("满足判定异常的标准");
          tr.tj_abnormalflag = 1;
          break;
        }
      }
    }
  }

  pub async fn update_checktiem_result_flag_without_dingliang(tr: &mut TjCheckiteminfo, db: &DbConnection) {
    tr.tj_abnormalflag = 0;
    tr.tj_abnormalshow = "".to_string();
    if tr.tj_result.trim().is_empty()
      || tr.tj_result.eq_ignore_ascii_case("阴性")
      || tr.tj_result.eq_ignore_ascii_case("阴性(-)")
      || tr.tj_result.eq_ignore_ascii_case("正常")
      || tr.tj_result.contains("未见异常")
    {
      // tr.tj_abnormalflag = 0;
      // tr.tj_abnormalshow = "".to_string();
      return;
    }
    let iteminfo = crate::SYSCACHE.get().unwrap().get_iteminfo(&tr.tj_itemid, &db).await;
    if tr.tj_result.eq_ignore_ascii_case(&iteminfo.tj_defaultresult) {
      tr.tj_abnormalflag = 0;
      tr.tj_abnormalshow = "".to_string();
      return;
    }
    if iteminfo.tj_valuetype.eq_ignore_ascii_case(&ValueType::DingLiang.to_string()) {
      //定量结果，不处理
      return;
    }
    // info!("非定量项目，检查参考结果......");
    let itemresults = crate::SYSCACHE.get().unwrap().get_itemresultinfos(&tr.tj_itemid, db).await;
    for pv in itemresults.into_iter() {
      if pv.tj_itemresult.is_empty() {
        continue;
      }

      // info!("item result info:{:?}", &pv);
      if tr.tj_result.eq_ignore_ascii_case(&pv.tj_itemresult) && pv.tj_sumflag == YesOrNo::Yes as i32 {
        // info!("满足判定异常的标准");
        tr.tj_abnormalflag = 1;
        break;
      }
    }
  }
}
