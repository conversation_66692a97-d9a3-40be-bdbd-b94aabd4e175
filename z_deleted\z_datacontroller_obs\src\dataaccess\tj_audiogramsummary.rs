use crate::{datasetup::DbConnection, entities::prelude::*};
use anyhow::{anyhow, Result};
use rbatis::crud::{Skip, CRUD};
use rbson::<PERSON>son;

impl TjAudiogramsummary {
    pub async fn query(testid: &String, db: &DbConnection) -> Option<TjAudiogramsummary> {
        if testid.is_empty() {
            return None;
        }
        let w = db.get_connection().new_wrapper().eq("tj_testid", testid); //

        let query_ret = db.get_connection().fetch_by_wrapper(w).await;

        match query_ret {
            Ok(v) => v,
            Err(e) => {
                error!("query error:{}", e);
                None
            }
        }
    }

    pub async fn query_many(testids: &Vec<String>, db: &DbConnection) -> Result<Vec<TjAudiogramsummary>> {
        if testids.len() <= 0 {
            return Err(anyhow!("testids empty"));
        }
        let w = db.get_connection().new_wrapper().do_if(testids.len() > 0, |w| w.r#in("tj_testid", &testids));

        let query_ret = db.get_connection().fetch_list_by_wrapper(w).await;

        match query_ret {
            Ok(v) => Ok(v),
            Err(e) => {
                error!("query error:{}", &e);
                Err(anyhow!("query many error:{:?}", &e))
            }
        }
    }

    // pub async fn query_many_by_dto(dto: &AudiogramDTO, db: &DbConnection) -> Result<Vec<TjAudiogramsummary>> {
    //     info!("query patient dto:{:#?}", &dto);
    //     let w = db
    //         .get_connection()
    //         .new_wrapper()
    //         .do_if(dto.testid.len() > 0, |w| w.r#in("tj_testid", dto.testid.to_owned().as_slice()))
    //         .do_if(dto.transear >= 0, |w| w.eq("tj_ear", dto.transear))
    //         .do_if(!dto.transtype >= 0, |w| w.eq("tj_adtype", dto.transtype))
    //         .do_if(!dto.freq > 0, |w| w.eq("tj_freq", dto.freq));

    //     let query_ret = db.get_connection().fetch_list_by_wrapper(w).await;
    //     // info!("query patient result:{:?}", &query_ret);
    //     match query_ret {
    //         Ok(v) => Ok(v),
    //         Err(e) => {
    //             error!("query error:{}", &e);
    //             Err(anyhow!("query many error:{:?}", &e))
    //         }
    //     }
    // }

    pub async fn save(info: &TjAudiogramsummary, db: &DbConnection) -> Result<TjAudiogramsummary> {
        if info.id == 0 {
            let ret = TjAudiogramsummary::insert(info, db).await;
            if ret.as_ref().is_err() {
                error!("error:{:?}", ret.as_ref().err().unwrap());
                return Err(anyhow!("error:{:?}", ret.as_ref().err().unwrap()));
            }
            let id = ret.unwrap();
            let mut ret_val = info.clone();
            ret_val.id = id;
            return Ok(ret_val);
        } else {
            let ret = TjAudiogramsummary::update(info, db).await;
            if ret.as_ref().is_err() {
                error!("error:{:?}", ret.as_ref().err().unwrap());
                return Err(anyhow!("error:{:?}", ret.as_ref().err().unwrap()));
            }
            Ok(info.clone())
        }
    }
    pub async fn save_many(infos: &Vec<TjAudiogramsummary>, db: &DbConnection) -> Result<()> {
        let (insert_vals, update_vals): (Vec<TjAudiogramsummary>, Vec<TjAudiogramsummary>) = infos.to_owned().into_iter().partition(|f| f.id == 0);
        if insert_vals.len() > 0 {
            let ret = TjAudiogramsummary::insert_many(&insert_vals, &db).await;
            if ret.as_ref().is_err() {
                error!("insrt many error: {:?}", ret.as_ref().err());
                return Err(anyhow::anyhow!("insert many error"));
            }
        }

        if update_vals.len() > 0 {
            for val in infos.iter() {
                let ret = db.get_connection().update_by_column("id", val).await;
                if ret.is_err() {
                    error!("update  error: {:?}", ret.err().unwrap().to_string());
                    return Err(anyhow::anyhow!("update many error"));
                }
            }
        }

        Ok(())
    }

    pub async fn insert(info: &TjAudiogramsummary, db: &DbConnection) -> Result<i64> {
        let ret = db.get_connection().save(info, &[]).await;
        if ret.as_ref().is_err() {
            error!("保存出错，错误信息：{}", ret.as_ref().err().unwrap().to_string());
            return Err(anyhow!("保存出错，错误信息：{}", ret.as_ref().err().unwrap().to_string()));
        }
        info!("result:{:?}", &ret);
        let id = ret.as_ref().unwrap().last_insert_id.map_or(0, |v| v);
        Ok(id)
    }

    pub async fn insert_many(infos: &Vec<TjAudiogramsummary>, db: &DbConnection) -> Result<()> {
        let ret = db.get_connection().save_batch(&infos, &[]).await;
        if ret.as_ref().is_err() {
            error!("保存信息失败:{}", ret.as_ref().err().unwrap().to_string());
            return Err(anyhow!("保存信息失败:{}", ret.as_ref().err().unwrap().to_string()));
        }

        Ok(())
    }

    pub async fn update(info: &TjAudiogramsummary, db: &DbConnection) -> Result<()> {
        let ret = db.get_connection().update_by_column("id", info).await;
        match ret {
            Ok(_v) => Ok(()),
            Err(e) => Err(anyhow!("update error: {}", e)),
        }
    }

    pub async fn update_may(vals: &Vec<TjAudiogramsummary>, conn: &DbConnection) -> Result<()> {
        //方法1： 采用wrapper的方法更新
        for val in vals {
            let w = conn.get_connection().new_wrapper().eq("id", val.id);
            let ret = conn.get_connection().update_by_wrapper(val, w, &[Skip::Value(Bson::Null), Skip::Column("id")]).await;
            if ret.as_ref().is_err() {
                error!("更新错误,错误信息:{},资产信息:{:#?}", ret.as_ref().err().unwrap().to_string(), &val);
                // return Err(anyhow!("update user assets info error"));
                continue;
            }

            let updated_rows = ret.unwrap();
            if updated_rows <= 0 {
                let insert_ret = conn.get_connection().save(&val, &[]).await;
                if insert_ret.as_ref().is_err() {
                    error!("插入错误,错误信息:{},资产信息:{:#?}", insert_ret.as_ref().err().unwrap().to_string(), &val);
                    continue;
                }
            }
        }
        Ok(())
    }
}
