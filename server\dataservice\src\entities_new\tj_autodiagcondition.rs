//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_autodiagcondition")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_disid: i32,
  pub tj_hazardfactor: i32,
  pub tj_metype: i32,
  pub tj_itemid: String,
  pub tj_operator: String,
  pub tj_refvalue: String,
  pub tj_conditionsymbol: String,
  pub tj_order: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
