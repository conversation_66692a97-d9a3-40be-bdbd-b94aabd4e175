use rbatis::{crud};
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;

#[derive(<PERSON><PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjGuideitem {
  pub id: i64,
  pub tj_guideid: i64,
  pub tj_itemid: String,
}
crud!(TjGuideitem {}, "tj_guideitem");
// rbatis::impl_select!(TjGuideitem{query_many(ids:&[i64]) => "`where tj_guideid in ${ids.sql()} `"});
rbatis::impl_select!(TjGuideitem{query_many(ids:&[i64]) =>
  "`where id > 0 `
  if !ids.is_empty():
    ` and tj_guideid in ${ids.sql()} `"});
rbatis::impl_delete!(TjGuideitem{delete(guideids:&[i64], itemids:&[String]) => "`where id > 0 and tj_guideid in ${guideids.sql()} and tj_itemid in ${itemids.sql()}`"});
