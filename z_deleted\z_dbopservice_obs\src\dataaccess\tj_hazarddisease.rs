
use anyhow::{anyhow, Result};
use rbatis::{crud};
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;

#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjHazarddisease {
  pub id: i64,
  pub tj_hid: i32,
  pub tj_testtype: i32,
  pub tj_forbidden: String,
  pub tj_targetdisease: String,
  pub tj_memo: String,
}
crud!(TjHazarddisease {}, "tj_hazarddisease");
rbatis::impl_select!(TjHazarddisease{query_many(ids:&[i64], testtype:i32) => 
  "`where id > 0 `
  if !ids.is_empty():
    ` and tj_hid in ${ids.sql()} `
  if testtype > 0:
    ` and tj_testtype = #{testtype} `"});
rbatis::impl_delete!(TjHazarddisease{delete(ids:&[i64]) => "`where id in ${ids.sql()} `"});

impl TjHazarddisease {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjHazarddisease, ) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjHazarddisease::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjHazarddisease::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
}
