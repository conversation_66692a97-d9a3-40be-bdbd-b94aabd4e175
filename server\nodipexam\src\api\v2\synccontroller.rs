use crate::api::httpresponse::{response_json_error, response_json_ok, response_json_value_error, response_json_value_ok, response_value_error, response_value_ok};
use crate::auth::auth::Claims;
use crate::common::filesvc::FileSvc;
use crate::config::settings::Settings;
use axum::extract::Multipart;
use axum::{Extension, Json};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};

use nodipservice::sync::syncdto::CorpuserDto;
use nodipservice::{dto::*, sync::medsyncsvc::MedsyncService};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

pub async fn sync_medinfos_from_polar(_claims: Claims, Extension(settings): Extension<Arc<Settings>>, Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  let userinfo = TjStaffadmin {
    tj_staffno: "admin".to_string(),
    ..Default::default()
  };
  let ret = MedsyncService::sync_medinfo_and_corpinfos_from_polar(&userinfo, settings.polar.orgid, &settings.polar.server, &settings.application.splitter, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjHazardinfo>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn sync_medresults_with_report_to_polar(Extension(db): Extension<Arc<DbConnection>>, Extension(settings): Extension<Arc<Settings>>, mut multipart: Multipart) -> Json<Value> {
  info!("start to synd medresults with resport to polar......");
  if settings.polar.server.is_empty() {
    return response_json_error("sync server is empty");
  }
  if settings.polar.orgid <= 0 {
    return response_json_error("invalid orgid");
  }

  let mut rptfile = "".to_string();
  let mut testid = "".to_string();
  while let Some(field) = multipart.next_field().await.unwrap() {
    let file_name = field.file_name();
    let name = field.name();
    info!("Field file name:{:?}", &field.file_name());
    info!("field name: {:?}", &field.name());
    if file_name.is_none() || name.is_none() {
      return response_json_error("invalid parameters");
    }
    testid = name.unwrap().to_string();
    if testid.is_empty() || testid == "0" {
      return response_json_error("invalid testid");
    }
    rptfile = file_name.unwrap().to_string();
    if rptfile.is_empty() {
      return response_json_error("invalid report file");
    }
    let ret = field.bytes().await;
    if ret.as_ref().is_err() {
      return response_json_error(&ret.err().unwrap().to_string());
    }
    let data = ret.unwrap();
    let ret = FileSvc::save_file(&settings.application.uploaddir, &rptfile, &data).await;
    if ret.as_ref().is_err() {
      return response_json_error(&ret.err().unwrap().to_string());
    }
    rptfile = ret.unwrap();
  }
  info!("testid to upload is:{}", &testid);
  info!("report to upload is:{}", &rptfile);

  let ret = MedsyncService::sync_medexam_to_polar(settings.polar.orgid, &testid, &rptfile, &settings.polar.server, &db).await;
  if ret.as_ref().is_err() {
    return response_json_error(&ret.err().unwrap().to_string());
  }
  response_json_ok("")
}

pub async fn sync_medresults_to_polar(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Extension(settings): Extension<Arc<Settings>>, Json(dto): Json<KeyDto>) -> Json<Value> {
  let ret = MedsyncService::sync_medexam_to_polar(settings.polar.orgid, &dto.key, "", &settings.polar.server, &db).await;
  if ret.as_ref().is_err() {
    return response_json_error(&ret.err().unwrap().to_string());
  }
  response_json_ok("")
}

pub async fn sync_medstatus_to_polar(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeysDto>) -> Json<Value> {
  // let typeids = dto.keys_int.to_owned();
  // let hdnames = dto.keys_str.to_owned();
  info!("dto is:{:?}", &dto);
  let ret = MedsyncService::sync_medexam_to_polar(0, "", "", "", &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjHazardinfo>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn add_corp_users(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Extension(settings): Extension<Arc<Settings>>, Json(dto): Json<CorpuserDto>) -> Json<Value> {
  // let hdnames = dto.keys_str.to_owned();
  // settings.polar.orgid, &testid, &rptfile, &settings.polar.server,
  let ret = MedsyncService::add_polar_corp_users(&dto, &settings.polar.server, settings.polar.orgid, &db).await;
  info!("add polar user response:{:#?}", &ret);
  if ret.as_ref().is_err() {
    return response_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_value_ok("", results)
}
