use super::v1::{httpcontroller, medexamcontroller, testcontroller};
use axum::{
  http::StatusCode,
  response::IntoResponse,
  routing::{get, post, put},
  Router,
};

pub fn create_route() -> Router {
  let v1_route = create_v1_route();

  let app = Router::new().route("/*path", get(httpcontroller::download_files))
    .nest("/api", v1_route)
    .nest("/apix", create_v2_route())
    // .layer(middleware::from_fn(crate::nmiddleware::nmiddleware::print_request_body))
    // .layer(ServiceBuilder::new().layer(Extension(dbconn)))
    // .layer(ServiceBuilder::new().layer(Extension(setting)));
    ;
  // add a fallback service for handling routes to unknown paths
  let app = app.fallback(handler_404);
  app
}

fn create_v1_route() -> Router {
  let app_route = Router::new().merge(sync_router());
  let v1_route = Router::new().nest("/v1", app_route);
  v1_route
}

fn create_v2_route() -> Router {
  let app_router = Router::new()
    .nest("/medexam", Router::new().route("/result", post(medexamcontroller::upload_medresults)))
    .nest(
      "/upload",
      Router::new()
        .route("/report", post(httpcontroller::upload_report))
        .route("/photo", post(httpcontroller::upload_photo)),
    )
    .nest(
      "/download",
      Router::new()
        .route("/report", post(httpcontroller::upload_report))
        .route("/photo", post(httpcontroller::download_photo)),
    );
  let v2_route = Router::new().nest("/v2", app_router);
  v2_route
}

fn sync_router() -> Router {
  let med_router = Router::new().nest(
    "/med",
    Router::new()
      .route("/query", post(medexamcontroller::sync_medinfo))
      .route("/status", post(medexamcontroller::update_medstatus))
      .route("/result", post(medexamcontroller::upload_medresults))
      .route("/medret", post(medexamcontroller::upload_medexam_results_with_report)),
  );

  let corp_router = Router::new().nest(
    "/corp",
    Router::new()
      .route("/query", post(medexamcontroller::sync_corpinfo))
      .route("/status", post(medexamcontroller::sync_corpstatus))
      .route("/report", post(medexamcontroller::upload_corpreports)),
  );

  let photo_router = Router::new().nest(
    "/photo",
    Router::new()
      .route("/upload", post(httpcontroller::upload_photo))
      .route("/download", post(httpcontroller::download_photo)),
  );

  let report_router = Router::new().nest("/upload", Router::new().route("/report", post(httpcontroller::upload_report)));

  let file_router = Router::new().nest(
    "/file",
    Router::new()
      .route("/upload", post(httpcontroller::upload_file))
      .route("/download", post(httpcontroller::download_files)),
  );

  let test_router = Router::new().route(
    "/test",
    put(testcontroller::protected)
      .post(testcontroller::authorize)
      .get(testcontroller::test_get)
      .delete(testcontroller::test_delete),
  );

  let app_router = Router::new()
    .merge(med_router)
    .merge(corp_router)
    .merge(photo_router)
    .merge(file_router)
    .merge(report_router)
    .merge(test_router);

  app_router
}

pub async fn handler_404() -> impl IntoResponse {
  (StatusCode::NOT_FOUND, "nothing to see here")
}
