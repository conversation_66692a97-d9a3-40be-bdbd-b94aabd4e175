#coding=utf-8

import sys
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from personalreport.poccureport import generate_pdf_personal_occureport
from corpreport.normalreport import generate_pdf_corp_normalreport
from dataentities.dbconn import session
from dataentities.tj_corpoccureport import TjCorpoccureport
from dataentities.ss_dictionary import SsDictionary
from dataentities.tj_checkallnew import TjCheckallnew
from dataentities.tj_corpoccureportinfo import TjCorpoccureportinfo
from dataentities.tj_patient import TjPatient
from dataentities.tj_medexaminfo import TjMedexaminfo
from dataentities.tj_testsummary import TjTestsummary
import constant
from dataentities.tj_corpinfo import TjCorpinfo
from dataentities.ss_area import SsArea
from dataentities.tj_checkiteminfo import TjCheckiteminfo
from dataentities.tj_departinfo import TjDepartinfo
from dataentities.tj_audiogramdetail import TjAudiogramdetail
from dataentities.tj_staffadmin import TjStaffadmin

# pdfmetrics.registerFont(TTFont('SimSun', 'SimSun.ttc'))  # 注册字体
# pdfmetrics.registerFont(TTFont('SimSun', 'msyh.ttc'))  # 注册字体
pdfmetrics.registerFont(TTFont("SimHei", "./fonts/simhei.ttf"))
pdfmetrics.registerFont(TTFont("SimSun", "./fonts/simsun.ttc"))  # 注册字体
pdfmetrics.registerFont(TTFont("SimSunHei", "./fonts/SimSun-Bold.ttf"))


def main(testid, filetype, rpttype, splitinrow, outdir):
    if testid == "":
        print("体检编号无效")
        return
    print("需要处理的体检编号:", testid)

    dicts = session.query(SsDictionary).all()

    cusotmer_dict = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictSysparm.value
        and dict.ss_pid == constant.SysParm.Customer.value
    )
    if cusotmer_dict is None:
        customer = ""
    else:
        customer = cusotmer_dict.ss_short.upper()
    # print("customer is:", customer)

    medinfo = (
        session.query(TjMedexaminfo).filter(TjMedexaminfo.tj_testid == testid).first()
    )
    if medinfo is None:
        print("体检编号的信息不存在")
        return
    corpinfo = session.query(TjCorpinfo).filter(TjCorpinfo.id == medinfo.tj_corpnum).first()
    
    if corpinfo is None:
        print("编号为:{}的体检单位不存在".format(medinfo.tj_corpnum))
        return
    
    areainfo = session.query(SsArea).filter(SsArea.area_code == corpinfo.tj_areacode).first()
    if areainfo is None:
        areaname = ""
    else:
        areaname = areainfo.area_fullname

    checkallinfo = session.query(TjCheckallnew).filter(TjCheckallnew.tj_testid == testid).first()
    checkitems = session.query(TjCheckiteminfo).filter(TjCheckiteminfo.tj_testid == testid).all()
    patient = session.query(TjPatient).filter(TjPatient.tj_pid == medinfo.tj_pid).first()
    staffs = session.query(TjStaffadmin).filter(TjStaffadmin.id >= 0).all()

    summaires = session.query(TjTestsummary).filter(TjTestsummary.tj_testid == testid).all()
    deptids = set()
    for sum in summaires:
        deptids.add(sum.tj_deptid)
    print("all deptids:",deptids)
    deptinfos = session.query(TjDepartinfo).filter(TjDepartinfo.tj_deptid.in_(deptids)).all()

    audiogram_dept = next((dept for dept in deptinfos if dept.tj_deptid == constant.AUDIOGRAM_DEPTID),None)
    audiogramdetails = []
    if audiogram_dept is not None:
        # print("有电测听数据。。。。。。")
        audiogramdetails = session.query(TjAudiogramdetail).filter(TjAudiogramdetail.tj_testid == testid).all()

    if outdir == "":
        outdir = "./reports"
    pagestyle = 0

    if filetype == constant.ReportFormat.Pdf.value:
        if rpttype == 0:  # 职业健康报告
            output = generate_pdf_personal_occureport(
                medinfo,
                patient,
                summaires,
                checkitems,audiogramdetails,
                checkallinfo,             
                corpinfo,
                deptinfos,
                dicts,
                customer,
                staffs,
                pagestyle,
                splitinrow,
                outdir,
                areaname,
            )
            print("output is:", output)
            # os.path.join(rptdir, output),
        elif rpttype == 1: # 普通报告
            pass
    else:
        pass


if __name__ == "__main__":
    n = len(sys.argv)
    print("Total arguments passed:", n)
    if n <= 4:
        print("请输入参数不对，无法处理")
        sys.exit()
    try:
        testid = sys.argv[1]
        filetype = int(sys.argv[2])
        rpttype = int(sys.argv[3])
        splitinrow = int(sys.argv[4])
        outdir = sys.argv[5]
    except ValueError:
        print("报告编号或者输出地址无效")
        sys.exit()

    sys.exit(main(testid, filetype, rpttype, splitinrow, outdir))
