use rbatis::crud;
// use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct VTjPacsresult {
  pub id: i64,
  pub tjbh: String,
  pub brxm: String,
  pub jclx: String,
  pub jcxm: String,
  pub jcmc: String,
  pub imagesight: String,
  pub imagediagnosis: String,
  pub jcys: String,
  pub sxys: String,
  pub bgys: String,
  pub bgrq: String,
  pub sfyc: i32,
}

crud!(VTjPacsresult {}, "v_tj_pacsresult");
rbatis::impl_select!(VTjPacsresult{select_all_by_testid(testid:&str) => "`where tjbh = #{testid} `"}, "v_tj_pacsresult");
rbatis::impl_delete!(VTjPacsresult{delete_many(testid:&str) => "`where tjbh = #{testid} `"}, "v_tj_pacsresult");
rbatis::impl_delete!(VTjPacsresult{clear(date:&String) => "`where bgrq < #{date} `"}, "v_tj_pacsresult");

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct VTjPacsresultV2 {
  pub id: i64,
  pub tjbh: String,
  pub brxm: String,
  pub jclx: String,
  pub jcxm: String,
  pub jcmc: String,
  pub imagesight: String,
  pub imagediagnosis: String,
  pub jcys: String,
  pub sxys: String,
  pub bgys: String,
  pub bgrq: String,
  pub sfyc: String,
}

crud!(VTjPacsresultV2 {}, "v_tj_pacsresult");
rbatis::impl_select!(VTjPacsresultV2{select_all_by_testid(testid:&str) => "`where tjbh = #{testid} `"}, "v_tj_pacsresult");
