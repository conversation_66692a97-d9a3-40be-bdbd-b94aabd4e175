//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "gj_cdc_checkitem")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub cdc_itemcode: String,
  pub cdc_item_name: String,
  pub cdc_item_level: i8,
  pub cdc_item_pcode: String,
  pub cdc_is_final: i8,
  pub tj_itemid: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
