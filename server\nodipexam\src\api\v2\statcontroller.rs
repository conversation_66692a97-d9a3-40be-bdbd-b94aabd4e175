use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok}, auth::auth::Claims, common::filesvc::FileSvc, config::settings::Settings
};
use axum::{response::IntoResponse, Extension, Json};
// use dbopservice::{dbinit::DbConnection};
use dataservice::dbinit::DbConnection;
use hyper::StatusCode;
use nodipservice::{dto::*, medexam::statsvc::StatSvc};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

//query_summaries
pub async fn stat_summaries(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<StatSummaryDto>) -> Json<Value> {
  info!("query summary dto:{:?}", &dto);

  let ret = StatSvc::stat_summaries(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<StatSummaryResponse>::new());
  }
  let results = ret.unwrap();
  info!("Total is:{}", results.len());
  response_json_value_ok(results.len() as u64, results)
}

pub async fn stat_hazardinfos_with_docx(
  _claims: Claims,
  Extension(config): Extension<Arc<Settings>>,
  Extension(db): Extension<Arc<DbConnection>>,
  Json(dto): Json<StatSummaryDto>,
) -> impl IntoResponse {
  info!("stat summary dto:{:?}", &dto);
  if config.hazardstats.is_none() {
    return Err((StatusCode::NOT_FOUND, "没有配置的统计文件信息，无法统计".to_string()));
  }
  let report_dir = config.application.reportdir.to_string();
  let stats = config.hazardstats.as_ref().unwrap().to_owned();

  let handle = tokio::spawn(async move {
    let ret = StatSvc::generate_stat_hazardinfos_docx(dto.stdate, dto.enddate, &stats, &report_dir, &db).await;
    // tx.send(ret).await;
    ret
  });
  let ret = handle.await.unwrap();
  if ret.as_ref().is_err() {
    error!("generate report file error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }
  let rptfile = ret.unwrap();

  info!("generated report is:{}", &rptfile);
  let ret = FileSvc::do_download(&format!("{}", rptfile)).await;
  if ret.as_ref().is_err() {
    error!("download file error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }

  Ok(ret.unwrap())
}

pub async fn stat_deptitem_with_docx(
  _claims: Claims,
  Extension(config): Extension<Arc<Settings>>,
  Extension(db): Extension<Arc<DbConnection>>,
  Json(dto): Json<StatSummaryDto>,
) -> impl IntoResponse {
  info!("stat dept item dto:{:?}", &dto);

  let report_dir = config.application.reportdir.to_string();

  let handle = tokio::spawn(async move {
    let ret = StatSvc::stat_dept_item(dto.stdate, dto.enddate, &report_dir, &db).await;
    // tx.send(ret).await;
    ret
  });
  let ret = handle.await.unwrap();
  if ret.as_ref().is_err() {
    error!("generate dept item file error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }
  let rptfile = ret.unwrap();

  // info!("generated report is:{}", &rptfile);
  let ret = FileSvc::do_download(&format!("{}", rptfile)).await;
  if ret.as_ref().is_err() {
    error!("download file error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }

  Ok(ret.unwrap())
}

pub async fn expoert_fees_summary(_claims: Claims, config: Extension<Arc<Settings>>, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<StatSummaryDto>) -> impl IntoResponse {
  let stdate = dto.stdate;
  let enddate = dto.enddate;
  let deptids = dto.deptids.to_owned();
  let ret = StatSvc::export_fee_by_departments(stdate, enddate, &deptids, &config.application.reportdir, &db).await;
  if ret.as_ref().is_err() {
    error!("export summary error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }
  let rptfile = ret.unwrap();

  info!("generated fee report is:{}", &rptfile);
  let ret = FileSvc::do_download(&format!("{}", rptfile)).await;
  if ret.as_ref().is_err() {
    error!("download file error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }
  Ok(ret.unwrap())
}
