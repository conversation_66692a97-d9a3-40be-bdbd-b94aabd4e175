use anyhow::{anyhow, Result};
use rbatis::rbatis_codegen::IntoSql;
use rbatis::{crud, executor::Executor, py_sql, sql};
use serde::{Deserialize, Serialize};

// #[crud_table(id_name:"id"|id_type:"i64"|table_name:"ext_checkiteminfo")]
#[derive(<PERSON><PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct ExtCheckiteminfo {
  pub id: i64,
  pub testid: String,
  pub tid: String,
  pub testername: String,
  pub idcard: String,
  pub psex: i32,
  pub csex: String,
  pub birthdate: String,
  pub phone: String,
  pub age: i32,
  pub itemname: String,
  pub itemid: String,
  pub itemid2: String,
  pub deptid: String,
  pub deptname: String,
  pub depttype: i32,
  pub requesterid: String,
  pub requestername: String,
  pub requestdate: i64,
  pub requestdate2: String,
  pub paytype: i32,
  pub packagename: String,
  pub zdym: String,
  pub sampletype: String,
  pub corpnum: i64,
  pub syncstatus: i32,
  pub uid: i64,
  pub lis: i32,
  pub pacs: i32,
}
crud!(ExtCheckiteminfo {}, "ext_checkiteminfo"); //crud = insert+select_by_column+update_by_column+delete_by_column
rbatis::impl_select!(ExtCheckiteminfo{query_many_by_method(testid:&str, depttype:i32) => "`where testid = #{testid} and depttype = #{depttype} `"});
rbatis::impl_delete!(ExtCheckiteminfo{delete_many(testid:&str) => "`where testid = #{testid} `"});
rbatis::impl_delete!(ExtCheckiteminfo{delete_many_by_ids(ids:&[i64]) => "`where id in ${ids.sql()} `"});
rbatis::impl_select!(ExtCheckiteminfo{query_many(testid:&str, depttype:i32) =>
  "`where id > 0 `
  if testid != '':
    ` and testid = #{testid} `
  if depttype > 0:
    ` and depttype = #{depttype} `"});
rbatis::impl_select!(ExtCheckiteminfo{query_many_by_testid_or_barcode(testid:&str, barcode:&str, depttype:i32) =>
  "`where id > 0 `
  if testid != '':
    ` and testid = #{testid} `
  if barcode != '':
    ` and tid = #{barcode} `
  if depttype > 0:
    ` and depttype = #{depttype} `"});
rbatis::impl_select!(ExtCheckiteminfo{query_many_by_method2(testids:&[&str], deptids:&[&str], depttype:i32) =>
  "`where id > 0 `
  if !testids.is_empty():
    ` and testid in ${testids.sql()} `
  if !deptids.is_empty():
    ` and deptid in ${deptids.sql()} `
  if depttype > 0:
    ` and depttype = #{depttype} `"});

rbatis::impl_delete!(ExtCheckiteminfo{clear(date:i64) => "`where requestdate < #{date} `"});
// rbatis::impl_delete!(ExtCheckiteminfo{delete_many(ids:&[i64]) => "`where id in ${ids.sql()} `"});

// impl_select!(ExtCheckiteminfo{select_by_method(ids:&[&str]) -> Vec => "`where id in ${ids.sql()}  limit 1`"});
// impl_select_page!(SysUser{select_page(name:&str,account:&str)=>
//     "`where del = 0`
//     if name != '':
//       ` and name like #{'%'+name+'%'}`
//     if account != '':
//       ` and account like #{'%'+account+'%'}`
//     if !sql.contains('count(1)'):
//      ` order by create_date desc`"});

impl ExtCheckiteminfo {
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<ExtCheckiteminfo>) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<ExtCheckiteminfo>, Vec<ExtCheckiteminfo>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = ExtCheckiteminfo::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = ExtCheckiteminfo::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }

  #[py_sql(
    "`select * from ext_checkiteminfo where id > 0 `
                  if testid != '':
                    ` and testid=#{testid} `
                  if !deptids.is_empty():
                    ` and deptid in ${deptids.sql()} `                   
                  if depttype > 0:
                    ` and depttype = #{depttype} `
                  "
  )]
  async fn query_many_by_dto(rb: &mut dyn Executor, testid: &str, deptids: &[&str], depttype: i32) -> Result<Vec<ExtCheckiteminfo>, rbatis::Error> {
    impled!()
  }

  #[sql("select * from ext_checkiteminfo where testid = ? and depttype = ?")]
  pub async fn query(rb: &mut dyn Executor, testid: &str, depttype: &i32) -> rbatis::Result<Vec<ExtCheckiteminfo>> {
    impled!()
  }
}
