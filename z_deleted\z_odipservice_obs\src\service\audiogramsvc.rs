use std::collections::HashSet;

use datacontroller::entities::{tj_audiogramdetail::TjAudiogramdetail, tj_medexaminfo::TjMedexaminfo};

use crate::{constant, dataview::nxmodel::NxAudiogramdetail};

pub struct AudiogramSvc;

impl AudiogramSvc {
  pub fn convert_to_saas_audiogram_details(details: &Vec<TjAudiogramdetail>, medinfo: &TjMedexaminfo, orgid: i64) -> Vec<NxAudiogramdetail> {
    let mut results: Vec<NxAudiogramdetail> = Vec::new();
    let freqs: Vec<i32> = details.iter().map(|f| f.tj_freq).collect::<HashSet<i32>>().into_iter().collect();
    info!("freqs:{:?}", &freqs);

    let default_result = -1000;
    for freq in freqs.into_iter() {
      let freq_val: Vec<&TjAudiogramdetail> = details.iter().filter(|&p| p.tj_freq == freq).map(|v| v).collect();
      let mut result = NxAudiogramdetail { ..Default::default() };
      result.p_medid = medinfo.p_medid;
      result.tj_testid = medinfo.tj_testid.to_owned();
      result.id = 0;
      result.tj_freq = freq;
      result.p_orgid = orgid;
      result.tj_itemid = "".to_string();
      result.tj_zeqd = freq_val
        .iter()
        .find(|&p| p.tj_freq == freq && p.tj_ear == constant::TransEar::Left as i32 && p.tj_adtype == constant::TransType::Air as i32)
        .map_or(default_result, |f| f.tj_result);
      result.tj_zeqdj = freq_val
        .iter()
        .find(|&p| p.tj_freq == freq && p.tj_ear == constant::TransEar::Left as i32 && p.tj_adtype == constant::TransType::Air as i32)
        .map_or(default_result, |f| f.tj_revise);
      result.tj_zegd = freq_val
        .iter()
        .find(|&p| p.tj_freq == freq && p.tj_ear == constant::TransEar::Left as i32 && p.tj_adtype == constant::TransType::Bone as i32)
        .map_or(default_result, |f| f.tj_result);
      result.tj_zegdj = freq_val
        .iter()
        .find(|&p| p.tj_freq == freq && p.tj_ear == constant::TransEar::Left as i32 && p.tj_adtype == constant::TransType::Bone as i32)
        .map_or(default_result, |f| f.tj_revise);

      result.tj_yeqd = freq_val
        .iter()
        .find(|&p| p.tj_freq == freq && p.tj_ear == constant::TransEar::Right as i32 && p.tj_adtype == constant::TransType::Air as i32)
        .map_or(default_result, |f| f.tj_result);
      result.tj_yeqdj = freq_val
        .iter()
        .find(|&p| p.tj_freq == freq && p.tj_ear == constant::TransEar::Right as i32 && p.tj_adtype == constant::TransType::Air as i32)
        .map_or(default_result, |f| f.tj_revise);
      result.tj_yegd = freq_val
        .iter()
        .find(|&p| p.tj_freq == freq && p.tj_ear == constant::TransEar::Right as i32 && p.tj_adtype == constant::TransType::Bone as i32)
        .map_or(default_result, |f| f.tj_result);
      result.tj_yegdj = freq_val
        .iter()
        .find(|&p| p.tj_freq == freq && p.tj_ear == constant::TransEar::Right as i32 && p.tj_adtype == constant::TransType::Bone as i32)
        .map_or(default_result, |f| f.tj_revise);

      results.push(result);
    }

    results
  }
}
