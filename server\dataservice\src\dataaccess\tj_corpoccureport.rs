use crate::entities::{prelude::*, tj_corpoccureport, tj_corpoccureport_info};
use anyhow::{anyhow, Result};
use sea_orm::{
  sea_query::{Expr, Query},
  ActiveModelTrait,
  ActiveValue::NotSet,
  ColumnTrait, Condition, DatabaseConnection, DbBackend, EntityTrait, QueryFilter, QueryTrait, Set,
};
use serde_json::json;
use log::*;

impl TjCorpoccureport {
  pub async fn query(id: i64, db: &DatabaseConnection) -> Result<Option<TjCorpoccureport>> {
    if id <= 0 {
      return Err(anyhow!("id <= 0"));
    }
    let ret = TjCorpoccureportEntity::find().filter(tj_corpoccureport::Column::Id.eq(id)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_by_rptnum(rptnum: &str, db: &DatabaseConnection) -> Result<Option<TjCorpoccureport>> {
    if rptnum.is_empty() {
      return Err(anyhow!("rpt num empty, not allowed"));
    }
    let ret = TjCorpoccureportEntity::find().filter(tj_corpoccureport::Column::TjReportnumint.eq(rptnum)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(
    dtstart: i64,
    dtend: i64,
    reportids: &Vec<i64>,
    corpid: i64,
    testtype: i32,
    reportnum: &str,
    testid: &str,
    testids: &Vec<String>,
    reporttype: i32,
    orptid: i64,
    db: &DatabaseConnection,
  ) -> Result<Vec<TjCorpoccureport>> {
    let mut conditions = Condition::all().add(tj_corpoccureport::Column::TjStatus.gt(0));

    if dtstart > 0 {
      conditions = conditions.add(tj_corpoccureport::Column::TjCreatedate.gte(dtstart));
    }
    if dtend > 0 {
      conditions = conditions.add(tj_corpoccureport::Column::TjCreatedate.lte(dtend));
    }
    if reportids.len() > 0 {
      conditions = conditions.add(tj_corpoccureport::Column::Id.is_in(reportids.to_owned()));
    }
    if corpid > 0 && corpid != 2 {
      conditions = conditions.add(tj_corpoccureport::Column::TjCorpid.eq(corpid));
    }
    if corpid == 2 {
      conditions = conditions.add(tj_corpoccureport::Column::TjCorpid.gt(corpid));
    }
    if testtype > 0 {
      conditions = conditions.add(tj_corpoccureport::Column::TjTesttype.eq(testtype));
    }
    if !reportnum.is_empty() {
      conditions = conditions.add(tj_corpoccureport::Column::TjReportnumint.contains(reportnum));
    }
    if !testid.is_empty() {
      conditions = conditions.add(
        tj_corpoccureport::Column::Id.in_subquery(
          Query::select()
            .column(tj_corpoccureport_info::Column::TjReportId)
            .from(tj_corpoccureport_info::Entity)
            .and_where(tj_corpoccureport_info::Column::TjTestId.eq(testid.to_owned()))
            .to_owned(),
        ),
      );
    }

    if testids.len() > 0 {
      conditions = conditions.add(
        tj_corpoccureport::Column::Id.in_subquery(
          Query::select()
            .column(tj_corpoccureport_info::Column::TjReportId)
            .from(tj_corpoccureport_info::Entity)
            .and_where(tj_corpoccureport_info::Column::TjTestId.is_in(testids.to_owned()))
            .to_owned(),
        ),
      );
    }
    if reporttype > -1 {
      conditions = conditions.add(tj_corpoccureport::Column::TjReporttype.eq(reporttype));
    }
    if orptid > 0 {
      conditions = conditions.add(tj_corpoccureport::Column::TjOrptid.eq(orptid));
    }
    let query = TjCorpoccureportEntity::find().filter(conditions);
    info!("query string: {:?}", &query.build(DbBackend::MySql).to_string());
    // let ret = TjCorpoccureportEntity::find().filter(conditions).all(db).await;
    let ret = query.all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjCorpoccureport, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_corpoccureport::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    // info!("new val is:{:#?}", &newval);
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjCorpoccureportEntity::update_many()
      .col_expr(tj_corpoccureport::Column::TjStatus, Expr::value(-10))
      .filter(Condition::all().add(tj_corpoccureport::Column::Id.is_in(ids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
}
