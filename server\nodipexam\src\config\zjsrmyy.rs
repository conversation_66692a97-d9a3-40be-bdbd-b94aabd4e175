use std::collections::HashMap;

use serde::{Deserialize, Serialize};

#[derive(Debu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Zjsrmyy {
  pub deptcode: String,
  pub deptname: String,
  pub executorinfo: HashMap<String, Executorinfo>,
}

#[derive(Debug, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Executorinfo {
  pub doctorid: String,
  pub doctorname: String,
}
