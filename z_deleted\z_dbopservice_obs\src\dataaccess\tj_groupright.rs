use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub struct TjGroupright {
  pub id: i64,
  pub tj_gid: i64,
  pub tj_rname: String,
  pub tj_vright: i32,
  pub tj_operator: String,
  pub tj_moddate: i64,
  pub tj_menuid: i32,
}
crud!(TjGroupright {}, "tj_groupright");

// impl TjGroupright {
//   #[py_sql("update tj_staffadmin set tj_groupid = 0 where tj_groupid = #{gid} ")]
//   pub async fn delete_group_rights(rb: &mut rbatis::RBatis, gid: &i64) -> rbatis::Result<()> {
//     impled!()
//   }
// }
