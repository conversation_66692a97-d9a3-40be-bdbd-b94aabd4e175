fn main() {
  // let text = "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"><soap:Body><ns2:allResponse xmlns:ns2=\"http://server.zhejian.com/\"><return><?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<data>\n    <returnCode>500</returnCode>\n</data>\n</return></ns2:allResponse></soap:Body></soap:Envelope>";

  // let response: Result<cdcuploader::service::zjservice::datastructs::Refusedreportcardresponse, serde_json::Error> = serde_json::from_str(text);
  // println!("response: {:?}", &response);
  // println!("Hello, world!");
  let uuid = uuid::Uuid::new_v4();
  println!("uuid: {}", uuid.simple().to_string());
}
