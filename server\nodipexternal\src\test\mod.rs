use crate::rusthl7::{EscapeSequence, Message};
use log::*;

#[tokio::test]
async fn test_extract_message() {
  log4rs::init_file("C:\\Users\\<USER>\\rainerix\\joesong\\nodipservers\\config\\nodipexam.yaml", Default::default()).unwrap();
  //   let msg = "MSH|^~\\&|HIS|HISSystem|EXAM|EXAMSystem|20230605082007||ACK^A01|4f7c224a-087c-4c3f-9f59-6f6f9f59d90b|P|2.4|||||CHN\rMSA|AA|AUZMMTG6-LF37-388K-MRYR-DUC56SJZOQ3S|{\"HIS\":{\"Rows\":{\"Row\":{\"ret\":\"T\",\"msg\":\"\",\"patid\":11311737,\"blh\":230601786}}}}";
  let msg = "MSH|^~\\&|HIS|HISSystem|EXAM|EXAMSystem|20230605082007||ACK^A01|4f7c224a-087c-4c3f-9f59-6f6f9f59d90b|P|2.4|||||CHN\rMSA|AA|AUZMMTG6-LF37-388K-MRYR-DUC56SJZOQ3S|{\"HIS\":{\"Rows\":{\"Row\":{\"ret\":\"T\",\"msg\":\"\",\"patid\":11311737,\"blh\":230601786}},\"Result\":{\"Status\":\"T\",\"MSG\":\"调用成功！\"}}}";

  let message = Message::try_from(msg).unwrap();
  let ret = crate::service::externalservice::ExternalService::extract_ack_a01(&message);
  println!("Result is : {:?}", &ret);
}

#[tokio::test]
async fn test_replace_message() {
  log4rs::init_file("C:\\Users\\<USER>\\rainerix\\joesong\\nodipservers\\config\\nodipexam.yaml", Default::default()).unwrap();
  let msg = "MSH|^~\\&|nodip|JoesongSoft|TECH|WinningSoft|20230712165936||DFT^P03|4305a355-9551-4c96-8958-504ada36068e|P|2.4|||||CHN\rEVN|P03|20230712165936\rPID|1||10009400^^^&HISPATID\rPV1|1|P|3022^体检中心||||admin2^管理员2|||||||||||1|10008156|101~体检费||||||||||||||||||||||||20230712165936||||||10008156\rORC|NW|10009400|||0|||||^^^^^^^0||admin2^^管理员2|||20230712165936||||^^^^^^^^^P~^^^^^^^^^D\rOBR|1|||11050000100_01^体检费^^0|0|20230712165936|||1|||||||||3022|体检中心||体检中心||&146^&&&1|||||||||||||20230712165936|||||||||^^^zk10009400|WCT^^^2\rFT1|1|10009400||20230712165936||0|11050000100_01^体检费|0|收费项目|1|146|146|3022^健康管理中心||||0|0||admin2\r";

  let newmsg = msg.replace("10009400^^^&HISPATID", "11311737^^^&HISPATID");
  info!("new message is:{newmsg}");
}

#[tokio::test]
async fn test_decode_message() {
  log4rs::init_file("C:\\Users\\<USER>\\rainerix\\joesong\\nodipservers\\config\\nodipexam.yaml", Default::default()).unwrap();
  let msg = "MSH|^~\\&|LIS|WinningSoft|HIS|WinningSoft|20230605120931|7|ORU^R01|db66a805-3a97-4f71-8317-fe03034e7581|P|2.4|||||CHN\rPID|1||2306050022^^^&PATID~1234567890^^^&BLH~2306050022^^^&CARDNO||^钱令||29岁|F\rPV1|1|P|||||||||||||||||2306050022||||||||||||||||||||||||||2023-06-05 08:21:18\rORC|OK|17144728^^0|zk061104756^^17144728|7^^尿常规|||||2023060508:59:01|9209^^体检操作员|||||||||2211^^李若兰||健康管理中心^^^^^^^^3022\rOBR|||||||2023060509:07:58|||||0||2023060508:59:01|^^^尿液|||尿常规沉渣2||7|尿常规||||0|||||||0806&&陈莉萍||||||||||||a0001\rOBX||0|BIL^胆红素(BIL)|阴性（-）|||阴性（-）|0||||||2023060509:12:30||||zk061104756^^bgdh\r";

  let message = Message::try_from(msg).unwrap();

  let tjbh = message.query("PV1.F19").to_string();
  info!("tjbh is:{tjbh}");
  let decoder = EscapeSequence::new(message.get_separators());
  let brxm = decoder.decode(message.query("PID.F5.C2")).to_string();
  info!("brxm:{brxm}");

  let patids = decoder.decode(message.query("PID.F3")).to_string();
  info!("PID.F3:{patids}");
  let patids: Vec<&str> = patids.split("~").collect();
  info!("Patids:{patids:?}");
  let testid = patids.into_iter().find(|p| p.contains("BLH")).unwrap_or_default();
  info!("testid:{testid}");

  let testid2: Vec<&str> = testid.split("^").filter(|&f| !f.is_empty()).map(|v| v).collect();
  info!("testids2:{testid2:?}");

  let idx = testid.chars().position(|p| p == '^').unwrap_or_default();
  info!("idx:{idx}");
  info!("final testid is:{}", &testid[0..idx]);
}
