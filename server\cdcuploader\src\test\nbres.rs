// use crat::client::*;
// use anyhow::{anyhow, Result};

use crate::client::zjresponse::ResponseDataNb;

#[test]
// #[tokio::test]
pub fn test_func() {
  //   let log_cfg = "C:\\Users\\<USER>\\rainerix\\joesong\\nodipservers\\config\\nodipexam.yaml";
  let txt_str = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<data>\n    <returnCode>-1</returnCode>\n    <message>部分数据格式异常！</message>\n    <errorDatas>\n        <errorReportCards>\n            <errorData>\n                <cardId>10012494</cardId>\n                <errorMessage>当检查类型为初检时,体检项参考值域表10，补充5个必填项！（11033,11034,10919,10924,11317）</errorMessage>\n                <returnCode>-1</returnCode>\n            </errorData>\n            <errorData>\n                <cardId>医疗机构编码+社会信用代码：330281008、913302817960405765</cardId>\n                <errorMessage>用人单位数据元保存成功</errorMessage>\n                <returnCode>0</returnCode>\n            </errorData>\n        </errorReportCards>\n    </errorDatas>\n</data>";

  // let de_ret: Result<ResponseDataNb, String> = yaserde::de::from_str(txt_str);
  // // info!("Result:{:#?}", &de_ret);
  // if de_ret.as_ref().is_err() {
  //   // return Err(anyhow!("{}", de_ret.as_ref().err().unwrap().to_string()));
  //   println!("----- Error:{}", de_ret.as_ref().err().unwrap().to_string());
  // }
  // let responseinfo = de_ret.unwrap();
  // if responseinfo.returncode.eq_ignore_ascii_case("0") {
  //   // return Ok("".to_string());
  //   println!("OK");
  // } else {
  //   let error_msg = responseinfo
  //     .errorlist
  //     .errors
  //     .errordatas
  //     .iter()
  //     .filter(|&f| !f.retcode.eq_ignore_ascii_case("0"))
  //     .map(|x: &crate::client::zjresponse::RErrorDataNb| x.message.clone())
  //     .collect::<Vec<String>>()
  //     .join(",");
  //   // return Err(anyhow!("{} => {}", responseinfo.message, error_msg));
  //   println!("Error:{}=>{}", responseinfo.message, error_msg)
  // }
}
