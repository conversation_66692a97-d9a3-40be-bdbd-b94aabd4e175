use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjHealthyinfo {
  pub id: i64,
  pub tj_testid: String,
  pub tj_pid: String,
  pub tj_surveydate: i32,
  pub tj_smoke: i32,
  pub tj_smokenum: String,
  pub tj_smokeyear: String,
  pub tj_smokemonth: String,
  pub tj_drink: i32,
  pub tj_drinknum: String,
  pub tj_drinkyear: String,
  pub tj_drinkmonth: String,
  pub tj_childrennum: String,
  pub tj_abortionnum: String,
  pub tj_stillbirthnum: String,
  pub tj_prematurenum: String,
  pub tj_abnormalnum: String,
  pub tj_childrenhealthy: String,
  pub tj_menarcheage: String,
  pub tj_period: String,
  pub tj_cycle: String,
  pub tj_menopauseage: String,
  pub tj_families: String,
  pub tj_modtime: i32,
}
crud!(TjHealthyinfo {}, "tj_healthyinfo");
rbatis::impl_select!(TjHealthyinfo{query_many(pid:&str) => "`where tj_pid = #{pid} `"});
rbatis::impl_select!(TjHealthyinfo{query(testid:&str) ->Option => "`where tj_testid = #{testid} limit 1 `"});
rbatis::impl_delete!(TjHealthyinfo{delete_many(testids:&[String]) => "`where tj_testid in ${testids.sql()} `"});

impl TjHealthyinfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjHealthyinfo) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjHealthyinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjHealthyinfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
}
