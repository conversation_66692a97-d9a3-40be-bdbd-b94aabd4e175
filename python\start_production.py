#!/usr/bin/env python3
# coding=utf-8

"""
Production Startup Script for Report Server

This script starts the Report Server using a production WSGI server
instead of the Flask development server.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def check_production_server(server_type):
    """Check if the production server is available"""
    try:
        if server_type == "gunicorn":
            import gunicorn
            return True
        elif server_type == "waitress":
            import waitress
            return True
        elif server_type == "uwsgi":
            # uWSGI is typically installed system-wide
            result = subprocess.run(["uwsgi", "--version"], capture_output=True)
            return result.returncode == 0
    except ImportError:
        return False
    except FileNotFoundError:
        return False
    return False


def install_production_server(server_type):
    """Install the production server"""
    print(f"Installing {server_type}...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", server_type], check=True)
        print(f"✓ {server_type} installed successfully")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ Failed to install {server_type}")
        return False


def start_gunicorn(host="0.0.0.0", port=8080, workers=None, config_file=None):
    """Start the server using Gunicorn"""
    if workers is None:
        import multiprocessing
        workers = multiprocessing.cpu_count() * 2 + 1
    
    cmd = [
        "gunicorn",
        "--bind", f"{host}:{port}",
        "--workers", str(workers),
        "--timeout", "300",  # 5 minutes for report generation
        "--keepalive", "2",
        "--max-requests", "1000",
        "--max-requests-jitter", "100",
        "--preload-app",
        "--access-logfile", "../log/gunicorn_access.log",
        "--error-logfile", "../log/gunicorn_error.log",
        "--log-level", "info"
    ]
    
    if config_file and os.path.exists(config_file):
        cmd.extend(["--config", config_file])
    
    cmd.append("wsgi:app")
    
    print(f"Starting Gunicorn with {workers} workers...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"Gunicorn failed to start: {e}")
        return False
    
    return True


def start_waitress(host="0.0.0.0", port=8080):
    """Start the server using Waitress"""
    cmd = [
        "waitress-serve",
        "--host", host,
        "--port", str(port),
        "--threads", "8",
        "--connection-limit", "1000",
        "--cleanup-interval", "30",
        "--log-untrusted-proxy-headers",
        "wsgi:app"
    ]
    
    print("Starting Waitress...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"Waitress failed to start: {e}")
        return False
    
    return True


def start_uwsgi(host="0.0.0.0", port=8080, workers=None):
    """Start the server using uWSGI"""
    if workers is None:
        import multiprocessing
        workers = multiprocessing.cpu_count() * 2 + 1
    
    cmd = [
        "uwsgi",
        "--http", f"{host}:{port}",
        "--module", "wsgi:app",
        "--processes", str(workers),
        "--threads", "2",
        "--master",
        "--vacuum",
        "--die-on-term",
        "--harakiri", "300",  # 5 minutes timeout
        "--max-requests", "1000",
        "--logto", "../log/uwsgi.log"
    ]
    
    print(f"Starting uWSGI with {workers} processes...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"uWSGI failed to start: {e}")
        return False
    
    return True


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Start Report Server with production WSGI server")
    parser.add_argument("--server", choices=["gunicorn", "waitress", "uwsgi", "auto"], 
                       default="auto", help="WSGI server to use")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8080, help="Port to bind to")
    parser.add_argument("--workers", type=int, help="Number of worker processes")
    parser.add_argument("--install", action="store_true", help="Install missing WSGI server")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("Report Server - Production Startup")
    print("=" * 60)
    
    # Ensure we're in the right directory
    if not os.path.exists("wsgi.py"):
        print("Error: wsgi.py not found. Please run this script from the python directory.")
        sys.exit(1)
    
    # Create log directory
    os.makedirs("../log", exist_ok=True)
    
    # Determine which server to use
    if args.server == "auto":
        # Try servers in order of preference
        servers = ["gunicorn", "waitress", "uwsgi"]
        selected_server = None
        
        for server in servers:
            if check_production_server(server):
                selected_server = server
                print(f"✓ Found {server}")
                break
        
        if not selected_server:
            print("No production WSGI server found.")
            if args.install:
                # Try to install Gunicorn (most popular)
                if install_production_server("gunicorn"):
                    selected_server = "gunicorn"
                else:
                    print("Failed to install production server.")
                    print("Please install manually: pip install gunicorn")
                    sys.exit(1)
            else:
                print("Available options:")
                print("  pip install gunicorn    # Recommended for Linux/Mac")
                print("  pip install waitress    # Recommended for Windows")
                print("  pip install uwsgi       # Advanced option")
                print("\nOr run with --install to automatically install Gunicorn")
                sys.exit(1)
    else:
        selected_server = args.server
        if not check_production_server(selected_server):
            if args.install:
                if not install_production_server(selected_server):
                    sys.exit(1)
            else:
                print(f"Error: {selected_server} is not installed")
                print(f"Install with: pip install {selected_server}")
                print("Or run with --install flag")
                sys.exit(1)
    
    print(f"\nStarting Report Server with {selected_server}")
    print(f"Server will be available at: http://{args.host}:{args.port}")
    print("API endpoints:")
    print(f"  Health Check: http://{args.host}:{args.port}/api/health")
    print(f"  Generate Report: POST http://{args.host}:{args.port}/api/reports/generate")
    print("\nPress Ctrl+C to stop the server")
    print("=" * 60)
    
    # Start the selected server
    success = False
    if selected_server == "gunicorn":
        config_file = "gunicorn.conf.py" if os.path.exists("gunicorn.conf.py") else None
        success = start_gunicorn(args.host, args.port, args.workers, config_file)
    elif selected_server == "waitress":
        success = start_waitress(args.host, args.port)
    elif selected_server == "uwsgi":
        success = start_uwsgi(args.host, args.port, args.workers)
    
    if not success:
        print("\nFailed to start production server.")
        print("Fallback: You can still use the development server with:")
        print("  python start_server.py")
        sys.exit(1)


if __name__ == "__main__":
    main()
