use crate::{auth::auth::Claims, common::filesvc::FileSvc, config::settings::Settings};
use axum::{response::IntoResponse, Extension, Json};
// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
use hyper::StatusCode;
use nodipservice::{dto::*, medexam::datasumsvc::DatasumSvc};
use std::sync::Arc;
use tracing::*;

pub async fn export_datas(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, config: Extension<Arc<Settings>>, Json(dto): Json<MultipleKeysDto>) -> impl IntoResponse {
  //
  let ret = DatasumSvc::export_medexam_with_details(&dto.keystr1, &dto.keystr2, dto.keyint1 as i32, &config.application.reportdir, &db).await;
  if ret.as_ref().is_err() {
    error!("export error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }
  let ret = FileSvc::do_download(&format!("{}", ret.unwrap())).await;
  if ret.as_ref().is_err() {
    error!("download file error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }

  Ok(ret.unwrap())
}
