use crate::entities::{prelude::*, tj_departinfo};
use anyhow::{anyhow, Result};
use sea_orm::{sea_query::Expr, ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjDepartinfo {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<TjDepartinfo>> {
    let ret = TjDepartinfoEntity::find().filter(tj_departinfo::Column::TjDeptid.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(ids: &Vec<i64>, deptids: &Vec<String>, depttype: i32, db: &DatabaseConnection) -> Result<Vec<TjDepartinfo>> {
    let mut conditions = Condition::all();
    if ids.len() > 0 {
      conditions = conditions.add(tj_departinfo::Column::Id.is_in(ids.to_owned()));
    }
    if deptids.len() > 0 {
      conditions = conditions.add(tj_departinfo::Column::TjDeptid.is_in(deptids.to_owned()));
    }
    if depttype > 0 {
      conditions = conditions.add(tj_departinfo::Column::TjDepttype.eq(depttype));
    }
    let ret = TjDepartinfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save(info: &TjDepartinfo, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_departinfo::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    Ok(val_id.unwrap())
  }
  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let ret = TjDepartinfoEntity::update_many()
      .col_expr(tj_departinfo::Column::TjFlag, Expr::value(-1))
      .filter(Condition::all().add(tj_departinfo::Column::Id.is_in(ids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
}
