use crate::{api::httpresponse::{response_json_value_error, response_json_value_ok}, auth::auth::Claims};
use axum::{Extension, Json};
// use dbopservice::{
//   dataaccess::{tj_instrumentinfo::TjInstrumentinfo, tj_itemrefinfo::TjItemrefinfo},
//   dbinit::DbConnection,
// };
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{basic::instrumentsvc::InstrumentSvc, dto::*};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

pub async fn query_instruments(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  let ret = InstrumentSvc::query_instrumentinfos(&db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn save_instrument(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjInstrumentinfo>) -> Json<Value> {
  info!("dto:{dto:?}");
  let ret = InstrumentSvc::save_instrumentinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
pub async fn delete_instrument(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjInstrumentinfo>) -> Json<Value> {
  info!("delete_instrument dto:{dto:?}");
  let ret = InstrumentSvc::delete_instrumentinfo(dto.id, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn query_instrument_items(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyDto>) -> Json<Value> {
  let ret = InstrumentSvc::query_instrument_items(&db, &dto.key).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn save_instrument_items(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjItemrefinfo>) -> Json<Value> {
  info!("dto:{dto:?}");
  let ret = InstrumentSvc::save_instrumentinfo_items(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
pub async fn delete_instrument_items(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjItemrefinfo>) -> Json<Value> {
  info!("dto:{dto:?}");
  let ret = InstrumentSvc::delete_instrumentinfo_items(dto.id, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
