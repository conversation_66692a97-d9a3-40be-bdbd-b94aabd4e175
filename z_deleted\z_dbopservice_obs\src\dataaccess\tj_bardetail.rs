use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(C<PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjBardetail {
  pub id: i64,
  pub tj_binum: String,
  pub tj_itemid: String,
  pub tj_barorder: i32,
}
crud!(TjBardetail {}, "tj_bardetail");
rbatis::impl_select!(TjBardetail{query_many_by_ids(ids:&[i64]) => "`where id in ${ids.sql()} `"});
rbatis::impl_select!(TjBardetail{query_many(ids:&[String]) =>
  "`where id > 0 `
  if !ids.is_empty():
    ` and tj_binum in ${ids.sql()} `"});
rbatis::impl_delete!(TjBardetail{delete(itemid:&String, binum:&String) => "`where tj_itemid = #{itemid} and tj_binum = #{binum} `"});

impl TjBardetail {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjBardetail) -> Result<i64> {
    if info.id > 0 {
      let ret = TjBardetail::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      return Ok(info.id);
    }
    let ret = TjBardetail::insert(rb, info).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap().last_insert_id.into())
  }
}
