use std::collections::{HashMap, HashSet};

use crate::config::settings::Settings;
use anyhow::{anyhow, Result};
use chrono::{Duration, Local};
// use dbopservice::{
//   dataaccess::{prelude::*, v_tj_pacsresult::VTjPacsresultV2},
//   dbinit::DbConnection,
// };
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{
  client::httpclient::HttpClient,
  common::constant::{DeptType, DictType, ExamStatus, PacsType, RecvResultCode, SysParm, YesOrNo},
  dto::{EgoDto, MultipleKeyDto, ResultMessage},
  external::{recvexternalpacs::RecvExternalPacs, wyhsservice::WyhsService},
  medexam::{medexamsvc::MedexamSvc, summarysvc::SummarySvc},
  SYSCACHE,
};
// use rbatis::RBatis;
use tracing::*;
use utility::timeutil;
pub struct RecvPacsData;

impl RecvPacsData {
  pub async fn recv_pacsdata(dto: &MultipleKeyDto, config: &Settings, extpacs: &RecvExternalPacs, db: &DbConnection) -> Vec<ResultMessage> {
    let testid = dto.keystr1.to_string();
    let mut lisval = dto.keystr2.to_owned();
    if lisval.is_empty() {
      lisval = "default".to_string();
    }

    if testid.is_empty() {
      let retmsg = vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: "体检编号为空，不能接收数据".to_string(),
      }];
      // return response_json_value_ok(1, retmsg);
      return retmsg;
    }

    let ret = TjMedexaminfo::query(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} {}", &testid, ret.as_ref().unwrap_err().to_string()),
      }];
    }
    let medret = ret.unwrap();
    if medret.is_none() {
      // return Err(anyhow!("不能找到体检号为:{}的体检信息", &testid));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} {}", &testid, "不能找到体检信息"),
      }];
    }
    let mut medinfo = medret.unwrap();

    if medinfo.tj_checkstatus <= ExamStatus::Appoint as i32 {
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 该体检者预约状态，不能接收数据", &testid),
      }];
    }
    //从武义壶山医院获取lis结果数据

    if config.external.exttype.eq_ignore_ascii_case("wyhs") {
      //武义壶山医院
      if config.external.serverurl.is_empty() {
        let retmsg = vec![ResultMessage {
          code: RecvResultCode::Failure as i32,
          message: "对接服务器未配置".to_string(),
        }];
        // return response_json_value_ok(1, retmsg);
        return retmsg;
      }

      let ret = WyhsService::receive_pacs_result(&medinfo, &config.external.serverurl, db).await;
      if ret.as_ref().is_err() {
        let retmsg = vec![ResultMessage {
          code: RecvResultCode::Failure as i32,
          message: ret.unwrap_err().to_string(),
        }];
        return retmsg;
      }
    } else {
      //从nodipego获取结果
      if config.application.pacsego == YesOrNo::Yes as i32 {
        let server = config.application.proxyserver.to_string();
        if server.is_empty() {
          let retmsg = vec![ResultMessage {
            code: RecvResultCode::Failure as i32,
            message: "ego服务器信息没有配置".to_string(),
          }];
          // return response_json_value_ok(1, retmsg);
          return retmsg;
        }

        let uri = format!("{}/api/extend/pacs/result", server);
        let ego_dto = EgoDto {
          testid: dto.keystr1.to_string(),
          extkey: lisval.to_string(),
        };
        let ret: Result<Vec<VTjPacsresult>> = HttpClient::send_http_post_request("", &uri, &Some(ego_dto)).await;
        info!("get from server:{},result:{:?}", &uri, &ret);
        if ret.as_ref().is_err() {
          error!("ego get error:{}", ret.as_ref().unwrap_err().to_string());
          let retmsg = vec![ResultMessage {
            code: RecvResultCode::Failure as i32,
            message: ret.as_ref().unwrap_err().to_string(),
          }];
          // return response_json_value_ok(1, retmsg);
          return retmsg;
        }
      } else {
        //从本地连接视图的方式获取结果数据
        let mut hassfyc = 0;
        if config.application.pacsversion.to_uppercase().eq_ignore_ascii_case("V2") {
          hassfyc = 1;
        }

        let pacs = config.extpacs.get(&lisval);
        if pacs.is_none() {
          // return response_json_value_error("pacs连接信息没有配置", "");
          let retmsg = vec![ResultMessage {
            code: RecvResultCode::Failure as i32,
            message: "pacs连接信息没有配置".to_string(),
          }];
          // return response_json_value_ok(1, retmsg);
          return retmsg;
        }
        let pacs = pacs.unwrap();
        let version = pacs.version;
        let extpacs_val = extpacs.get_pacskey();
        if extpacs_val.eq_ignore_ascii_case(&lisval) {
          let ret = extpacs.query_from_external_pacs(&testid, hassfyc, version, db).await;
          if ret.as_ref().is_err() {
            let retmsg = vec![ResultMessage {
              code: RecvResultCode::Failure as i32,
              message: ret.as_ref().unwrap_err().to_string(),
            }];
            // return response_json_value_ok(1, retmsg);
            return retmsg;
          }
        } else {
          //多pacs，需要连接新数据库
        }
        //
      }
    }
    let mut recv_results: Vec<ResultMessage> = Vec::new();

    info!("start to recv pacs by dto:{:?}", &dto);
    // let testid = dto.keystr1.to_owned();
    let testids: Vec<String> = vec![testid.to_string()];
    let deptids = dto.keystr3.to_owned().unwrap_or_default();
    // let deptids: Vec<&str> = deptids_string.iter().map(|v| v.as_str()).collect();
    // let import_type = dto.keyint1;
    let auto_summary = dto.keyint1;

    let ret = TjCheckallnew::query(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} {}", &testid, ret.as_ref().unwrap_err().to_string()),
      }];
    }
    let ckall = ret.unwrap();
    if ckall.is_none() {
      // return Err(anyhow!("没有{testid}的总检信息"));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 没有总检信息", &testid),
      }];
    }
    let ckall = ckall.unwrap();
    if ckall.tj_castatus == YesOrNo::Yes as i32 {
      // return Err(anyhow!("体检号为:{}的体检已经总检，不能再次接收数据", &testid));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 已经总检，不能再次接收数据", &testid),
      }];
    }

    let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} {}", &testid, ret.as_ref().unwrap_err().to_string()),
      }];
    }
    let medret = ret.unwrap();
    if medret.is_none() {
      // return Err(anyhow!("不能找到体检号为:{}的体检信息", &testid));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 不能找到体检人员信息", &testid),
      }];
    }
    let ptinfo = medret.unwrap();

    let ret = TjPatienthazards::query(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} {}", &testid, ret.as_ref().unwrap_err().to_string()),
      }];
    }
    let pthazards = ret.unwrap();

    let ret = TjTestsummary::query_many(&testids, &deptids, -1, -1, -1, "", -1, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} {}", &testid, ret.as_ref().unwrap_err().to_string()),
      }];
    }
    let mut summaries = ret.unwrap();
    if summaries.len() <= 0 {
      // return Err(anyhow!("没有{testid}在这些科室的检查信息"));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 没有这些科室的检查信息", &testid),
      }];
    }
    let ret = TjCheckiteminfo::query_many(&testids, &deptids, -1, -1, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} {}", &testid, ret.as_ref().unwrap_err().to_string()),
      }];
    }
    let ciinfos_all = ret.unwrap();
    if ciinfos_all.len() <= 0 {
      // return Err(anyhow!("没有{testid}在这些科室的检查数据"));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 没有这些科室的检查数据", &testid),
      }];
    }
    info!("start to query pacs result data from external system");
    let ret = RecvPacsData::query_from_external_pacsresult(&testid, config, &db).await;
    if ret.as_ref().is_err() {
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} ", ret.unwrap_err().to_string()),
      }];
    }
    let pacsresults = ret.unwrap();
    info!("Pacs result is:{:?}", &pacsresults);
    if pacsresults.len() <= 0 {
      error!("对接pacs系统没有该体检编号的结果数据");
      // return Err(anyhow!("pacs系统没有该体检编号的结果数据"));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 对接pacs系统没有该体检编号的结果数据", &testid),
      }];
    }

    //开始处理接收结果
    for dptid in deptids.iter() {
      let mut recv_ret = ResultMessage { ..Default::default() };

      let sum = summaries.iter_mut().find(|f| f.tj_deptid.eq_ignore_ascii_case(&dptid));
      if sum.is_none() {
        recv_ret.code = RecvResultCode::Failure as i32;
        recv_ret.message = format!(
          "{} 没有科室{}的检查信息",
          medinfo.tj_testid,
          SYSCACHE.get().unwrap().get_department(&dptid, &db).await.tj_deptname
        );
        recv_results.push(recv_ret);
        continue;
      }
      let sum = sum.unwrap();

      //从pacs获取结果数据
      let ret = RecvPacsData::import_pacs_result(&mut medinfo, sum, &pthazards, &ptinfo, &ciinfos_all, &pacsresults, auto_summary, db).await;
      recv_results.push(ret);
    }

    recv_results
  }

  async fn import_pacs_result(
    medinfo: &mut TjMedexaminfo,
    sum: &mut TjTestsummary,
    pthazards: &Vec<TjPatienthazards>,
    ptinfo: &TjPatient,
    ciinfos_all: &Vec<TjCheckiteminfo>,
    pacsresults: &Vec<VTjPacsresult>,
    auto_summary: i64,
    // staff: &TjStaffadmin,
    db: &DbConnection,
  ) -> ResultMessage {
    // info!("pacs系统的结果数据:{:?}", &pacsresults);

    let mut recv_ret: ResultMessage = ResultMessage { ..Default::default() };
    let mut ciinfos: Vec<TjCheckiteminfo> = ciinfos_all.iter().filter(|&f| f.tj_deptid.eq_ignore_ascii_case(&sum.tj_deptid)).map(|v| v.to_owned()).collect();
    if ciinfos.len() <= 0 {
      // error!("{}", ret.as_ref().unwrap_err().to_string());
      recv_ret.code = RecvResultCode::Failure as i32;
      recv_ret.message = "无此科室的检查信息".to_string();
      return recv_ret;
    }

    let dict_pacsret_rule = SYSCACHE.get().unwrap().get_dict(DictType::DictSysparm as i32, SysParm::PacsCode as i32, &db).await;
    let pacs_rule = match dict_pacsret_rule.ss_short.as_str() {
      "2" => PacsType::JCXM as i32,
      _ => PacsType::JCLX as i32,
    };
    // let mut iteminfos: Vec<TjIteminfo> = Vec::new();
    let mut deptinfos: Vec<TjDepartinfo> = Vec::new();
    let mut result_parse = "".to_string();
    let mut item_parse = "".to_string();
    //肝彩超@肝区回声稍粗，血管走行尚清，肝实质内见：10mm×7mm、7mm×6mm无回声区，门静脉内径正常范围
    let mut sights_map: HashMap<String, String> = HashMap::new();

    // if pacs_rule == PacsType::JCXM as i32 {
    let itemids: Vec<String> = ciinfos.iter().map(|v| v.tj_itemid.to_string()).collect();
    // let ret = TjIteminfo::query_all_with_lisnum(&db.get_connection()).await;
    let ret = TjIteminfo::query_many(&itemids, -1, -1, -1, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("query iteminfo by lisnum error:{}", ret.as_ref().unwrap_err().to_string());
      recv_ret.code = RecvResultCode::Failure as i32;
      recv_ret.message = ret.as_ref().unwrap_err().to_string();
      return recv_ret;
    }
    let iteminfos = ret.unwrap();
    // } else {
    if pacs_rule == PacsType::JCLX as i32 {
      // info!("按照检查类型来获取数据，需要查找科室信息......");
      //需要查找科室信息
      let ret = TjDepartinfo::query_many(&vec![], &vec![], 0, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("query departinfo error:{}", ret.as_ref().unwrap_err().to_string());
        recv_ret.code = RecvResultCode::Failure as i32;
        recv_ret.message = ret.as_ref().unwrap_err().to_string();
        return recv_ret;
      }
      deptinfos = ret.unwrap();

      let dictinfo = SYSCACHE.get().unwrap().get_dict(DictType::DictSysparm as i32, SysParm::PacsResut as i32, &db).await;
      if !dictinfo.ss_short.is_empty() {
        result_parse = dictinfo.ss_short.to_string();
      }
      let dictinfo = SYSCACHE.get().unwrap().get_dict(DictType::DictSysparm as i32, SysParm::PacsItem as i32, &db).await;
      if !dictinfo.ss_short.is_empty() {
        item_parse = dictinfo.ss_short.to_string();
      }

      info!("开始处理科室代码为:{}的结果信息, 拆分字符串信息:{:?},{:?}", &sum.tj_deptid, &result_parse, &item_parse);
      if result_parse.is_empty() {
        info!("没有配置结果拆分条件，采用换行来进行结果拆分");
        result_parse = "n".to_string();
      }
      // if !result_parse.is_empty() {
      //imagesight
      // 肝彩超@肝区回声稍粗，血管走行尚清，肝实质内见：10mm×7mm、7mm×6mm无回声区，门静脉内径正常范围#
      // 胆彩超@胆囊大小正常范围，胆囊壁毛，胆汁透声差，囊内见成堆绿豆至黄豆大强回声伴声影，总胆管未见扩张#
      // 胰彩超@胰腺大小形态正常，回声均匀，胰管无扩张;
      // 脾彩超@脾脏厚度正常范围，脾区回声均匀#
      // 双肾彩超@双肾大小、形态正常，肾窦回声清晰，无分离，实质回声正常。CDFI:未见异常血流信息#
      //例子2
      // 肝彩超@肝区回声稍粗，血管走行尚清，门静脉内径正常范围#
      // 胆彩超@胆囊大小正常范围，胆囊壁毛，胆汁透声好，总胆管内径正常范围#
      // 胰彩超@胰腺大小形态正常，回声均匀，胰管无扩张#
      // 脾彩超@脾脏厚度正常范围，脾区回声均匀#
      // 双肾彩超@双肾大小、形态正常，肾窦回声清晰，无分离，实质回声正常#
      //imagediagnosis
      // 肝囊肿;
      // 胆囊结石;
      // 胰未见明显异常;
      // 脾未见明显异常;
      // 双肾未见明显异常;
      // info!("拆分项目的字符串非空，需要进行结果拆分,拆分字符串信息:{:?},{:?}", &result_parse, &item_parse);
      for pacsret in pacsresults.iter() {
        let mut imagesight = pacsret.imagesight.to_string(); //.replace("\r\n", "");
        if imagesight.is_empty() {
          imagesight = pacsret.imagediagnosis.clone();
        }
        let sights: Vec<String>;
        if result_parse.eq_ignore_ascii_case("n") {
          sights = imagesight.lines().map(|v| v.trim().to_string()).collect();
        } else {
          sights = imagesight.split(&result_parse).filter(|&x| !x.is_empty()).map(|v| v.trim().to_string()).collect();
        }
        if !item_parse.is_empty() {
          for sight in sights.into_iter() {
            let sgs: Vec<String> = sight.split(&item_parse).filter(|&x| !x.is_empty()).map(|v| v.to_string()).collect();
            if sgs.len() >= 2 {
              sights_map.insert(sgs[0].to_string(), sgs[1].trim().to_string());
            }
          }
        } else {
          //试用tj_lisnum来
          for ciinfo in ciinfos.iter() {
            let it = iteminfos.iter().find(|v| v.tj_itemid.eq_ignore_ascii_case(&ciinfo.tj_itemid));
            if it.is_some() {
              let itinfo = it.unwrap();
              if !itinfo.tj_lisnum.is_empty() {
                let ret = sights.iter().find(|v| v.contains(&itinfo.tj_lisnum));
                if ret.is_some() {
                  sights_map.insert(ciinfo.tj_itemname.to_string(), ret.unwrap().to_string());
                }
              }
            }
          }
        }
      }
      info!("拆分结果:{:?}", &sights_map);
      // }
    }

    let mut not_found = 0;
    let mut checkdoctor = "".to_string();
    let mut recheckdoctor = "".to_string();
    let mut checkdate = 0;
    // let mut imagediagnosis = "".to_string();
    let imagediagnosis: String;
    let mut imagediagnosises: HashSet<String> = HashSet::new();
    for ciinfo in ciinfos.iter_mut() {
      if ciinfo.tj_combineflag == YesOrNo::Yes as i32 {
        continue;
      }

      // //温州迪安单独特殊处理心电图
      // if (ciinfo.tj_itemid.eq_ignore_ascii_case("070002") || ciinfo.tj_itemid.eq_ignore_ascii_case("070016"))
      //   && SYSCACHE
      //     .get()
      //     .unwrap()
      //     .get_dict(DictType::DictSysparm as i32, SysParm::ExternalName as i32, db)
      //     .await
      //     .ss_short
      //     .to_uppercase()
      //     .eq_ignore_ascii_case("DIAN")
      // {
      //   //
      //   if let Some(ecg_result) = pacsresults.iter().find(|&f| f.jclx.to_uppercase().eq_ignore_ascii_case("ECG")) {
      //     if ecg_result.imagesight.is_empty() {
      //       error!("{}结果为空，不处理:{:?}", &sum.tj_deptid, &ecg_result);
      //       not_found += 1;
      //       continue;
      //     }
      //     ciinfo.tj_result = ecg_result.imagediagnosis.to_string();
      //     ciinfo.tj_checkdoctor = ecg_result.jcys.to_string();
      //     if ecg_result.imagediagnosis.contains("正常心电图") || ecg_result.imagediagnosis.contains("正常范围心电图") {
      //       ciinfo.tj_abnormalflag = 0;
      //     } else {
      //       ciinfo.tj_abnormalflag = 1;
      //     }
      //     ciinfo.tj_itemrange = "".to_string();
      //     ciinfo.tj_abnormalshow = "".to_string();
      //     ciinfo.tj_checkdate = timeutil::convert_datetime_to_timestamp(&ecg_result.bgrq, "%Y-%m-%d %H:%M:%S");
      //     ciinfo.tj_recheckdate = ciinfo.tj_checkdate;
      //     ciinfo.tj_recheckdoctor = ecg_result.bgys.to_string();
      //     checkdoctor = ecg_result.jcys.to_string();
      //     recheckdoctor = ecg_result.bgys.to_string();
      //     checkdate = ciinfo.tj_checkdate;
      //     // imagediagnosis = ecg_result.imagediagnosis.to_string();
      //     imagediagnosises.insert(ecg_result.imagediagnosis.trim().to_string());
      //   }
      //   continue;
      // }

      if pacs_rule == PacsType::JCLX as i32 {
        //按照检查类型来，比如US等
        // info!("按照检查项目类型的方式来获取pacs数据");
        // let deptinfo = SYSCACHE.get().unwrap().get_department(&sum.tj_deptid, &db).await;
        let deptinfo = deptinfos.iter().find(|&f| f.tj_deptid.eq_ignore_ascii_case(&sum.tj_deptid));
        if deptinfo.is_none() {
          error!("不能找到科室代码未{}的科室信息", &sum.tj_deptid);
          not_found += 1;
          continue;
        }
        let deptinfo = deptinfo.unwrap();
        if deptinfo.tj_zdym.is_empty() {
          error!("没有配置科室{}的zdym", &sum.tj_deptid);
          not_found += 1;
          continue;
        }
        let pacsrets: Vec<VTjPacsresult> = pacsresults.iter().filter(|&v| v.jclx.eq_ignore_ascii_case(&deptinfo.tj_zdym)).map(|v| v.to_owned()).collect();
        if pacsrets.len() <= 0 {
          error!("没有科室:{}的pacs结果信息", &sum.tj_deptid);
          not_found += 1;
          continue;
        }
        if pacsrets[0].imagediagnosis.is_empty() && pacsrets[0].bgrq.is_empty() && pacsrets[0].imagesight.is_empty() {
          error!("科室:{}的pacs结果信息为空，不处理", &sum.tj_deptid);
          not_found += 1;
          continue;
        }
        // let pacsret = pacsret.unwrap();
        // if pacsret.imagesight.is_empty() {
        //   error!("{}结果为空，不处理:{:?}", &sum.tj_deptid, &pacsret);
        //   not_found += 1;
        //   continue;
        // }
        let mut pacsret = pacsrets[0].to_owned();
        let pacsret_jcxm = pacsrets.iter().find(|v| v.jcxm.eq_ignore_ascii_case(&ciinfo.tj_synid));
        if pacsret_jcxm.is_some() {
          pacsret = pacsret_jcxm.unwrap().to_owned();
        }
        let retinfo = sights_map.get(&ciinfo.tj_itemname);
        if retinfo.is_none() {
          info!("不能通过项目名称找到拆分后的结果数据，采用imagesight做结果");
          ciinfo.tj_result = pacsret.imagesight.to_string();
        } else {
          info!("通过项目名称找到拆分后的结果数据，结果信息:{:?}", &retinfo);
          ciinfo.tj_result = retinfo.unwrap().to_string();
        }
        ciinfo.tj_checkdoctor = pacsret.jcys.to_string();
        ciinfo.tj_abnormalflag = pacsret.sfyc;
        ciinfo.tj_itemrange = "".to_string();
        ciinfo.tj_abnormalshow = "".to_string();
        ciinfo.tj_checkdate = timeutil::convert_datetime_to_timestamp(&pacsret.bgrq, "%Y-%m-%d %H:%M:%S");
        ciinfo.tj_recheckdate = ciinfo.tj_checkdate;
        ciinfo.tj_recheckdoctor = pacsret.bgys.to_string();

        checkdoctor = pacsret.jcys.to_string();
        recheckdoctor = pacsret.bgys.to_string();
        checkdate = ciinfo.tj_checkdate;
        // imagediagnosis.push_str(&pacsret.imagediagnosis);
        // imagediagnosis.push_str("\r\n");
        imagediagnosises.insert(pacsret.imagediagnosis.trim().to_string());
      } else {
        // info!("按照检查项目代码的方式来处理数据");
        //按照检查项目来，即明确的项目编号
        let iteminfo = iteminfos.iter().find(|&p| p.tj_itemid.eq_ignore_ascii_case(&ciinfo.tj_itemid));
        if iteminfo.is_none() {
          error!("不能找到项目编号为：{}的项目信息，或者该项目未配置对接项目编号", &ciinfo.tj_itemid);
          not_found += 1;
          continue;
        }
        let iteminfo = iteminfo.unwrap();
        if iteminfo.tj_lisnum.is_empty() {
          error!("项目编号：{}未配置对接项目的编号", &iteminfo.tj_itemid);
          not_found += 1;
          continue;
        }
        let lisnums: Vec<String> = iteminfo.tj_lisnum.split(&[',', '，'][..]).filter(|v| !v.is_empty()).map(|val| val.to_string()).collect();
        if lisnums.len() <= 0 {
          error!("对接项目编号无效");
          not_found += 1;
          continue;
        }
        let mut pacsret: Option<VTjPacsresult> = None;
        for linum in lisnums {
          let pcrt = pacsresults.iter().find(|&f| f.jcxm.eq_ignore_ascii_case(&linum));
          if pcrt.is_none() {
            continue;
          } else {
            pacsret = Some(pcrt.unwrap().to_owned());
          }
        }
        if pacsret.is_none() {
          error!("找不到项目编号为：{}的对接项目信息", &ciinfo.tj_itemid);
          not_found += 1;
          continue;
        }
        //设置结果
        let pacsret = pacsret.unwrap();
        if pacsret.imagesight.is_empty() {
          error!("{}结果为空，不处理:{:?}", &sum.tj_deptid, &pacsret);
          not_found += 1;
          continue;
        }
        ciinfo.tj_result = pacsret.imagesight.to_string();
        ciinfo.tj_checkdoctor = pacsret.jcys.to_string();
        ciinfo.tj_abnormalflag = pacsret.sfyc;
        ciinfo.tj_itemrange = "".to_string();
        ciinfo.tj_abnormalshow = "".to_string();
        ciinfo.tj_checkdate = timeutil::convert_datetime_to_timestamp(&pacsret.bgrq, "%Y-%m-%d %H:%M:%S");
        ciinfo.tj_recheckdate = ciinfo.tj_checkdate;
        ciinfo.tj_recheckdoctor = pacsret.bgys.to_string();

        checkdoctor = pacsret.jcys.to_string();
        recheckdoctor = pacsret.bgys.to_string();
        checkdate = ciinfo.tj_checkdate;
        // imagediagnosis.push_str(&pacsret.imagediagnosis);
        // imagediagnosis.push_str("\r\n");
        imagediagnosises.insert(pacsret.imagediagnosis.trim().to_string());
      }
    }
    // info!("Imagediagnosises is:{:?}", &imagediagnosises);
    imagediagnosis = imagediagnosises.into_iter().collect::<Vec<String>>().join("\n");
    info!("NOT FOUND value is:{not_found}");
    if not_found != ciinfos.len() - 1 {
      //有结果需要保存
      for val in ciinfos.iter_mut() {
        if val.tj_combineflag == YesOrNo::Yes as i32 {
          val.tj_checkdate = checkdate;
          val.tj_recheckdate = checkdate;
          val.tj_checkdoctor = checkdoctor.to_string();
          val.tj_recheckdoctor = recheckdoctor.to_string();
        }
      }
      let ret = TjCheckiteminfo::save_many(&ciinfos, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        recv_ret.code = RecvResultCode::Failure as i32;
        recv_ret.message = ret.as_ref().unwrap_err().to_string();
        return recv_ret;
      }
    }

    //全部接收完成   sum.tj_summary = //imagediagnosis
    if not_found == 0 && auto_summary == YesOrNo::Yes as i64 {
      sum.tj_summary = imagediagnosis.to_string();
      sum.tj_checkdate = checkdate;
      sum.tj_date = checkdate;
      sum.tj_checkdoctor = recheckdoctor.to_string();
      sum.tj_doctor = recheckdoctor.to_string();
      //开始自动小结
      let ret = SummarySvc::summary(medinfo, &ptinfo, sum, &mut ciinfos, &pthazards, 0, 0, &imagediagnosis, &db).await;
      info!("自动小结结果：{:?}", &ret);
      let status_ret = MedexamSvc::update_medexam_status_updown(&medinfo.tj_testid, 0, 1, db).await;
      if status_ret.is_err() {
        error!("更新体检状态失败");
      }
    }
    if not_found == 0 {
      recv_ret.code = RecvResultCode::Ok as i32;
      recv_ret.message = format!(
        "{} 在科室{}数据接收成功",
        medinfo.tj_testid,
        SYSCACHE.get().unwrap().get_department(&sum.tj_deptid, &db).await.tj_deptname
      );
    } else if not_found > 0 && not_found < ciinfos.len() - 1 {
      recv_ret.code = RecvResultCode::PartialOK as i32;
      recv_ret.message = format!(
        "{} 在科室{}数据接收部分成功",
        medinfo.tj_testid,
        SYSCACHE.get().unwrap().get_department(&sum.tj_deptid, &db).await.tj_deptname
      );
    } else {
      recv_ret.code = RecvResultCode::Failure as i32;
      recv_ret.message = format!(
        "{} 在科室{}数据接收失败",
        medinfo.tj_testid,
        SYSCACHE.get().unwrap().get_department(&sum.tj_deptid, &db).await.tj_deptname
      );
    }

    recv_ret
  }

  // async fn init_db_connection(lis: &Extpacs) -> Result<RBatis> {
  //   let rb = RBatis::new();
  //   match lis.dbtype {
  //     1 => {
  //       // let ret = rb.init_opt(
  //       //   rbdc_oracle::driver::OracleDriver {},
  //       //   rbdc_oracle::options::OracleConnectOptions {
  //       //     username: lis.username.to_owned(),
  //       //     password: lis.password.to_owned(),
  //       //     connect_string: lis.uri.to_owned(),
  //       //   },
  //       // );
  //       // if ret.as_ref().is_err() {
  //       //   return Err(anyhow!("init oracle connection error:{}", ret.as_ref().unwrap_err().to_string()));
  //       // }
  //     }
  //     2 => {
  //       let ret = rb.init(rbdc_mssql::driver::MssqlDriver {}, &lis.uri);
  //       if ret.as_ref().is_err() {
  //         return Err(anyhow!("init mssql server connection error:{}", ret.as_ref().unwrap_err().to_string()));
  //       }
  //     }
  //     3 => {
  //       let ret = rb.init(rbdc_mysql::driver::MysqlDriver {}, &lis.uri);
  //       if ret.as_ref().is_err() {
  //         return Err(anyhow!("init mysql server connection error:{}", ret.as_ref().unwrap_err().to_string()));
  //       }
  //     }
  //     _ => {
  //       return Err(anyhow!("unsupported database type, check configuration"));
  //     }
  //   }
  //   Ok(rb)
  // }

  pub async fn auto_recv_pacsresult(config: &Settings, auto_summary: i64, db: &DbConnection) -> Result<Vec<ResultMessage>> {
    let local_now = Local::now();
    let checked_add = local_now.checked_add_signed(Duration::days(-21));
    if checked_add.is_none() {
      return Err(anyhow!("date time error"));
    }
    let checked_add = checked_add.unwrap();
    let two_week_before = checked_add.timestamp();
    let current_date = local_now.timestamp();
    // info!("time stamp is:{}", &two_week_before);
    //query from tj_medexaminfo
    let ret = TjMedexaminfo::query_many(
      two_week_before,
      current_date,
      &vec![],
      &vec![],
      0,
      -1,
      &vec![],
      &vec![],
      &vec![ExamStatus::Examining as i32],
      -1,
      &"".to_string(),
      &db.get_connection(),
    )
    .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut medinfos = ret.unwrap();
    if medinfos.len() <= 0 {
      info!("没有需要接收数据的体检人员信息");
      return Ok(vec![]);
    }
    let testids: Vec<String> = medinfos.iter().map(|v| v.tj_testid.to_string()).collect();
    info!("本次需要自动接收的体检号：{:?}", &testids);
    // let testids_str: Vec<&str> = testids.iter().map(|v| v.as_str()).collect();
    let pids: Vec<String> = medinfos.iter().map(|v| v.tj_pid.to_string()).collect();
    // let pids: Vec<&str> = pids_string.iter().map(|v| v.as_str()).collect();

    //获取需要接收的科室信息（仅接收实验室数据)
    let deptinfos = SYSCACHE.get().unwrap().get_departs(&vec![], &db).await;
    let deptids: Vec<String> = deptinfos
      .iter()
      .filter(|&d| d.tj_depttype == DeptType::Function as i32)
      .map(|v| v.tj_deptid.to_string())
      .collect();
    info!("需要接收的科室信息:{:?}", &deptids);
    let deptids_str: Vec<String> = deptids.iter().map(|v| v.to_string()).collect();

    let ret = TjPatient::query_many(&pids, "", &vec![], &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ptinfos_all = ret.unwrap();

    let ret = TjPatienthazards::query_many(&testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let pthazards_all = ret.unwrap();

    // let iteminfos = SYSCACHE.get().unwrap().get_iteminfos(&vec![], db).await;

    let mut recv_results: Vec<ResultMessage> = Vec::new();

    for medinfo in medinfos.iter_mut() {
      info!("开始接收:{}的结果数据", &medinfo.tj_testid);

      let testids_2_str: Vec<String> = vec![medinfo.tj_testid.to_string()];

      let ret = TjTestsummary::query_many(&testids_2_str, &deptids_str, 0, 0, YesOrNo::No as i32, "", -1, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let mut summaries = ret.unwrap();
      if summaries.len() <= 0 {
        info!("该体检者没有未完成的科室，不需要接收");
        // return Ok(vec![]);
        continue;
      }

      let deptids_2: Vec<String> = summaries.iter().map(|v| v.tj_deptid.to_string()).collect();
      // let deptids_2_str: Vec<&str> = deptids_2.iter().map(|v| v.as_str()).collect();
      let ret = TjCheckiteminfo::query_many(&deptids_2, &deptids_2, -1, -1, &vec![], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let ciinfos_all = ret.unwrap();
      if ciinfos_all.len() <= 0 {
        // return Err(anyhow!("没有该体检者在这些科室的检查数据"));
        continue;
      }

      let ret = RecvPacsData::query_from_external_pacsresult(&medinfo.tj_testid, &config, &db).await;
      if ret.as_ref().is_err() {
        error!("query error:{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let pacsresults = ret.unwrap();
      if pacsresults.len() <= 0 {
        info!("pacs没有体检号：{}的结果数据", &medinfo.tj_testid);
        continue;
      }

      let ptinfo = ptinfos_all.iter().find(|p| p.tj_pid.eq_ignore_ascii_case(&medinfo.tj_pid));
      if ptinfo.is_none() {
        error!("不能找到编号为{}的人员信息", &medinfo.tj_pid);
        continue;
      }
      let ptinfo = ptinfo.unwrap();
      let pthazards: Vec<TjPatienthazards> = pthazards_all
        .iter()
        .filter(|f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid))
        .map(|v| v.to_owned())
        .collect();
      for summary in summaries.iter_mut() {
        // let ret = RecvLabdata::recv_external_lab_result_by_dept(medinfo, summary, &pthazards, &ptinfo, &ciinfos_all, &lisresults, &iteminfos, auto_summary, db).await;
        if summary.tj_isfinished == YesOrNo::Yes as i32 {
          continue;
        }
        let ret = RecvPacsData::import_pacs_result(medinfo, summary, &pthazards, &ptinfo, &ciinfos_all, &pacsresults, auto_summary, db).await;

        recv_results.push(ret);
      }
    }

    Ok(recv_results)
  }

  async fn query_from_external_pacsresult(testid: &String, _config: &Settings, db: &DbConnection) -> Result<Vec<VTjPacsresult>> {
    // info!("start to init pacs connection......");
    // let ret = RecvPacsData::init_db_connection(&pacs).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    //   // return vec![ResultMessage {
    //   //   code: RecvResultCode::Failure as i32,
    //   //   message: format!("{} {}", &testid, ret.as_ref().unwrap_err().to_string()),
    //   // }];
    // }
    // let mut rb = ret.unwrap();
    // let mut pacsresults: Vec<VTjPacsresult> = Vec::new();
    // if pacs.version == 1 {
    let ret = VTjPacsresult::query_many(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      // return vec![ResultMessage {
      //   code: RecvResultCode::Failure as i32,
      //   message: format!("{} {}", &testid, ret.as_ref().unwrap_err().to_string()),
      // }];
    }

    let pacsresults = ret.unwrap();
    // }
    Ok(pacsresults)
  }
  // fn get_pacsresult(pacs: &VTjPacsresult, iteminfo: &TjIteminfo) -> TjPacsresult {
  //   TjPacsresult {
  //     id: 0,
  //     tj_testid: pacs.tjbh.to_string(),
  //     tj_patientname: pacs.brxm.to_string(),
  //     tj_itemid: iteminfo.tj_itemid.to_string(),
  //     tj_itemname: iteminfo.tj_itemname.to_string(),
  //     tj_jclx: pacs.jclx.to_string(),
  //     tj_jcxm: pacs.jcxm.to_string(),
  //     tj_jcmc: pacs.jcmc.to_string(),
  //     tj_jcys: pacs.jcys.to_string(),
  //     tj_sxys: pacs.sxys.to_string(),
  //     tj_bgys: pacs.bgys.to_string(),
  //     tj_sfyc: pacs.sfyc,
  //     tj_importer: "".to_string(),
  //     tj_imagesight: pacs.imagesight.to_string(),
  //     tj_imagediagnosis: pacs.imagediagnosis.to_string(),
  //     tj_bgrq: timeutil::convert_datetime_to_timestamp(&pacs.bgrq, "%Y%m%d %H%M%S"),
  //     tj_importdate: timeutil::current_timestamp(),
  //   }
  // }
}
