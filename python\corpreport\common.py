import os
import constant
from dataentities.dbconn import session
from dataentities.tj_staffadmin import TjStaffadmin


def get_zjys(dicts, creator):
    zjys = ""

    dict_info = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictSysparm.value
        and dict.ss_pid == constant.SysParm.EZJeSign.value
    )
    if dict_info is None:
        pass
    else:
        if dict_info.ss_short == "1":  # 电子签名
            # 先检查该主检医生的签名
            staff = (
                session.query(TjStaffadmin).filter(TjStaffadmin.id == creator).first()
            )
            if staff is not None:
                staff_esign = staff.tj_esign
                if staff_esign != "":
                    esign = "./sign/{}".format(staff_esign)
                    if os.path.exists(esign):
                        zjys = esign
            if zjys == "":  # 再检查系统配置的总检医生
                print("开始根据系统配置的主检医生处理：", dict_info.ss_name)
                esign = "./sign/{}".format(dict_info.ss_name)
                if os.path.exists(esign):
                    zjys = esign
        elif dict_info.ss_short == "2":
            staff = (
                session.query(TjStaffadmin).filter(TjStaffadmin.id == creator).first()
            )
            if staff is None:
                zjys = dict_info.ss_name
            else:
                zjys = staff.tj_staffname
        else:
            zjys = ""

        return zjys


def get_shys(dicts):
    shys = ""
    dict_info = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictSysparm.value
        and dict.ss_pid == constant.SysParm.ESHYiSheng.value
    )
    if dict_info is None:
        pass
    else:
        if dict_info.ss_short == "1":  # esign
            esign = "./sign/{}".format(dict_info.ss_name)
            if os.path.exists(esign):
                shys = esign
        elif dict_info.ss_short == "2":
            shys = dict_info.ss_name
        else:
            shys = ""
    # print("审核医生：", shys)
    return shys

def get_zbr(dicts):
    pzys = ""
    dict_info = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictSysparm.value
        and dict.ss_pid == constant.SysParm.ZBRSign.value
    )
    if dict_info is None:
        pass
    else:
        if dict_info.ss_short == "1":  # esign
            esign = "./sign/{}".format(dict_info.ss_name)
            if os.path.exists(esign):
                pzys = esign
        elif dict_info.ss_short == "2":
            pzys = dict_info.ss_name
        else:
            pzys = ""
    return pzys


def get_pzys(dicts):
    pzys = ""
    dict_info = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictSysparm.value
        and dict.ss_pid == constant.SysParm.PZeSign.value
    )
    if dict_info is None:
        pass
    else:
        if dict_info.ss_short == "1":  # esign
            esign = "./sign/{}".format(dict_info.ss_name)
            if os.path.exists(esign):
                pzys = esign
        elif dict_info.ss_short == "2":
            pzys = dict_info.ss_name
        else:
            pzys = ""
    return pzys


def get_customer_name(dicts):
    customer_name = ""
    dict_info = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictCustomer.value
        and dict.ss_pid == constant.CustomerInfo.CustomerName.value
    )
    if dict_info is None:
        pass
    else:
        customer_name = dict_info.ss_name
    return customer_name


def get_customer_stamp(dicts):
    dwgz = ""
    # 体检单位是否显示电子章
    dict_info = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictSysparm.value
        and dict.ss_pid == constant.SysParm.Stample.value
    )
    if dict_info is None:
        pass
    else:
        if dict_info.ss_short == "1" or dict_info.ss_short == "2":
            filename = "./sign/{}".format(dict_info.ss_name)
            # print("corp stamp file name is:", filename)
            if os.path.exists(filename):
                dwgz = filename
            else:
                dwgz = ""
    return dwgz
