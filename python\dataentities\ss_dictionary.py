# import datetime
# from sqlalchemy.orm import sessionmaker
# from sqlalchemy.orm import scoped_session
from sqlalchemy import (
    Column,
    Integer,
    String,
)
from dataentities.dbconn import Base


class SsDictionary(Base):
    # 数据库中存储的表名
    __tablename__ = "ss_dictionary"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(
        Integer, name="id", primary_key=True, autoincrement=True,
        nullable=False
    )
    ss_typeid = Column(Integer, name="ss_typeid", nullable=False)
    ss_pid = Column(Integer, name="ss_pid", nullable=False)
    ss_short = Column(String(64), name="ss_short", nullable=False, unique=True)
    ss_name = Column(
        String(128),
        name="ss_name",
        nullable=False,
    )
    ss_showorder = Column(Integer, name="ss_showorder", nullable=False)
    ss_memo = Column(String(128), name="ss_memo", nullable=False)
    # __table__args__ = (
    #     UniqueConstraint("name", "age", "phone"),  # 联合唯一约束
    #     Index("name", "addr", unique=True),       # 联合唯一索引
    # )

    # def __str__(self):
    #     return f"object : <id:{self.id} name:{self.name}>"
