use crate::entities::{prelude::*, tj_staffadmin};
use anyhow::{anyhow, Result};
use sea_orm::{sea_query::Expr, ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjStaffadmin {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<TjStaffadmin>> {
    if code.is_empty() {
      return Err(anyhow!("code is empty, not allowed"));
    }
    let ret = TjStaffadminEntity::find().filter(tj_staffadmin::Column::TjStaffno.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }
  pub async fn query_by_id(id: i64, db: &DatabaseConnection) -> Result<Option<TjStaffadmin>> {
    let ret = TjStaffadminEntity::find().filter(tj_staffadmin::Column::Id.eq(id)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }
  pub async fn query_many(staffnos: &Vec<String>, staffids: &Vec<i64>, db: &DatabaseConnection) -> Result<Vec<TjStaffadmin>> {
    let mut conditions = Condition::all().add(tj_staffadmin::Column::TjStatus.gt(0));
    if staffnos.len() > 0 {
      conditions = conditions.add(tj_staffadmin::Column::TjStaffno.is_in(staffnos.to_owned()));
    }
    if staffids.len() > 0 {
      conditions = conditions.add(tj_staffadmin::Column::Id.is_in(staffids.to_owned()));
    }
    let ret = TjStaffadminEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjStaffadmin, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_staffadmin::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn delete(staffids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjStaffadminEntity::update_many()
      .col_expr(tj_staffadmin::Column::TjStatus, Expr::value(-1))
      .filter(Condition::all().add(tj_staffadmin::Column::Id.is_in(staffids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
}
