use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(C<PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjCorpoccureportInfo {
  pub id: i64,
  pub tj_report_id: i64,
  pub tj_test_id: String,
  pub tj_rptnum: String,
}
crud!(TjCorpoccureportInfo {}, "tj_corpoccureport_info");
rbatis::impl_select!(TjCorpoccureportInfo{query(testid:&str) ->Option => "`where tj_test_id = #{testid} limit 1 `"});
rbatis::impl_select!(TjCorpoccureportInfo{query_many(ids:&[i64]) => "`where tj_report_id in ${ids.sql()} `"});
rbatis::impl_delete!(TjCorpoccureportInfo{delete_many(testids:&[String]) => "`where tj_test_id in ${testids.sql()} `"});
rbatis::impl_select!(TjCorpoccureportInfo{query_many_by_testids(testids:&[String]) => "`where tj_test_id in ${testids.sql()} `"});
