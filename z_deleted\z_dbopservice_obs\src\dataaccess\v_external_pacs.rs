// use anyhow::{anyhow, Result};
use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct VExternalPacs {
  pub testid: String,
  pub testername: String,
  pub tid: String,
  pub birthdate: String,
  pub idcard: String,
  pub psex: i32,
  pub age: i32,
  pub itemid2: String,
  pub itemid: String,
  pub itemname: String,
  pub pacs: String,
  pub deptid: String,
  pub deptname: String,
  pub requesterid: String,
  pub requestername: String,
  pub requestdate: String,
  pub diag: String,
  pub price: String,
  pub phone: String,
}
crud!(VExternalPacs {}, "v_external_pacs");
rbatis::impl_select!(VExternalPacs{query_many(testid:&str, barcode:&str) =>
  "`where id > 0 `
  if testid != '':
    ` and testid = #{testid} `
  if barcode != '':
    ` and tid = #{barcode} `"});
