use crate::{datasetup::DbConnection, entities::prelude::*};
use anyhow::{anyhow, Result};
use rbatis::crud::{Skip, CRUD};
use rbson::Bson;

impl TjCorpoccureport {
  pub async fn query(rptnum: &String, db: &DbConnection) -> Option<TjCorpoccureport> {
    if rptnum.is_empty() {
      return None;
    }
    let w = db.get_connection().new_wrapper().eq("tj_reportnumint", rptnum); //

    let query_ret = db.get_connection().fetch_by_wrapper(w).await;

    match query_ret {
      Ok(v) => v,
      Err(e) => {
        error!("query report info by reportnum error:{}", e);
        None
      }
    }
  }

  pub async fn query_by_id(id: i64, db: &DbConnection) -> Option<TjCorpoccureport> {
    if id <= 0 {
      return None;
    }
    let w = db.get_connection().new_wrapper().eq("id", id); //

    let query_ret = db.get_connection().fetch_by_wrapper(w).await;

    match query_ret {
      Ok(v) => v,
      Err(e) => {
        error!("query report info by id error:{}", e);
        None
      }
    }
  }

  pub async fn query_many(rptnums: &Vec<String>, db: &DbConnection) -> Result<Vec<TjCorpoccureport>> {
    // if rptnums.len() <= 0 {
    //     return Err(anyhow!("report numbers empty"));
    // }
    let w = db.get_connection().new_wrapper().do_if(rptnums.len() > 0, |w| w.r#in("tj_reportnumint", &rptnums));

    let query_ret = db.get_connection().fetch_list_by_wrapper(w).await;

    match query_ret {
      Ok(v) => Ok(v),
      Err(e) => {
        error!("query error:{}", &e);
        Err(anyhow!("query many error:{:?}", &e))
      }
    }
  }

  pub async fn query_many_by_dto(
    reportids: &Vec<i64>,
    dtstart: &str,
    dtend: &str,
    corp: &str,
    testtype: &str,
    pname: &str,
    testid: &str,
    reportnum: &str,
    db: &DbConnection,
  ) -> Result<Vec<TjCorpoccureport>> {
    // info!("query patient dto:{:#?}", &dto);
    let w = db
      .get_connection()
      .new_wrapper()
      .do_if(reportids.len() > 0, |w| w.r#in("id", reportids.to_owned().as_slice()))
      .do_if(pname.len() > 0, |w| w.like("tj_pname", pname.to_owned()))
      .do_if(!dtstart.is_empty(), |w| w.gt("tj_createdate", dtstart.to_owned()))
      .do_if(!dtend.is_empty(), |w| w.lt("tj_createdate", dtend.to_owned()))
      .do_if(!corp.is_empty(), |w| w.eq("tj_corpid", corp.to_owned()))
      .do_if(!testtype.is_empty(), |w| w.like("tj_testtype", testtype.to_owned()))
      .do_if(!testid.is_empty(), |w| w.like("tj_pidcard", testid.to_owned()))
      .do_if(!reportnum.is_empty(), |w| w.like("tj_reportnum", reportnum.to_owned()))
      .do_if(!dtstart.is_empty(), |w| w.like("tj_pidcard", dtstart.to_owned()));

    let query_ret = db.get_connection().fetch_list_by_wrapper(w).await;
    // info!("query patient result:{:?}", &query_ret);
    match query_ret {
      Ok(v) => Ok(v),
      Err(e) => {
        error!("query error:{}", &e);
        Err(anyhow!("query many error:{:?}", &e))
      }
    }
  }

  pub async fn save(info: &TjCorpoccureport, db: &DbConnection) -> Result<TjCorpoccureport> {
    if info.id == 0 {
      let ret = TjCorpoccureport::insert(info, db).await;
      if ret.as_ref().is_err() {
        error!("error:{:?}", ret.as_ref().err().unwrap());
        return Err(anyhow!("error:{:?}", ret.as_ref().err().unwrap()));
      }
      let id = ret.unwrap();
      let mut ret_val = info.clone();
      ret_val.id = id;
      return Ok(ret_val);
    } else {
      let ret = TjCorpoccureport::update(info, db).await;
      if ret.as_ref().is_err() {
        error!("error:{:?}", ret.as_ref().err().unwrap());
        return Err(anyhow!("error:{:?}", ret.as_ref().err().unwrap()));
      }
      Ok(info.clone())
    }
  }
  pub async fn save_many(infos: &Vec<TjCorpoccureport>, db: &DbConnection) -> Result<()> {
    let (insert_vals, update_vals): (Vec<TjCorpoccureport>, Vec<TjCorpoccureport>) = infos.to_owned().into_iter().partition(|f| f.id == 0);
    if insert_vals.len() > 0 {
      let ret = TjCorpoccureport::insert_many(&insert_vals, &db).await;
      if ret.as_ref().is_err() {
        error!("insrt many error: {:?}", ret.as_ref().err());
        return Err(anyhow::anyhow!("insert many error"));
      }
    }

    if update_vals.len() > 0 {
      for val in infos.iter() {
        let ret = db.get_connection().update_by_column("id", val).await;
        if ret.is_err() {
          error!("update  error: {:?}", ret.err().unwrap().to_string());
          return Err(anyhow::anyhow!("update many error"));
        }
      }
    }

    Ok(())
  }

  pub async fn insert(info: &TjCorpoccureport, db: &DbConnection) -> Result<i64> {
    let ret = db.get_connection().save(info, &[]).await;
    if ret.as_ref().is_err() {
      error!("保存出错，错误信息：{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("保存出错，错误信息：{}", ret.as_ref().err().unwrap().to_string()));
    }
    info!("result:{:?}", &ret);
    let id = ret.as_ref().unwrap().last_insert_id.map_or(0, |v| v);
    Ok(id)
  }

  pub async fn insert_many(infos: &Vec<TjCorpoccureport>, db: &DbConnection) -> Result<()> {
    let ret = db.get_connection().save_batch(&infos, &[]).await;
    if ret.as_ref().is_err() {
      error!("保存信息失败:{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("保存信息失败:{}", ret.as_ref().err().unwrap().to_string()));
    }

    Ok(())
  }

  pub async fn update(info: &TjCorpoccureport, db: &DbConnection) -> Result<()> {
    let ret = db.get_connection().update_by_column("id", info).await;
    match ret {
      Ok(_v) => Ok(()),
      Err(e) => Err(anyhow!("update error: {}", e)),
    }
  }

  pub async fn update_may(vals: &Vec<TjCorpoccureport>, conn: &DbConnection) -> Result<()> {
    //方法1： 采用wrapper的方法更新
    for val in vals {
      let w = conn.get_connection().new_wrapper().eq("id", val.id);
      let ret = conn.get_connection().update_by_wrapper(val, w, &[Skip::Value(Bson::Null), Skip::Column("id")]).await;
      if ret.as_ref().is_err() {
        error!("更新错误,错误信息:{},资产信息:{:#?}", ret.as_ref().err().unwrap().to_string(), &val);
        // return Err(anyhow!("update user assets info error"));
        continue;
      }

      let updated_rows = ret.unwrap();
      if updated_rows <= 0 {
        let insert_ret = conn.get_connection().save(&val, &[]).await;
        if insert_ret.as_ref().is_err() {
          error!("插入错误,错误信息:{},资产信息:{:#?}", insert_ret.as_ref().err().unwrap().to_string(), &val);
          continue;
        }
      }
    }
    Ok(())
  }
}
