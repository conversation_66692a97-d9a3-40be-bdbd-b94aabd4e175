use anyhow::{anyhow, Result};
use rbatis::rbatis_codegen::IntoSql;
use rbatis::{crud, py_sql};
use serde::{Deserialize, Serialize};

#[derive(Clone, Default, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjCorpinfo {
  pub id: i64,
  pub tj_corpid: String,
  pub tj_corpname: String,
  pub tj_contactor: String,
  pub tj_contactorjob: String,
  pub tj_principle: String,
  pub tj_phone: String,
  pub tj_fax: String,
  pub tj_areacode: String,
  pub tj_address: String,
  pub tj_postcode: String,
  pub tj_industry2: String,
  pub tj_economic2: String,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_operator: i64,
  pub tj_adddate: i64,
  pub tj_testtype: i32,
  pub tj_password: String,
  pub tj_mail: String,
  pub tj_memo: String,
  pub tj_orgcode: String,
  pub tj_gbcode: String,
  pub tj_ecotypeh: i32,
  pub tj_ecotype: i32,
  pub tj_industryh: i32,
  pub tj_industry: i32,
  pub tj_secondcode: String,
  pub tj_secondname: String,
  pub tj_corpscale: i32,
  pub tj_total: i32,
  pub tj_totalf: i32,
  pub tj_operationer: i32,
  pub tj_operationerf: i32,
  pub tj_hazarders: i32,
  pub tj_hazardersf: i32,
  pub tj_externalstaff: i32,
  pub tj_syncflag: i32,
  pub tj_status: i32,
  pub tj_monitortype: String,
  pub p_cmpid: i64,
}
crud!(TjCorpinfo {}, "tj_corpinfo");
rbatis::impl_select!(TjCorpinfo{query(code:i64) -> Option => "`where id = #{code} limit 1 `"});

impl TjCorpinfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjCorpinfo) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjCorpinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjCorpinfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
  #[py_sql(
    "`select * from tj_corpinfo where id > 0 `
        if corpname != '':
          ` and tj_corpname like #{'%'+corpname+'%'} `
        if !corpids.is_empty():
          ` and id in ${corpids.sql()} `                   
        if !orgcode.is_empty():
          ` and tj_orgcode in ${orgcode.sql()} `     
      "
  )]
  async fn query_many(rb: &mut rbatis::RBatis, corpids: &[i64], corpname: &str, orgcode: &[&str]) -> Result<Vec<TjCorpinfo>, rbatis::Error> {
    impled!()
  }

  // #[sql("update tj_corpinfo set tj_status = -1 where id = ? ")]
  // pub async fn delete(rb: &mut rbatis::RBatis, id: &i64) -> rbatis::Result<i64> {
  //   impled!()
  // }

  #[py_sql("update tj_corpinfo set tj_status = -1 where id in ${ids.sql()} ")]
  pub async fn delete(rb: &mut rbatis::RBatis, ids: &[i64]) -> rbatis::Result<()> {
    impled!()
  }
}
