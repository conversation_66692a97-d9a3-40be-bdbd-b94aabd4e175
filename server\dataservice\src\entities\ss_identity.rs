//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, De<PERSON>ult, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "ss_identity")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  // #[sea_orm(column_name = "ID_NAME")]
  pub id_name: String,
  // #[sea_orm(column_name = "ID_CVALUE")]
  pub id_cvalue: i64,
  // #[sea_orm(column_name = "ID_OVALUE")]
  pub id_ovalue: i32,
  // #[sea_orm(column_name = "ID_INCVALUE")]
  pub id_incvalue: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
