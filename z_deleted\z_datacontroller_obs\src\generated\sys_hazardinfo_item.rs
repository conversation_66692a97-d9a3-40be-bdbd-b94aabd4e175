//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "sys_hazardinfo_item")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub s_code: String,
    pub item_id: String,
    pub s_name: String,
    pub s_unit_name: String,
    pub s_result_min: String,
    pub s_resutl_max: String,
    pub s_key: i8,
    pub mark: String,
    pub s_unit_name_value: String,
    pub s_result_min_value: String,
    pub s_result_max_value: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
