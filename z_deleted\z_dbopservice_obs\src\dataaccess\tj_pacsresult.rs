use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjPacsresult {
  pub id: i64,
  pub tj_testid: String,
  pub tj_patientname: String,
  pub tj_itemid: String,
  pub tj_itemname: String,
  pub tj_jclx: String,
  pub tj_jcxm: String,
  pub tj_jcmc: String,
  pub tj_jcys: String,
  pub tj_sxys: String,
  pub tj_bgys: String,
  pub tj_sfyc: i32,
  pub tj_importer: String,
  pub tj_imagesight: String,
  pub tj_imagediagnosis: String,
  pub tj_bgrq: i64,
  pub tj_importdate: i64,
}
crud!(TjPacsresult {}, "tj_pacsresult");
rbatis::impl_select!(TjPacsresult{query_many(testid:&str) => "`where tj_testid = #{testid} `"});
rbatis::impl_delete!(Tj<PERSON>acsresult{delete(testids:&[&str]) => "`where tj_testid in ${testids.sql()} `"});
rbatis::impl_delete!(TjPacsresult{clear(date:i64) => "`where tj_importdate < #{date} `"});
