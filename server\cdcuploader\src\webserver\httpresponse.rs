use axum::extract::J<PERSON>;
use serde_json::Value;
// use axum::response::J<PERSON>;
use serde::{Deserialize, Serialize};
// use serde_json::{json, Value};
#[derive(<PERSON><PERSON><PERSON>q, <PERSON><PERSON>, Debug)]
pub enum HttpCode {
  OK = 200,
  Error = 201,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ResponseBody<T> {
  pub code: i32,
  pub message: String,
  pub data: T,
}

impl<T> ResponseBody<T> {
  pub fn new(code: i32, message: &str, data: T) -> ResponseBody<T> {
    ResponseBody {
      code: code,
      message: message.to_string(),
      data,
    }
  }
}
#[derive(Debug, Serialize, Deserialize)]
pub struct DataBody<T> {
  pub total: usize,
  pub data: T,
}

// impl<T> DataBody<T> {
//   pub fn new(total: usize, data: T) -> DataBody<T> {
//     DataBody { total: total, data: data }
//   }
// }

pub fn response_error(error: &String) -> <PERSON><PERSON><Value> {
  // let error_msg = format!("{:?}", error);
  let res = ResponseBody::new(HttpCode::Error as i32, &error, "");

  Json(serde_json::json!(res))
}
// pub fn response_ok()
