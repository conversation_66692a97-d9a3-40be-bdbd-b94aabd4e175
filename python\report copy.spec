# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Add runtime hook to suppress pkg_resources warning
import os
runtime_hook_path = os.path.join(os.path.dirname(__file__), 'runtime_hook.py')
with open(runtime_hook_path, 'w') as f:
    f.write('''
import warnings
warnings.filterwarnings('ignore', category=UserWarning, module='PyInstaller.loader.pyimod02_importers')
''')

a = Analysis(
    ['report.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('fonts/*', 'fonts'),  # Include fonts directory
        ('images/*', 'images'),  # Include images directory
        ('config/*', 'config'),  # Include config directory
    ],
    hiddenimports=[
        'reportlab',
        'sqlalchemy',
        'pymysql',
        'dataentities',
        'corpreport',
        'personalreport',
        'common',
        'config',
        'importlib.metadata',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[runtime_hook_path],  # Add the runtime hook
    excludes=['pkg_resources'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='report',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
