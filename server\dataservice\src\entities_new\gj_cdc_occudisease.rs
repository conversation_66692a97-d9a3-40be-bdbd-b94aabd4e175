//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "gj_cdc_occudisease")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub cdc_occdis_code: String,
  pub cdc_occdis_name: String,
  pub cdc_occdis_pcode: String,
  pub cdc_remark: String,
  pub tj_disid: i32,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
