//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, Default, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "v_tj_lisresult")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tjbh: String,
  pub brxm: String,
  pub xmxh: String,
  pub xmmc: String,
  pub xmdw: String,
  pub xmjg: String,
  pub sfyc: i32,
  pub gdbj: String,
  pub ckdz: String,
  pub ckgz: String,
  pub ckfw: String,
  pub jyys: String,
  pub bgrq: String,
  // pub bgrq: String,
  pub bgys: String,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
