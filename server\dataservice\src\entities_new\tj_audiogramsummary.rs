//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_audiogramsummary")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_adsummary: Option<String>,
  pub tj_adsuggestion: Option<String>,
  pub tj_isabnormal: Option<i32>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
