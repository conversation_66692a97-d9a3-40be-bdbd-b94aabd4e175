use num_enum::TryFromPrimitive;
use serde::{Deserialize, Serialize};
use strum_macros::*;
// use serde_json::Value;

pub const BUSINESS_TYPE: i32 = 3; //业务类型
pub const SYSTEM_SN: &str = "1005";
pub const DEPT_SN: &str = "240628009"; //240320061
pub const DEPT_NAME: &str = "体检中心"; //240320061
pub const YIBU_CODE_OK: i32 = 0;

#[derive(Debug, <PERSON>lone, Default, Serialize, Deserialize)]
pub struct YibuResponse<T> {
  #[serde(rename = "code")]
  pub code: i32,
  #[serde(rename = "message")]
  pub message: String,
  #[serde(rename = "result")]
  pub result: Option<T>,
}

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(i32)]
pub enum YibuSex {
  Male = 1,
  Female = 2,
  Unknown = 9,
}

//urgent {1: 紧急, 2: 普通}
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, TryFromPrimitive, PartialEq, Eq)]
#[repr(i32)]
pub enum YibuUrgent {
  Urgent = 1,
  Normal = 2,
}

//classify {1: 门诊, 2: 住院, 3: 体检}
// #[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
// #[repr(i32)]
// pub enum YibuClassify {
//   Menzhen = 1,
//   Zhuyuan = 2,
//   Tijian = 3,
// }

#[derive(Debug, Eq, Display, PartialEq, EnumString, AsRefStr, IntoStaticStr)]
#[repr(i32)]
pub enum YibuApi {
  /// Random Docs
  #[strum(to_string = "/systemapp/publicapi/system/interface/request/3000211")]
  QueryLisItems,
  #[strum(to_string = "/systemapp/publicapi/system/interface/request/1000202")]
  QueryHisExamItems,
  #[strum(to_string = "/systemapp/publicapi/system/interface/request/3000106")]
  QueryCheckResults,
  #[strum(to_string = "/systemapp/publicapi/system/interface/request/3000208")]
  QueryLisResults,
  #[strum(to_string = "/systemapp/publicapi/system/interface/request/4000101")]
  MedexamProfile,
  #[strum(to_string = "/systemapp/publicapi/system/interface/request/8000101")]
  MedexamFeeApplicaiton,
  #[strum(to_string = "/systemapp/publicapi/system/interface/request/8000102")]
  MedexamFeeCancelApplicaiton,
  #[strum(to_string = "/systemapp/publicapi/system/interface/request/3000101")]
  MedexamCheckApplication,
  #[strum(to_string = "/systemapp/publicapi/system/interface/request/3000109")]
  MedexamCheckCancelApplication,
  #[strum(to_string = "/systemapp/publicapi/system/interface/request/3000201")]
  MedexamLisApplication,
  #[strum(to_string = "/systemapp/publicapi/system/interface/request/3000209")]
  MedexamLisCancelApplication,
  #[strum(to_string = "a1")]
  MedexamExample,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct QueryLisItemsDto {
  #[serde(rename = "hospitalId")]
  pub hospitalid: String,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct QueryHisItemsDto {
  #[serde(rename = "typeSns")]
  pub type_sns: String,
  #[serde(rename = "usingTag")]
  pub using_tag: i32,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct HisExamItems {
  #[serde(rename = "itemsSn")]
  pub itemssn: String,
  #[serde(rename = "itemsName")]
  pub itemsname: String,
  #[serde(rename = "initials")]
  pub initials: Option<String>,
  #[serde(rename = "unit")]
  pub unit: Option<String>,
  #[serde(rename = "price")]
  pub price: Option<f32>,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct QueryResultsDto {
  #[serde(rename = "number")]
  pub number: String,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuCheckresult {
  pub sn: Option<String>, //检查编号:由本服务生成，完成后会返回值，后续修改时需要传入此值
  #[serde(rename = "systemSn")]
  pub system_sn: Option<String>, //系统编号，对应接入系统的编号
  #[serde(rename = "sourceSn")]
  pub source_sn: Option<String>, //业务流水号，业务系统中对申请单的编号
  #[serde(rename = "code")]
  pub code: Option<String>, //申请号，对应单据上的编号
  #[serde(rename = "barCode")]
  pub bar_code: Option<String>, //条码号，如果需要可以传入
  #[serde(rename = "classify")]
  pub classify: Option<i32>, //{1: 门诊, 2: 住院, 3: 体检}根据应用系统传入值，用于区分code对应哪个系统
  #[serde(rename = "number")]
  pub number: Option<String>, //就诊编号（门诊/住院/体检编号）
  #[serde(rename = "patientSn")]
  pub patient_sn: Option<String>, //患者索引号，如果没有患者索引服务为空
  #[serde(rename = "patientName")]
  pub patient_name: Option<String>, //患者姓名
  #[serde(rename = "idcard")]
  pub idcard: Option<String>, //患者身份证号
  #[serde(rename = "gender")]
  pub gender: Option<i32>, //患者性别，传代码{1: 男, 2: 女, 9: 未知}
  #[serde(rename = "birthday")]
  pub birthday: Option<i64>, //患者出生日期，用Unix时间戳（毫秒）表示
  #[serde(rename = "age")]
  pub age: Option<String>, //年龄,如“23岁”，“9月”，“7天”
  #[serde(rename = "phone")]
  pub phone: Option<String>, //患者联系电话
  #[serde(rename = "address")]
  pub address: Option<String>, //患者地址
  #[serde(rename = "urgent")]
  pub urgent: Option<i32>, //紧急标志，传代码{1: 紧急, 2: 普通}
  #[serde(rename = "deptSn")]
  pub dept_sn: Option<String>, //
  #[serde(rename = "deptName")]
  pub dept_name: Option<String>, //
  #[serde(rename = "areaSn")]
  pub area_sn: Option<String>, //
  #[serde(rename = "areaName")]
  pub area_name: Option<String>, //
  #[serde(rename = "bed")]
  pub bed: Option<String>, //
  #[serde(rename = "icd")]
  pub icd: Option<String>, //诊断编码
  #[serde(rename = "diagnosis")]
  pub diagnosis: Option<String>, //诊断名称
  #[serde(rename = "type")]
  pub checktype: Option<String>, //检查类型，如CT,DR
  #[serde(rename = "itemsSn")]
  pub items_sn: Option<String>, //检查项目编号，如果多个项目，可以通过items字段中传列表，单个项目不需要传列表。
  #[serde(rename = "name")]
  pub checkname: Option<String>, //检查名称
  #[serde(rename = "partMethod")]
  pub part_method: Option<String>, //检查部位及方法
  #[serde(rename = "remark")]
  pub remark: Option<String>, //检查要求及其他说明
  #[serde(rename = "orderDeptSn")]
  pub order_dept_sn: Option<String>, //开单科室编号
  #[serde(rename = "orderDeptName")]
  pub order_dept_name: Option<String>, //开单科室名称
  #[serde(rename = "orderPersonSn")]
  pub order_person_sn: Option<String>, //开单人员编号（即医生编号）
  #[serde(rename = "orderPersonName")]
  pub order_person_name: Option<String>, //开单人员编号（即医生姓名）
  #[serde(rename = "executeDeptSn")]
  pub execute_dept_sn: Option<String>, //执行科室编号
  #[serde(rename = "executeDeptName")]
  pub execute_dept_name: Option<String>, //执行科室
  #[serde(rename = "orderTime")]
  pub order_time: Option<i64>, //开单时间，Unix时间戳（毫秒）
  #[serde(rename = "payment")]
  pub payment: Option<i64>, //金额
  #[serde(rename = "masterSn")]
  pub master_sn: Option<String>, //目标系统码
  #[serde(rename = "masterName")]
  pub master_name: Option<String>, //目标系统名称
  #[serde(rename = "objective")]
  pub objective: Option<String>, //检查目的
  #[serde(rename = "time")]
  pub apply_time: Option<i64>, //申请时间
  //   #[serde(rename = "orderTime")]
  //   pub order_time: Option<String>, //开单时间
  #[serde(rename = "appointmentTime")]
  pub appointment_timee: Option<i64>, //预约时间
  #[serde(rename = "executeTime")]
  pub execute_time: Option<i64>, //执行时间
  #[serde(rename = "resultTime")]
  pub result_time: Option<i64>, //结果时间
  #[serde(rename = "cancelTime")]
  pub cancel_time: Option<i64>, //取消时间
  #[serde(rename = "executeSn")]
  pub execute_sn: Option<String>, //执行编号，PACS系统生成的编号
  #[serde(rename = "imageSn")]
  pub image_sn: Option<String>, //影像编号
  #[serde(rename = "result")]
  pub result: Option<String>, //检查结
  #[serde(rename = "resultIndex")]
  pub result_index: Option<String>, //检查结
  #[serde(rename = "imageRemark")]
  pub image_remark: Option<String>, //结果指标
  #[serde(rename = "imageHint")]
  pub image_hint: Option<String>, //影像提示
  #[serde(rename = "suggest")]
  pub suggest: Option<String>, //医生建议
  #[serde(rename = "reportRemark")]
  pub report_remark: Option<String>, //报告备注
  #[serde(rename = "critical")]
  pub critical: Option<String>, //危急值
  #[serde(rename = "reporterSn")]
  pub reporter_sn: Option<String>, //报告医生编号
  #[serde(rename = "reporter")]
  pub reporter: Option<String>, //报告医生姓名
  #[serde(rename = "reviewerSn")]
  pub reviewer_sn: Option<String>, //报告审核医生编号
  #[serde(rename = "reviewer")]
  pub reviewer: Option<String>, //报告审核医生
  #[serde(rename = "dicom")]
  pub dicom: Option<String>, //影像地址
  #[serde(rename = "reportPath")]
  pub report_path: Option<String>, //报告地址
  #[serde(rename = "cloudImageUrl")]
  pub cloud_image_url: Option<String>, //云影像地址
  #[serde(rename = "qrCode")]
  pub qr_code: Option<String>, //二维码
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuLisResults {
  #[serde(rename = "laboratory")]
  pub laboratory: YibuLaboratory,
  #[serde(rename = "resultList")]
  pub resultlist: Vec<YibuLisresultInfo>,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuLaboratory {
  #[serde(rename = "barCode")]
  pub bar_code: Option<String>,
  #[serde(rename = "number")]
  pub number: Option<String>,
  // executeDoctorName
  #[serde(rename = "executeDoctorName")]
  pub execute_doctor_name: Option<String>,
  // auditDoctorName
  #[serde(rename = "auditDoctorName")]
  pub audit_doctor_name: Option<String>,
  // implementTime
  #[serde(rename = "implementTime")]
  pub implement_time: Option<i64>,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuLisresultInfo {
  pub id: Option<i64>, //
  #[serde(rename = "applySn")]
  pub apply_sn: Option<String>, //
  #[serde(rename = "systemSn")]
  pub system_sn: Option<String>, //系统编号，对应接入系统的编号，用于向系统回传
  #[serde(rename = "classify")]
  pub classify: Option<i32>, //业务类型{1:门诊，2:住院，3:体检，4:急诊}
  #[serde(rename = "number")]
  pub number: Option<String>, //number
  #[serde(rename = "sourceSn")]
  pub source_sn: Option<String>, //业务流水号，业务系统中对申请单的编号
  #[serde(rename = "code")]
  pub code: Option<String>, //申请号, 对应单据上的编号
  #[serde(rename = "barCode")]
  pub bar_code: Option<String>, //条码号
  #[serde(rename = "itemsSn")]
  pub items_sn: Option<String>, //大项目编码
  #[serde(rename = "name")]
  pub name: Option<String>, //大项目名称

  #[serde(rename = "projectSn")]
  pub project_sn: Option<String>, //项目编码
  #[serde(rename = "projectName")]
  pub project_name: Option<String>, //项目名称
  #[serde(rename = "sampleSn")]
  pub sample_sn: Option<String>, //标本编号
  #[serde(rename = "result")]
  pub result: Option<String>, //结果
  #[serde(rename = "unit")]
  pub unit: Option<String>, //单位
  #[serde(rename = "scope")]
  pub scope: Option<String>, //参考范围
  #[serde(rename = "upper")]
  pub upper: Option<String>, //偏高
  #[serde(rename = "lower")]
  pub lower: Option<String>, //偏低
  #[serde(rename = "urgency")]
  pub urgency: Option<String>, //危急值（1/2/3，从低到高的危急级别）
  #[serde(rename = "status")]
  pub status: Option<String>, //状态（上/下）
  #[serde(rename = "sample")]
  pub sample: Option<String>, //样本类型
  #[serde(rename = "orderNumber")]
  pub order_number: Option<i32>, //排序
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuProfile {
  #[serde(rename = "age")]
  pub age: Option<i32>, //患者年龄
  #[serde(rename = "classify")]
  pub classify: Option<i32>, //业务类型{1:门诊，2:住院，3:体检}
  #[serde(rename = "gender")]
  pub gender: Option<i32>, //患者性别 {1:男 2: 女 9：未知}
  #[serde(rename = "patientName")]
  pub patient_name: Option<String>, //患者姓名
  #[serde(rename = "identityNumber")]
  pub identity_number: Option<String>, //身份证号
  #[serde(rename = "addr")]
  pub addr: Option<String>, //地址
  #[serde(rename = "birthday")]
  pub birthday: Option<i64>, //出生日期（时间戳格式）用Unix时间戳（毫秒）表示
  #[serde(rename = "email")]
  pub email: Option<String>, //邮箱地址
  #[serde(rename = "enterpriseName")]
  pub enterprise_name: Option<String>, //企业名称
  #[serde(rename = "number")]
  pub number: Option<String>, //就诊编号（门诊/住院/体检编号）
  #[serde(rename = "phone")]
  pub phone: Option<String>, //联系电话
  #[serde(rename = "work")]
  pub work: Option<String>, //工作单位
  #[serde(rename = "personSn")]
  pub person_sn: Option<String>, //操作人编号
  #[serde(rename = "personName")]
  pub person_name: Option<String>, //操作人名称
  #[serde(rename = "billDeptSn")]
  pub bill_dept_sn: Option<String>, //开单科室编号
  #[serde(rename = "billDeptName")]
  pub bill_dept_name: Option<String>, //开单科室名称
  #[serde(rename = "state")]
  pub state: Option<i32>, //状态
  #[serde(rename = "patientSn")]
  pub patient_sn: Option<String>, //患者编号
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuFee {
  #[serde(rename = "systemSn")]
  pub system_sn: Option<String>, //系统编码（默认1005）
  #[serde(rename = "orderSn")]
  pub order_sn: Option<String>, //订单号
  #[serde(rename = "classify")]
  pub classify: Option<i32>, //业务类型{1:门诊，2:住院，3: 体检}
  #[serde(rename = "remark")]
  pub remark: Option<String>, //订单描述
  #[serde(rename = "payType")]
  pub pay_type: Option<i32>, //支付类型{1：个人支付，2：企业支付}
  #[serde(rename = "enterpriseName")]
  pub enterprise_name: Option<String>, //企业名称，当支付类型为2时不可空
  #[serde(rename = "number")]
  pub number: Option<String>, //就诊编号（门诊/住院/体检编号）
  #[serde(rename = "patientSn")]
  pub patient_sn: Option<String>, //患者索引号
  #[serde(rename = "orderPayment")]
  pub order_payment: Option<f32>, //应收金额
  #[serde(rename = "selfPayment")]
  pub self_payment: Option<f32>, //实收金额
  #[serde(rename = "itemsList")]
  pub items_list: Option<Vec<YibuFeeItem>>, //订单项目明细
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuFeeCancel {
  #[serde(rename = "orderSn")]
  pub order_sn: String,
  #[serde(rename = "projectSns")]
  pub project_sns: String,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuFeeCancelResponse {
  #[serde(rename = "orderSn")]
  pub order_sn: String,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuFeeItem {
  #[serde(rename = "sourceSn")]
  pub source_sn: Option<String>, //业务流水号
  // #[serde(rename = "orderSn")]
  // pub order_sn: Option<String>, //业务流水号
  #[serde(rename = "projectSn")]
  pub project_sn: Option<String>, //项目编号
  #[serde(rename = "projectName")]
  pub project_name: Option<String>, //项目名称
  #[serde(rename = "batchNumber")]
  pub batch_number: Option<String>, //批号
  #[serde(rename = "specifications")]
  pub specifications: Option<String>, //规格
  #[serde(rename = "num")]
  pub num: Option<i32>, //数量
  #[serde(rename = "saleUnit")]
  pub sale_unit: Option<String>, //单位
  #[serde(rename = "price")]
  pub price: Option<f32>, //单价
  #[serde(rename = "payment")]
  pub payment: Option<f32>, //金额
  #[serde(rename = "bookkeepingTime")]
  pub bookkeeping_time: Option<i64>, //记账时间
  #[serde(rename = "billPersonSn")]
  pub bill_person_sn: Option<String>, //开单人编号
  #[serde(rename = "billPersonName")]
  pub bill_person_name: Option<String>, //开单人姓名
  #[serde(rename = "billDeptSn")]
  pub bill_dept_sn: Option<String>, //开单科室编号
  #[serde(rename = "billDeptName")]
  pub bill_dept_name: Option<String>, //开单科室
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuCheckApplication {
  pub id: Option<i64>,
  pub sn: Option<String>, //由本服务生成，完成后会返回值，后续修改时需要传入此值
  #[serde(rename = "orderSn")]
  pub order_sn: Option<String>, //记录调用申请收费后返回的订单编号
  #[serde(rename = "systemSn")]
  pub system_sn: Option<String>, //系统编号，对应接入系统的编号
  #[serde(rename = "sourceSn")]
  pub source_sn: Option<String>, //业务流水号，业务系统中对申请单的编号
  #[serde(rename = "code")]
  pub code: Option<String>, //申请号，对应单据上的编号
  #[serde(rename = "barCode")]
  pub bar_code: Option<String>, //条码号，如果需要可以传入
  #[serde(rename = "classify")]
  pub classify: Option<i32>, //{1: 门诊, 2: 住院, 3: 体检}根据应用系统传入值，用于区分code对应哪个系统
  #[serde(rename = "number")]
  pub number: Option<String>, //就诊编号（门诊/住院/体检编号）
  #[serde(rename = "patientSn")]
  pub patient_sn: Option<String>, //患者索引号，如果没有患者索引服务为空
  #[serde(rename = "patientNumber")]
  pub patient_number: Option<String>, //患者索引号，如果没有患者索引服务为空
  #[serde(rename = "patientName")]
  pub patient_name: Option<String>, //患者姓名
  #[serde(rename = "idcard")]
  pub idcard: Option<String>, //患者身份证号
  #[serde(rename = "gender")]
  pub gender: Option<i32>, //患者性别，传代码{1: 男, 2: 女, 9: 未知}
  #[serde(rename = "birthday")]
  pub birthday: Option<i64>, //患者出生日期，用Unix时间戳（毫秒）表示
  #[serde(rename = "age")]
  pub age: Option<String>, //年龄,如“23岁”，“9月”，“7天”
  #[serde(rename = "phone")]
  pub phone: Option<String>, //患者联系电话
  #[serde(rename = "address")]
  pub address: Option<String>, //患者地址
  #[serde(rename = "urgent")]
  pub urgent: Option<i32>, //紧急标志，传代码{1: 紧急, 2: 普通}
  #[serde(rename = "deptSn")]
  pub dept_sn: Option<String>, //患者就诊/住院科室编号
  #[serde(rename = "deptName")]
  pub dept_name: Option<String>, //患者就诊/住院科室名称
  #[serde(rename = "areaSn")]
  pub area_sn: Option<String>, //患者住院病区编号
  #[serde(rename = "areaName")]
  pub area_name: Option<String>, //患者住院病区名称
  #[serde(rename = "bed")]
  pub bed: Option<String>, //床位
  #[serde(rename = "icd")]
  pub icd: Option<String>, //诊断编码
  #[serde(rename = "diagnosis")]
  pub diagnosis: Option<String>, //诊断名称
  #[serde(rename = "type")]
  pub check_type: Option<String>, //检查类型，如CT,DR
  #[serde(rename = "itemsSn")]
  pub items_sn: Option<String>, //检查项目编号，如果多个项目，可以通过items字段中传列表，单个项目不需要传列表
  #[serde(rename = "name")]
  pub name: Option<String>, //检查名称
  #[serde(rename = "partMethod")]
  pub part_method: Option<String>, //检查部位及方法
  #[serde(rename = "remark")]
  pub remark: Option<String>, //检查要求及其他说明
  #[serde(rename = "orderDeptSn")]
  pub order_dept_sn: Option<String>, //开单科室
  #[serde(rename = "orderDeptName")]
  pub order_dept_name: Option<String>, //开单科室
  #[serde(rename = "orderPersonSn")]
  pub order_person_sn: Option<String>, //开单人员编号（即医生编号）
  #[serde(rename = "orderPersonName")]
  pub order_person_name: Option<String>, //开单人员编号（即医生）
  #[serde(rename = "executeDeptSn")]
  pub execute_dept_sn: Option<String>, //执行科室编号
  #[serde(rename = "executeDeptName")]
  pub execute_dept_name: Option<String>, //执行科室
  #[serde(rename = "orderTime")]
  pub order_time: Option<i64>, //开单时间，Unix时间戳（毫秒）
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuCheckCancelApplication {
  #[serde(rename = "sn")]
  pub sn: String,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuLisApplication {
  pub id: Option<i64>,
  #[serde(rename = "applySn")]
  pub apply_sn: Option<String>, //编号，系统生成
  #[serde(rename = "orderSn")]
  pub order_sn: Option<String>, //记录申请收费后返回的订单编号
  #[serde(rename = "systemSn")]
  pub system_sn: Option<String>, //系统编号，对应系统接入编号
  #[serde(rename = "sourceSn")]
  pub source_sn: Option<String>, //业务流水号，业务系统中对申请单的编号
  #[serde(rename = "code")]
  pub code: Option<String>, //申请号, 对应单据上的编号
  #[serde(rename = "barCode")]
  pub bar_code: Option<String>, //条码号
  #[serde(rename = "classify")]
  pub classify: Option<i32>, //业务类型{1:门诊，2:住院，3:体检，4:急诊}
  #[serde(rename = "number")]
  pub number: Option<String>, //就诊编号（门诊/住院/体检编号）
  #[serde(rename = "patientSn")]
  pub patient_sn: Option<String>, //患者索引
  #[serde(rename = "patientNumber")]
  pub patient_number: Option<String>, //患者索引号，如果没有患者索引服务为空
  #[serde(rename = "company")]
  pub company: Option<String>, //单位名称，体检单位
  #[serde(rename = "patientName")]
  pub patient_name: Option<String>, //患者姓名
  #[serde(rename = "idcard")]
  pub idcard: Option<String>, //患者身份证号
  #[serde(rename = "gender")]
  pub gender: Option<i32>, //性别{1:男,2:女,9:未知}
  #[serde(rename = "birthday")]
  pub birthday: Option<i64>, //出生日期, 用Unix时间戳表示
  #[serde(rename = "age")]
  pub age: Option<String>, //年龄, 如“23岁”，“9月”，“7天”
  #[serde(rename = "phone")]
  pub phone: Option<String>, //电话
  #[serde(rename = "address")]
  pub address: Option<String>, //地址
  #[serde(rename = "urgent")]
  pub urgent: Option<i32>, //加急标志{1：紧急，2：普通}
  #[serde(rename = "deptSn")]
  pub dept_sn: Option<String>, //科室编号，表示患者就诊/住院科室
  #[serde(rename = "deptName")]
  pub dept_name: Option<String>, //科室名称，表示患者就诊/住院科室名称
  #[serde(rename = "areaSn")]
  pub area_sn: Option<String>, //病区编号，表示患者住院病区
  #[serde(rename = "areaName")]
  pub area_name: Option<String>, //病区名称，表示患者住院病区名称
  #[serde(rename = "bed")]
  pub bed: Option<String>, //床位号
  #[serde(rename = "icd")]
  pub icd: Option<String>, //诊断编号
  #[serde(rename = "diagnosis")]
  pub diagnosis: Option<String>, //诊断名称
  #[serde(rename = "orderDeptSn")]
  pub order_dept_sn: Option<String>, //开单科室编号
  #[serde(rename = "orderDeptName")]
  pub order_dept_name: Option<String>, //开单科室名称
  #[serde(rename = "orderDoctorSn")]
  pub order_doctor_sn: Option<String>, //开单医生编号
  #[serde(rename = "orderDoctorName")]
  pub order_doctor_name: Option<String>, //开单医生姓名
  #[serde(rename = "typeSn")]
  pub type_sn: Option<String>, //样本类型编号
  #[serde(rename = "typeName")]
  pub type_name: Option<String>, //样本类型名称
  #[serde(rename = "itemsSn")]
  pub items_sn: Option<String>, //项目代码
  #[serde(rename = "name")]
  pub name: Option<String>, //项目名称
  #[serde(rename = "num")]
  pub num: Option<i32>, //数量
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct YibuLisCancelApplication {
  #[serde(rename = "applySn")]
  pub apply_sn: String,
}
