use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use utility::{
  serde::{deserialize_string_to_i64, serialize_i64_to_string},
  timeutil::{self, current_timestamp},
};

use crate::prelude::PAYMETHOD;

use super::medinfoview::MedexaminfosView;

//同步信息视图，saas平台使用
#[derive(Debug, Default, Serialize, Deserialize, Clone)]
pub struct MedexamSyncView {
  //体检信息
  pub id: i64,
  #[serde(deserialize_with = "deserialize_string_to_i64")]
  pub p_cmpid: i64,
  #[serde(deserialize_with = "deserialize_string_to_i64")]
  pub p_wkid: i64,
  #[serde(deserialize_with = "deserialize_string_to_i64")]
  pub p_medid: i64,

  pub p_testid: String,
  pub p_testtype: i32,
  pub p_meddate: String,
  pub p_jtid: i64,
  pub p_isrecheck: i32,
  pub p_wkyears: Decimal,
  pub p_wkmonthes: Decimal,
  pub p_hdyears: Decimal,
  pub p_hdmonthes: Decimal,
  pub p_status: i32, //体检状态
  pub p_orgid: String,
  pub p_stdate: String,  //上岗时间
  pub p_enddate: String, //离港时间
  pub p_empid: String,   //工号
  pub p_wtcode: String,  //工种代码

  //毒害因素
  pub hdids: Vec<i64>,
  pub hdnames: String,
  //体检机构名称
  pub p_orgname: String,

  //人员信息
  pub wk_name: String,
  pub wk_sex: i32,
  pub wk_marriage: i32,
  pub wk_birthdate: String,
  pub wk_workdate: String,
  pub wk_idcard: String,
  pub wk_phone: String,
  pub wk_address: String,
  pub wk_postcode: String,

  //检查结论
  pub p_result: String,
  pub tj_hazard: String,
  pub tj_diseasename: String,
  pub p_ocuabnormal: String,
  pub p_othabnormal: String,
  pub p_ocuconclusion: String,
  pub p_othconclusion: String,
  pub p_ocusuggestion: String,
  pub p_othsuggestion: String,
  pub p_ocuopinion: String,
  pub p_othopinion: String,
  pub p_doctor: String,
  pub p_checkdate: String,

  //额外的名称字段
  pub str_jtname: String, //工种
  pub str_sex: String,
  pub str_marriage: String,
  pub str_status: String,
  pub str_testtype: String,

  pub create_date: i64,
  pub creator: String,
}

pub fn convert_to_medexam_info_view(info: &MedexamSyncView) -> MedexaminfosView {
  let medview = MedexaminfosView {
    id: 0,
    p_cmpid: info.p_cmpid,
    p_wkid: info.p_wkid,
    p_medid: info.p_medid,
    testid: "".to_string(),
    age: timeutil::get_age_from_birthdate(&info.wk_birthdate, "%Y-%m-%d"),
    testtypeid: info.p_testtype,
    testtypename: info.str_testtype.to_owned(),
    worktypeid: info.str_jtname.to_owned(),
    worktypename: info.str_jtname.to_owned(),
    worktypecode: info.p_wtcode.to_owned(),
    workage: format!("{}年", (info.p_wkyears + (info.p_wkmonthes / Decimal::from(12))).round_dp(2)),
    poisionfactor: info.hdnames.to_owned(),
    poisionage: format!("{}年", (info.p_hdyears + (info.p_hdmonthes / Decimal::from(12))).round_dp(2)),
    recorddate: current_timestamp(),
    printflagid: 0,
    printflagname: "".to_string(),
    printtimes: 0,
    testdate: timeutil::convert_date_to_timestamp(&info.p_meddate, "%Y-%m-%d"),
    reportnum: "".to_string(),
    preportnum: "".to_string(),
    statusid: info.p_status,
    statusname: info.str_status.to_owned(),
    peid: 0,
    empid: info.p_empid.to_owned(),
    paymethod: PAYMETHOD::Company as i32,
    packagename: "".to_string(),
    additional: "".to_string(),
    pname: info.wk_name.to_owned(),
    pid: info.p_wkid.to_string(),
    sexid: info.wk_sex,
    sexname: "".to_string(),
    pidcard: info.wk_idcard.to_owned(),
    pmarriageid: info.wk_marriage,
    nation: "".to_string(),
    pmarriagename: info.str_marriage.to_owned(),
    pcareer: "".to_string(),
    paddress: info.wk_address.to_owned(),
    pmobile: info.wk_phone.to_owned(),
    pbirthday: info.wk_birthdate.to_owned(),
    photo: "".to_string(),
    corpnum: info.p_cmpid,
    corpname: "".to_string(),
    checkresultesid: "".to_string(),
    checkresultsstr: info.p_result.to_string(),
    checkresultesname: "".to_string(),
    checkstatusid: 1,
    checkstatusname: "".to_string(),
    checkalldate: 0,
    forbidden: "".to_string(),
    targetdis: "".to_string(),
    staffid: 0,
    staffname: "".to_string(),
    inspecteddepart: "".to_string(),
    notexamineddepart: "".to_string(),
    rechecktimes: 0,
    // oldtestid: "".to_string(),
    isrecheck: info.p_isrecheck,
    upload: 0,
    syncstatus: 0,
    idx: 0,
    testcatid: 2,
    testcatname: "企业".to_string(),
  };

  medview
}
