use bytes::BytesMut;
// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
use futures::{SinkExt, StreamExt};
use hl7_mllp_codec::MllpCodec;
use moka::future::Cache;
use nodipservice::medexam::patientsvc::PatientSvc;
use std::{error::Error, net::SocketAddr, sync::Arc, time::Duration};
use tokio::{
  net::{TcpListener, TcpStream},
  sync::RwLock,
};
use tokio_util::codec::Framed;
use utility::uidservice::UidgenService;
use log::*;

use crate::{
  common::constant::DatabaseType,
  config::settings::Settings,
  rusthl7::Message,
  service::{
    externalservice::{DftMessage, ExternalService, Row},
    hl7client::Hl7Client,
    internalservice::InternalService,
    messages::Message<PERSON><PERSON><PERSON>,
  },
};
use std::convert::TryFrom;

mod api;
mod common;
mod config;
mod nxmiddleware;
mod rusthl7;
mod service;
#[cfg(test)]
mod test;

#[tokio::main]
async fn main() {
  let cfg = "config/nodipexternal.yaml";
  log4rs::init_file(cfg, Default::default()).unwrap();
  // loggings::log_init(log_cfg);

  let sys_config = Settings::init().expect("init configuration error");

  let server_addr = format!("{}:{}", sys_config.server.host.to_string(), sys_config.server.port);

  info!("Configuration file:{:?}", &sys_config);
  let external_server = sys_config.external.serverurl.clone();
  if external_server.is_empty() {
    error!("external server is empty......");
    return;
  }
  let (tx, mut rx) = futures::channel::mpsc::unbounded::<String>();

  let (tx_dft, mut rx_dft) = futures::channel::mpsc::unbounded::<Row>();
  let (tx_p03, mut rx_p03) = futures::channel::mpsc::unbounded::<DftMessage>();

  let http_url = format!("{}:{}", sys_config.application.apphost, sys_config.application.appport);
  let _httpaddr: SocketAddr = http_url.as_str().parse().expect("parse url error");
  let dbconn = Arc::new(DbConnection::new(&sys_config.database.uri.as_str(), DatabaseType::MySql as i32).await);
  let dbconn_clone = dbconn.clone();
  let dbconn_clone2 = dbconn.clone();
  let uidgen = UidgenService::new(1, 1);

  // let message_cache: DashMap<String, DftMessage> = DashMap::new();
  let message_cache: Cache<String, DftMessage> = Cache::builder().time_to_idle(Duration::from_secs(120)).build();

  let app = api::routers::create_route(dbconn, Arc::new(sys_config), Arc::new(RwLock::new(uidgen)), Arc::new(tx), Arc::new(tx_p03));

  let version = env!("CARGO_PKG_VERSION");
  // let mut mx_interval = tokio::time::interval(std::time::Duration::from_secs(5 as u64));

  tokio::spawn(async move {
    let mut hl7client = Hl7Client::init(&external_server).await;
    loop {
      tokio::select! {
         msg = rx.next() => {
          if let Some(hlmsg) = msg {
            info!("message to be send:\r{:?}",&hlmsg);
            if let Ok(message) = Message::try_from(hlmsg.as_ref()){
              let msg_type = message.query("MSH.F8");
              if msg_type == "DFT^P03"{//收费项目，等返回后再发
                error!("收费报文，暂时不处理......");
              } else {
                for n in 1..10 {
                  let ret = hl7client.transport.send(BytesMut::from(hlmsg.as_str())).await;
                  if ret.as_ref().is_err(){
                    error!("第{}次发送失败:{}",n,ret.as_ref().unwrap_err().to_string());
                    hl7client.retry_connect().await;
                    continue;
                  }else{
                    info!("message send result:{:?}", &ret);
                    break;
                  }
                }
              }
            }
          }
        }
        p03 = rx_p03.next()=>{
          if let Some(p03msg) = p03{
            info!("收到dft p03 message，写入缓存，等建档成功后使用:{:?}",&p03msg);
             message_cache.insert(p03msg.adtmsgid.to_owned(), p03msg.to_owned()).await;
            //  message_cache.insert("AUZMMTG6-LF37-388K-MRYR-DUC56SJZOQ3S".to_string(), p03msg.to_owned()).await;
          }
        }
        dft = rx_dft.next() => {
          if let Some(dftrow) = dft {
            info!("建档成功,开始发送收费信息:{:?}",&dftrow);
            //根据msgid从缓存中读取数据
            // if let Some(dftp03) = message_cache.entry(dftrow.msg.clone()){
            if let Some(dftp03) = message_cache.get(&dftrow.msg).await {
              //更新数据库中的patid
              let ret = PatientSvc::update_patient_patid(dftrow.patid.to_string().as_str(), dftp03.pid.as_str(),&dbconn_clone2).await;
              if ret.as_ref().is_err(){
                error!("Can't update patid with error:{}",ret.as_ref().unwrap_err().to_string());
              }
              let p03msg = dftp03.dftmsg.to_string();
              let p03msg_new = p03msg.replace(format!("^^^&HISPATID").as_str(),format!("{}^^^&HISPATID",dftrow.patid).as_str());
              info!("dft^p03 message is:\r{}",&p03msg_new);
              // {patid}^^^&HISPATID
              for n in 1..10 {
              let ret = hl7client.transport.send(BytesMut::from(p03msg_new.as_str())).await;
                if ret.as_ref().is_err(){
                  error!("第{}次发送失败:{}",n,ret.as_ref().unwrap_err().to_string());
                  hl7client.retry_connect().await;
                  continue;
                }else{
                  info!("message send result:{:?}", &ret);
                  break;
                }
              }
            }else{
              error!("不能从缓存中获取id为:{}的dft^p03消息",&dftrow.msg);
            }
          }
        }
      msg = hl7client.transport.next() => {
          if let Some(result) = msg {
            match result {
              Ok(msg) => {
                let str_msg = String::from_utf8_lossy(&msg);
                if let Ok(message) = Message::try_from(str_msg.as_ref()) {
                  info!("receive message from platform:\r{}", message);
                  let msg_type = message.query("MSH.F8");
                  info!("message type from msh.f8 is:{}", &msg_type);
                  match msg_type {
                    // "ORU^R01" => {
                    //   //报告发布
                    //   if let Err(e) = InternalService::handl_lab_result(&message, &dbconn_clone).await {
                    //     error!("handle result error:{}", e.to_string());
                    //   }
                    // }
                    "ACK^A01" => {
                      //建档成功后发送收费信息
                      if let Ok(dftmsg) = ExternalService::extract_ack_a01(&message) {
                        let ret = tx_dft.unbounded_send(dftmsg);
                        if ret.as_ref().is_err() {
                          error!("发送dftp03到管道失败:{}", ret.as_ref().unwrap_err().to_string());
                        }
                      }
                    }
                    _ => {
                      info!("message do not need to be handled......")
                    }
                  }
                }
              }
              Err(e) => {
                error!("Error from MLLP transport: {:?}", e);
                hl7client.retry_connect().await;
                // return Err(e.into());
              }
            }
          // } else {
          //   // hl7client.retry_connect().await;
          //   info!("Connection closed by the server, should try to reconnect");
          }
        }
      }
    }
  });

  // //version 3
  // let tcp_stream = TcpStream::connect(&external_server).await.expect("connect to server error");
  // info!("connected to hl7 server:{}", &external_server);
  // let transport = Framed::new(tcp_stream, MllpCodec::new());
  // let (mut sink, mut stream) = transport.split();
  // tokio::spawn(async move {
  //   loop {
  //     tokio::select! {
  //       msg = rx.next() =>{
  //         if let Some(hlmsg) = msg{
  //           info!("message to be send:\r{:?}",&hlmsg);
  //           if let Ok(message) = Message::try_from(hlmsg.as_ref()){
  //             let msg_type = message.query("MSH.F8");
  //             if msg_type == "DFT^P03"{//收费项目，等返回后再发
  //               error!("收费报文，暂时不处理......");
  //             } else {
  //               let ret = sink.send(BytesMut::from(hlmsg.as_str())).await;
  //               if ret.as_ref().is_err(){
  //                 error!("发送失败:{}",ret.as_ref().unwrap_err().to_string());
  //               }else{
  //                 info!("message send result:{:?}", &ret);
  //               }
  //             }
  //           }
  //         }
  //       }
  //       p03 = rx_p03.next()=>{
  //         if let Some(p03msg) = p03{
  //           info!("收到dft p03 message，写入缓存，等建档成功后使用:{:?}",&p03msg);
  //            message_cache.insert(p03msg.adtmsgid.to_owned(), p03msg.to_owned()).await;
  //           //  message_cache.insert("AUZMMTG6-LF37-388K-MRYR-DUC56SJZOQ3S".to_string(), p03msg.to_owned()).await;
  //         }
  //       }
  //       dft = rx_dft.next() => {
  //         if let Some(dftrow) = dft {
  //           info!("建档成功,开始发送收费信息:{:?}",&dftrow);
  //           //根据msgid从缓存中读取数据
  //           // if let Some(dftp03) = message_cache.entry(dftrow.msg.clone()){
  //           if let Some(dftp03) = message_cache.get(&dftrow.msg) {
  //             //更新数据库中的patid
  //             let ret = PatientSvc::update_patient_patid(dftrow.patid.to_string().as_str(), dftp03.pid.as_str(),&dbconn_clone2).await;
  //             if ret.as_ref().is_err(){
  //               error!("Can't update patid with error:{}",ret.as_ref().unwrap_err().to_string());
  //             }
  //             let p03msg = dftp03.dftmsg.to_string();
  //             let p03msg_new = p03msg.replace(format!("^^^&HISPATID").as_str(),format!("{}^^^&HISPATID",dftrow.patid).as_str());
  //             info!("dft^p03 message is:\r{}",&p03msg_new);
  //             // {patid}^^^&HISPATID
  //             let ret = sink.send(BytesMut::from(p03msg_new.as_str())).await;
  //               if ret.as_ref().is_err(){
  //                 error!("发送失败:{}",ret.as_ref().unwrap_err().to_string());
  //               }else{
  //                 info!("message send result:{:?}", &ret);
  //               }
  //               // let _ = message_cache.remove(&dftrow.msg);
  //           }else{
  //             error!("不能从缓存中获取id为:{}的dft^p03消息",&dftrow.msg);
  //           }
  //         }
  //       }
  //     }
  //   }
  // });
  // tokio::spawn(async move {
  //   loop {
  //     tokio::select! {
  //       msg = stream.next() =>{
  //         if let Some(result) = msg{
  //           match result {
  //             Ok(msg) => {
  //               // Large console output remmed out to test throughput.  Feel free to unrem for better  diagnostic output.
  //               let str_msg = String::from_utf8_lossy(&msg);
  //               if let Ok(message) = Message::try_from(str_msg.as_ref()) {
  //                 info!("receive message from platform:\r{}", message);
  //                 let msg_type = message.query("MSH.F8");
  //                 info!("message type from msh.f8 is:{}", &msg_type);
  //                 match msg_type {
  //                   // "ORU^R01" => {
  //                   //   //报告发布
  //                   //   if let Err(e) = InternalService::handl_lab_result(&message, &dbconn_clone).await {
  //                   //     error!("handle result error:{}", e.to_string());
  //                   //   }
  //                   // }
  //                   "ACK^A01" => {
  //                     //建档成功后发送收费信息
  //                     if let Ok(dftmsg) = ExternalService::extract_ack_a01(&message) {
  //                       let ret = tx_dft.unbounded_send(dftmsg);
  //                       if ret.as_ref().is_err() {
  //                         error!("发送dftp03到管道失败:{}", ret.as_ref().unwrap_err().to_string());
  //                       }
  //                     }
  //                   }
  //                   _ => {
  //                     info!("message do not need to be handled......")
  //                   }
  //                 }
  //               }
  //               // let ack_msg = MessageHelper::generate_ack_message();
  //               // let _ = tx_clone.unbounded_send(ack_msg);
  //             }
  //             Err(e) => {
  //               error!("Error from MLLP transport: {:?}", e);
  //               // return Err(e.into());
  //             }
  //           }
  //         } else {
  //           info!("Connection closed by the server, should try to reconnect");
  //         }
  //       }
  //     }
  //   }
  // });
  // //end of version 3

  info!("hl7 server started on {}, version:{}, {}", &server_addr, &version, env!("CARGO_PKG_DESCRIPTION"));
  let listener = TcpListener::bind(&server_addr).await.expect("lister bind error");
  //create server
  tokio::spawn(async move {
    loop {
      if let Ok((stream, addr)) = listener.accept().await {
        // tokio::spawn(async move {
        info!("Connection opened from {addr:?}");
        if let Err(e) = process(stream, &dbconn_clone).await {
          error!("Failed to process connection; error = {}", e);
        }
        // });
      }
    }
  });

  info!("web server started on {}, version:{}, {}", &http_url, &version, env!("CARGO_PKG_DESCRIPTION"));
  // axum::Server::bind(&httpaddr)
  //   // .serve(app.into_make_service())
  //   .serve(app.into_make_service_with_connect_info::<SocketAddr>())
  //   .with_graceful_shutdown(shutdown_signal())
  //   .await
  //   .expect("start server error");
  let listener = tokio::net::TcpListener::bind(&http_url).await.expect("bind error");

  axum::serve(listener, app.into_make_service_with_connect_info::<SocketAddr>()).await.expect("start server error");
}

// async fn shutdown_signal() {
//   let ctrl_c = async {
//     signal::ctrl_c().await.expect("failed to install Ctrl+C handler");
//   };

//   #[cfg(unix)]
//   let terminate = async {
//     signal::unix::signal(signal::unix::SignalKind::terminate())
//       .expect("failed to install signal handler")
//       .recv()
//       .await;
//   };

//   #[cfg(not(unix))]
//   let terminate = std::future::pending::<()>();

//   tokio::select! {
//       _ = ctrl_c => {},
//       _ = terminate => {},
//   }

//   info!("signal received, starting graceful shutdown");
// }

async fn process(stream: TcpStream, db: &Arc<DbConnection>) -> Result<(), Box<dyn Error>> {
  let mut transport = Framed::new(stream, MllpCodec::new());

  while let Some(result) = transport.next().await {
    match result {
      Ok(msg) => {
        let str_msg = String::from_utf8_lossy(&msg);
        if let Ok(message) = Message::try_from(str_msg.as_ref()) {
          info!("receive message from platform:\r{}", message);
          let msg_type = message.query("MSH.F8");
          info!("message type from msh.f8 is:{}", &msg_type);
          let msg_id = message.query("MSH.F9");
          info!("message id:{msg_id}");
          match msg_type {
            "ORU^R01" => {
              //报告发布
              if let Ok(()) = InternalService::handl_lab_result(&message, &db).await {
                let ack = MessageHelper::generate_oru_message(msg_id);
                info!("ack message for ORU^R01 is:\r{ack}");
                let ack_msg = BytesMut::from(ack.as_str()); //<ACK> ascii char, simple ack
                let ret = transport.send(ack_msg).await; //because this is through the codec it gets wrapped in MLLP header/footer for us
                info!("ack message send result:{:?}", &ret);
              } else {
                error!("handle result error");
              }
            }
            "ORM^O01" => {
              let ack = MessageHelper::generate_orr_message(msg_id);
              info!("ack message for ORM^O01 is:\r{ack}");
              let ack_msg = BytesMut::from(ack.as_str()); //<ACK> ascii char, simple ack
              let ret = transport.send(ack_msg).await; //because this is through the codec it gets wrapped in MLLP header/footer for us
              info!("ack message send result:{:?}", &ret);
            }
            _ => {
              info!("message do not need to be handled......")
            }
          }
        }
      }
      Err(e) => {
        println!("Error from MLLP transport: {:?}", e);
        return Err(e.into());
      }
    }
  }
  info!("Connection closed...");
  Ok(())
}
