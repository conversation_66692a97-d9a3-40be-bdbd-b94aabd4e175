//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_audiogramrevise")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_freq: i32,
  pub tj_sex: i32,
  pub tj_startage: i32,
  pub tj_endage: i32,
  pub tj_revise: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
