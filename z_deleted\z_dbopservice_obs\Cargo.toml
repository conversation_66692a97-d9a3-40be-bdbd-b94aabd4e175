[package]
name = "dbopservice"
version = "0.2.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { path = "../utility" }

#other deps
serde = { version = "1", features = ["derive"] }
tokio = { version = "1", features = ["full"] }
log = "0.4"
fast_log = "1.5"

anyhow = "1"

chrono = "0.4"

rbs = { version = "4.5.2" }
rbatis = { version = "4.5.6" }
rbdc-mysql = { version = "4.5.1" }

#非windows平台，用rustls
# [target.'cfg(not(target_os = "windows"))'.dependencies]
# # [target.'cfg(linux)'.dependencies]
# rbatis = { version = "4.5.1" }
# rbdc-mysql = { version = "4.5.0" }
# rbdc-mssql = { version = "4.4" }
# rbdc-oracle={version="0.3.0"}

#Windows平台，用native-tls
# [target.'cfg(windows)'.dependencies]
# [target.'cfg(target_os = "windows")'.dependencies]
# rbatis = { version = "4.5.1", default-features = false, features = [
#     "tls-native-tls",
#     "default_mode",
# ] }
# rbdc-mysql = { version = "4.5.0", default-features = false, features = [
#     "tls-native-tls",
# ] }
# rbdc-mssql = { version = "4.4", default-features = false, features = [
#     "tls-native-tls",
# ] }
# rbdc-oracle={version="0.3.0"}

# x86_64-unknown-linux-gnu
# [target.x86_64-unknown-linux-gnu.dependencies]
# rbatis = { version = "4.3.8"}
# rbdc-mysql={version="4.3.3"}
# rbdc-mssql={version="4.3.0"}

# x86_64-pc-windows-msvc
# [target.x86_64-pc-windows-msvc.dependencies]
# rbatis = { version = "4.3", default-features = false, features = ["tls-native-tls","default_mode"] }
# rbdc-mssql={version="4.3", default-features = false, features = ["tls-native-tls"]}
# rbdc-mysql={version="4.3", default-features = false, features = ["tls-native-tls"]}

# [target.'cfg(windows)'.build-dependencies]
# rbatis = { version = "4.3", default-features = false, features = ["tls-native-tls","default_mode"] }
# rbdc-mssql={version="4.3", default-features = false, features = ["tls-native-tls"]}
# rbdc-mysql={version="4.3", default-features = false, features = ["tls-native-tls"]}

# [target.'cfg(linux)'.build-dependencies]
# rbatis = { version = "4.3.8"}
# rbdc-mysql={version="4.3.3"}
# rbdc-mssql={version="4.3.0"}
# rbdc-oracle={version="0.3.0"}
