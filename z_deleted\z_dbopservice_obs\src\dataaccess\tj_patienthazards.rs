use anyhow::{anyhow, Result};
use rbatis::{crud};
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;

#[derive(C<PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjPatienthazards {
  pub id: i64,
  pub tj_testid: String,
  pub tj_hid: i64,
  pub tj_poisionage: String,
  pub tj_typeid: i32,
  pub tj_diseases: String,
  pub tj_recheckitems: String,
  pub tj_olcorpname: String, //接触相应职业病危害因素的用人单位名称
}
crud!(TjPatienthazards {}, "tj_patienthazards");
rbatis::impl_select!(TjPatienthazards{query(testid:&str) => "`where tj_testid = #{testid} `"});
rbatis::impl_select!(TjPatienthazards{query_many(testids:&[&str]) => "`where tj_testid in ${testids.sql()} `"});
rbatis::impl_delete!(TjPatienthazards{delete(testids:&[&str]) => "`where tj_testid in ${testids.sql()} `"});

impl TjPatienthazards {
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<TjPatienthazards>) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<TjPatienthazards>, Vec<TjPatienthazards>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = TjPatienthazards::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = TjPatienthazards::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }
}
