use crate::{common::constant::HttpCode, config::settings::Settings, nxmiddleware::printmiddleware::print_request_response, Client};
// use anyhow::{anyhow, Result};
use axum::{
  body::Body,
  extract::{DefaultBodyLimit, Extension, OriginalUri, Request, State},
  http::uri::Uri,
  http::Method,
  middleware,
  response::{IntoResponse, Response},
  routing::{delete, get, post, put},
  Router,
};
// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
use hyper::StatusCode;
use nodipservice::external::{recvexternallis::RecvExternalLis, recvexternalpacs::RecvExternalPacs};
use std::sync::Arc;
use tokio::sync::RwLock;
use tower_http::cors::{Any, CorsLayer};
use utility::uidservice::UidgenService;
// use odipservice::prelude::DictService;
use tower::ServiceBuilder;
use tracing::*;

use super::{
  httpresponse::ResponseBody,
  v2::{
    accountcontroller, akacontroller, audiogramcontroller, cdccontroller, checkallcontroller, checkitemcontroller, corpinfocontroller, datasumcontroller, documentcontroller,
    externalcontroller, filectl, hazardcontroller, hcardcontroller, instrumentcontroller, itemcontroller, medexamcontroller, packagecontroller, patientcontroller, reportcontroller,
    statcontroller, summarycontroller, synccontroller, syscontroller,
  },
};

pub fn create_route(
  dbconn: Arc<DbConnection>,
  setting: Arc<Settings>,
  /*syscache: Arc<SysCache>,*/ uid: Arc<RwLock<UidgenService>>,
  tx: Arc<tokio::sync::mpsc::UnboundedSender<i32>>,
  externallis: Arc<RecvExternalLis>,
  externalpacs: Arc<RecvExternalPacs>,
  client: Client,
) -> Router {
  // let v1_route = create_v1_route(client); //.with_state(client);
  let proxy_route = create_proxy_route(client);
  let v2_route = create_v2_route();
  let downfile_route = Router::new().route("/{*path}", get(filectl::download_file));
  // let dbconn = Arc::new(db);

  let cors = CorsLayer::new()
    // allow `GET` and `POST` when accessing the resource
    .allow_methods([Method::GET, Method::POST, Method::PUT, Method::OPTIONS, Method::DELETE])
    // allow requests from any origin
    .allow_origin(Any)
    .allow_headers(Any);

  let app = Router::new()
    .merge(downfile_route)
    .merge(proxy_route)
    .merge(v2_route)
    // .nest("/", downfile_route.merge(proxy_route).merge(v2_route))
    .layer(middleware::from_fn(print_request_response))
    .layer(ServiceBuilder::new().layer(Extension(dbconn)))
    // .layer(ServiceBuilder::new().layer(Extension(syscache)))
    .layer(DefaultBodyLimit::max(4096 * 10 * 1000))
    .layer(ServiceBuilder::new().layer(Extension(setting)))
    .layer(ServiceBuilder::new().layer(Extension(uid)))
    .layer(ServiceBuilder::new().layer(Extension(tx)))
    .layer(ServiceBuilder::new().layer(Extension(externallis)))
    .layer(ServiceBuilder::new().layer(Extension(externalpacs)))
    .route_layer(cors);
  // .with_state(shared_state);
  // add a fallback service for handling routes to unknown paths
  // let app = app.fallback(handler_404.into_service());
  app
}

fn create_proxy_route(client: Client) -> Router {
  let proxyrouter = Router::new()
    .route("/apid/{*key}", get(dams_handler).post(dams_handler).put(dams_handler).delete(dams_handler))
    .route("/apic/{*key}", get(cdc_handler).post(cdc_handler).put(cdc_handler).delete(cdc_handler))
    // .route("/api/report/{*key}", get(report_handler).post(report_handler).put(report_handler).delete(report_handler))
    .route("/api/{*key}", get(proxy_handler).post(proxy_handler).put(proxy_handler).delete(proxy_handler))
    .with_state(client);
  proxyrouter
}

fn create_v2_route() -> Router {
  let account_route = Router::new()
    .route("/login", post(accountcontroller::user_login))
    .route("/password", post(accountcontroller::change_password));
  let medinfo_route = Router::new().nest(
    "/medinfo",
    Router::new()
      .route(
        "/",
        // post(medexamcontroller::insert_medexaminfo).
        post(medexamcontroller::save_medexaminfo).delete(medexamcontroller::delete_medexaminfo),
      )
      .route("/query/{testid}", get(medexamcontroller::query_medexaminfo))
      .route("/query", post(medexamcontroller::query_medexaminfos))
      .route("/query/testids", post(medexamcontroller::query_medexaminfos_by_testids))
      .route("/quick", post(medexamcontroller::quick_register))
      .route("/register", post(medexamcontroller::medexam_register))
      .route("/recheck", post(medexamcontroller::recheck_register))
      .route("/status", post(medexamcontroller::medexam_update_status))
      // .route("/update", post(medexamcontroller::update_medexaminfo))
      .route("/print", post(medexamcontroller::update_personal_report_print_status))
      .nest(
        "/hazards",
        Router::new()
          .route(
            "/",
            post(medexamcontroller::query_medinfo_hazards_by_testid).put(medexamcontroller::update_medinfo_hazards_results),
          )
          .route("/update", post(medexamcontroller::update_medinfo_hazards)),
      )
      .nest(
        "/healthyinfo",
        Router::new()
          .route("/", post(medexamcontroller::save_healthyinfos))
          .route("/query", post(medexamcontroller::query_healthyinfos)),
      ),
  );

  let summary_route = Router::new().nest(
    "/summary",
    Router::new()
      .route("/", post(summarycontroller::save_summary).delete(summarycontroller::delete_summary))
      .route("/query", post(summarycontroller::query_summaries))
      .route("/auto", post(summarycontroller::auto_summary))
      .nest(
        "/disinfos",
        Router::new()
          .route(
            "/",
            post(summarycontroller::save_diseaseinfos)
              .put(summarycontroller::update_diseaseinfos)
              .delete(summarycontroller::delete_diseaseinfos),
          )
          .route("/query", post(summarycontroller::query_diseaseinfos)),
      ),
  );

  let audiogram_route = Router::new().nest(
    "/audio",
    Router::new()
      .route("/", post(audiogramcontroller::save_audiogramdetails).delete(audiogramcontroller::delete_audiogramdetails))
      .route("/query", post(audiogramcontroller::query_audiograms))
      .route("/auto", post(audiogramcontroller::auto_summary_audiograms))
      .route("/revise", post(akacontroller::query_audiogramrevises))
      .nest(
        "/summary",
        Router::new()
          .route("/", post(audiogramcontroller::save_audiogram_summary).delete(audiogramcontroller::delete_audiogram_summary))
          .route("/query", post(audiogramcontroller::query_audiogram_summaries)),
      ),
  );
  let checkall_route = Router::new().nest(
    "/checkall",
    Router::new()
      .route("/", post(checkallcontroller::save_checkall).delete(checkallcontroller::delete_checkall))
      .route("/query", post(checkallcontroller::query_checkalls))
      .route("/auto", post(checkallcontroller::auto_checkall)),
  );

  let checkitem_route = Router::new().nest(
    "/checkitem",
    Router::new()
      .route("/", post(checkitemcontroller::save_checkitems))
      .route("/modify", post(checkitemcontroller::update_checkitems))
      .route("/query", post(checkitemcontroller::query_checkitems))
      .route("/result", post(checkitemcontroller::update_checkitem_result_flag)),
  );

  let ptinfo_route = Router::new().nest(
    "/patient",
    Router::new()
      .route("/", put(patientcontroller::update_patient).post(patientcontroller::save_patient))
      .route("/query/{idcard}", get(patientcontroller::query_patient_info))
      .route("/query", post(patientcontroller::query_patient_infos)),
  );

  let hcard_route = Router::new().nest(
    "/hcard",
    Router::new()
      // .route("/", post(checkitemcontroller::update_checkitems))
      .route("/query", post(hcardcontroller::query_hcardinfos)),
  );

  let sysinfo_route = Router::new().nest(
    "/sys",
    Router::new()
      .route("/id", post(syscontroller::get_id_by_testtype))
      // .route("/dicts", post(syscontroller::query_sys_dicts))
      .route("/infos", post(syscontroller::query_info_configs))
      .route("/identity", post(syscontroller::query_sys_identity))
      .route("/area", post(akacontroller::query_areainfos))
      .route("/monitors", post(akacontroller::query_monitorinfos))
      .route("/extcfg", post(akacontroller::query_external_configs))
      .nest(
        "/dicts",
        Router::new()
          .route("/", post(syscontroller::update_sys_dicts))
          .route("/query", post(syscontroller::query_sys_dicts_v2)),
      ),
  );

  let corpinfo_route = Router::new().nest(
    "/corpinfo",
    Router::new()
      .route("/", post(corpinfocontroller::save_corpinfo).delete(corpinfocontroller::delete_corpinfos))
      .route("/{id}", get(corpinfocontroller::get_corpinfo))
      .route("/query", post(corpinfocontroller::query_corp_infos)),
  );

  let items_route = Router::new().nest(
    "/item",
    Router::new()
      .route("/", post(itemcontroller::save_iteminfos).delete(itemcontroller::delete_iteminfos))
      .route("/query", post(itemcontroller::query_iteminfos))
      .nest(
        "/combine",
        Router::new()
          .route("/", post(itemcontroller::save_combine_iteminfos).delete(itemcontroller::delete_combine_iteminfos))
          .route("/query", post(itemcontroller::query_combine_iteminfos)),
      )
      .nest(
        "/result",
        Router::new()
          .route("/", post(itemcontroller::save_item_resultinfo).delete(itemcontroller::delete_item_resultinfos))
          .route("/query", post(itemcontroller::query_item_resultinfos)),
      )
      .nest(
        "/type",
        Router::new()
          .route("/", post(itemcontroller::save_itemtype).delete(itemcontroller::delete_itemtype))
          .route("/query", post(itemcontroller::query_itemtypes)),
      )
      .nest(
        "/range",
        Router::new()
          .route("/", post(itemcontroller::save_itemrangeinfo).delete(itemcontroller::delete_itemrangeinfos))
          .route("/query", post(itemcontroller::query_itemrangeinfos)),
      )
      .nest(
        "/samples",
        Router::new()
          // .route("/", post(itemcontroller::save_itemtype).delete(itemcontroller::delete_itemtype))
          .route("/query", post(itemcontroller::query_sampletypes)),
      )
      .nest(
        "/lisnum",
        Router::new()
          // .route("/", post(itemcontroller::save_itemtype).delete(itemcontroller::delete_itemtype))
          .route("/", post(itemcontroller::update_iteminfo_lisnum)),
      ),
  );

  let depart_route = Router::new().nest(
    "/departinfo",
    Router::new()
      .route("/", post(akacontroller::save_departments).delete(akacontroller::delete_departments))
      .route("/query", post(akacontroller::query_departments))
      .nest(
        "/item",
        Router::new()
          .route("/", post(akacontroller::save_deptitems).delete(akacontroller::delete_deptitem))
          .route("/query", post(akacontroller::query_deptitems)),
      ),
  );

  let hazard_route = Router::new().nest(
    "/hazard",
    Router::new()
      .route("/", post(hazardcontroller::save_hazardinfos).delete(hazardcontroller::delete_hazardinfos))
      .route("/query", post(hazardcontroller::query_hazardinfos))
      .route("/connect", post(hazardcontroller::connect_hazardinfos))
      .nest(
        "/types",
        Router::new()
          .route("/", post(hazardcontroller::save_hazardtypes).delete(hazardcontroller::delete_hazardtypes))
          .route("/query", post(hazardcontroller::query_hazardtypes)),
      )
      .nest(
        "/item",
        Router::new()
          .route("/", post(hazardcontroller::save_hazarditems).delete(hazardcontroller::delete_hazarditems))
          .route("/query", post(hazardcontroller::query_hazarditems)),
      )
      .nest(
        "/disease",
        Router::new()
          .route("/", post(hazardcontroller::save_hazarddiseases).delete(hazardcontroller::delete_hazarddiseases))
          .route("/query", post(hazardcontroller::query_hazarddiseases)),
      )
      .nest(
        "/law",
        Router::new()
          .route("/", post(hazardcontroller::save_hazardlaws).delete(hazardcontroller::delete_hazardlaws))
          .route("/query", post(hazardcontroller::query_hazardlaws)),
      ),
  );

  let autodiag_route = Router::new().nest(
    "/autodiag",
    Router::new()
      .route("/", post(akacontroller::save_autodiagconditions).delete(akacontroller::delete_autodiagconditions))
      .route("/query", post(akacontroller::query_autodiagconditions)),
  );

  let disease_route = Router::new().nest(
    "/disease",
    Router::new()
      .route("/", post(akacontroller::save_diseases).delete(akacontroller::delete_diseases))
      .route("/query", post(akacontroller::query_diseases))
      .nest(
        "/occu",
        Router::new()
          .route("/", post(akacontroller::save_occuconditions))
          .route("/query", post(akacontroller::query_occuconditions)),
      ),
  );

  let staff_route = Router::new().nest(
    "/staff",
    Router::new()
      .route("/", post(akacontroller::save_staff).delete(akacontroller::delete_staff))
      .route("/query", post(akacontroller::query_staffs))
      .nest(
        "/depts",
        Router::new()
          .route("/", post(akacontroller::save_staff_depts).delete(akacontroller::delete_staff_dept))
          .route("/query", post(akacontroller::query_staff_depts)),
      )
      .nest(
        "/rights",
        Router::new()
          .route("/", post(akacontroller::save_staff_rights))
          .route("/query", post(akacontroller::query_staff_rights)),
      ),
  );

  let group_route = Router::new().nest(
    "/group",
    Router::new()
      .route("/", post(akacontroller::save_groupinfo).delete(akacontroller::delete_groupinfo))
      .route("/query", post(akacontroller::query_groupinfos))
      .nest(
        "/rights",
        Router::new()
          .route("/", post(akacontroller::save_grouprights))
          .route("/query", post(akacontroller::query_grouprights)),
      ),
  );

  let package_route = Router::new().nest(
    "/package",
    Router::new()
      .route("/", post(packagecontroller::save_packageinfo).delete(packagecontroller::delete_packageinfo))
      .route("/query", post(packagecontroller::query_packageinfos))
      .nest(
        "/detail",
        Router::new()
          .route("/", delete(packagecontroller::remove_package_details))
          .route("/query", post(packagecontroller::query_package_details)),
      ),
  );

  let bar_route = Router::new().nest(
    "/bar",
    Router::new()
      .route("/", post(akacontroller::save_barnameinfo).delete(akacontroller::delete_barnameinfo))
      .route("/query", post(akacontroller::query_barnameinfos))
      .nest(
        "/detail",
        Router::new()
          .route("/", post(akacontroller::save_bardetail).delete(akacontroller::delete_bardetail))
          .route("/query", post(akacontroller::query_bardetails)),
      )
      .nest(
        "/item",
        Router::new()
          .route("/", post(akacontroller::save_baritems).delete(akacontroller::delete_baritems))
          .route("/query", post(akacontroller::query_baritems)),
      ),
  );

  let guide_route = Router::new().nest(
    "/guide",
    Router::new()
      .route("/", post(akacontroller::save_guideinfo).delete(akacontroller::delete_guideinfo))
      .route("/query", post(akacontroller::query_guideinfos))
      .nest(
        "/item",
        Router::new()
          .route("/", post(akacontroller::save_guide_items).delete(akacontroller::delete_guide_items))
          .route("/query", post(akacontroller::query_guide_items)),
      ),
  );

  let stat_route = Router::new().nest(
    "/stat",
    Router::new()
      .route("/summary", post(statcontroller::stat_summaries))
      .route("/query", post(statcontroller::stat_summaries))
      .route("/hdsum_v2", post(statcontroller::stat_hazardinfos_with_docx))
      .route("/deptitem", post(statcontroller::stat_deptitem_with_docx))
      .route("/fee", post(statcontroller::expoert_fees_summary)),
  );

  let report_route = Router::new().nest(
    "/report",
    Router::new()
      .route("/query", post(reportcontroller::query_reports))
      // .route("/remove", post(reportcontroller::remove_from_report))
      .route("/", post(reportcontroller::save_report).delete(reportcontroller::delete_report))
      .nest(
        "/infos",
        Router::new()
          .route("/", post(reportcontroller::save_reportinfos_detail).delete(reportcontroller::remove_from_report))
          .route("/query", post(reportcontroller::qury_reportinfos_detail))
          .route("/testids", post(reportcontroller::query_reportinfo_by_testids)),
      ),
  );
  let law_route = Router::new().nest(
    "/laws",
    Router::new()
      .route("/", post(akacontroller::save_lawinfos).delete(akacontroller::delete_lawinfos))
      // .route("/remove", post(reportcontroller::remove_from_report))
      .route("/query", post(akacontroller::query_lawinfos)),
  );

  let datasum_route = Router::new().nest("/data", Router::new().route("/", post(datasumcontroller::export_datas)));

  let instrument_route = Router::new().nest(
    "/instrument",
    Router::new()
      .route("/", post(instrumentcontroller::save_instrument).delete(instrumentcontroller::delete_instrument))
      .route("/query", post(instrumentcontroller::query_instruments))
      .nest(
        "/items",
        Router::new()
          .route("/", post(instrumentcontroller::save_instrument_items).delete(instrumentcontroller::delete_instrument_items))
          .route("/query", post(instrumentcontroller::query_instrument_items)),
      ),
  );

  let upload_route = Router::new().nest(
    "/upload",
    Router::new()
      .route("/photo", post(filectl::upload_photo))
      .route("/sign", post(filectl::upload_sign))
      .route("/file", post(filectl::upload_file)),
  );

  let download_route = Router::new().nest(
    "/download",
    Router::new()
      .route("/photo", post(filectl::download_photo))
      .route("/sign", post(filectl::download_sign))
      .route("/file", post(filectl::download_file)),
  );

  // let updown_route = Router::new().nest(
  //   "/",
  //   Router::new()
  //     .nest(
  //       "/upload",
  //       Router::new()
  //         .route("/photo", post(filectl::upload_photo))
  //         .route("/sign", post(filectl::upload_sign))
  //         .route("/file", post(filectl::upload_file)),
  //     )
  //     .nest(
  //       "/download",
  //       Router::new()
  //         .route("/photo", post(filectl::download_photo))
  //         .route("/sign", post(filectl::download_sign))
  //         .route("/file", post(filectl::download_file)),
  //     ),
  // );

  let sync_route = Router::new().nest(
    "/sync",
    Router::new()
      .route("/appoint", post(synccontroller::sync_medinfos_from_polar))
      .route("/result", post(synccontroller::sync_medresults_to_polar))
      .route("/report", post(synccontroller::sync_medresults_with_report_to_polar))
      .route("/status", post(synccontroller::sync_medstatus_to_polar))
      .route("/corpuser", post(synccontroller::add_corp_users)),
  );

  let external_route = Router::new().nest(
    "/external",
    Router::new()
      .route("/lis/infos", post(externalcontroller::for_external_lis_checkitems))
      .route("/lis/xmxx", post(externalcontroller::from_external_lis_xmxx))
      .route("/pacs/infos", post(externalcontroller::for_external_pacs_checkitems))
      .route("/lis/results", post(externalcontroller::for_external_lis_results)) //外部调用该接口上传lis数据
      .route("/pacs/results", post(externalcontroller::external_pacs_results)) //外部调用该接口上传pacs数据
      .route("/items", post(externalcontroller::for_external_items))
      .route("/upload", post(externalcontroller::external_upload))
      .route("/orders", post(externalcontroller::from_external_orders))
      .route("/lis/import", post(externalcontroller::import_lab_data))
      .nest(
        "/recv",
        Router::new()
          .route("/lis", post(externalcontroller::external_recv_lis))
          .route("/pacs", post(externalcontroller::external_recv_pacs))
          .route("/audio", post(externalcontroller::external_recv_audiogram)),
      )
      .nest(
        "/srmyy",
        Router::new()
          .route("/chargeflag", post(externalcontroller::zjsrmyy_update_charge_flag))
          .route("/lis/status", post(externalcontroller::zjsrmyy_update_lis_status))
          .route("/lis/results", post(externalcontroller::zjsrmyy_recv_lis_results))
          .route("/lis/import", post(externalcontroller::zjsrmyy_import_lis_results))
          .route("/exam/results", post(externalcontroller::zjsrmyy_recv_pacs_results))
          .route("/exam/status", post(externalcontroller::zjsrmyy_update_pacs_status)),
      ),
  );
  let doc_route = Router::new().nest(
    "/docs",
    Router::new()
      .route("/comp", post(documentcontroller::generate_corp_report))
      .route("/personal", post(documentcontroller::generate_corp_report))
      .route("/recheck", post(documentcontroller::generate_corp_recheck_notice)),
  );
  // let protected_route = Router::new().nest("/protect", Router::new().route("/", method_router))
  let cdc_route = Router::new().nest(
    "/cdc",
    Router::new()
      .route("/checkitem", post(cdccontroller::pair_checkitem))
      .route("/dicts", post(cdccontroller::query_dictionaries)), // .route("/card", post(cdccontroller::query_cardlist))
  );
  let app_route = Router::new().nest(
    "/apix/v2",
    Router::new()
      .merge(account_route)
      .merge(medinfo_route)
      .merge(audiogram_route)
      .merge(autodiag_route)
      .merge(ptinfo_route)
      .merge(depart_route)
      .merge(items_route)
      .merge(staff_route)
      .merge(group_route)
      .merge(package_route)
      .merge(guide_route)
      .merge(summary_route)
      .merge(disease_route)
      .merge(corpinfo_route)
      .merge(hazard_route)
      .merge(checkall_route)
      .merge(checkitem_route)
      .merge(bar_route)
      .merge(sysinfo_route)
      .merge(report_route)
      .merge(datasum_route)
      .merge(stat_route)
      .merge(sync_route)
      // .merge(updown_route)
      .merge(upload_route)
      .merge(download_route)
      .merge(hcard_route)
      .merge(doc_route)
      .merge(external_route)
      .merge(cdc_route)
      .merge(instrument_route)
      .merge(law_route),
  );

  app_route
}

async fn proxy_handler(Extension(settings): Extension<Arc<Settings>>, OriginalUri(original_uri): OriginalUri, State(client): State<Client>, mut req: Request) -> Response<Body> {
  if settings.application.proxyserver.is_empty() {
    return Response::builder()
      .status(StatusCode::NOT_FOUND)
      .body(Body::from("proxy backserver server is not configed"))
      .unwrap();
  }
  let uri = format!("{}{}", &settings.application.proxyserver, original_uri);
  // info!("proxy forward uri:{}", &uri);
  let ret = Uri::try_from(uri);
  if ret.as_ref().is_err() {
    error!("{}", ret.as_ref().unwrap_err().to_string());
    return Response::builder().status(StatusCode::NOT_FOUND).body(Body::from("无效的后台服务器")).unwrap();
  }
  *req.uri_mut() = ret.unwrap();
  let ret = client.request(req).await;
  if ret.as_ref().is_err() {
    error!("{}", ret.as_ref().unwrap_err().to_string());
    return Response::builder().status(StatusCode::NOT_FOUND).body(Body::from("不能连接到后台服务器")).unwrap();
  }
  ret.unwrap().into_response()
}

// async fn report_handler(Extension(settings): Extension<Arc<Settings>>, OriginalUri(original_uri): OriginalUri, State(client): State<Client>, mut req: Request) -> Response<Body> {
//   if settings.application.reportserver.is_empty() {
//     return Response::builder()
//       .status(StatusCode::NOT_FOUND)
//       .body(Body::from("proxy backserver server is not configed"))
//       .unwrap();
//   }
//   let uri = format!("{}{}", &settings.application.reportserver, original_uri);
//   // info!("proxy forward uri:{}", &uri);
//   let ret = Uri::try_from(uri);
//   if ret.as_ref().is_err() {
//     error!("{}", ret.as_ref().unwrap_err().to_string());
//     return Response::builder().status(StatusCode::NOT_FOUND).body(Body::from("无效的后台服务器")).unwrap();
//   }
//   *req.uri_mut() = ret.unwrap();
//   let ret = client.request(req).await;
//   if ret.as_ref().is_err() {
//     error!("{}", ret.as_ref().unwrap_err().to_string());
//     return Response::builder().status(StatusCode::NOT_FOUND).body(Body::from("不能连接到后台服务器")).unwrap();
//   }
//   ret.unwrap().into_response()
// }

async fn cdc_handler(Extension(settings): Extension<Arc<Settings>>, OriginalUri(original_uri): OriginalUri, State(client): State<Client>, mut req: Request) -> Response<Body> {
  if settings.application.cdcserver.is_empty() {
    let res = ResponseBody::new(HttpCode::Error as i32, "cdcupload server is not configed", "");
    let resbody = serde_json::to_string(&res).unwrap_or_default();
    return Response::builder().status(StatusCode::NOT_FOUND).body(Body::from(resbody)).unwrap();
  }
  let uri = format!("{}{}", &settings.application.cdcserver, original_uri);
  // info!("proxy forward uri:{}", &uri);
  let ret = Uri::try_from(uri);
  if ret.as_ref().is_err() {
    error!("{}", ret.as_ref().unwrap_err().to_string());
    return Response::builder().status(StatusCode::NOT_FOUND).body(Body::from("无效的cdc上报服务器")).unwrap();
  }
  *req.uri_mut() = ret.unwrap();
  let ret = client.request(req).await;
  if ret.as_ref().is_err() {
    error!("{}", ret.as_ref().unwrap_err().to_string());
    return Response::builder().status(StatusCode::NOT_FOUND).body(Body::from("不能连接到cdc上报服务器")).unwrap();
  }
  ret.unwrap().into_response()
}

async fn dams_handler(Extension(settings): Extension<Arc<Settings>>, OriginalUri(original_uri): OriginalUri, State(client): State<Client>, mut req: Request) -> Response<Body> {
  if settings.application.dserver.is_empty() {
    let res = ResponseBody::new(HttpCode::Error as i32, "cdcupload server is not configed", "");
    let resbody = serde_json::to_string(&res).unwrap_or_default();
    return Response::builder().status(StatusCode::NOT_FOUND).body(Body::from(resbody)).unwrap();
  }
  let uri = format!("{}{}", &settings.application.dserver, original_uri);
  // info!("proxy forward uri:{}", &uri);
  let ret = Uri::try_from(uri);
  if ret.as_ref().is_err() {
    error!("{}", ret.as_ref().unwrap_err().to_string());
    return Response::builder().status(StatusCode::NOT_FOUND).body(Body::from("无效的dams服务器")).unwrap();
  }
  *req.uri_mut() = ret.unwrap();
  let ret = client.request(req).await;
  if ret.as_ref().is_err() {
    error!("{}", ret.as_ref().unwrap_err().to_string());
    return Response::builder().status(StatusCode::NOT_FOUND).body(Body::from("不能连接到dams服务器")).unwrap();
  }
  ret.unwrap().into_response()
}
