use crate::entities::{iteminfo_v, prelude::*};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter};

impl IteminfoV {
  pub async fn query_many(code: &str, db: &DatabaseConnection) -> Result<Vec<IteminfoV>> {
    let mut condition = Condition::all();
    if !code.is_empty() {
      condition = condition.add(iteminfo_v::Column::Itemid.eq(code));
    }

    let ret = IteminfoVEntity::find().filter(condition).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
