#coding=utf-8

import sys
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from corpreport.occureport import generate_pdf_corp_occureport
from corpreport.occuradioreport import generate_pdf_corp_radio_occureport
from corpreport.normalreport import generate_pdf_corp_normalreport
from corpreport.occuradioreportdocx import generate_docx_corp_radio_occureport
from corpreport.occureportdocx import generate_docx_corp_occureport
from dataentities.dbconn import session
from dataentities.tj_corpoccureport import TjCorpoccureport
from dataentities.ss_dictionary import SsDictionary
from dataentities.tj_checkallnew import TjCheckallnew
from dataentities.tj_corpoccureportinfo import TjCorpoccureportinfo
from dataentities.tj_patient import TjPatient
from dataentities.tj_medexaminfo import TjMedexaminfo
import constant
from dataentities.tj_corpinfo import TjCorpinfo
from dataentities.ss_area import SsArea
# from dataentities.tj_patienthazards import TjPatienthazards

# pdfmetrics.registerFont(TTFont('SimSun', 'SimSun.ttc'))  # 注册字体
# pdfmetrics.registerFont(TTFont('SimSun', 'msyh.ttc'))  # 注册字体

# Try different possible paths for the font files
font_paths = [
    ("./fonts/simhei.ttf", "./fonts/simsun.ttc"),
    ("../fonts/simhei.ttf", "../fonts/simsun.ttc"),
    # ("../../fonts/simhei.ttf", "../../fonts/simsun.ttc")
]

fonts_registered = False
for simhei_path, simsun_path in font_paths:
    try:
        pdfmetrics.registerFont(TTFont("SimHei", simhei_path))
        pdfmetrics.registerFont(TTFont("SimSun", simsun_path))  # 注册字体
        fonts_registered = True
        break
    except Exception:
        continue

if not fonts_registered:
    print("Warning: Could not register fonts. Report generation may fail.")
    # Try to register with system fonts as fallback
    try:
        pdfmetrics.registerFont(TTFont("SimHei", "simhei.ttf"))
        pdfmetrics.registerFont(TTFont("SimSun", "simsun.ttc"))
    except Exception:
        print("Warning: System fonts also not available.")

def main(rptid, reporttype, pagestyle, splitinrow, outdir):
    if rptid <= 0:
        print("报告编号无效")
        return
    print("需要处理的报告编号:", rptid)

    dicts = session.query(SsDictionary).all()

    cusotmer_dict = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictSysparm.value
        and dict.ss_pid == constant.SysParm.Customer.value
    )
    if cusotmer_dict is None:
        customer = ""
    else:
        customer = cusotmer_dict.ss_short.upper()
    # print("customer is:", customer)

    rptinfo = (
        session.query(TjCorpoccureport).filter(TjCorpoccureport.id == rptid).first()
    )
    if rptinfo is None:
        print("报告信息不存在")
        return
    corpinfo = (
        session.query(TjCorpinfo).filter(TjCorpinfo.id == rptinfo.tj_corpid).first()
    )
    if corpinfo is None:
        print("编号为:{}的体检单位不存在".format(rptinfo.tj_corpid))
        return
    areainfo = session.query(SsArea).filter(SsArea.area_code == corpinfo.tj_areacode).first()
    if areainfo is None:
        areaname = ""
    else:
        areaname = areainfo.area_fullname
    rptdetails = (
        session.query(TjCorpoccureportinfo)
        .filter(TjCorpoccureportinfo.tj_report_id == rptinfo.id)
        .all()
    )
    testids = []
    for rinfo in rptdetails:
        testids.append(rinfo.tj_test_id)
    checkallinfos = (
        session.query(TjCheckallnew).filter(TjCheckallnew.tj_testid.in_(testids)).all()
    )
    medinfos = (
        session.query(TjMedexaminfo).filter(TjMedexaminfo.tj_testid.in_(testids)).all()
    )
    # pthazards = session.query(TjPatienthazards).filter(TjPatienthazards.tj_testid.in_(testids)).all()

    pids = []
    for med in medinfos:
        pids.append(med.tj_pid)
    # print("所有总检数据:", len(checkallinfos))

    if outdir == "":
        outdir = "./reports"
    patients = session.query(TjPatient).filter(TjPatient.tj_pid.in_(pids)).all()

    if reporttype == constant.ReportFormat.Pdf.value:
        if rptinfo.tj_reporttype == 0:  # 职业健康报告
            output = generate_pdf_corp_occureport(
                rptinfo,
                checkallinfos,
                # pthazards,
                medinfos,
                patients,
                corpinfo,
                dicts,
                customer,
                pagestyle,
                splitinrow,
                outdir,
                areaname,
            )
            print("output is:", output)
            # os.path.join(rptdir, output),
        elif rptinfo.tj_reporttype == 1:
            generate_pdf_corp_normalreport(
                rptinfo,
                checkallinfos,
                medinfos,
                patients,
                corpinfo,
                dicts,
                customer,
                outdir,
            )
        elif rptinfo.tj_reporttype == 2:  # 放射报告
            output = generate_pdf_corp_radio_occureport(
                rptinfo,
                checkallinfos,
                medinfos,
                patients,
                corpinfo,
                dicts,
                customer,
                pagestyle,
                splitinrow,
                outdir,
                areaname,
            )
            print("output is:", output)
    elif reporttype == constant.ReportFormat.Docx.value:  # generate docx
        if rptinfo.tj_reporttype == 0:
            output = generate_docx_corp_occureport(
                rptinfo,
                checkallinfos,
                medinfos,
                patients,
                corpinfo,
                dicts,
                customer,
                pagestyle,
                splitinrow,
                outdir,
                areaname,
            )
            print("output is:", output)
        elif rptinfo.tj_reporttype == 1:
            pass
        elif rptinfo.tj_reporttype == 2:
            output = generate_docx_corp_radio_occureport(
                rptinfo,
                checkallinfos,
                medinfos,
                patients,
                corpinfo,
                dicts,
                customer,
                pagestyle,
                splitinrow,
                outdir,
                areaname,
            )
            print("output is:", output)
    else:
        pass


if __name__ == "__main__":
    n = len(sys.argv)
    print("Total arguments passed:", n)
    if n <= 4:
        print("请输入报告编号跟报告存储目录")
        sys.exit()
    try:
        rptid = int(sys.argv[1])
        rpttype = int(sys.argv[2])
        pagestyle = int(sys.argv[3])
        splitinrow = int(sys.argv[4])
        outdir = sys.argv[5]
    except ValueError:
        print("报告编号或者输出地址无效")
        sys.exit()

    sys.exit(main(rptid, rpttype, pagestyle, splitinrow, outdir))
