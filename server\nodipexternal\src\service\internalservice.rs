use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use log::*;

use crate::{
  common::constant,
  rusthl7::{EscapeSequence, Message, Segment},
  service::hl7segment::msh::msh,
};

pub struct InternalService;

impl InternalService {
  pub async fn handl_lab_result(message: &Message<'_>, db: &DbConnection) -> Result<()> {
    //
    // let decoder = EscapeSequence::new(message.get_separators());
    if let Ok(msh) = msh(&message) {
      if let Some(ms) = msh.msh_8_security {
        info!("security is:{}", &ms.source);
        match ms.source {
          "7" => {
            let ret = InternalService::handle_result(&message, &db).await;
            if ret.as_ref().is_err() {
              error!("{}", ret.as_ref().unwrap_err().to_string());
              return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
            }
          }
          "2" => {
            let ret = InternalService::cancel_result(&message, &db).await;
            if ret.as_ref().is_err() {
              error!("{}", ret.as_ref().unwrap_err().to_string());
              return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
            }
          }
          _ => error!("unknow security......{}", &ms.source),
        }
      } else {
        error!("empty msh security......");
      }
    }

    Ok(())
  }

  async fn handle_result(message: &Message<'_>, db: &DbConnection) -> Result<()> {
    info!("开始处理结果信息......");
    let ret = message.segments_by_identifier(&constant::MessageCode::ORC.to_string());
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let orcinfos = ret.unwrap();

    let ret = message.segments_by_identifier(&constant::MessageCode::OBR.to_string());
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let obrinfos = ret.unwrap();

    let mut lisret = InternalService::get_general_result(&orcinfos, &obrinfos);
    let decoder = EscapeSequence::new(message.get_separators());

    let patids = message.query("PID.F3");
    let tjbh = InternalService::get_testid(patids);
    // lisret.tjbh = message.query("PV1.F19").to_string();
    if tjbh.is_empty() {
      return Err(anyhow! {"不能解析到体检编号......"});
    }
    lisret.tjbh = tjbh;
    lisret.brxm = decoder.decode(message.query("PID.F5.C2")).to_string();
    info!("general lis result:{:?}", &lisret);

    let ret = message.segments_by_identifier(&constant::MessageCode::OBX.to_string());
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let obxs = ret.unwrap();

    //具体结果
    // let mut obx_results: Vec<ObxSegment> = Vec::new();
    let mut lisresults: Vec<VTjLisresult> = Vec::new();
    for obx in obxs.into_iter() {
      let xmxx: Vec<&str> = obx.query("F3").split('^').collect();
      if xmxx.len() != 2 {
        error!("invalid xmxx:{}", obx.query("F3"));
        continue;
      }

      let retinfo = VTjLisresult {
        id: 0,
        tjbh: lisret.tjbh.clone(),
        brxm: lisret.brxm.clone(),
        xmxh: xmxx[0].to_string(),
        xmmc: xmxx[1].to_string(),
        xmdw: decoder.decode(obx.query("F6").to_string()).to_string(),
        xmjg: obx.query("F4").to_string(),
        sfyc: match obx.query("F8").parse().unwrap_or_default() {
          0 => 0,
          _ => 1,
        }, //0 正常 1 偏低 2 偏高
        gdbj: match obx.query("F8") {
          "1" => "↓".to_string(),
          "2" => "↑".to_string(),
          _ => "".to_string(),
        },
        ckdz: "".to_string(),
        ckgz: "".to_string(),
        ckfw: obx.query("F7").to_string(),
        jyys: lisret.jyys.to_owned(),
        bgrq: lisret.bgrq.to_owned(),
        bgys: lisret.bgys.to_owned(),
      };
      info!("lis result is:{:?}", &retinfo);
      lisresults.push(retinfo);
    }
    let xmshs: Vec<String> = lisresults.iter().map(|v| v.xmxh.to_string()).collect();
    // let xmshs: Vec<&str> = xmshs.iter().map(|v| &**v).collect();
    //检查数据是否已经存在
    let ret = VTjLisresult::query_many_by_xmxh(&lisret.tjbh, &xmshs, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("query error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let exist_results = ret.unwrap();
    if exist_results.len() > 0 {
      let exist_ids: Vec<i64> = exist_results.iter().map(|v| v.id).collect();
      let ret = VTjLisresult::delete_many_by_ids(&exist_ids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("delete error:{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = VTjLisresult::save_many(&lisresults, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("save error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(())
  }
  async fn cancel_result(message: &Message<'_>, db: &DbConnection) -> Result<()> {
    info!("结果取消......");
    let patids = message.query("PID.F3");
    let tjbh = InternalService::get_testid(patids);
    // lisret.tjbh = message.query("PV1.F19").to_string();
    if tjbh.is_empty() {
      return Err(anyhow! {"不能解析到体检编号......"});
    }
    let ret = VTjLisresult::delete_many(&tjbh, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("delete error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(())
  }

  fn get_general_result(orcinfos: &Vec<&Segment>, obrinfos: &Vec<&Segment>) -> VTjLisresult {
    let mut examdoctorid = "";
    let mut examdoctorname = "";
    let mut checkdate = "".to_string();
    let mut checkdoctorid = "";
    let mut checkdoctorname = "";

    for val in orcinfos.into_iter() {
      if !examdoctorid.is_empty() && !examdoctorname.is_empty() {
        break;
      }
      let splitters: Vec<&str> = val.query("F19").split('^').collect();
      if splitters.len() == 1 && examdoctorid.is_empty() {
        examdoctorid = splitters.get(0).map_or("", |f| f.to_owned());
      }
      if splitters.len() == 3 && examdoctorname.is_empty() {
        examdoctorname = splitters.get(2).map_or("", |f| f.to_owned());
      }
    }

    for val in obrinfos.into_iter() {
      if !checkdoctorid.is_empty() && !checkdoctorname.is_empty() && !checkdate.is_empty() {
        break;
      }

      if checkdate.is_empty() {
        let obr7 = val.query("F7");
        if obr7.len() == 16 {
          checkdate = format!("{}-{}-{} {}", &obr7[0..4], &obr7[4..6], &obr7[6..8], &obr7[8..16]);
        } else {
          checkdate = obr7.to_string();
        }
      }

      let splitters: Vec<&str> = val.query("F32").split('&').collect();
      if splitters.len() == 1 && checkdoctorid.is_empty() {
        checkdoctorid = splitters.get(0).map_or("", |f| f.to_owned());
      }
      if splitters.len() == 3 && checkdoctorname.is_empty() {
        checkdoctorname = splitters.get(2).map_or("", |f| f.to_owned());
      }
    }

    let mut ret = VTjLisresult { ..Default::default() };
    ret.jyys = examdoctorname.to_string();
    ret.bgys = checkdoctorname.to_string();
    ret.bgrq = checkdate;
    // info!("lis result:{:?}", &ret);

    ret
  }
  fn get_testid(patids: &str) -> String {
    let patids: Vec<&str> = patids.split("~").collect();
    let mut final_testid = "".to_string();
    info!("Patids:{patids:?}");
    if patids.len() > 0 {
      if let Some(testid) = patids.into_iter().find(|p| p.contains("BLH")) {
        info!("testid:{testid}");

        //采用分割得办法
        //  let testid2: Vec<&str> = testid.split("^").filter(|&f| !f.is_empty()).map(|v| v).collect();
        // info!("testids2:{testid2:?}");
        if let Some(idx) = testid.chars().position(|p| p == '^') {
          info!("idx:{idx}");
          final_testid = testid[0..idx].to_string();
          info!("final testid is:{}", &final_testid);
        }
      }
    }
    final_testid
  }
}
