-- 预约信息
DROP TABLE if exists `dams_appoint`;

CREATE TABLE `dams_appoint` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `order_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '平台订单号 平台唯一标识',
    `package_number` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '院内体检套餐编号 由迪安提供',
    `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '体检人员姓名',
    `id_card` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号',
    `sex` int NOT NULL DEFAULT 0 COMMENT '性别 0 男 ， 1 女',
    `age` int NOT NULL DEFAULT 0 COMMENT '体检人员年龄',
    `phone` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号码',
    `marry` int NOT NULL DEFAULT 3 COMMENT '婚姻状况 1.已婚2.未婚3.未知',
    `job_type` int NOT NULL DEFAULT 1 COMMENT '工种 1.普工 2.管理人员 3.放射性从业人员 4.焊工5.电工 6.架子工 7.油漆工 8.司机',
    `company` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '施工方单位名称',
    `comnanrld` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '施工方单位编码',
    `hospital_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '体检医院名称',
    `check_time` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预约体检时间',
    `physical_examination_package` int NOT NULL DEFAULT 0 COMMENT '体检套餐 1.常规体检2.职业体检',
    `working_years` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工龄',
    `contact_damage` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接害工龄',
    `hazardous_factors` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '危害因素',
    `physical_examination_type` int NOT NULL DEFAULT 0 COMMENT '参数：1.上岗2.在岗 3.离岗',
    `reservestr1` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预留字段',
    `reservestr2` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预留字段',
    `reservestr3` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预留字段',
    `create_date` bigint NOT NULL DEFAULT 0 COMMENT '时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC COMMENT = '预约信息';


DROP TABLE if exists `dams_uploadhis`;

CREATE TABLE `dams_uploadhis` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `testid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '体检编号',
    `ordno` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
    `result` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结果',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC COMMENT = '回传日志信息';

