from sqlalchemy import (
    Column,
    Integer,
    String,
)
from dataentities.dbconn import Base


class TjTestsummary(Base):
    # 数据库中存储的表名
    __tablename__ = "tj_testsummary"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    tj_testid = Column(String(64), nullable=False)
    tj_deptid = Column(String(64), nullable=False, unique=False)
    tj_summary = Column(String, nullable=False)
    tj_suggestion = Column(String, nullable=False)
    tj_isfinished = Column(Integer, nullable=False)
    tj_doctorid = Column(String(256), nullable=False)
    tj_doctor = Column(String(256), nullable=False)
    tj_date = Column(Integer, nullable=False)
    tj_forceend = Column(Integer, nullable=False)
    tj_checkdoctor = Column(String(512), nullable=False)
    tj_checkdate = Column(Integer, nullable=False)