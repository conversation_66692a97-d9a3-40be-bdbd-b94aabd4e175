use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjBarnameinfo {
  pub id: i64,
  pub tj_barnum: String,
  pub tj_barname: String,
  pub tj_bardesc: String,
  pub tj_barorder: i32,
}
crud!(TjBarnameinfo {}, "tj_barnameinfo");
// rbatis::impl_select!(TjBarnameinfo{query_many_by_ids(ids:&[i64]) => "`where id in ${ids.sql()} `"});
rbatis::impl_select!(TjBarnameinfo{query_many(ids:&[i64]) =>
  "`where id > 0 `
  if !ids.is_empty():
    ` and id in ${ids.sql()} `"});

impl TjBarnameinfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjBarnameinfo) -> Result<i64> {
    if info.id > 0 {
      let ret = TjBarnameinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      return Ok(info.id);
    }
    let ret = TjBarnameinfo::insert(rb, info).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap().last_insert_id.into())
  }
}
