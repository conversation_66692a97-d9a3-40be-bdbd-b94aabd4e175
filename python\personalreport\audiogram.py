from decimal import Decimal
import constant

class TjAudiogramresult:
    empty_value = -200
    tj_testid = ""
    tj_avgsgp = empty_value
    tj_avgyyp = empty_value
    tj_avgzyp = empty_value
    tj_avgsyp = empty_value
    tj_avgygp = empty_value
    tj_avgzgp = empty_value
    tj_avgyty = empty_value
    tj_avgzty = empty_value
    tj_avgsty = empty_value
    details=[]
    def __init__(self,details) -> None:
        self.details = details

    def compute_audiogram_result(self):
        if len(self.details) <= 0:
            return
        self.tj_testid = self.details[0].tj_testid
        # 先计算右耳
        v1 = self.empty_value; v2 = self.empty_value; v3 = self.empty_value; v4 = self.empty_value; v5 = self.empty_value; v6 = self.empty_value;
        lv1 = self.empty_value; lv2 = self.empty_value; lv3 = self.empty_value; lv4 = self.empty_value; lv5 = self.empty_value; lv6 = self.empty_value;
        
        r500 = next(filter(lambda dt: dt.tj_freq == 500 and dt.tj_ear == constant.Ear.Right.value and dt.tj_adtype == constant.Trans.Air.value,self.details),None)
        if r500 is not None:
            v1=r500.tj_revise
        r1000 = next(filter(lambda dt: dt.tj_freq == 1000 and dt.tj_ear == constant.Ear.Right.value and dt.tj_adtype == constant.Trans.Air.value,self.details),None)
        if r1000 is not None:
            v2=r1000.tj_revise
        r2000 = next(filter(lambda dt: dt.tj_freq == 2000 and dt.tj_ear == constant.Ear.Right.value and dt.tj_adtype == constant.Trans.Air.value,self.details),None)
        if r2000 is not None:
            v3=r2000.tj_revise
        r3000 = next(filter(lambda dt: dt.tj_freq == 3000 and dt.tj_ear == constant.Ear.Right.value and dt.tj_adtype == constant.Trans.Air.value,self.details),None)
        if r3000 is not None:
            v4=r3000.tj_revise
        r4000 = next(filter(lambda dt: dt.tj_freq == 4000 and dt.tj_ear == constant.Ear.Right.value and dt.tj_adtype == constant.Trans.Air.value,self.details),None)
        if r4000 is not None:
            v5=r4000.tj_revise
        r6000 = next(filter(lambda dt: dt.tj_freq == 6000 and dt.tj_ear == constant.Ear.Right.value and dt.tj_adtype == constant.Trans.Air.value,self.details),None)
        if r6000 is not None:
            v6=r6000.tj_revise

        if v1 > self.empty_value and v2 > self.empty_value and v3 > self.empty_value and v5 > self.empty_value:
              self.tj_avgyty = round(((v1 + v2 + v3) / 3) * 0.9 + v5 * 0.1) #右耳平均加权值
              self.tj_avgyyp = round((v1 + v2 + v3) / 3) #右语频

        if v4 > self.empty_value and v5 > self.empty_value and v6 > self.empty_value :
              self.tj_avgygp = round((v4 + v5 + v6) / 3) #右高频

        l500 = next(filter(lambda dt: dt.tj_freq == 500 and dt.tj_ear == constant.Ear.Left.value and dt.tj_adtype == constant.Trans.Air.value,self.details),None)
        if l500 is not None:
            lv1=l500.tj_revise
        l1000 = next(filter(lambda dt: dt.tj_freq == 1000 and dt.tj_ear == constant.Ear.Left.value and dt.tj_adtype == constant.Trans.Air.value,self.details),None)
        if l1000 is not None:
            lv2=l1000.tj_revise
        l2000 = next(filter(lambda dt: dt.tj_freq == 2000 and dt.tj_ear == constant.Ear.Left.value and dt.tj_adtype == constant.Trans.Air.value,self.details),None)
        if l2000 is not None:
            lv3=l2000.tj_revise
        l3000 = next(filter(lambda dt: dt.tj_freq == 3000 and dt.tj_ear == constant.Ear.Left.value and dt.tj_adtype == constant.Trans.Air.value,self.details),None)
        if l3000 is not None:
            lv4=l3000.tj_revise
        l4000 = next(filter(lambda dt: dt.tj_freq == 4000 and dt.tj_ear == constant.Ear.Left.value and dt.tj_adtype == constant.Trans.Air.value,self.details),None)
        if l4000 is not None:
            lv5=l4000.tj_revise
        l6000 = next(filter(lambda dt: dt.tj_freq == 6000 and dt.tj_ear == constant.Ear.Left.value and dt.tj_adtype == constant.Trans.Air.value,self.details),None)
        if l6000 is not None:
            lv6=l6000.tj_revise

        if lv1 > self.empty_value and lv2 > self.empty_value and lv3 > self.empty_value and lv5 > self.empty_value:
              self.tj_avgzty = round(((float(lv1) + lv2 + lv3) / 3) * float(0.9) + lv5 * 0.1) #左耳平均加权值
              self.tj_avgzyp = round((lv1 + lv2 + lv3) / 3) #左语频

        if lv4 > self.empty_value and lv5 > self.empty_value and lv6 > self.empty_value :
              self.tj_avgzgp = round((lv4 + lv5 + lv6) / 3,0) #左高频

        if self.tj_avgzgp > self.empty_value and self.tj_avgygp > self.empty_value:
            self.tj_avgsgp = round((self.tj_avgzgp+self.tj_avgygp)/2)
        if self.tj_avgzyp> self.empty_value and self.tj_avgyyp > self.empty_value:
            self.tj_avgsyp = round((self.tj_avgzyp+self.tj_avgyyp)/2)
        if self.tj_avgzty> self.empty_value and self.tj_avgyty > self.empty_value:
            self.tj_avgsty = round((self.tj_avgzty+self.tj_avgyty)/2)#Decimal((self.tj_avgzty+self.tj_avgyty)/2).quantize(Decimal(0))
    
