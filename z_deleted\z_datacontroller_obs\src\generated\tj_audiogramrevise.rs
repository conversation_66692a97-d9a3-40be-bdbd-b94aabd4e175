//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_audiogramrevise")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_freq: i32,
    pub tj_sex: i32,
    pub tj_startage: i32,
    pub tj_endage: i32,
    pub tj_revise: i32,
}

#[derive(<PERSON><PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
