use crate::config::settings::Extpacs;
use anyhow::{anyhow, Result};
use serde_json::json;
use tokio_util::compat::Compat;
use utility::timeutil;

use canyon_sql::crud::CrudOperations;
use canyon_sql::db_clients::tiberius::{Client, Config};
use canyon_sql::macros::{canyon_entity, CanyonCrud, CanyonMapper, Fields, ForeignKeyable};
use canyon_sql::runtime::tokio::net::TcpStream;
use canyon_sql::runtime::tokio_util::compat::TokioAsyncWriteCompatExt;

#[derive(Debu<PERSON>, <PERSON><PERSON>, De<PERSON>ult, Fields, CanyonCrud, CanyonMapper, ForeignKeyable)]
#[canyon_entity(table_name = "HisJCRW")]
pub struct HisJCRW {
  #[primary_key]
  pub lsh: i64, //流水号
  pub jch: String,      //检查号，如超声号、X线号
  pub djxh: i32,        //登记序号：1-住院、2-门诊、3-急诊、4-体检
  pub bah: String,      //病案号
  pub bqmc: String,     //病区名称
  pub bqdm: String,     //病区代码
  pub brch: String,     //病人床号
  pub jzkh: String,     //就诊卡号
  pub brxm: String,     //病人姓名
  pub brxb: i32,        //病人性别
  pub brnl: i32,        //病人年龄
  pub csrq: String,     //病人出生日期 datetime
  pub jtzz: String,     //家庭住址
  pub lxdh: String,     //联系电话
  pub ylxh: i32,        //医疗序号，即检查项目代码
  pub ylmc: String,     //医疗名称，即检查项目名称
  pub sjysgh: String,   //申检医生工号，即开单医生工号
  pub sjysxm: String,   //申检医生姓名
  pub sjksdm: String,   //申检科室代码
  pub sjksmc: String,   //申检科室名称
  pub sjsj: String,     //申检时间 datetime
  pub lczd: String,     //临床诊断
  pub djysgh: String,   //登记医生工号
  pub djysxm: String,   //登记医生姓名
  pub djrq: String,     //登记日期 datetime
  pub jclxdm: String,   //检查类型代码，即HisJCLX.jclxdm字段
  pub jcbwdm: String,   //检查部位代码，即HisJCBW.jcbwdm字段
  pub jcfsdm: String,   //检查方式代码，即HisJCFS.jcfsdm字段
  pub jcsbdm: String,   //检查设备代码，即HisJCSB.jcsbdm字段
  pub jcfy: f32,        //检查费用 numeric
  pub yyh: String,      //预约号
  pub yyrq: String,     //预约日期  datetime
  pub jcysgh: String,   //检查医生工号
  pub jcysxm: String,   //检查医生姓名
  pub jcrq: String,     //检查日期 datetime
  pub bgysgh: String,   //报告医生工号
  pub bgysxm: String,   //报告医生姓名
  pub bgrq: String,     //报告填写日期 datetime
  pub shysgh: String,   //审核医生工号
  pub shysxm: String,   //审核医生姓名
  pub shrq: String,     //审核日期 datetime
  pub dyysgh: String,   //打印医生工号
  pub dyysxm: String,   //打印医生姓名
  pub dyrq: String,     //打印日期 datetime
  pub jcjf: String,     //检查机房
  pub jcsb: String,     //检查设备
  pub see: String,      //检查所见
  pub jcjg: String,     //检查结果
  pub status: String,   //检查任务状态：预约、登记、检查、报告、审核
  pub studyuid: String, //检查全球唯一号：Study Instance UID
  pub sqdh: String,     //电子申请单号
  pub brbh: String,     //病人编号：根据用户配置的策略，由接口自动生成。
  pub jcdh: String,     //检查单号：根据用户配置的策略，由接口自动生成。
  pub sfzh: String,     //身份证号
  pub yqmc: String,     //院区名称
  pub zymb: String,     //转院目标
  pub lcxx: String,     //临床信息LCXX格式=提交日期^^临床信心^^病史摘要^^体检信息^^检查结果^^特殊说明^^|开单项目
  pub brtz: i32,        //病人体重
  pub jpfy: f32,        //胶片费用
  pub nlxx: String, //NLXX字段，支持由HIS系统输入年龄的详细信息。年龄信息数据规则为三位数字（不足三位补零）加年龄单位，年龄单位支持Y（年）、M（月）、W（周）、D（日）、H（小时）。举例说明，HIS系统填入020Y，表示20岁；填入030M，表示30个月；填入007W，表示7周；填入120D，表示120天；填入300H，表示300小时。注：如该字段为空，检查年龄计算采用原有计算方式。
  pub jcpd: String, //检查排队号
}

#[derive(Debug)]
pub struct LaidaPacs {
  conn: String,
  client: Client<Compat<TcpStream>>,
}

impl LaidaPacs {
  pub async fn new(default_pacs: &Extpacs) -> Result<Self> {
    let tconfig = Config::from_jdbc_string(&default_pacs.uri).expect("init from jdbc error");
    let tcp = TcpStream::connect(tconfig.get_addr()).await.expect("tcpstream connect error");
    tcp.set_nodelay(true).expect("tcp stream set no delay error");

    let client = Client::connect(tconfig, tcp.compat_write()).await.expect("connect with tiberium client error");
    Ok(LaidaPacs { conn: "".to_string(), client })
  }

  pub async fn insert(&mut self, jcrw: &HisJCRW) -> Result<u64> {
    let mut jcwr_mut = jcrw.clone();
    let ret = jcwr_mut.insert().await;
    Ok(0)
  }

  pub async fn delete(&mut self, testid: &str, lcxx: &str) -> Result<i64> {
    Ok(0)
  }
}
