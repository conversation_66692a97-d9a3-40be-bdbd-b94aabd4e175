use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok, DataBody, ResponseBody},
  auth::auth::Claims,
  common::constant::{self},
};
use axum::{Extension, Json};
use tracing::*;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{dto::*, medexam::checkallsvc::CheckallSvc};
use serde_json::Value;
use std::sync::Arc;

pub async fn query_checkalls(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<CheckallQueryDto>) -> Json<Value> {
  info!("start to query checkalls by dto:{:?}", &dto);

  let ret = CheckallSvc::query_checkalls(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjCheckallnew>::new());
  }

  let results = ret.unwrap();
  let databody = DataBody::new(results.len() as u64, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn auto_checkall(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyDto>) -> Json<Value> {
  info!("start to auto checkalls for testid:{:?}", &dto);

  let ret = CheckallSvc::auto_checkall(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjCheckallnew>::new());
  }

  let results = ret.unwrap();
  let databody = DataBody::new(1, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn save_checkall(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(info): Json<TjCheckallnew>) -> Json<Value> {
  info!("start to save checkall by dto:{:?}", &info);

  let ret = CheckallSvc::save_checkalls(&info, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }

  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn delete_checkall(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(info): Json<TjCheckallnew>) -> Json<Value> {
  info!("start to delete checkall by dto:{:?}", &info);
  let ret = CheckallSvc::delete_checkall(&info, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }

  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
