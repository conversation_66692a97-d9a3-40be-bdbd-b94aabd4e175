from sqlalchemy import (
    Column,
    Integer,
    String,
)
from dataentities.dbconn import Base


class TjDepartinfo(Base):
    # 数据库中存储的表名
    __tablename__ = "tj_departinfo"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    tj_deptid = Column(String(64), nullable=False)
    tj_deptname = Column(String(64), nullable=False, unique=False)
    tj_showorder = Column(Integer, nullable=False)
    tj_depttype = Column(Integer, nullable=False)
    tj_deptinfo = Column(String, nullable=False)
    tj_pricinple = Column(Integer, nullable=False)
    tj_deptaddr = Column(String, nullable=False)
    tj_pyjm = Column(String, nullable=False)
    tj_zdym = Column(String(256), nullable=False)
    tj_flag = Column(Integer, nullable=False)
    tj_operator = Column(Integer, nullable=False)
    tj_moddate = Column(Integer, nullable=False)
    tj_diagtype = Column(Integer, nullable=False)
    tj_reportmode = Column(Integer, nullable=False)
    tj_reportorder = Column(Integer, nullable=False)
    tj_sex = Column(Integer, nullable=False)