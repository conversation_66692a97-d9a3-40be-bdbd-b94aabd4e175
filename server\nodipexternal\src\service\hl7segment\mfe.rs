#[allow(unused)]
use crate::rusthl7::{Field, Hl7ParseError, Message, Separators};
use anyhow::Result;
use std::fmt::Display;

#[derive(Debug, PartialEq)]
pub struct MfeSegment<'a> {
  pub source: &'a str,
  pub msg_encoding_characters: Separators,
  pub mse_1_record_level_event_code: Field<'a>,
  pub mse_2_mfn_control_id: Option<Field<'a>>,
  pub mse_3_effective_datetime: Option<Field<'a>>,
  pub mse_4_primary_key_value_value: Option<Field<'a>>,
  pub mse_5_primary_key_value_type: Option<Field<'a>>,
}

impl<'a> MfeSegment<'a> {
  pub fn parse<S: Into<&'a str>>(input: S, delims: &Separators) -> Result<MfeSegment<'a>, Hl7ParseError> {
    let input = input.into();

    let mut fields = input.split(delims.field);

    assert!(fields.next().unwrap() == "MFE");

    let msh = MfeSegment {
      source: input,
      msg_encoding_characters: delims.to_owned(),
      mse_1_record_level_event_code: Field::parse_mandatory(fields.next(), delims)?,
      mse_2_mfn_control_id: Field::parse_optional(fields.next(), delims)?,
      mse_3_effective_datetime: Field::parse_optional(fields.next(), delims)?,
      mse_4_primary_key_value_value: Field::parse_optional(fields.next(), delims)?,
      mse_5_primary_key_value_type: Field::parse_optional(fields.next(), delims)?,
    };

    Ok(msh)
  }
}
/// Common formatter trait implementation for the strongly-typed segment
impl<'a> Display for MfeSegment<'a> {
  /// Required for to_string() and other formatter consumers
  fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
    write!(f, "{}", self.source)
  }
}
/// Common clone trait implementation for the strongly-typed segment
impl<'a> Clone for MfeSegment<'a> {
  /// Creates a new Message object using _the same source_ slice as the original.
  fn clone(&self) -> Self {
    let delims = self.msg_encoding_characters;
    MfeSegment::parse(self.source, &delims).unwrap()
  }
}

/// Extracts header element for external use
pub fn _mfe<'a>(msg: &Message<'a>) -> Result<MfeSegment<'a>, Hl7ParseError> {
  let seg = msg.segments_by_identifier("MFE").unwrap()[0];
  let segment = MfeSegment::parse(seg.source, &msg.get_separators()).expect("Failed to parse MFE segment");
  Ok(segment)
}
