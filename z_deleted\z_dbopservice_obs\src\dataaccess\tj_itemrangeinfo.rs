use anyhow::{anyhow, Result};
use rbatis::{py_sql};
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjItemrangeinfo {
  pub id: i64,
  pub tj_itemid: String,
  pub tj_sex: i32,
  pub tj_startage: i32,
  pub tj_endage: i32,
  pub tj_lowvalue: String,
  pub tj_uppervalue: String,
  pub tj_displowhigh: String,
}
rbatis::crud!(TjItemrangeinfo {}, "tj_itemrangeinfo");

rbatis::impl_select!(TjItemrangeinfo{query_itemrangeinfos(age:i32, sex:i32, itemids:&Vec<String>) => "
    ` where id > 0 `
    if !itemids.is_empty():
      ` and tj_itemid in ${itemids.sql()} `
    if age >= 0:
      ` and (tj_startage <= #{age} and tj_endage > #{age} `
    if sex >= 0:
      ` and tj_sex = #{sex} `
"});

impl TjItemrangeinfo {
  pub async fn save(rb: &rbatis::RBatis, info: &TjItemrangeinfo) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjItemrangeinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjItemrangeinfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }

  #[py_sql("delete from tj_itemrangeinfo where id in ${ids.sql()} ")]
  pub async fn delete(rb: &rbatis::RBatis, ids: &[i64]) -> rbatis::Result<()> {
    impled!()
  }
}
