use crate::api::respons::response_json_error;
use crate::common::filesvc::FileSvc;
use crate::config::settings::Settings;
use odipservice::dto::dto::KeyDto;
// use crate::nxmiddleware::token::Claims;
use axum::body::{self, Empty, Full, StreamBody};
use axum::extract::{Extension, Multipart, Path};
use axum::response::{IntoResponse, Response};
use axum::Json;
use headers::{HeaderMap, HeaderValue};
use hyper::{header, StatusCode};
use odipservice::response::httpresponse::{HttpCode, ResponseBody};
use serde_json::Value;
use std::fs;
use std::sync::Arc;
use tokio_util::io::ReaderStream;

// *************************
pub async fn upload_photo(Extension(config): Extension<Arc<Settings>>, multipart: Multipart) -> Json<Value> {
  info!("start to upload file to server......");

  let ret = FileSvc::do_upload("", &config.system.photodir, multipart).await;
  if ret.as_ref().is_err() {
    error!("upload photo error:{}", ret.as_ref().err().unwrap().to_string());
    return response_json_error(&ret.err().unwrap().to_string());
  }
  // while let Some(field) = multipart.next_field().await.unwrap() {
  //   info!("file name is:{:?}", &field.file_name());
  //   info!("name is:{:?}", &field.name());
  //   let filename = field.file_name();
  //   if filename.is_none() {
  //     return response_json_error("file name is empty");
  //   }
  //   let filename = filename.unwrap().to_owned();
  //   let ret = field.bytes().await;
  //   if ret.as_ref().is_err() {
  //     return response_json_error(&ret.err().unwrap().to_string());
  //   }
  //   let data = ret.unwrap();
  //   let ret = File::create(format!("{}/{}", config.system.photodir, filename));
  //   if ret.as_ref().is_err() {
  //     return response_json_error(&ret.err().unwrap().to_string());
  //   }
  //   let mut f = ret.unwrap();
  //   let ret = f.write_all(&data);
  //   if ret.as_ref().is_err() {
  //     return response_json_error(&ret.err().unwrap().to_string());
  //   }
  //   let ret = f.sync_all();
  //   if ret.as_ref().is_err() {
  //     return response_json_error(&ret.err().unwrap().to_string());
  //   }
  // }
  let res = ResponseBody::new(HttpCode::OK as i32, "OK", "");
  Json(serde_json::json!(res))
}

pub async fn download_photo(Extension(config): Extension<Arc<Settings>>, Json(dto): Json<KeyDto>) -> impl IntoResponse {
  let down_file = format!("{}/{}", config.system.photodir, dto.key);
  info!("download file is:{}", &down_file);
  let file = match tokio::fs::File::open(down_file).await {
    Ok(file) => file,
    Err(err) => {
      error!("{}, report file not found: {}", &dto.key, err);
      return Err((StatusCode::NOT_FOUND, format!("")));
    }
  };
  // convert the `AsyncRead` into a `Stream`
  info!("File is:{:?}", &file);
  let stream = ReaderStream::new(file);
  // convert the `Stream` into an `axum::body::HttpBody`
  let body = StreamBody::new(stream);

  let mut headers = HeaderMap::new();
  headers.insert(header::CONTENT_TYPE, "image/jpeg".parse().unwrap());
  //下载
  headers.insert(header::CONTENT_DISPOSITION, format!("attachment;filename=\"{}\"", dto.key).parse().unwrap());
  Ok((headers, body))
}

// *************************
pub async fn upload_file(Extension(config): Extension<Arc<Settings>>, multipart: Multipart) -> Json<Value> {
  info!("start to upload file to server......");
  let ret = FileSvc::do_upload("", &config.system.uploaddir, multipart).await;
  if ret.as_ref().is_err() {
    error!("upload photo error:{}", ret.as_ref().err().unwrap().to_string());
    return response_json_error(&ret.err().unwrap().to_string());
  }
  // while let Some(field) = multipart.next_field().await.unwrap() {
  //   info!("file name is:{:?}", &field.file_name());
  //   info!("name is:{:?}", &field.name());

  //   let filename = field.file_name();
  //   if filename.is_none() {
  //     return response_json_error("file name is empty");
  //   }
  //   let filename = filename.unwrap().to_owned();

  //   let ret = field.bytes().await;
  //   if ret.as_ref().is_err() {
  //     return response_json_error(&ret.err().unwrap().to_string());
  //   }
  //   let data = ret.unwrap();

  //   let ret = File::create(format!("{}/{}", config.system.uploaddir, filename));
  //   if ret.as_ref().is_err() {
  //     return response_json_error(&ret.err().unwrap().to_string());
  //   }
  //   let mut f = ret.unwrap();
  //   let ret = f.write_all(&data);
  //   if ret.as_ref().is_err() {
  //     return response_json_error(&ret.err().unwrap().to_string());
  //   }
  //   let ret = f.sync_all();
  //   if ret.as_ref().is_err() {
  //     return response_json_error(&ret.err().unwrap().to_string());
  //   }
  // }
  let res = ResponseBody::new(HttpCode::OK as i32, "OK", "");
  Json(serde_json::json!(res))
}

pub async fn download_files(Extension(config): Extension<Arc<Settings>>, Path(path): Path<String>) -> impl IntoResponse {
  let path = path.trim_start_matches('/');
  info!("Path is:{}", path);
  let mime_type = mime_guess::from_path(path).first_or_text_plain();
  info!("mime type:{:?}", &mime_type);

  let paths = fs::read_dir(config.system.filedir.as_str()).unwrap();

  for pth in paths {
    if pth.is_ok() {
      let file_path = pth.unwrap();

      if file_path.file_name().eq_ignore_ascii_case(path) {
        let ret = fs::read(file_path.path());
        if ret.as_ref().is_err() {
          error!("read file error:{:?}", &file_path);
          continue;
        }
        let file_content = ret.unwrap();

        info!("file path:{:?}", file_path);
        return Response::builder()
          .status(StatusCode::OK)
          .header(header::CONTENT_TYPE, HeaderValue::from_str(mime_type.as_ref()).unwrap())
          .body(body::boxed(Full::from(file_content)))
          .unwrap();
      }
    }
  }
  Response::builder().status(StatusCode::NOT_FOUND).body(body::boxed(Empty::new())).unwrap()
}

pub async fn upload_report(Extension(config): Extension<Arc<Settings>>, multipart: Multipart) -> Json<Value> {
  info!("start to upload report to server......");
  let ret = FileSvc::do_upload("", &config.system.reportdir, multipart).await;
  if ret.as_ref().is_err() {
    error!("upload report error:{}", ret.as_ref().err().unwrap().to_string());
    return response_json_error(&ret.err().unwrap().to_string());
  }
  let res = ResponseBody::new(HttpCode::OK as i32, "OK", "");
  Json(serde_json::json!(res))
}
