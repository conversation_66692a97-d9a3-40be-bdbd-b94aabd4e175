// use dataservice::dbinit::DbConnection;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
// use rust_decimal::prelude::ToPrimitive;
// use rust_decimal::prelude::ToPrimitive;
use utility::timeutil;
use tracing::*;

use super::patientsvc::PatientSvc;
use crate::common::constant;
use anyhow::{anyhow, Result};

pub struct HealthycardSvc;

impl HealthycardSvc {
  pub async fn query_latest_hcard_by_idcard(idcard: &str, db: &DbConnection) -> Result<TjHealthycardinfo> {
    let ret = TjHealthycardinfo::query_latest(&idcard, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let hc_ret = ret.unwrap();
    if hc_ret.is_none() {
      // return Ok(TjHealthycardinfo { ..Default::default() });
      //如果没有，则从体检信息表中查找上一次的健康证体检
      info!("健康证表中没有该体检者的健康证信息，从体检信息表中查找");
      let ret = TjMedexaminfo::query_many_by_idcard_testtype(idcard, constant::TestType::JKZ as i32, constant::ExamStatus::Register as i32, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      if let Ok(medinfos) = ret {
        if medinfos.len() <= 0 {
          return Ok(TjHealthycardinfo { ..Default::default() });
        }
        //get the latest one
        let latestone = medinfos.iter().max_by_key(|f| f.id);
        if latestone.is_none() {
          return Ok(TjHealthycardinfo { ..Default::default() });
        }
        let latest_medinfo = latestone.unwrap();

        let ret = TjCheckallnew::query(&latest_medinfo.tj_testid, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
        let latest_ckall = ret.unwrap();
        if latest_ckall.is_none() {
          return Ok(TjHealthycardinfo { ..Default::default() });
        }
        let latest_ckall = latest_ckall.unwrap();
        if latest_ckall.tj_typeid != constant::CheckResultType::Normal as i32 && latest_ckall.tj_typeid != constant::CheckResultType::Other as i32 {
          //上一次健康证体检不合格
          return Ok(TjHealthycardinfo { ..Default::default() });
        }
        let hcinfo = TjHealthycardinfo {
          id: 0,
          tj_testid: latest_medinfo.tj_testid.to_owned(),
          tj_pid: "".to_string(),
          tj_pname: "".to_string(),
          tj_pidcard: idcard.to_string(),
          tj_meddate: latest_medinfo.tj_testdate,
          tj_expdate: timeutil::timestamp_add_days(latest_medinfo.tj_testdate, 365),
          tj_opdate: timeutil::current_timestamp(),
          tj_operator: 0,
        };
        // let ret = TjHealthycardinfo::save(&hcinfo, &db.get_connection()).await;
        // if ret.as_ref().is_err() {
        //   error!("{}", ret.as_ref().unwrap_err().to_string());
        //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        // }
        return Ok(hcinfo);
      }
    }
    Ok(hc_ret.unwrap())
  }

  pub async fn save_hcardinfo(medinfo: &TjMedexaminfo, ckinfo: &TjCheckallnew, db: &DbConnection) -> Result<i64> {
    if medinfo.tj_testtype != constant::TestType::JKZ as i32 {
      return Err(anyhow!("非健康证体检，无需制作健康证信息"));
    }
    // let ret = CheckallSvc::query_checkall_by_testid(&medinfo.tj_testid, db).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let ck_retinfo = ret.unwrap();
    // if ck_retinfo.is_none() {
    //   return Err(anyhow!("不能找到该体检号的总检信息"));
    // }
    // let ckinfo = ck_retinfo.unwrap();
    if ckinfo.tj_typeid == constant::CheckResultType::Recheck as i32
      || ckinfo.tj_typeid == constant::CheckResultType::Forbidden as i32
      || ckinfo.tj_typeid == constant::CheckResultType::Oculike as i32
      || ckinfo.tj_typeid == constant::CheckResultType::Addional as i32
    {
      return Err(anyhow!("检查结果异常，不满足制作健康证的要求"));
    }
    let ret = PatientSvc::query_patient_by_testid(&medinfo.tj_testid, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let retinfo = ret.unwrap();
    if retinfo.is_none() {
      return Err(anyhow!("不能找到该体检号的体检人员信息"));
    }
    let ptinfo = retinfo.unwrap();

    let ret = HealthycardSvc::delete_hcardinfo(&medinfo.tj_testid, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }

    let hcinfo = TjHealthycardinfo {
      tj_testid: medinfo.tj_testid.to_owned(),
      tj_pid: ptinfo.tj_pid.to_owned(),
      tj_pname: ptinfo.tj_pname.to_owned(),
      tj_pidcard: ptinfo.tj_pidcard.to_owned(),
      tj_meddate: medinfo.tj_testdate,
      tj_expdate: utility::timeutil::timestamp_add_days(medinfo.tj_testdate, 365),
      tj_opdate: utility::timeutil::current_timestamp(),
      tj_operator: 0,
      id: 0,
    };

    let ret = TjHealthycardinfo::save(&hcinfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn delete_hcardinfo(testid: &str, db: &DbConnection) -> Result<u64> {
    if testid.is_empty() {
      return Err(anyhow!("testid is empty, not allowed"));
    }
    let ret = TjHealthycardinfo::delete(testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
}
