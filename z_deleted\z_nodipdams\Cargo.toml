[package]
name = "nodipdams"
version = "0.1.0"
edition = "2021"
description = "build time: 2024-11-02"
resolver = "2"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { workspace = true }
dataservice = { workspace = true }
nodipservice = { workspace = true }

serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tokio = { workspace = true, features = ["full"] }
tokio-util = { workspace = true, features = ["full"] }

axum = { workspace = true, features = ["multipart", "tokio", "original-uri"] }
axum-extra = { workspace = true, features = ["typed-header"] }

tower = { workspace = true, features = ["util", "filter"] }
hyper = { workspace = true, features = ["full"] }
hyper-util = { version = "0.1", features = ["client-legacy"] }
tower-http = { workspace = true, features = ["trace", "fs", "cors"] }
headers = { workspace = true }
http-body-util = { workspace = true }
futures = { workspace = true }

anyhow = { workspace = true }
thiserror = { workspace = true }
config = { workspace = true }
log = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter"] }
jsonwebtoken = { workspace = true }
# chrono = "0.4"
mime_guess = { workspace = true }
itertools = { workspace = true }
reqwest = { workspace = true, default-features = false, features = [
    "json",
    "stream",
    "multipart",
    "rustls-tls",
] }

uuid = { workspace = true }
rust_decimal = { workspace = true }

chrono = { workspace = true }
strum = { workspace = true }

base64 = "0.22"
