use anyhow::{anyhow, Result};
use rbatis::rbatis_codegen::IntoSql;
use rbatis::{crud, py_sql, rbdc::db::ExecResult};
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjDepartinfo {
  pub id: i64,
  pub tj_deptid: String,
  pub tj_deptname: String,
  pub tj_showorder: i32,
  pub tj_depttype: i32,
  pub tj_deptinfo: String,
  pub tj_pricinple: i32,
  pub tj_deptaddr: String,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_flag: i32,
  pub tj_operator: i32,
  pub tj_moddate: i64,
  pub tj_diagtype: i32,
  pub tj_reportmode: i32,
  pub tj_reportorder: i32,
  pub tj_sex: i32,
}
crud!(TjDepartinfo {}, "tj_departinfo");
rbatis::impl_select!(TjDepartinfo{query(deptid:&str) ->Option => "`where tj_deptid = #{deptid} limit 1`"});
rbatis::impl_select!(TjDepartinfo{query_many(ids:&[i64], deptids:&[String], depttype:i32) => 
  "`where tj_flag >= 0 `
   if !deptids.is_empty():
    ` and tj_deptid in ${deptids.sql()} `   
   if !ids.is_empty():
    ` and id in ${ids.sql()} `  
  if depttype > 0:
    ` and tj_depttype = #{depttype} `"});
// rbatis::impl_update!(TjDepartinfo{query(deptid:&str) => "`where tj_deptid = #{deptid} `"});
impl TjDepartinfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjDepartinfo) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjDepartinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      // ret.unwrap().rows_affected
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjDepartinfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }

  #[py_sql("update tj_departinfo set tj_flag = -1 where id in ${ids.sql()} ")]
  pub async fn delete(rb: &mut rbatis::RBatis, ids: &[i64]) -> Result<ExecResult, rbatis::Error> {
    impled!()
  }
}
