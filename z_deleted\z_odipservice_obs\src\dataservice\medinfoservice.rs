use std::sync::Arc;

use anyhow::{anyhow, Result};
use datacontroller::datasetup::DbConnection;
use utility::timeutil::current_timestamp;
pub struct MedinfoService {}

impl MedinfoService {
    pub async fn update_medinfo_upload_status(testid: &Vec<String>, dbconn: &Arc<DbConnection>) -> Result<()> {
        let testids = testid.iter().map(|x| format!("'{}'", x)).collect::<Vec<String>>().join(",");
        // info!("testids:{}", &testids);
        let sqlstr = format!(
            "update tj_medexaminfo set tj_upload = {},tj_uploadtime={} where tj_testid in ({})",
            1,
            current_timestamp(),
            testids
        );
        let ret = dbconn.execute_sql(sqlstr.as_str()).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("update error:{}", ret.as_ref().err().unwrap().to_string()));
        }
        Ok(())
    }
}
