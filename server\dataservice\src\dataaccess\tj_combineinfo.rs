use crate::entities::{prelude::*, tj_combineinfo};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjCombineinfo {
  pub async fn query_many(code: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjCombineinfo>> {
    // if code.len() <= 0 {
    //   return Ok(vec![]);
    // }
    let mut conditions = Condition::all().add(tj_combineinfo::Column::Id.gt(0));
    if code.len() > 0 {
      conditions = conditions.add(tj_combineinfo::Column::TjCombid.is_in(code.to_owned()));
    }
    let ret = TjCombineinfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_many(info: &Vec<TjCombineinfo>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = info.len();
    let (insert_vals, update_vals): (Vec<TjCombineinfo>, Vec<TjCombineinfo>) = info.to_owned().into_iter().partition(|f| f.id == 0);

    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_combineinfo::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_combineinfo::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjCombineinfoEntity::insert_many(active_insert_values).exec(db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_combineinfo::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    Ok(total as i64)
  }

  pub async fn save(info: &TjCombineinfo, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_combineinfo::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    // info!("new val is:{:#?}", &newval);
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn delete_by_combid(combid: &String, db: &DatabaseConnection) -> Result<u64> {
    if combid.is_empty() {
      return Err(anyhow!("combid is empty, not allowed"));
    }
    let conditions = Condition::all().add(tj_combineinfo::Column::TjCombid.eq(combid));
    let ret = TjCombineinfoEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
  pub async fn delete_by_itemid(itemid: &String, db: &DatabaseConnection) -> Result<u64> {
    if itemid.is_empty() {
      return Err(anyhow!("itemid is empty, not allowed"));
    }
    let conditions = Condition::all().add(tj_combineinfo::Column::TjItemid.eq(itemid));
    let ret = TjCombineinfoEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let conditions = Condition::all().add(tj_combineinfo::Column::Id.is_in(ids.to_owned()));
    let ret = TjCombineinfoEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
