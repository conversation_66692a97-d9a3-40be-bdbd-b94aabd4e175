//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_occupationhistory")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_testid: String,
  pub tj_startdate: i32,
  pub tj_enddate: i32,
  pub tj_corpname: String,
  pub tj_workshop: String,
  pub tj_worktype: String,
  pub tj_worktypename: String,
  pub tj_harmful: String,
  pub tj_harmfulname: String,
  pub tj_protective: String,
  pub tj_raddaynum: String,
  pub tj_radtotalnum: String,
  pub tj_radoverdose: String,
  pub tj_radexposure: String,
  pub tj_radcode: String,
  pub tj_radtype: String,
  pub tj_radprotective: String,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
