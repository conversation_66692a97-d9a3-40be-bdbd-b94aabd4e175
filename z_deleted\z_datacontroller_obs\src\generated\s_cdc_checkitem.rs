//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "s_cdc_checkitem")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub cdc_itemcode: String,
    pub cdc_item_name: String,
    pub cdc_deptid: i32,
    pub cdc_type: i32,
    pub tj_itemid: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
