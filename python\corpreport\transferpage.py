from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.units import inch

# from reportlab.rl_config import defaultPageSize
from reportlab.lib.styles import getSampleStyleSheet
import os


def generate_transfer_file(rptinfo, outdir):
    # PAGE_HEIGHT = defaultPageSize[1]
    # PAGE_WIDTH = defaultPageSize[0]
    outfile = "{}.pdf".format(rptinfo.tj_reportnumint)
    output = os.path.join(outdir, outfile)
    styles = getSampleStyleSheet()
    doc = SimpleDocTemplate(output)
    Story = [Spacer(1, 2 * inch)]
    style = styles["Normal"]
    for i in range(100):
        bogustext = ("This is Paragraph number %s. " % i) * 20
        p = Paragraph(bogustext, style)
        Story.append(p)
        Story.append(Spacer(1, 0.2 * inch))
    doc.build(Story)
