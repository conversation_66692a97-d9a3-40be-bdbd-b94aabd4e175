//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_iteminfo")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    #[sea_orm(unique)]
    pub tj_itemid: String,
    pub tj_itemtype: i32,
    pub tj_itemname: String,
    pub tj_itemunit: String,
    pub tj_showorder: i32,
    pub tj_sex: i32,
    pub tj_itemprice: f32,
    pub tj_valuetype: String,
    pub tj_defaultresult: String,
    pub tj_reftype: String,
    pub tj_uppervalue: String,
    pub tj_lowvalue: String,
    pub tj_highoffset: String,
    pub tj_lowoffset: String,
    pub tj_okflag: i32,
    pub tj_barflag: i32,
    pub tj_combineflag: i32,
    pub tj_autodiagflag: i32,
    pub tj_lisnum: String,
    pub tj_pyjm: String,
    pub tj_zdym: String,
    pub tj_memo: String,
    pub tj_operator: i32,
    pub tj_moddate: i64,
    pub sample_code: String,
    pub tj_extcode: String,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::tj_checkiteminfo::Entity")]
    TjCheckiteminfo,
    #[sea_orm(has_many = "super::tj_hazarditem::Entity")]
    TjHazarditem,
    #[sea_orm(has_many = "super::tj_itemrefinfo::Entity")]
    TjItemrefinfo,
    #[sea_orm(has_many = "super::tj_packagedetail::Entity")]
    TjPackagedetail,
}

impl Related<super::tj_checkiteminfo::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjCheckiteminfo.def()
    }
}

impl Related<super::tj_hazarditem::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjHazarditem.def()
    }
}

impl Related<super::tj_itemrefinfo::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjItemrefinfo.def()
    }
}

impl Related<super::tj_packagedetail::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjPackagedetail.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
