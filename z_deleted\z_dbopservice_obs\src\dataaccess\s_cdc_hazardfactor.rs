// use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(C<PERSON>, Default, Debug, PartialEq, Serialize, Deserialize)]
pub struct SCdcHazardfactor {
  pub id: i64,
  pub cdc_code: String,
  pub cdc_hazardname: String,
  pub cdc_memo: String,
  pub cdc_initmonitor: i32,
  pub hazard_type: i32,
}
crud!(SCdcHazardfactor {}, "s_cdc_hazardfactor");
rbatis::impl_select!(SCdcHazardfactor{query_many(hdcodes:&Vec<String>, monitor:i32) => "`where cdc_code in ${hdcodes.sql()} and cdc_initmonitor = #{monitor} `"});
