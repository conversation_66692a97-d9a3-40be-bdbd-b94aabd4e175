use crate::{config::settings::Settings, nxmiddleware::printmiddleware::print_request_response, service::externalservice::DftMessage};
use axum::{extract::Extension, middleware, routing::post, Router};
// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
use std::sync::Arc;
use utility::uidservice::UidgenService;

use tokio::sync::RwLock;
// use odipservice::prelude::DictService;
use tower::ServiceBuilder;

use super::v1::externalcontroller;

pub fn create_route(
  dbconn: Arc<DbConnection>,
  setting: Arc<Settings>,
  uid: Arc<RwLock<UidgenService>>,
  tx: Arc<futures::channel::mpsc::UnboundedSender<String>>,
  txp03: Arc<futures::channel::mpsc::UnboundedSender<DftMessage>>,
) -> Router {
  let v2_route = create_v2_route();
  // let dbconn = Arc::new(db);
  let app = Router::new()
    .nest("/", v2_route)
    .layer(middleware::from_fn(print_request_response))
    .layer(ServiceBuilder::new().layer(Extension(dbconn)))
    // .layer(ServiceBuilder::new().layer(Extension(syscache)))
    .layer(ServiceBuilder::new().layer(Extension(setting)))
    .layer(ServiceBuilder::new().layer(Extension(uid)))
    .layer(ServiceBuilder::new().layer(Extension(tx)))
    .layer(ServiceBuilder::new().layer(Extension(txp03)));
  // add a fallback service for handling routes to unknown paths
  // let app = app.fallback(handler_404.into_service());
  app
}

fn create_v2_route() -> Router {
  let medinfo_route = Router::new().nest(
    "/external",
    Router::new()
      .route(
        "/",
        // post(medexamcontroller::insert_medexaminfo).
        post(externalcontroller::external_extend).delete(externalcontroller::external_extend),
      )
      .route("/query", post(externalcontroller::external_extend)),
  );

  let app_route = Router::new().nest("/apiext/v1", Router::new().merge(medinfo_route));

  app_route
}
