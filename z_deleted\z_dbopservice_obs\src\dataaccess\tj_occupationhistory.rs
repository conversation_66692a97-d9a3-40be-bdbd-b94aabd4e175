use anyhow::{anyhow, Result};
use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjOccupationhistory {
  pub id: i64,
  pub tj_testid: String,
  pub tj_startdate: i32,
  pub tj_enddate: i32,
  pub tj_corpname: String,
  pub tj_workshop: String,
  pub tj_worktype: String,
  pub tj_worktypename: String,
  pub tj_harmful: String,
  pub tj_harmfulname: String,
  pub tj_protective: String,
  pub tj_raddaynum: String,
  pub tj_radtotalnum: String,
  pub tj_radoverdose: String,
  pub tj_radexposure: String,
  pub tj_radcode: String,
  pub tj_radtype: String,
  pub tj_radprotective: String,
}
crud!(TjOccupationhistory {}, "tj_occupationhistory");
rbatis::impl_delete!(TjOccupationhistory{delete(testid:&str) => "`where tj_testid = #{testid} `"});
rbatis::impl_select!(TjOccupationhistory{query(testid:&str) => "`where tj_testid = #{testid} `"});

impl TjOccupationhistory {
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<TjOccupationhistory>) -> Result<i64> {
    if infos.len() <= 0 {
      // return Err(anyhow!("empty data"));
      return Ok(0);
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<TjOccupationhistory>, Vec<TjOccupationhistory>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = TjOccupationhistory::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = TjOccupationhistory::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }
}
