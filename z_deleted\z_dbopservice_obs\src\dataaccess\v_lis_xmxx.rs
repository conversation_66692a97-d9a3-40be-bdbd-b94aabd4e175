use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub struct VLisXmxx {
  // pub id: i32,
  pub itemid: String,
  pub chinesename: String,
  pub englishab: String,
  pub unit: String,
}
crud!(VLisXmxx {}, "v_lis_xmxx");
// rbatis::impl_select!(VLisXmxx{query_many() => "`where id > 0 `"});
