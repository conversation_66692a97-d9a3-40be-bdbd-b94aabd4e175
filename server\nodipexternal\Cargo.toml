[package]
name = "nodipexternal"
version = "0.10.0"
edition = "2021"
description = "build time: 2024-04-03"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[features]
string_index = []

[dependencies]
utility = { path = "../utility" }
dataservice = { path = "../dataservice" }
# dbopservice = { path = "../dbopservice" }
nodipservice = { path = "../nodipservice" }

serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1", features = ["full"] }
tokio-util = { version = "0.7", features = ["full"] }


axum = { version = "0.7.2", features = ["multipart", "tokio", "original-uri"] }
axum-extra = { version = "0.9.0", features = ["typed-header"] }

tower = { version = "0.4", features = ["util", "filter"] }
hyper = { version = "1.0.1", features = ["full"] }
hyper-util = { version = "0.1.1", features = ["client-legacy"] }
tower-http = { version = "0.5.0", features = ["trace", "fs"] }
headers = "0.4"
http-body-util = "0.1.0"

anyhow = "1.0"
thiserror = "1.0"
# dashmap = "5"
log = "0.4"
log4rs = "1"
config = "0.14.0"
moka = { version = "0.12.1", features = ["future"] }

# rust-hl7 = "0.5"
hl7-mllp-codec = "0.4"
regex = "1.5"

bytes = "1"
bytes-utils = "0.1"
futures = "0.3"
# lazy_static = "1"
# once_cell = "1.17.2"
hex = "0.4"
strum = { version = "0.26.2", features = ["derive"] }

[dependencies.uuid]
version = "1"
features = [
    "v4",                # Lets you generate random UUIDs
    "fast-rng",          # Use a faster (but still sufficiently random) RNG
    "macro-diagnostics", # Enable better diagnostics for compile-time UUIDs
]
