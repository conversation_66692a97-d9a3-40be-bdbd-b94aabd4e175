use anyhow::{anyhow, Result};
use chrono::{Duration, Local};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};

use crate::{
  common,
  dto::{KeyIntDto, KeyIntsDto, KeysDto},
  SYSCACHE,
};

pub struct AkaSvc;

impl AkaSvc {
  pub async fn query_autodiagconditions(dto: &Vec<i64>, db: &DbConnection) -> Result<Vec<TjAutodiagcondition>> {
    info!("query autodiag condition parameters:{dto:?}");

    let infos = crate::SYSCACHE.get().unwrap().get_autodiag_conditions(&dto, &db).await;

    Ok(infos)
  }
  pub async fn save_autodiagconditions(infos: &TjAutodiagcondition, db: &DbConnection) -> Result<i64> {
    info!("save auto diagconditions :{infos:?}");
    let ret = TjAutodiagcondition::save(&infos, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(crate::common::constant::CacheType::AutoDiag as i32, &dbclone).await;
    });
    Ok(ret.unwrap())
  }

  pub async fn delete_autodiagconditions(ids: &Vec<i64>, db: &DbConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let ret = TjAutodiagcondition::delete(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(crate::common::constant::CacheType::AutoDiag as i32, &dbclone).await;
    });
    Ok(ids.len() as u64)
  }

  pub async fn query_audiogramrevises(db: &DbConnection) -> Result<Vec<TjAudiogramrevise>> {
    let ret = TjAudiogramrevise::query_many(&db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_areainfos(_key: &String, areacodes: &Vec<String>, db: &DbConnection) -> Result<Vec<SsArea>> {
    let areas = SYSCACHE.get().unwrap().get_areas_by_codes(&areacodes, &db).await;
    Ok(areas)
  }

  pub async fn query_monitorinfos(_db: &DbConnection) -> Result<Vec<SsMonitor>> {
    let areas = SYSCACHE.get().unwrap().get_monitors().await;
    Ok(areas)
  }
  pub async fn query_external_configs(db: &DbConnection) -> Result<Vec<TjExternal>> {
    let ret = TjExternal::query_many(0, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn query_diseases(disids: &Vec<i64>, deptids: &Vec<i64>, typeids: &Vec<i64>, db: &DbConnection) -> Result<Vec<TjDiseases>> {
    // let ret = TjDiseases::query_many(&mut db.get_connection(), &dto).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let infos = ret.unwrap();
    let infos = crate::SYSCACHE.get().unwrap().get_diseases(disids, deptids, typeids, &db).await;
    Ok(infos)
  }

  pub async fn save_diseases(info: &TjDiseases, db: &DbConnection) -> Result<i64> {
    let ret = TjDiseases::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(crate::common::constant::CacheType::Diseases as i32, &dbclone).await;
    });
    Ok(ret.unwrap())
  }

  pub async fn delete_diseases(info: &TjDiseases, db: &DbConnection) -> Result<u64> {
    // let ids: Vec<i64> = vec![info.id];
    let ret = TjDiseases::delete(&vec![info.id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(crate::common::constant::CacheType::Diseases as i32, &dbclone).await;
    });
    Ok(1 as u64)
  }
  //law
  pub async fn query_lawinfos(_db: &DbConnection) -> Result<Vec<TjEvallaw>> {
    let infos = crate::SYSCACHE.get().unwrap().get_evallaws().await;
    Ok(infos)
  }

  pub async fn save_lawinfo(info: &TjEvallaw, db: &DbConnection) -> Result<i64> {
    let ret = TjEvallaw::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      info!("start to recache lawinfos......");
      crate::SYSCACHE.get().unwrap().update_caches(crate::common::constant::CacheType::EvalLaw as i32, &dbclone).await;
    });
    Ok(ret.unwrap())
  }
  pub async fn delete_lawinfos(infos: &Vec<TjEvallaw>, db: &DbConnection) -> Result<u64> {
    // let ids: Vec<i64> = vec![info.id];
    let ids = infos.iter().map(|v| v.id).collect();
    let ret = TjEvallaw::delete(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(crate::common::constant::CacheType::EvalLaw as i32, &dbclone).await;
    });
    Ok(1 as u64)
  }
  pub async fn delete_lawinfo(info: &TjEvallaw, db: &DbConnection) -> Result<u64> {
    // let ids: Vec<i64> = vec![info.id];
    let ret = TjEvallaw::delete(&vec![info.id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(crate::common::constant::CacheType::EvalLaw as i32, &dbclone).await;
    });
    Ok(1 as u64)
  }
  //guide
  pub async fn query_guideinfos(dto: &KeyIntsDto, db: &DbConnection) -> Result<Vec<TjGuideinfo>> {
    let ret = crate::SYSCACHE.get().unwrap().get_guideinfos(&dto.keys, &db).await;
    Ok(ret)
    // let ret = TjGuideinfo::query_many(&mut db.get_connection(), &dto.keys).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
  }

  pub async fn save_guideinfo(dto: &TjGuideinfo, db: &DbConnection) -> Result<i64> {
    // let retid: i64; // = 0;
    // if dto.id <= 0 {
    //   let ret = TjGuideinfo::insert( &dto,&db.get_connection()).await;
    //   if ret.as_ref().is_err() {
    //     error!("{}", ret.as_ref().unwrap_err().to_string());
    //     return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    //   }
    //   retid = ret.unwrap().last_insert_id.into();
    // } else {
    //   let ret = TjGuideinfo::update_by_column(&mut db.get_connection(), &dto, "id").await;
    //   if ret.as_ref().is_err() {
    //     error!("{}", ret.as_ref().unwrap_err().to_string());
    //     return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    //   }
    //   retid = ret.unwrap().rows_affected as i64;
    // }
    let ret = TjGuideinfo::save(&dto, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(common::constant::CacheType::Guideinfo as i32, &dbclone).await;
    });

    Ok(ret.unwrap())
  }

  pub async fn delete_guideinfo(dto: &TjGuideinfo, db: &DbConnection) -> Result<i64> {
    if dto.id <= 0 {
      return Err(anyhow!("ID <= 0,not allowed"));
    }
    let ret = TjGuideinfo::delete(&vec![dto.id], &db.get_connection()).await;
    // let ret = TjGuideinfo::delete_by_column(&mut db.get_connection(), "id", &dto.id).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(common::constant::CacheType::Guideinfo as i32, &dbclone).await;
    });
    Ok(ret.unwrap() as i64)
  }
  pub async fn query_guide_items(dto: &KeyIntDto, db: &DbConnection) -> Result<Vec<TjGuideitem>> {
    let ret = crate::SYSCACHE.get().unwrap().get_guideitems(dto.key, &db).await;
    Ok(ret)
    // let ret = TjGuideitem::query_many(&mut db.get_connection(), &vec![dto.key]).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
  }
  pub async fn save_guide_items(dtos: &Vec<TjGuideitem>, db: &DbConnection) -> Result<i64> {
    if dtos.len() <= 0 {
      return Err(anyhow!("items are empty, not allowed"));
    }
    // let guideid = dtos[0].tj_guideid;

    // let ret = TjGuideitem::select_by_column(&mut db.get_connection(), "tj_guideid", guideid).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let exist_items = ret.unwrap();
    // let insert_gitems: Vec<TjGuideitem> = dtos
    //   .iter()
    //   .filter(|&f1| exist_items.iter().find(|&f| f.tj_itemid.eq_ignore_ascii_case(&f1.tj_itemid)).is_none())
    //   .map(|v| v.to_owned())
    //   .collect();
    // if insert_gitems.len() > 0 {
    let ret = TjGuideitem::save_many(&dtos, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    // }
    // let delete_gitems: Vec<TjGuideitem> = exist_items
    //   .iter()
    //   .filter(|&f| dtos.iter().find(|&f1| f1.tj_itemid.eq_ignore_ascii_case(&f.tj_itemid)).is_none())
    //   .map(|v| v.to_owned())
    //   .collect();
    // if delete_gitems.len() > 0 {
    //   let delids = delete_gitems.into_iter().map(|v| v.id).collect::<Vec<i64>>();
    //   let ret = TjGuideitem::delete_by_column_batch(&mut db.get_connection(), "id", &delids).await;
    //   if ret.as_ref().is_err() {
    //     error!("{}", ret.as_ref().unwrap_err().to_string());
    //     return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    //   }
    // }
    // let ret = TjGuideitem::select_by_column(&mut db.get_connection(), "tj_guideid", guideid).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(common::constant::CacheType::GuideItem as i32, &dbclone).await;
    });
    Ok(dtos.len() as i64)
  }
  pub async fn delete_guide_items(guideids: &Vec<i64>, itemids: &Vec<String>, db: &DbConnection) -> Result<i64> {
    let ret = TjGuideitem::delete(&guideids, &itemids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(common::constant::CacheType::GuideItem as i32, &dbclone).await;
    });
    Ok(ret.unwrap() as i64)
  }
  //bar
  pub async fn query_barinfos(dto: &KeyIntsDto, db: &DbConnection) -> Result<Vec<TjBarnameinfo>> {
    let ret = crate::SYSCACHE.get().unwrap().get_barnames(&dto.keys, &db).await;
    Ok(ret)
    // let ret = TjBarnameinfo::query_many(&mut db.get_connection(), &dto.keys).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
  }
  pub async fn save_barnameinfo(info: &TjBarnameinfo, db: &DbConnection) -> Result<i64> {
    let ret = TjBarnameinfo::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(common::constant::CacheType::Barname as i32, &dbclone).await;
    });

    Ok(ret.unwrap())
  }

  pub async fn delete_barnameinfo(dto: &TjBarnameinfo, db: &DbConnection) -> Result<i64> {
    if dto.id <= 0 {
      return Err(anyhow!("ID <= 0,not allowed"));
    }
    let ret = TjBarnameinfo::delete(dto.id, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(common::constant::CacheType::Barname as i32, &dbclone).await;
    });
    Ok(ret.unwrap() as i64)
  }

  pub async fn query_baritems(dto: &KeysDto, db: &DbConnection) -> Result<Vec<TjBaritems>> {
    let ret = crate::SYSCACHE.get().unwrap().get_baritems(&dto.keys, &db).await;
    Ok(ret)
    // let ret = TjBaritems::query_many(&mut db.get_connection(), &dto.keys).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
  }
  pub async fn save_baritem(dto: &TjBaritems, db: &DbConnection) -> Result<i64> {
    // let retid: i64; // = 0;
    // if dto.id <= 0 {
    //   let ret = TjBaritems::insert(&mut db.get_connection(), &dto).await;
    //   if ret.as_ref().is_err() {
    //     error!("{}", ret.as_ref().unwrap_err().to_string());
    //     return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    //   }
    //   retid = ret.unwrap().last_insert_id.into();
    // } else {
    //   let ret = TjBaritems::update_by_column(&mut db.get_connection(), &dto, "id").await;
    //   if ret.as_ref().is_err() {
    //     error!("{}", ret.as_ref().unwrap_err().to_string());
    //     return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    //   }
    //   retid = ret.unwrap().rows_affected as i64;
    // }
    let ret = TjBaritems::save(dto, &db.get_connection()).await;
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(common::constant::CacheType::Baritem as i32, &dbclone).await;
    });

    Ok(ret.unwrap())
  }
  pub async fn delete_baritem(dto: &TjBaritems, db: &DbConnection) -> Result<i64> {
    if dto.id <= 0 {
      return Err(anyhow!("ID <= 0,not allowed"));
    }
    if dto.tj_binum.is_empty() {
      return Err(anyhow!("invalid baritems"));
    }
    let ret = TjBardetail::delete("", &dto.tj_binum, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let ret = TjBaritems::delete(dto.id, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(common::constant::CacheType::Baritem as i32, &dbclone).await;
    });
    Ok(ret.unwrap() as i64)
  }

  pub async fn query_bardetails(dto: &KeysDto, db: &DbConnection) -> Result<Vec<TjBardetail>> {
    let ret = crate::SYSCACHE.get().unwrap().get_bardetails(&dto.keys, &db).await;
    Ok(ret)
    // let ret = TjBardetail::query_many(&mut db.get_connection(), &dto.keys).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
  }

  pub async fn save_bardetail(info: &TjBardetail, db: &DbConnection) -> Result<i64> {
    let ret = TjBardetail::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    // let retid: i64; // = 0;
    // if dto.id <= 0 {
    //   let ret = TjBardetail::insert(&mut db.get_connection(), &dto).await;
    //   if ret.as_ref().is_err() {
    //     error!("{}", ret.as_ref().unwrap_err().to_string());
    //     return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    //   }
    //   retid = ret.unwrap().last_insert_id.into();
    // } else {
    //   let ret = TjBardetail::update_by_column(&mut db.get_connection(), &dto, "id").await;
    //   if ret.as_ref().is_err() {
    //     error!("{}", ret.as_ref().unwrap_err().to_string());
    //     return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    //   }
    //   retid = ret.unwrap().rows_affected as i64;
    // }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(common::constant::CacheType::Bardetails as i32, &dbclone).await;
    });

    Ok(ret.unwrap())
  }

  pub async fn delete_bardetail(dto: &TjBardetail, db: &DbConnection) -> Result<i64> {
    // if dto.id <= 0 {
    //   return Err(anyhow!("ID <= 0,not allowed"));
    // }
    let ret = TjBardetail::delete(&dto.tj_itemid, &dto.tj_binum, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(common::constant::CacheType::Bardetails as i32, &dbclone).await;
    });
    Ok(ret.unwrap() as i64)
  }

  pub async fn clear_data(db: &DbConnection) {
    let local_now = Local::now();
    // println!("local now is:{:?}", &local_now);
    if let Some(checked_add) = local_now.checked_add_signed(Duration::days(-365)) {
      let one_year_before = checked_add.timestamp();
      info!("one year before time stamp is:{}", &one_year_before);
      //2)清理ext_checkiteminfo
      let ret = ExtCheckiteminfo::clear(one_year_before, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("clear ExtCheckiteminfo error:{}", ret.as_ref().unwrap_err().to_string());
      }
    }

    if let Some(checked_add) = local_now.checked_add_signed(Duration::days(-100)) {
      let one_year_before = checked_add.timestamp();
      info!("100 days before time stamp is:{}", &one_year_before);
      //1)清理tj_labresult,
      let ret = TjLabresult::clear(one_year_before, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("clear labresult error:{}", ret.as_ref().unwrap_err().to_string());
      }
      //tj_pacsresult
      let ret = TjPacsresult::clear(one_year_before, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("clear pacsresult error:{}", ret.as_ref().unwrap_err().to_string());
      }
      //3)清理v_tj_lisresult
      //4)清理v_tj_pacsresult
      let one_year_before_time = utility::timeutil::format_timestamp(one_year_before);
      let ret = VTjLisresult::clear(&one_year_before_time, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("clear VTjLisresult error:{}", ret.as_ref().unwrap_err().to_string());
      }
      let ret = VTjPacsresult::clear(&one_year_before_time, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("clear VTjPacsresult error:{}", ret.as_ref().unwrap_err().to_string());
      }
    }

    // Ok(())
  }

  pub async fn query_occuconditions(db: &DbConnection) -> Result<Vec<TjOccucondition>> {
    let ret = TjOccucondition::query_many("", &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("get error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save_occuconditions(info: &TjOccucondition, db: &DbConnection) -> Result<i64> {
    let ret = TjOccucondition::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("get error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(common::constant::CacheType::Occucond as i32, &dbclone).await;
    });
    Ok(ret.unwrap())
  }
}
