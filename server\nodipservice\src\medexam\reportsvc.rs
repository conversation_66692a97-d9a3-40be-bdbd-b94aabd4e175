use std::collections::HashMap;

use anyhow::{anyhow, Result};
use tracing::*;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};

use crate::dto::CorpQueryDto;
pub struct ReportSvc;

impl ReportSvc {
  pub async fn query_reports(dto: &CorpQueryDto, db: &DbConnection) -> Result<Vec<TjCorpoccureport>> {
    // let dict = SYSCACHE.get().unwrap().get_dict(1, 1, &db).await;
    // info!("get dicts ...................................:{dict:?}");
    let pname = dto.pname.to_string();
    let mut testids: Vec<String> = Vec::new();
    let medtestids: Vec<String>;
    if !pname.is_empty() {
      let ret = TjMedexaminfo::query_many(0, 0, &vec![], &vec![], -1, -1, &vec![], &vec![], &vec![], -1, &pname, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      medtestids = ret.unwrap().into_iter().map(|m| m.tj_testid.to_string()).collect::<Vec<String>>();
      info!("根据姓名查到的体检信息:{medtestids:?}");
      if medtestids.len() <= 0 {
        return Ok(vec![]);
      }
      for val in medtestids.iter() {
        testids.push(val.clone());
      }
    }
    let ret = TjCorpoccureport::query_many(
      dto.dtstart,
      dto.dtend,
      &dto.reportids,
      dto.corpid,
      dto.testtype,
      &dto.reportnum,
      &dto.testid,
      &testids,
      dto.reporttype,
      dto.orptid,
      &db.get_connection(),
    )
    .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_report(info: &TjCorpoccureport, db: &DbConnection) -> Result<i64> {
    let ret = TjCorpoccureport::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  //delete report
  pub async fn delete_report(ids: &Vec<i64>, db: &DbConnection) -> Result<i32> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let ret = TjCorpoccureport::delete(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = TjCorpoccureportInfo::query_many(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let rpt_infos = ret.unwrap(); //all tj_corpoccureport_infos;
    let testids: Vec<String> = rpt_infos.iter().map(|f| f.tj_test_id.to_string()).collect();
    info!("testids are:{:?}", &testids);
    if testids.len() <= 0 {
      return Ok(ids.len() as i32);
    }
    let ret = TjMedexaminfo::update_report_numbers(&testids, "", &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = TjCorpoccureportInfo::delete_many(&testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ids.len() as i32)
  }

  pub async fn save_reportinfos_detail(infos: &Vec<TjCorpoccureportInfo>, db: &DbConnection) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("infos are empty"));
    }
    let testids: Vec<String> = infos.iter().map(|f| f.tj_test_id.to_string()).collect();
    let ret = TjCorpoccureportInfo::delete_many(&testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = TjCorpoccureportInfo::save_many(&infos, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }

    let mut testids_rptnums: HashMap<String, Vec<String>> = HashMap::new();
    infos.iter().for_each(|f| {
      if let Some(val) = testids_rptnums.get_mut(&f.tj_rptnum) {
        val.push(f.tj_test_id.to_string());
      } else {
        testids_rptnums.insert(f.tj_rptnum.to_string(), vec![f.tj_test_id.to_string()]);
      }
    });
    info!("to updated values:{:?}", &testids_rptnums);
    for val in testids_rptnums.into_iter() {
      let _ = TjMedexaminfo::update_report_numbers(&val.1, &val.0, &db.get_connection()).await;
    }

    Ok(infos.len() as i64)
  }

  pub async fn qury_reportinfos_detail(rptids: &Vec<i64>, db: &DbConnection) -> Result<Vec<TjCorpoccureportInfo>> {
    if rptids.len() <= 0 {
      return Err(anyhow!("testids are empty"));
    }
    // let testids: Vec<&str> = testids.iter().map(|v| v.as_str()).collect();
    let ret = TjCorpoccureportInfo::query_many(&rptids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn remove_from_report(testids: &Vec<String>, db: &DbConnection) -> Result<i32> {
    if testids.len() <= 0 {
      return Err(anyhow!("testids are empty"));
    }
    // let testids: Vec<&str> = testids.iter().map(|v| v.as_str()).collect();
    let ret = TjCorpoccureportInfo::delete_many(&testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }

    let ret = TjMedexaminfo::update_report_numbers(&testids, "", &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(testids.len() as i32)
  }

  pub async fn query_reportinfo_by_testids(testids: &Vec<String>, db: &DbConnection) -> Result<Vec<TjCorpoccureportInfo>> {
    if testids.len() <= 0 {
      return Err(anyhow!("testids are empty"));
    }
    // let testids: Vec<&str> = testids.iter().map(|v| v.as_str()).collect();
    let ret = TjCorpoccureportInfo::query_many_by_testids(&testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
    // Ok(testids.len() as i32)
  }
}
