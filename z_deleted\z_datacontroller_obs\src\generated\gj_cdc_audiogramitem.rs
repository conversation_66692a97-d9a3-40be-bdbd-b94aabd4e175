//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "gj_cdc_audiogramitem")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(unique)]
    pub cdc_item_code: Option<String>,
    pub cdc_item_name: Option<String>,
    pub cdc_item_pcode: Option<String>,
    pub tj_itemid: Option<String>,
    pub tj_adtype: Option<i32>,
    pub tj_ear: Option<i32>,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
