use crate::{common::constant::HttpCode, config::settings::Settings, nxmiddleware::printmiddleware::print_request_response, Client};
// use anyhow::{anyhow, Result};
use axum::{
  body::Body,
  extract::{DefaultBodyLimit, Extension, OriginalUri, Request, State},
  http::uri::Uri,
  http::Method,
  middleware,
  response::{IntoResponse, Response},
  routing::{delete, get, post, put},
  Router,
};

use hyper::StatusCode;
// use nodipservice::common::uidservice::UidgenService;
use std::sync::Arc;
use tokio::sync::RwLock;
use tower::ServiceBuilder;
use tower_http::cors::{Any, CorsLayer};

use super::{
  httpresponse::ResponseBody,
  v1::{accountcontroller, filectl},
};

pub fn create_route(
  // dbconn: Arc<DbConnection>,
  setting: Arc<Settings>,
  //  uid: Arc<RwLock<UidgenService>>,
  tx: Arc<tokio::sync::mpsc::UnboundedSender<i32>>,
  client: Client,
) -> Router {
  // let v1_route = create_v1_route(client); //.with_state(client);
  let proxy_route = create_proxy_route(client);
  let v1_route = create_v1_route();
  let downfile_route = Router::new().route("/*path", get(filectl::download_file));
  // let dbconn = Arc::new(db);

  let cors = CorsLayer::new()
    // allow `GET` and `POST` when accessing the resource
    .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
    // allow requests from any origin
    .allow_origin(Any);

  let app = Router::new()
    .nest("/", downfile_route.merge(proxy_route).merge(v1_route))
    .layer(middleware::from_fn(print_request_response))
    // .layer(ServiceBuilder::new().layer(Extension(dbconn)))
    // .layer(ServiceBuilder::new().layer(Extension(syscache)))
    .layer(DefaultBodyLimit::max(4096 * 10 * 1000))
    .layer(ServiceBuilder::new().layer(Extension(setting)))
    // .layer(ServiceBuilder::new().layer(Extension(uid)))
    .layer(ServiceBuilder::new().layer(Extension(tx)))
    .route_layer(cors);
  // .with_state(shared_state);
  // add a fallback service for handling routes to unknown paths
  // let app = app.fallback(handler_404.into_service());
  app
}

fn create_proxy_route(client: Client) -> Router {
  let proxyrouter = Router::new()
    .route("/api/*key", get(proxy_handler).post(proxy_handler).put(proxy_handler).delete(proxy_handler))
    .with_state(client);
  proxyrouter
}

fn create_v1_route() -> Router {
  let account_route = Router::new().route("/login", post(accountcontroller::user_login));

  let updown_route = Router::new().nest(
    "/",
    Router::new()
      .nest(
        "/upload",
        Router::new()
          .route("/photo", post(filectl::upload_photo))
          // .route("/sign", post(filectl::upload_sign))
          .route("/file", post(filectl::upload_file)),
      )
      .nest(
        "/download",
        Router::new()
          .route("/photo", post(filectl::download_photo))
          // .route("/sign", post(filectl::download_sign))
          .route("/file", post(filectl::download_file)),
      ),
  );

  let app_route = Router::new().nest("/apix/v1", Router::new().merge(account_route).merge(updown_route));

  app_route
}

async fn proxy_handler(Extension(settings): Extension<Arc<Settings>>, OriginalUri(original_uri): OriginalUri, State(client): State<Client>, mut req: Request) -> Response<Body> {
  if settings.application.proxyserver.is_empty() {
    return Response::builder()
      .status(StatusCode::NOT_FOUND)
      .body(Body::from("proxy backserver server is not configed"))
      .unwrap();
  }
  let uri = format!("{}{}", &settings.application.proxyserver, original_uri);
  // info!("proxy forward uri:{}", &uri);
  let ret = Uri::try_from(uri);
  if ret.as_ref().is_err() {
    error!("{}", ret.as_ref().unwrap_err().to_string());
    return Response::builder().status(StatusCode::NOT_FOUND).body(Body::from("无效的后台服务器")).unwrap();
  }
  *req.uri_mut() = ret.unwrap();
  let ret = client.request(req).await;
  if ret.as_ref().is_err() {
    error!("{}", ret.as_ref().unwrap_err().to_string());
    return Response::builder().status(StatusCode::NOT_FOUND).body(Body::from("不能连接到后台服务器")).unwrap();
  }
  ret.unwrap().into_response()
}
