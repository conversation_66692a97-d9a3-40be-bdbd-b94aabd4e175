use anyhow::Result;
use axum::{body::Bytes, extract::Multipart, Extension, Json};

use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{
  client::httpclient::HttpClient,
  common::constant::YesOrNo,
  dto::{AudiogramResponse, ExternalDTO, ExternalDto, ExternalLisDto, KeyDto, MultipleKeyDto},
  external::{herenkj::OutboundResponseDto, recvexternallis::RecvExternalLis, recvexternalpacs::RecvExternalPacs, ZjsrmyyService},
  medexam::externalservice::ExternalService, // SYSCACHE,
};
// use rbatis::RBatis;
// use rbdc_mssql::driver::MssqlDriver;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::*;
use utility::uidservice::UidgenService;

use crate::{
  api::httpresponse::{response_json_error, response_json_ok, response_json_value_error, response_json_value_ok},
  common::filesvc::FileSvc,
  config::settings::Settings,
  external::recv::{recvlabdata::RecvLabdata, recvpacsdata::RecvPacsData},
};
use nodipservice::external::{dianservice::DianService, externalservice::ExtService, localservice::LocalExternal};
#[derive(Debug, Default, Serialize, Deserialize)]
struct ExtLisKey {
  pub extkey: String,
}

pub async fn for_external_lis_checkitems(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<ExternalDto>) -> Json<Value> {
  let testid = dto.testid.to_string();
  let barcode = dto.barcode.to_string();

  let ret = VExternalLis::query_many(&testid, &barcode, &db.get_connection()).await;
  if ret.as_ref().is_err() {
    let empty: Vec<ExtCheckiteminfo> = Vec::new();
    error!("external lis checkitems error:{}", ret.as_ref().unwrap_err().to_string());
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), empty);
  }
  let infos = ret.unwrap();
  info!("external lis checkitems:{:?}", &infos);
  response_json_value_ok(infos.len() as u64, infos)
}

pub async fn for_external_pacs_checkitems(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<ExternalDto>) -> Json<Value> {
  let testid = dto.testid.to_string();
  let barcode = dto.barcode.to_string();

  let ret = VExternalPacs::query_many(&testid, &barcode, &db.get_connection()).await;
  if ret.as_ref().is_err() {
    let empty: Vec<ExtCheckiteminfo> = Vec::new();
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), empty);
  }
  let infos = ret.unwrap();
  response_json_value_ok(infos.len() as u64, infos)
}

pub async fn from_external_lis_xmxx(
  // Extension(db): Extension<Arc<DbConnection>>,
  config: Extension<Arc<Settings>>,
  extlis: Extension<Arc<RecvExternalLis>>,
  Json(dto): Json<KeyDto>,
) -> Json<Value> {
  if config.external.exttype.eq_ignore_ascii_case("wyhs") {
    //武义壶山医院
    let ret = nodipservice::external::wyhsservice::WyhsService::query_lis_items(&config.external.serverurl).await;
    if ret.as_ref().is_err() {
      error!("wyhs get error:{}", ret.as_ref().unwrap_err().to_string());
      return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), "");
    }
    let ret_data = ret.unwrap();
    return response_json_value_ok(ret_data.len() as u64, ret_data);
  }

  if config.application.lisego == YesOrNo::Yes as i32 {
    let server = config.application.proxyserver.to_string();
    if server.is_empty() {
      return response_json_value_error("ego服务器信息没有配置", "");
    }
    let key = ExtLisKey { extkey: dto.key.to_string() };
    let uri = format!("{}/api/extend/lis/xmxx", server);
    let ret: Result<Vec<VLisXmxx>> = HttpClient::send_http_post_request("", &uri, &Some(key)).await;
    // info!("get from server:{},result:{:?}", &uri, &ret);
    if ret.as_ref().is_err() {
      error!("ego get error:{}", ret.as_ref().unwrap_err().to_string());
      return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), "");
    }
    let ret_data = ret.unwrap();
    return response_json_value_ok(ret_data.len() as u64, ret_data);
  } else {
    info!("从本地连接视图的方式获取项目代码信息......");
    let mut lisval = dto.key.to_string();
    if lisval.is_empty() {
      lisval = "default".to_string();
    }
    // let mut extlis = extlis.write().await;
    let extlis_val = extlis.get_liskey();
    if extlis_val.eq_ignore_ascii_case(&lisval) {
      //default
      let ret = extlis.query_lis_xmxx().await;
      if ret.as_ref().is_err() {
        return response_json_value_error(ret.unwrap_err().to_string().as_str(), "");
      }
      let ret_data = ret.unwrap();
      return response_json_value_ok(ret_data.len() as u64, ret_data);
    }
    let ret_data: Vec<VLisXmxx> = vec![];
    response_json_value_ok(0 as u64, ret_data)
    // let ret = VLisXmxx::query_many("", &db.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   return response_json_value_error(ret.unwrap_err().to_string().as_str(), "");
    // }
    // let ret_data = ret.unwrap();
    // response_json_value_ok(ret_data.len() as u64, ret_data)
  }
}

pub async fn for_external_items(Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  let ret = IteminfoV::query_many("", &db.get_connection()).await;
  if ret.as_ref().is_err() {
    let empty: Vec<IteminfoV> = Vec::new();
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), empty);
  }
  let infos = ret.unwrap();
  response_json_value_ok(infos.len() as u64, infos)
}

//外部调用该接口上传lis数据
pub async fn for_external_lis_results(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<Vec<ExternalLisDto>>) -> Json<Value> {
  info!("external lis results upload:{:?}", &dto);
  let ret = ExternalService::upload_external_lis_results(&dto, &db).await;
  if ret.is_err() {
    return response_json_error(&ret.unwrap_err().to_string());
  }
  response_json_ok("Ok")
}

pub async fn external_pacs_results() -> Json<Value> {
  response_json_value_ok(0, "")
}

pub async fn from_external_orders(config: Extension<Arc<Settings>>) -> Json<Value> {
  match config.external.exttype.to_lowercase().as_str() {
    "dian" => {
      let ret = DianService::get_orders(&config.external.serverurl).await;
      if ret.as_ref().is_err() {
        return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), "");
      }
      let ret = ret.unwrap();
      return response_json_value_ok(0, ret);
    }
    _ => {
      return response_json_value_ok(0, "");
    }
  }
}

pub async fn import_lab_data(db: Extension<Arc<DbConnection>>, Json(dto): Json<Vec<TjLabresult>>) -> Json<Value> {
  info!("lab results,total:{}", dto.len());
  let ret = LocalExternal::import_lab_data(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), "");
  }
  response_json_value_ok(1, "".to_string())
}

pub async fn external_upload(db: Extension<Arc<DbConnection>>, config: Extension<Arc<Settings>>, uid: Extension<Arc<RwLock<UidgenService>>>, Json(dto): Json<ExternalDTO>) -> Json<Value> {
  // info!("开始外部对接，参数:{:?}", &dto);

  let mut idgen = uid.write().await;
  let ret = ExtService::do_external_upload(
    &db,
    &config.external.exttype,
    // &config.application.proxyserver,
    &config.external.serverurl,
    &config.audiogram.dburi,
    &mut idgen,
    1,
    &dto,
  )
  .await;
  info!("对接结果:{:?}", &ret);
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), "");
  }
  response_json_value_ok(1, "")
}

//lis数据接收
pub async fn external_recv_lis(db: Extension<Arc<DbConnection>>, config: Extension<Arc<Settings>>, extlis: Extension<Arc<RecvExternalLis>>, Json(dto): Json<MultipleKeyDto>) -> Json<Value> {
  info!("接收lis数据信息:{dto:?}");

  let import_type = config.application.lisimporttype;
  // let mut extlis = extlis.write().await;
  // let staff = SYSCACHE.get().unwrap().get_staff(claims.userid as i64, &db).await;
  let ret_data = RecvLabdata::recv_labdata(&dto, &config, import_type, &extlis, &db).await;
  response_json_value_ok(ret_data.len() as u64, ret_data)
}

pub async fn external_recv_pacs(
  db: Extension<Arc<DbConnection>>,
  config: Extension<Arc<Settings>>,
  extpacs: Extension<Arc<RecvExternalPacs>>,
  Json(dto): Json<MultipleKeyDto>,
) -> Json<Value> {
  // let pacs = pacs.unwrap();
  // let staff = SYSCACHE.get().unwrap().get_staff(claims.userid as i64, &db).await;
  // let mut extpacs = extpacs.write().await;
  let ret_data = RecvPacsData::recv_pacsdata(&dto, &config, &extpacs, &db).await;
  // if ret.as_ref().is_err() {
  //   return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), "");
  // }
  // let ret_data = ret.unwrap();
  response_json_value_ok(ret_data.len() as u64, ret_data)
}

pub async fn external_recv_audiogram(db: Extension<Arc<DbConnection>>, config: Extension<Arc<Settings>>, Json(dto): Json<KeyDto>) -> Json<Value> {
  match config.external.exttype.to_lowercase().as_str() {
    "dian" => {
      // info!("开始接收迪安的电测听结果");
      let testid = dto.key.to_owned();
      if testid.is_empty() {
        return response_json_value_error("testid is empty,not allowed", AudiogramResponse { ..Default::default() });
      }
      let ret = DianService::receive_audiogram(&testid, &config.audiogram.dburi, &db).await;
      if ret.as_ref().is_err() {
        return response_json_value_error("empty configuration", AudiogramResponse { ..Default::default() });
      }
      let adret = ret.unwrap();
      return response_json_value_ok(
        1,
        AudiogramResponse {
          testid: testid,
          addetails: adret.0,
          adresult: adret.1,
        },
      );
    }
    _ => return response_json_value_error("not supported yet", AudiogramResponse { ..Default::default() }),
  }
  // return response_json_value_error("testid is empty,not allowed", AudiogramResponse { ..Default::default() });
}

// ========================= zjsrmyy

pub async fn zjsrmyy_update_charge_flag(db: Extension<Arc<DbConnection>>, bytes: Bytes) -> Json<Value> {
  let mut resp = OutboundResponseDto::<String> {
    result_code: nodipservice::external::herenkj::CODE_OK.to_string(),
    desc_message: "".to_string(),
    ..Default::default()
  };
  let ret = std::str::from_utf8(&bytes);
  if ret.is_err() {
    resp.result_code = nodipservice::external::herenkj::CODE_ERROR.to_string();
    resp.desc_message = "不能解析数据".to_string();
    return Json(serde_json::json!(resp));
  }
  let datas = ret.unwrap();
  info!(datas, "更新收费状态");
  let ret = ZjsrmyyService::update_charge_flag(datas, &db).await;
  resp.result_code = ret.result_code;
  resp.desc_message = ret.desc_message;
  Json(serde_json::json!(resp))
}

pub async fn zjsrmyy_recv_lis_results(db: Extension<Arc<DbConnection>>, bytes: Bytes) -> Json<Value> {
  let mut resp = OutboundResponseDto::<String> {
    result_code: nodipservice::external::herenkj::CODE_OK.to_string(),
    desc_message: "".to_string(),
    ..Default::default()
  };
  let ret = std::str::from_utf8(&bytes);
  if ret.is_err() {
    resp.result_code = nodipservice::external::herenkj::CODE_ERROR.to_string();
    resp.desc_message = "不能解析数据".to_string();
    return Json(serde_json::json!(resp));
  }
  let datas = ret.unwrap();
  // info!("接收化验结果数据:{}", &datas);

  let ret = ZjsrmyyService::receive_lis_result(datas, &db).await;
  resp.result_code = ret.result_code;
  resp.desc_message = ret.desc_message;
  Json(serde_json::json!(resp))
}

pub async fn zjsrmyy_update_lis_status(db: Extension<Arc<DbConnection>>, bytes: Bytes) -> Json<Value> {
  let mut resp = OutboundResponseDto::<String> {
    result_code: nodipservice::external::herenkj::CODE_OK.to_string(),
    desc_message: "".to_string(),
    ..Default::default()
  };
  let ret = std::str::from_utf8(&bytes);
  if ret.is_err() {
    resp.result_code = nodipservice::external::herenkj::CODE_ERROR.to_string();
    resp.desc_message = "不能解析数据".to_string();
    return Json(serde_json::json!(resp));
  }
  let datas = ret.unwrap();

  let ret = ZjsrmyyService::update_lab_status(datas, &db).await;
  resp.result_code = ret.result_code;
  resp.desc_message = ret.desc_message;
  Json(serde_json::json!(resp))
}

pub async fn zjsrmyy_recv_pacs_results(db: Extension<Arc<DbConnection>>, bytes: Bytes) -> Json<Value> {
  let mut resp = OutboundResponseDto::<String> {
    result_code: nodipservice::external::herenkj::CODE_OK.to_string(),
    desc_message: "".to_string(),
    ..Default::default()
  };
  let ret = std::str::from_utf8(&bytes);
  if ret.is_err() {
    resp.result_code = nodipservice::external::herenkj::CODE_ERROR.to_string();
    resp.desc_message = "不能解析数据".to_string();
    return Json(serde_json::json!(resp));
  }
  let datas = ret.unwrap();
  // info!("接收体检结果数据:{}", &datas);

  let ret = ZjsrmyyService::receive_pacs_result(datas, &db).await;
  resp.result_code = ret.result_code;
  resp.desc_message = ret.desc_message;
  Json(serde_json::json!(resp))
}

pub async fn zjsrmyy_update_pacs_status(db: Extension<Arc<DbConnection>>, bytes: Bytes) -> Json<Value> {
  let mut resp = OutboundResponseDto::<String> {
    result_code: nodipservice::external::herenkj::CODE_OK.to_string(),
    desc_message: "".to_string(),
    ..Default::default()
  };
  let ret = std::str::from_utf8(&bytes);
  if ret.is_err() {
    resp.result_code = nodipservice::external::herenkj::CODE_ERROR.to_string();
    resp.desc_message = "不能解析数据".to_string();
    return Json(serde_json::json!(resp));
  }
  let datas = ret.unwrap();

  let ret = ZjsrmyyService::update_function_exam_status(datas, &db).await;
  resp.result_code = ret.result_code;
  resp.desc_message = ret.desc_message;
  Json(serde_json::json!(resp))
}

pub async fn zjsrmyy_import_lis_results(db: Extension<Arc<DbConnection>>, mut multipart: Multipart) -> Json<Value> {
  let mut filename = "".to_string();
  let dir = "temp";
  let _ = std::fs::create_dir_all(dir);
  while let Some(field) = multipart.next_field().await.unwrap() {
    // let fname = field.name();
    let file_name = field.file_name();

    info!("file_name:{:?}", &file_name);

    if file_name.is_none() {
      filename = utility::timeutil::current_timestamp_millis().to_string()
    } else {
      filename = file_name.unwrap().to_string();
    }

    let ret = field.bytes().await;
    if ret.as_ref().is_err() {
      // return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      return response_json_error(&ret.err().unwrap().to_string());
    }
    let data = ret.unwrap();
    // let new_filename = format!("{}.{}", newname, extension);
    let ret = FileSvc::save_file(dir, &filename, &data).await;
    if ret.as_ref().is_err() {
      return response_json_error(&ret.err().unwrap().to_string());
    }
  }
  //read file and import data
  if filename.is_empty() {
    return response_json_error("filename is empty");
  }
  let newfilename = format!("{}/{}", dir, filename);
  let ret = ZjsrmyyService::import_lis_results(&newfilename, &db).await;
  if ret.as_ref().is_err() {
    return response_json_error(&ret.err().unwrap().to_string());
  }
  response_json_ok("ok")
}
