use crate::entities::{prelude::*, v_lis_xmxx};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter};

impl VLisXmxx {
  pub async fn query_many(code: &str, db: &DatabaseConnection) -> Result<Vec<VLisXmxx>> {
    let mut conditions = Condition::all();
    if !code.is_empty() {
      conditions = conditions.add(v_lis_xmxx::Column::Itemid.contains(code));
    }
    let ret = VLisXmxxEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
