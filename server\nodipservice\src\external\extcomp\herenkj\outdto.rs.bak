use serde::{Deserialize, Serialize};

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, Serial<PERSON>, Deserialize)]
#[serde(rename = "args")]
pub struct PatBasicInfo {
  #[serde(rename = "patName")]
  pub patname: String, //
  pub sex: String,      //
  pub birthday: String, //YYYY-MM-DD
  #[serde(rename = "idCard")]
  pub idcard: String, //
  #[serde(rename = "phoneNumber")]
  pub phonenumber: String, //
  #[serde(rename = "companyName")]
  pub companyname: String, //
  #[serde(rename = "physicalType")]
  pub physicaltype: String, //1-个人 2-单位
  #[serde(rename = "regDate")]
  pub regdate: String, //申请时间
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
#[serde(rename = "args")]
pub struct ChargeInfo {
  #[serde(rename = "physicalID")]
  pub physicalid: String, //
  #[serde(rename = "physicalType")]
  pub physicaltype: String, //
  #[serde(rename = "name")]
  pub patname: String, //
  pub sex: String, //
  pub age: String, //
  #[serde(rename = "companyName")]
  pub companyname: String, //
  #[serde(rename = "enterDateTime")]
  pub enterdatetime: String, //登记日期
  pub charges: String, //金额
  #[serde(rename = "OperationID")]
  pub operationid: String, //YYYY-MM-DD
  #[serde(rename = "performedBy")]
  pub performedby: String, //
  #[serde(rename = "patientID")]
  pub patientid: String, //门诊
  #[serde(rename = "cancelledFlag")]
  pub cancelledflag: String, //0-正常 1-作废
  #[serde(rename = "vipCharges")]
  pub vipcharges: String, //Vip自费金额
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
#[serde(rename = "args")]
pub struct GeneralOrderInfo {
  #[serde(rename = "physicalID")]
  pub physicalid: String, //
  #[serde(rename = "ApplyNo")]
  pub apply_no: String, //申请单号
  #[serde(rename = "PatientID")]
  pub patient_id: String, //病历号
  #[serde(rename = "ChargeType")]
  pub charge_type: String, //费别
  #[serde(rename = "Costs")]
  pub costs: String, //费用
  #[serde(rename = "VisitDate")]
  pub visit_date: String, //就诊日期以YYYYMMDD HH24:MI:SS格式传入
  #[serde(rename = "OrderNo")]
  pub order_no: String, //医嘱号
  #[serde(rename = "ClinDiag")]
  pub clin_diag: String, //临床诊断
  #[serde(rename = "PhysSign")]
  pub phys_sign: String, //体征
  #[serde(rename = "ClinSymp")]
  pub clin_symp: String, //临床症状
  #[serde(rename = "RelevantDiag")]
  pub relevant_diag: String, //其他诊断
  #[serde(rename = "ExamReason")]
  pub exam_reason: String, //检查目的
  #[serde(rename = "Notice")]
  pub notice: String, //注意事项
  #[serde(rename = "Status")]
  pub status: String, //检查状态  1、开立  2、作废 3、修改
  #[serde(rename = "ChargeIndicator")]
  pub charge_indicator: String, //收费状态 0、未收费 1、已收费  2、未知
  #[serde(rename = "PatientSource")]
  pub patient_source: String, //病人来源
  #[serde(rename = "ExamClassCode")]
  pub exam_class_code: String, //检查类别代码
  #[serde(rename = "ExamClassName")]
  pub exam_class_name: String, //检查类别名称
  #[serde(rename = "ReqDeptNo")]
  pub req_dept_no: String, //科申请室代码
  #[serde(rename = "ReqDeptName")]
  pub req_dept_name: String, //申请科室名称
  #[serde(rename = "ReqPhysicianID")]
  pub req_physician_id: String, //申请医生ID
  #[serde(rename = "ReqPhysician")]
  pub req_physician: String, //申请医生名称
  #[serde(rename = "ReqDateTime")]
  pub req_date_time: String, //申请时间
  #[serde(rename = "reqMemo")]
  req_memo: String, // 申请备注
  #[serde(rename = "priority")]
  priority: String, // 优先标志: 1=紧急, 0=普通
  #[serde(rename = "schDateTime")]
  sch_date_time: String, // 预约安排时间
  #[serde(rename = "schMemo")]
  sch_memo: String, // 预约说明
  #[serde(rename = "performedBy")]
  performed_by: String, // 执行科室代码
  #[serde(rename = "performedByName")]
  performed_by_name: String, // 执行科室名称
  #[serde(rename = "reqAreaCode")]
  req_area_code: String, // 开单院区
  #[serde(rename = "performedAreaCode")]
  performed_area_code: String, // 执行院区
  #[serde(rename = "technicianId")]
  technician_id: String, // 检查技师工号
  #[serde(rename = "technician")]
  technician: String, // 检查技师姓名
  #[serde(rename = "examDateTime")]
  exam_date_time: String, // 检查时间
  #[serde(rename = "verifierDocNo")]
  verifier_doc_no: String, // 审核医生工号
  #[serde(rename = "verifierDocName")]
  verifier_doc_name: String, // 审核医生姓名
  #[serde(rename = "verifierDocPhoneNo")]
  verifier_doc_phone_no: String, // 审核医生电话
  #[serde(rename = "verifierDateTime")]
  verifier_date_time: String, // 审核时间 2019-06-06 00:00:00
  #[serde(rename = "babyNo")]
  baby_no: String, // 婴儿标识
  #[serde(rename = "examItems")]
  exam_items: Vec<ExamItem>, // 申请项目列表
  #[serde(rename = "phoneNo")]
  phone_no: String, // 患者联系方式
  #[serde(rename = "chiefComplaint")]
  chief_complaint: String, // 主述
  #[serde(rename = "physicalExam")]
  physical_exam: String, // 体征
  #[serde(rename = "deliveryDate")]
  delivery_date: String, // 送检日期
  #[serde(rename = "attendingDate")]
  attending_date: String, // 主诊日期
  #[serde(rename = "diagnosisType")]
  diagnosis_type: String, // 诊断类型
  #[serde(rename = "sliceAmount")]
  slice_amount: String, // 切片张数
  #[serde(rename = "memo")]
  memo: String, // 其他
  #[serde(rename = "laboratoryExam")]
  laboratory_exam: String, // 实验室检查
  #[serde(rename = "imagingExam")]
  imaging_exam: String, // 影像学检查
  #[serde(rename = "preoperativeRadiotherapyDate")]
  preoperative_radiotherapy_date: String, // 术前放疗时间
  #[serde(rename = "postoperativeRadiotherapy")]
  postoperative_radiotherapy: String, // 术后放疗
  #[serde(rename = "postoperativeRadiotherapyDate")]
  postoperative_radiotherapy_date: String, // 术后放疗时间
  #[serde(rename = "examPurpose")]
  exam_purpose: String, // 检查目的
  #[serde(rename = "specialExamItem")]
  special_exam_item: String, // 特殊检查项目
  #[serde(rename = "specializedExam")]
  specialized_exam: String, // 专科检查
  #[serde(rename = "leafageNodeId")]
  leafage_node_id: String, // 仪器号
  #[serde(rename = "empty")]
  empty: Option<bool>, // 空腹标志
  #[serde(rename = "schTime")]
  sch_time: String, // 预约时间
  #[serde(rename = "historySummary")]
  history_summary: String, // 病史摘要
  #[serde(rename = "yjRemark")]
  yj_remark: String, // 医生备注
  #[serde(rename = "patientType")]
  patient_type: String, // 病人类型: O=门诊, I=住院, E=急诊, T=体检
  #[serde(rename = "registerFlag")]
  register_flag: String, // 预约标识: 0=无需预约, 1=预约
  #[serde(rename = "wardCode")]
  ward_code: String, // 病区代码
  #[serde(rename = "wardName")]
  ward_name: String, // 病区名称
}

// 检查项目子结构体
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ExamItem {
  #[serde(rename = "itemNo")]
  item_no: String, // 项目ID
  #[serde(rename = "examItem")]
  exam_item: String, // 检查子项目名称
  #[serde(rename = "examItemCode")]
  exam_item_code: String, // 检查子项目代码
  #[serde(rename = "examSubClass")]
  exam_sub_class: String, // 检查子项类型
  #[serde(rename = "examThirdItemCode")]
  exam_third_item_code: String, // 检查孙项代码
  #[serde(rename = "refOrderNo")]
  ref_order_no: String, // 关联医嘱父ID
  #[serde(rename = "refOrderSubNo")]
  ref_order_sub_no: String, // 关联医嘱子ID
  #[serde(rename = "collectionVolume")]
  collection_volume: Option<f64>, // 标本数量
  #[serde(rename = "specimenActionCode")]
  specimen_action_code: String, // 标本处理动作代码
  #[serde(rename = "relevantClinicalInfo")]
  relevant_clinical_info: String, // 相关诊断信息
  #[serde(rename = "specimenSourceName")]
  specimen_source_name: String, // 标本名称及部位信息
  #[serde(rename = "specimenSourcePart")]
  specimen_source_part: String, // 标本部位
  #[serde(rename = "numbersOfSampleContainers")]
  numbers_of_sample_containers: String, // 标本容器数
  #[serde(rename = "collectorComment")]
  collector_comment: String, // 送检医生备注
  #[serde(rename = "costs")]
  costs: Option<f64>, // 费用
}

// 检验主申请单信息结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct LabOrderInfo {
  #[serde(rename = "OrderID")]
  order_id: String, // 医嘱号
  #[serde(rename = "physicalID")]
  physical_id: String, // 体检号
  #[serde(rename = "ApplyNo")]
  apply_no: String, // 申请单号
  #[serde(rename = "PriorityIndicator")]
  priority_indicator: String, // 优先标志: 1=紧急, 0=普通
  #[serde(rename = "PatientID")]
  patient_id: String, // 病历号
  #[serde(rename = "PatientSource")]
  patient_source: String, // 病人来源类型
  #[serde(rename = "SubjectCode")]
  subject_code: String, // 检验主题代码
  #[serde(rename = "SubjectName")]
  subject_name: String, // 检验主题名称
  #[serde(rename = "TestCauseCode")]
  test_cause_code: String, // 检验目的代码
  #[serde(rename = "TestCauseName")]
  test_cause_name: String, // 检验目的名称
  #[serde(rename = "RelevantClinicDiag")]
  relevant_clinic_diag: String, // 临床诊断
  #[serde(rename = "NotesForSpcm")]
  notes_for_spcm: String, // 标本说明
  #[serde(rename = "SpcmSampleDateTime")]
  spcm_sample_date_time: String, // 标本采样时间
  #[serde(rename = "SpcmReceviedDateTime")]
  spcm_received_date_time: String, // 标本收到时间
  #[serde(rename = "RequestedDateTime")]
  requested_date_time: String, // 申请时间
  #[serde(rename = "OrderingDeptCode")]
  ordering_dept_code: String, // 申请科室代码
  #[serde(rename = "OrderingDeptName")]
  ordering_dept_name: String, // 申请科室名称
  #[serde(rename = "OrderingProviderID")]
  ordering_provider_id: String, // 申请医生ID
  #[serde(rename = "OrderingProviderName")]
  ordering_provider_name: String, // 申请医生姓名
  #[serde(rename = "PerformedBy")]
  performed_by: String, // 执行科室
  #[serde(rename = "PerformedByCode")]
  performed_by_code: String, // 执行科室代码
  #[serde(rename = "ExecuteDoctorID")]
  execute_doctor_id: String, // 执行医生ID
  #[serde(rename = "ExecuteDoctorName")]
  execute_doctor_name: String, // 执行医生姓名
  #[serde(rename = "ReqAreaCode")]
  req_area_code: String, // 开单院区
  #[serde(rename = "PerformedAreaCode")]
  performed_area_code: String, // 执行院区
  #[serde(rename = "ChargeIndicate")]
  charge_indicate: String, // 收费状态: 0=未收费, 1=已收费
  #[serde(rename = "VerifiedDateTime")]
  verified_date_time: String, // 校对时间
  #[serde(rename = "VerifiedBy")]
  verified_by: String, // 校对者
  #[serde(rename = "VerifiedByID")]
  verified_by_id: String, // 校对者ID
  #[serde(rename = "AuditDateTime")]
  audit_date_time: String, // 审核时间
  #[serde(rename = "AuditDoctor")]
  audit_doctor: String, // 审核医生
  #[serde(rename = "AuditDoctorID")]
  audit_doctor_id: String, // 审核医生ID
  #[serde(rename = "Costs")]
  costs: Option<f64>, // 费用
  #[serde(rename = "ChargeType")]
  charge_type: String, // 费用类别
  #[serde(rename = "specimenCode")]
  specimen_code: String, // 样本编号
  #[serde(rename = "specimenName")]
  specimen_name: String, // 样本名称
  #[serde(rename = "Charges")]
  charges: Option<f64>, // 应收费用
  #[serde(rename = "LabOrderItems")]
  lab_order_items: Vec<LabOrderItem>, // 检验项目列表
  #[serde(rename = "PurposeOfInspection")]
  purpose_of_inspection: String, // 检验目的
  #[serde(rename = "DrugResistanceType")]
  drug_resistance_type: String, // 耐药类型
  #[serde(rename = "SampleCharacter")]
  sample_character: String, // 样本性状
  #[serde(rename = "rcptno")]
  rcptno: String, // 发票号
}

// 检验项目子结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct LabOrderItem {
  #[serde(rename = "ItemNo")]
  item_no: String, // 项目序号
  #[serde(rename = "ItemName")]
  item_name: String, // 项目名称
  #[serde(rename = "ItemCode")]
  item_code: String, // 项目代码
  #[serde(rename = "SpcmSampleDateTime")]
  spcm_sample_date_time: String, // 标本采样日期及时间
  #[serde(rename = "SpcmReceivedDateTime")]
  spcm_received_date_time: String, // 标本收到日期及时间
  #[serde(rename = "ExecuteDate")]
  execute_date: String, // 执行日期
  #[serde(rename = "Notes")]
  notes: String, // 备注
}

// 病人基本信息结构体
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PatientInfo {
  #[serde(rename = "PatSex")]
  pat_sex: String, // 性别: M=男性, F=女性, O=未知
  #[serde(rename = "Name")]
  name: String, // 病人姓名
  #[serde(rename = "ChargeType")]
  charge_type: String, // 费用类别
  #[serde(rename = "MaritalStatus")]
  marital_status: String, // 婚姻状况: M=已婚, B=未婚, D=离婚, W=丧偶, O=其他
  #[serde(rename = "MaritalStatusCode")]
  marital_status_code: String, // 婚姻状况代码
  #[serde(rename = "BirthPlace")]
  birth_place: String, // 出生地
  #[serde(rename = "BirthPlaceCode")]
  birth_place_code: String, // 出生地代码
  #[serde(rename = "PresentAddressProvice")]
  present_address_province: String, // 现住地址省份
  #[serde(rename = "PresentAddressCity")]
  present_address_city: String, // 所在城市
  #[serde(rename = "PresentAddressCounty")]
  present_address_county: String, // 所在县
  #[serde(rename = "PresentAddressOthers")]
  present_address_others: String, // 详细地址
  #[serde(rename = "PresentAddressZipcode")]
  present_address_zipcode: String, // 所在地邮编
  #[serde(rename = "IDNO")]
  id_no: String, // 身份证或军官证
  #[serde(rename = "PhoneNumber")]
  phone_number: String, // 电话号码
  #[serde(rename = "PhoneNumberBusiness")]
  phone_number_business: String, // 单位电话号码
  #[serde(rename = "PhoneNumberHome")]
  phone_number_home: String, // 家庭电话号码
  #[serde(rename = "BirthPlaceName")]
  birth_place_name: String, // 出生地名字
  #[serde(rename = "Nation")]
  nation: String, // 民族
  #[serde(rename = "NationCode")]
  nation_code: String, // 民族代码
  #[serde(rename = "CityzenShipName")]
  citizenship_name: String, // 国籍名称
  #[serde(rename = "CityzenShipCode")]
  citizenship_code: String, // 国籍代码
  #[serde(rename = "UnitInContrName")]
  unit_in_contr_name: String, // 合同单位名称
  #[serde(rename = "JobCode")]
  job_code: String, // 工作代码
  #[serde(rename = "JobName")]
  job_name: String, // 工作名称
  #[serde(rename = "NextOfKin")]
  next_of_kin: String, // 联系人姓名
  #[serde(rename = "NextOfKinPhone")]
  next_of_kin_phone: String, // 联系人电话号码
  #[serde(rename = "NextOfKinZipCode")]
  next_of_kin_zip_code: String, // 联系人邮政编码
  #[serde(rename = "NextOfKinAddr")]
  next_of_kin_addr: String, // 联系人地址
  #[serde(rename = "RelationshipCode")]
  relationship_code: String, // 与联系人关系代码
  #[serde(rename = "Relationship")]
  relationship: String, // 与联系人关系
  #[serde(rename = "HealthCardType")]
  health_card_type: String, // 医保卡类别
  #[serde(rename = "HealthCardNo")]
  health_card_no: String, // 医保卡号
  #[serde(rename = "HospitalCardType")]
  hospital_card_type: String, // 医院卡号类型
  #[serde(rename = "HospitalCardNo")]
  hospital_card_no: String, // 医院卡号
  #[serde(rename = "PatAge")]
  pat_age: String, // 年龄
  #[serde(rename = "DOB")]
  dob: String, // 出生日期
  #[serde(rename = "VIPIndicator")]
  vip_indicator: String, // VIP号
}

// 病人就诊信息结构体
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PatVisitInfo {
  #[serde(rename = "PatientClass")]
  patient_class: String, // 病人类型: O=门诊, I=住院, T=体检
  #[serde(rename = "AttendingDoctorID")]
  attending_doctor_id: String, // 主治医师ID
  #[serde(rename = "AttendingDoctorName")]
  attending_doctor_name: String, // 主治医师姓名
  #[serde(rename = "ReferringDoctorID")]
  referring_doctor_id: String, // 经治医师或转介医师ID
  #[serde(rename = "ReferringDoctorName")]
  referring_doctor_name: String, // 经治医师或转介医师姓名
  #[serde(rename = "VIPIndicator")]
  vip_indicator: String, // VIP号
  #[serde(rename = "AdmittingDoctorID")]
  admitting_doctor_id: String, // 接诊医生编码
  #[serde(rename = "AdmittingDoctorName")]
  admitting_doctor_name: String, // 接诊医生姓名
  #[serde(rename = "DischargeDateTime")]
  discharge_date_time: String, // 接诊结束时间
  #[serde(rename = "BloodType")]
  blood_type: String, // 血型
  #[serde(rename = "BloodTypeRH")]
  blood_type_rh: String, // RH类型
}

//2.5.1.	GetXMTC获取项目套餐
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PackageItem {
  #[serde(rename = "tcid")]
  tcid: String, // 套餐ID，长度10，非空
  #[serde(rename = "tcmc")]
  tcmc: String, // 套餐名称，长度40，非空
  #[serde(rename = "bm")]
  bm: String, // 套餐编码，长度6，非空
  #[serde(rename = "xmmc")]
  xmmc: String, // 项目名称，长度30，非空
  #[serde(rename = "dj")]
  dj: String, // 单价，长度7，非空
  #[serde(rename = "sl")]
  sl: String, // 数量，长度5，非空
  #[serde(rename = "je")]
  je: String, // 金额，长度8，非空
  #[serde(rename = "xmid")]
  xmid: String, // 收费项目ID，长度8，非空
  #[serde(rename = "bmh")]
  bmh: String, // 部门号，长度5，非空
}

//2.5.2.	GetSFXM获取收费项目

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ChargeItem {
  #[serde(rename = "xmmc")]
  xmmc: String, // 项目名称，长度30，非空
  #[serde(rename = "xmid")]
  xmid: String, // 收费项目ID，长度10，非空
  #[serde(rename = "bm")]
  bm: String, // 拼音编码，长度6，非空
  #[serde(rename = "dj")]
  dj: String, // 单价，长度7，非空
  #[serde(rename = "dw")]
  dw: String, // 单位，长度4，非空
  #[serde(rename = "fylbh")]
  fylbh: String, // 费用类别号，长度4，非空
}
