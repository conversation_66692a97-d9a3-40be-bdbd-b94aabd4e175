#[allow(unused)]
use crate::rusthl7::{Field, Hl7ParseError, Message, Separators};
use anyhow::Result;
use std::fmt::Display;

#[derive(Debug, PartialEq)]
pub struct Pv1Segment<'a> {
  pub source: &'a str,
  //this initial layout largely stolen from the _other_ hl7 crate: https://github.com/njaremko/hl7
  pub msg_encoding_characters: Separators,
  pub pv1_1_set_id: Option<Field<'a>>,
  pub pv1_2_patient_class: Field<'a>,
  pub pv1_3_assigned_patient_location: Option<Field<'a>>,
  pub pv1_4_admission_type: Option<Field<'a>>,
  pub pv1_5_preadmit_number: Option<Field<'a>>,
  pub pv1_6_prior_patient_location: Option<Field<'a>>,
  pub pv1_7_attending_doctor: Option<Field<'a>>,
  pub pv1_8_referring_doctor: Option<Field<'a>>,
  pub pv1_9_consulting_doctor: Option<Field<'a>>,
  pub pv1_10_hospital_service: Option<Field<'a>>,
  pub pv1_11_temporary_location: Option<Field<'a>>,
  pub pv1_12_preadmit_test_indicator: Option<Field<'a>>,
  pub pv1_13_re_admission_indicator: Option<Field<'a>>,
  pub pv1_14_admit_source: Option<Field<'a>>,
  pub pv1_15_ambulatory_status: Option<Field<'a>>,
  pub pv1_16_vip_indicator: Option<Field<'a>>,
  pub pv1_17_admitting_doctor: Option<Field<'a>>,
  pub pv1_18_patient_type: Option<Field<'a>>,
  pub pv1_19_visit_number: Option<Field<'a>>,
  pub pv1_20_financial_class: Option<Field<'a>>,
  pub pv1_21_charge_price_indicator: Option<Field<'a>>,
  pub pv1_22_courtesy_code: Option<Field<'a>>,
  pub pv1_23_credit_rating: Option<Field<'a>>,
  pub pv1_24_contract_code: Option<Field<'a>>,
  pub pv1_25_contract_effective_date: Option<Field<'a>>,
  pub pv1_26_contract_amount: Option<Field<'a>>,
  pub pv1_27_contract_period: Option<Field<'a>>,
  pub pv1_28_interest_code: Option<Field<'a>>,
  pub pv1_29_transfer_to_bad_debt_code: Option<Field<'a>>,
  pub pv1_30_transfer_to_bad_debt_date: Option<Field<'a>>,
  pub pv1_31_bad_debt_agency_code: Option<Field<'a>>,
  // pub pv1_32_bad_debt_transfer_amount: Option<Field<'a>>,
  // pub pv1_33_bad_debt_recovery_amount: Option<Field<'a>>,
  // pub pv1_34_delete_account_indicator: Option<Field<'a>>,
  // pub pv1_35_delete_account_date: Option<Field<'a>>,
  // pub pv1_36_discharge_disposition: Option<Field<'a>>,
  // pub pv1_37_discharged_to_location: Option<Field<'a>>,
  // pub pv1_38_diet_type: Option<Field<'a>>,
  // pub pv1_39_servicing_facility: Option<Field<'a>>,
  // pub pv1_40_bed_status: Option<Field<'a>>,
  // pub pv1_41_account_status: Option<Field<'a>>,
  // pub pv1_42_pending_location: Option<Field<'a>>,
  // pub pv1_43_prior_temporary_location: Option<Field<'a>>,
  // pub pv1_44_admit_date_time: Option<Field<'a>>,
  // pub pv1_45_discharge_date_time: Option<Field<'a>>,
  // pub pv1_46_current_patient_balance: Option<Field<'a>>,
  // pub pv1_47_total_charges: Option<Field<'a>>,
  // pub pv1_48_total_adjustments: Option<Field<'a>>,
  // pub pv1_49_total_payments: Option<Field<'a>>,
  // pub pv1_50_alternate_visit_id: Option<Field<'a>>,
  // pub pv1_51_visit_indicator: Option<Field<'a>>,
  // pub pv1_52_other_healthcare_provider: Option<Field<'a>>,
  // pub pv1_53_service_episode_description: Option<Field<'a>>,
  // pub pv1_54_service_episode_identifier: Option<Field<'a>>,
}

impl<'a> Pv1Segment<'a> {
  pub fn parse<S: Into<&'a str>>(input: S, delims: &Separators) -> Result<Pv1Segment<'a>, Hl7ParseError> {
    let input = input.into();

    let mut fields = input.split(delims.field);

    assert!(fields.next().unwrap() == "PV1");

    let seg = Pv1Segment {
      source: input,
      msg_encoding_characters: delims.to_owned(),
      pv1_1_set_id: Field::parse_optional(fields.next(), delims)?,
      pv1_2_patient_class: Field::parse_mandatory(fields.next(), delims)?,
      pv1_3_assigned_patient_location: Field::parse_optional(fields.next(), delims)?,
      pv1_4_admission_type: Field::parse_optional(fields.next(), delims)?,
      pv1_5_preadmit_number: Field::parse_optional(fields.next(), delims)?,
      pv1_6_prior_patient_location: Field::parse_optional(fields.next(), delims)?,
      pv1_7_attending_doctor: Field::parse_optional(fields.next(), delims)?,
      pv1_8_referring_doctor: Field::parse_optional(fields.next(), delims)?,
      pv1_9_consulting_doctor: Field::parse_optional(fields.next(), delims)?,
      pv1_10_hospital_service: Field::parse_optional(fields.next(), delims)?,
      pv1_11_temporary_location: Field::parse_optional(fields.next(), delims)?,
      pv1_12_preadmit_test_indicator: Field::parse_optional(fields.next(), delims)?,
      pv1_13_re_admission_indicator: Field::parse_optional(fields.next(), delims)?,
      pv1_14_admit_source: Field::parse_optional(fields.next(), delims)?,
      pv1_15_ambulatory_status: Field::parse_optional(fields.next(), delims)?,
      pv1_16_vip_indicator: Field::parse_optional(fields.next(), delims)?,
      pv1_17_admitting_doctor: Field::parse_optional(fields.next(), delims)?,
      pv1_18_patient_type: Field::parse_optional(fields.next(), delims)?,
      pv1_19_visit_number: Field::parse_optional(fields.next(), delims)?,
      pv1_20_financial_class: Field::parse_optional(fields.next(), delims)?,
      pv1_21_charge_price_indicator: Field::parse_optional(fields.next(), delims)?,
      pv1_22_courtesy_code: Field::parse_optional(fields.next(), delims)?,
      pv1_23_credit_rating: Field::parse_optional(fields.next(), delims)?,
      pv1_24_contract_code: Field::parse_optional(fields.next(), delims)?,
      pv1_25_contract_effective_date: Field::parse_optional(fields.next(), delims)?,
      pv1_26_contract_amount: Field::parse_optional(fields.next(), delims)?,
      pv1_27_contract_period: Field::parse_optional(fields.next(), delims)?,
      pv1_28_interest_code: Field::parse_optional(fields.next(), delims)?,
      pv1_29_transfer_to_bad_debt_code: Field::parse_optional(fields.next(), delims)?,
      pv1_30_transfer_to_bad_debt_date: Field::parse_optional(fields.next(), delims)?,
      pv1_31_bad_debt_agency_code: Field::parse_optional(fields.next(), delims)?,
      // pv1_32_bad_debt_transfer_amount: Field::parse_optional(fields.next(), delims)?,
      // pv1_33_bad_debt_recovery_amount: Field::parse_optional(fields.next(), delims)?,
      // pv1_34_delete_account_indicator: Field::parse_optional(fields.next(), delims)?,
      // pv1_35_delete_account_date: Field::parse_optional(fields.next(), delims)?,
      // pv1_36_discharge_disposition: Field::parse_optional(fields.next(), delims)?,
      // pv1_37_discharged_to_location: Field::parse_optional(fields.next(), delims)?,
      // pv1_38_diet_type: Field::parse_optional(fields.next(), delims)?,
      // pv1_39_servicing_facility: Field::parse_optional(fields.next(), delims)?,
      // pv1_40_bed_status: Field::parse_optional(fields.next(), delims)?,
      // pv1_41_account_status: Field::parse_optional(fields.next(), delims)?,
      // pv1_42_pending_location: Field::parse_optional(fields.next(), delims)?,
      // pv1_43_prior_temporary_location: Field::parse_optional(fields.next(), delims)?,
      // pv1_44_admit_date_time: Field::parse_optional(fields.next(), delims)?,
      // pv1_45_discharge_date_time: Field::parse_optional(fields.next(), delims)?,
      // pv1_46_current_patient_balance: Field::parse_optional(fields.next(), delims)?,
      // pv1_47_total_charges: Field::parse_optional(fields.next(), delims)?,
      // pv1_48_total_adjustments: Field::parse_optional(fields.next(), delims)?,
      // pv1_49_total_payments: Field::parse_optional(fields.next(), delims)?,
      // pv1_50_alternate_visit_id: Field::parse_optional(fields.next(), delims)?,
      // pv1_51_visit_indicator: Field::parse_optional(fields.next(), delims)?,
      // pv1_52_other_healthcare_provider: Field::parse_optional(fields.next(), delims)?,
      // pv1_53_service_episode_description: Field::parse_optional(fields.next(), delims)?,
      // pv1_54_service_episode_identifier: Field::parse_optional(fields.next(), delims)?,
    };

    Ok(seg)
  }
}
/// Common formatter trait implementation for the strongly-typed segment
impl<'a> Display for Pv1Segment<'a> {
  /// Required for to_string() and other formatter consumers
  fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
    write!(f, "{}", self.source)
  }
}
/// Common clone trait implementation for the strongly-typed segment
impl<'a> Clone for Pv1Segment<'a> {
  /// Creates a new Message object using _the same source_ slice as the original.
  fn clone(&self) -> Self {
    let delims = self.msg_encoding_characters;
    Pv1Segment::parse(self.source, &delims).unwrap()
  }
}
/// Extracts header element for external use
pub fn _pv1<'a>(msg: &Message<'a>) -> Result<Pv1Segment<'a>, Hl7ParseError> {
  let seg = msg.segments_by_identifier("PV1").unwrap()[0];
  let segment = Pv1Segment::parse(seg.source, &msg.get_separators()).expect("Failed to parse Pv1 segment");
  Ok(segment)
}
