#[allow(unused)]
use crate::rusthl7::{Field, Hl7ParseError, Message, Separators};
use anyhow::Result;
use std::fmt::Display;

#[derive(Debug, PartialEq)]
pub struct EvnSegment<'a> {
  pub source: &'a str,
  pub msg_encoding_characters: Separators,
  pub evn_1_event_type: Option<Field<'a>>,
  pub evn_2_record_datetime: Field<'a>,
}

impl<'a> EvnSegment<'a> {
  pub fn parse<S: Into<&'a str>>(input: S, delims: &Separators) -> Result<EvnSegment<'a>, Hl7ParseError> {
    let input = input.into();

    let mut fields = input.split(delims.field);

    assert!(fields.next().unwrap() == "EVN");

    let msh = EvnSegment {
      source: input,
      msg_encoding_characters: delims.to_owned(),
      evn_1_event_type: Field::parse_optional(fields.next(), delims)?,
      evn_2_record_datetime: Field::parse_mandatory(fields.next(), delims)?,
    };

    Ok(msh)
  }
}
/// Common formatter trait implementation for the strongly-typed segment
impl<'a> Display for EvnSegment<'a> {
  /// Required for to_string() and other formatter consumers
  fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
    write!(f, "{}", self.source)
  }
}
/// Common clone trait implementation for the strongly-typed segment
impl<'a> Clone for EvnSegment<'a> {
  /// Creates a new Message object using _the same source_ slice as the original.
  fn clone(&self) -> Self {
    let delims = self.msg_encoding_characters;
    EvnSegment::parse(self.source, &delims).unwrap()
  }
}

/// Extracts header element for external use
pub fn _evn<'a>(msg: &Message<'a>) -> Result<EvnSegment<'a>, Hl7ParseError> {
  let seg = msg.segments_by_identifier("EVN").unwrap()[0];
  let segment = EvnSegment::parse(seg.source, &msg.get_separators()).expect("Failed to parse EVN segment");
  Ok(segment)
}
