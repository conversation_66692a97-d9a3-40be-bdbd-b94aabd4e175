use crate::entities::{prelude::*, tj_external};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter};

impl TjExternal {
  pub async fn query_many(code: i32, db: &DatabaseConnection) -> Result<Vec<TjExternal>> {
    let mut condition = Condition::all();
    if code > 0 {
      condition = condition.add(tj_external::Column::TjExttype.eq(code));
    }
    let ret = TjExternalEntity::find().filter(condition).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
