use num_enum::TryFromPrimitive;
use serde::{Deserialize, Serialize};
use strum::{Display, EnumCount, EnumDiscriminants, EnumString};

pub const AUDIOGRAM_DEPT_ID: &str = "0109";
// pub const FILE_DIR: &str = "./files";
pub const AUDIOGRAM_ITEMID: &str = "3901";

#[derive(Debug, Eq, PartialEq, EnumString, Display, EnumCount, EnumDiscriminants)]
pub enum Platform {
  /// Random Docs
  #[strum(to_string = "zj")]
  Zhejiang,
  #[strum(to_string = "nb")]
  Ningbo,
  #[strum(serialize = "wz")]
  Wenzhou,
  #[strum(serialize = "sx")]
  Shan<PERSON>,
  #[strum(serialize = "zwx")]
  Zhongweixin,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParmDto {
  pub tid: Vec<String>,
  pub datatype: i32,
  pub optype: i32,
  pub force: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CardlistqueryDto {
  pub pagenum: String,
  pub pagesize: String,
  pub writedatebegin: String,
  pub writedateend: String,
  pub checktimebegin: String,
  pub checktimeend: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Refusedcard {
  pub reportcode: String,
  pub code: String,
  pub auditlevel: String,
  pub auditorname: String,
  pub auditdate: String,
  pub auditdesc: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CardlistqueryResponseDto {
  pub returncode: String,
  pub message: String,
  pub reportcardlist: Vec<Refusedcard>,
}

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(u8)]
pub enum DataType {
  ALL = 0,
  HEALTHY = 1,
  CORPINFO = 2,
}
#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(u8)]
pub enum OperationType {
  ADD = 1,
  UPDATE = 2,
  DELETE = 3,
}
