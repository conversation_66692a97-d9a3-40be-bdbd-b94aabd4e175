use anyhow::{anyhow, Result};
// use chrono::NaiveDate;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use crate::common::constant;
use tracing::*;
use dataservice::{dbinit::DbConnection, entities::prelude::*};

use rust_xlsxwriter::{Color, Format, FormatAlign, FormatBorder, Workbook};
use std::collections::{HashMap, HashSet};
use utility::timeutil;
pub struct DatasumSvc;

impl DatasumSvc {
  pub async fn export_medexam_with_details(testids: &Vec<String>, syncids: &Vec<String>, flag: i32, dir: &str, db: &DbConnection) -> Result<String> {
    //select from medexaminfos by testid;

    // let mut syncids = syncids.to_owned();
    // if syncids.is_empty() {
    //   let iteminfos = crate::SYSCACHE.get().unwrap().get_iteminfos(&vec![], -1, 1, -1, &vec![], db).await;
    //   syncids = iteminfos.iter().map(|v| v.tj_itemid.to_string()).collect();
    // }
    // info!("sync ids are:{:?}", &syncids);
    let chunk_size = 200;
    let sub_testids: Vec<Vec<String>> = testids.chunks(chunk_size).map(|v| v.into()).collect();
    let mut medinfos: Vec<TjMedexaminfo> = vec![];
    // info!("start to query medexam infos......");
    for val in sub_testids.iter() {
      let ret = TjMedexaminfo::query_many(0, 0, &val, &vec![], 0, -1, &vec![], &vec![], &vec![], -1, &"".to_string(), &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("query medinfos error:{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      let medinfo = ret.unwrap();
      medinfos.extend(medinfo.into_iter());
    }
    // info!("start to query patient infos......");
    let pids = medinfos.iter().map(|v| v.tj_pid.to_string()).collect::<Vec<String>>();
    let sub_pids: Vec<Vec<String>> = pids.chunks(chunk_size).map(|v| v.to_owned()).collect();
    let mut ptinfos: Vec<TjPatient> = vec![];
    for val in sub_pids.iter() {
      let ret = TjPatient::query_many(&val, "", &vec![], &vec![], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!(" error:{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      let ptinfo = ret.unwrap();
      ptinfos.extend(ptinfo.into_iter());
    }
    // info!("start to query checkitem infos......");
    let mut checkitems: Vec<TjCheckiteminfo> = vec![];
    for val in sub_testids.iter() {
      info!("start to query checkitem infos......with testids:{:?}", &val);
      let ret = TjCheckiteminfo::query_many(&val, &vec![], flag, -1, &syncids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!(" error:{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      let ci = ret.unwrap();
      checkitems.extend(ci.into_iter());
    }

    let mut items_details: HashMap<String, HashSet<String>> = HashMap::new();

    checkitems.sort_by(|f1, f2| f1.tj_deptid.cmp(&f2.tj_deptid).then(f1.tj_synid.cmp(&f2.tj_synid)));
    info!("start to write all results to file, total:{}", &checkitems.len());
    for item in checkitems.iter() {
      match items_details.get_mut(&item.tj_synid) {
        Some(v) => {
          if item.tj_combineflag == constant::YesOrNo::No as i32 {
            v.insert(item.tj_itemid.to_owned());
          }
        }
        None => {
          if item.tj_combineflag == constant::YesOrNo::No as i32 {
            items_details.insert(item.tj_synid.to_owned(), vec![item.tj_itemid.to_owned()].into_iter().collect());
          } else {
            items_details.insert(item.tj_synid.to_owned(), HashSet::new()); //组合项目
          }
        }
      }
    }

    let mut workbook = Workbook::new();

    let merge_format = Format::new()
      .set_border(FormatBorder::Thin)
      // .set_background_color(Color::Gray)
      .set_align(FormatAlign::Center)
      .set_align(FormatAlign::VerticalCenter);
    let border_format = Format::new()
      .set_border(FormatBorder::Thin)
      // .set_background_color(Color::Gray)
      .set_align(FormatAlign::Center)
      .set_align(FormatAlign::VerticalCenter);

    let abnormal_form = Format::new().set_font_color(Color::Red);
    let normal_form = Format::new().set_font_color(Color::Black);

    // Add a worksheet to the workbook.
    let worksheet = workbook.add_worksheet();
    // Set the column width for clarity.
    let _ = worksheet.set_column_width(0, 13); //.expect("set column with error");
    let _ = worksheet.set_column_width(3, 13); //.expect("set column with error");

    let mut col_idx = 5;
    let mut col_sub_idx = 5;
    let default_itemret = TjCheckiteminfo { ..Default::default() };
    let default_pt = TjPatient { ..Default::default() };

    //第一行，项目名称
    let _ = worksheet.merge_range(0, 0, 1, 0, "姓名", &merge_format); //.expect("merge error");
    let _ = worksheet.merge_range(0, 1, 1, 1, "性别", &merge_format); //.expect("merge error");
    let _ = worksheet.merge_range(0, 2, 1, 2, "体检号", &merge_format); //.expect("merge error");
    let _ = worksheet.merge_range(0, 3, 1, 3, "出生日期", &merge_format); //.expect("merge error");
    let _ = worksheet.merge_range(0, 4, 1, 4, "身份证号", &merge_format); //.expect("merge error");

    // worksheet.write_with_format(0, 0, "姓名".to_string(), &border_format).expect("write error");
    // worksheet.write_with_format(0, 1, "性别".to_string(), &border_format).expect("write error");
    // worksheet.write_with_format(0, 2, "体检号".to_string(), &border_format).expect("write error");

    for (key, val) in items_details.iter() {
      let last_col = col_idx + val.len() as u16 - 1;
      // info!("col_idx is:{}, last column is:{}", col_idx, last_col);
      let comb = checkitems.iter().find(|p| p.tj_itemid.eq_ignore_ascii_case(&key)).unwrap_or(&default_itemret);
      if val.len() == 1 {
        let _ = worksheet.write_with_format(0, col_idx, &comb.tj_itemname, &border_format);
      //.expect("write error");
      } else {
        let _ = worksheet.merge_range(0, col_idx, 0, last_col, &comb.tj_itemname, &merge_format);
        //.expect("merge error");
      }
      col_idx = last_col + 1;
      for item in val.iter() {
        let iteminfo = checkitems.iter().find(|p| p.tj_itemid.eq_ignore_ascii_case(&item)).unwrap_or(&default_itemret);
        let mut itemrange = "".to_string();
        if !iteminfo.tj_itemrange.is_empty() {
          itemrange = format!("({}{})", iteminfo.tj_itemrange, iteminfo.tj_itemunit);
        }
        let _ = worksheet
          .write_with_format(1, col_sub_idx, format!("{}{}", iteminfo.tj_itemname, itemrange), &border_format)
          // .expect("write error")
          ;
        col_sub_idx += 1;
      }
    }
    // 详细结果，包括人员信息
    let mut row_idx = 2;
    for medinfo in medinfos.iter() {
      //人员信息
      let pt = ptinfos.iter().find(|f| f.tj_pid.eq_ignore_ascii_case(&medinfo.tj_pid)).unwrap_or(&default_pt);
      let _ = worksheet.write(row_idx, 0, pt.tj_pname.clone()); //.expect("write error");
      let sex = match pt.tj_psex {
        1 => "男".to_string(),
        2 => "女".to_string(),
        _ => "".to_string(),
      };
      let _ = worksheet.write(row_idx, 1, sex); //.expect("write error");
      let _ = worksheet.write(row_idx, 2, medinfo.tj_testid.to_string()); //.expect("write error");
      let _ = worksheet.write(row_idx, 3, pt.tj_pbirthday.to_string()); //.expect("write error");
      let _ = worksheet.write(row_idx, 4, pt.tj_pidcard.to_string());
      //结果信息
      col_idx = 5; //每次结果信息都是i从第三列开始
      for (k, val) in items_details.iter() {
        if k.eq_ignore_ascii_case("3901") {
          info!("忽略电测听处理......");
          continue;
        }
        for item in val.iter() {
          let iteminfo = checkitems
            .iter()
            .find(|p| p.tj_itemid.eq_ignore_ascii_case(&item) && p.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid))
            .unwrap_or(&default_itemret);
          if iteminfo.tj_abnormalflag == constant::YesOrNo::Yes as i32 {
            let _ = worksheet
              .write_with_format(row_idx, col_idx, format!("{}", iteminfo.tj_result), &abnormal_form)
              // .expect("write error")
              ;
          } else {
            let _ = worksheet
              .write_with_format(row_idx, col_idx, format!("{}", iteminfo.tj_result), &normal_form)
              // .expect("write error")
              ;
          }
          col_idx += 1;
        }
      }
      row_idx += 1;
    }
    // info!("save file to local directory......");
    // Save the file to disk.
    let filename = format!("{dir}/datasum_{}.xlsx", timeutil::current_timestamp());
    let _ = workbook.save(&filename); //.expect("save file error");

    Ok(filename)
  }
}
