
use anyhow::{anyhow, Result};
use rbatis::{crud};
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;

#[derive(C<PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjHazarditem {
  pub id: i64,
  pub tj_hid: i32,
  pub tj_itemid: String,
  pub tj_testtype: i32,
  pub tj_oflag: i8,
  pub tj_showorder: i32,
  pub tj_memo: String,
}
crud!(TjHazarditem {}, "tj_hazarditem");
rbatis::impl_select!(TjHazarditem{query_many(ids:&[i64], testtypes:&[i32]) => 
  "`where id > 0 `
  if !ids.is_empty():
    ` and tj_hid in ${ids.sql()} `
  if !testtypes.is_empty():
    ` and tj_testtype in ${testtypes.sql()} `
  "});
rbatis::impl_delete!(TjHazarditem{delete(ids:&[i64]) => "`where id in ${ids.sql()} `"});

impl TjHazarditem {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjHazarditem, ) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjHazarditem::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjHazarditem::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
}
