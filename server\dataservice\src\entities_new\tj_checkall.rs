//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_checkall")]
pub struct Model {
  #[sea_orm(column_name = "ID", primary_key)]
  pub id: i32,
  #[sea_orm(unique)]
  pub tj_testid: String,
  pub tj_typeid: Option<i32>,
  pub tj_diseasename: Option<String>,
  pub tj_ocuresult: Option<String>,
  pub tj_ocusuggestion: Option<String>,
  pub tj_othresult: Option<String>,
  pub tj_othsuggestion: Option<String>,
  pub tj_staffid: Option<i32>,
  pub tj_checkdate: Option<DateTimeUtc>,
  pub tj_castatus: Option<i32>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
