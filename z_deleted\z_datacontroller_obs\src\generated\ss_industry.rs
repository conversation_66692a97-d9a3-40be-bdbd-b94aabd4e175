//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "ss_industry")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub ss_code: String,
    pub ss_name: String,
    pub ss_parent: String,
    pub ss_pyjm: String,
    pub ss_type: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
