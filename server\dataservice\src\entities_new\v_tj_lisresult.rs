//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "v_tj_lisresult")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tjbh: String,
  pub brxm: String,
  pub xmxh: String,
  pub xmmc: String,
  pub xmdw: String,
  pub xmjg: String,
  pub sfyc: i32,
  pub gdbj: String,
  pub ckdz: String,
  pub ckgz: String,
  pub ckfw: String,
  pub jyys: String,
  pub bgrq: String,
  pub bgys: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
