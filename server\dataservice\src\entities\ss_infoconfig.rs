//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, De<PERSON>ult, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "ss_infoconfig")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub ss_code: String,
  pub ss_name: String,
  pub ss_pyjm: String,
  pub ss_type: i32,
  pub ss_parent: String,
  pub ss_monitor: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
