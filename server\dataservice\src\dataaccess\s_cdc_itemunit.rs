use crate::entities::{prelude::*, s_cdc_itemunit};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter};
// use serde_json::json;

impl SCdcItemunit {
  pub async fn query_many(item_code: &str, db: &DatabaseConnection) -> Result<Vec<SCdcItemunit>> {
    let mut conditions = Condition::all();
    if !item_code.is_empty() {
      conditions = conditions.add(s_cdc_itemunit::Column::ItemCode.contains(item_code));
    }

    let ret = SCdcItemunitEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }
}
