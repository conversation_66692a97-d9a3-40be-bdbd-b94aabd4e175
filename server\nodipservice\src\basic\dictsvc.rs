use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;

use crate::{common::constant, dto::KeyIntDto};
pub struct DictSvc;
impl DictSvc {
  pub async fn update_dictionary(dict: &SsDictionary, db: &DbConnection) -> Result<String> {
    let ret = SsDictionary::save(&dict, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("error: {}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(crate::common::constant::CacheType::Dict as i32, &dbclone).await;
    });
    Ok("".to_string())
  }
  pub async fn get_id_by_testtype(testtype: i32, db: &DbConnection) -> Result<String> {
    // let mut id_ret: String = "".to_string();
    let testtype_dict = crate::SYSCACHE.get().unwrap().get_dict(constant::DictType::DictTesttype as i32, testtype, db).await;
    let precode = testtype_dict.ss_memo.to_string(); //.map_or("".to_string(), |f| f.ss_memo.to_string());

    let identity_code = match testtype {
      x if x == constant::TestType::JKZ as i32 => "jkz".to_string(),
      _ => "tjbh".to_string(),
    };
    info!("current identity code is:{}", &identity_code);
    let ret = DictSvc::query_sys_identity(&identity_code, db).await;
    if ret.as_ref().is_err() {
      error!("get id by testtype error: {}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let id_ret = format!("{}{}", precode, ret.unwrap().id_cvalue);
    // info!("新的ID:{}", &id_ret);
    Ok(id_ret.trim().to_string())
  }

  pub async fn query_sys_dicts_v2(dto: &KeyIntDto, db: &DbConnection) -> Result<Vec<SsDictionary>> {
    let ret = crate::SYSCACHE.get().unwrap().get_dicts(dto.key as i32, db).await;
    Ok(ret)
    // let ret = SsDictionary::query_many( 0, 0).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
  }

  pub async fn query_sys_dicts(db: &DbConnection) -> Result<Vec<SsDictionary>> {
    let ret = SsDictionary::query_many(0, 0, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_sys_identity(code: &str, db: &DbConnection) -> Result<SsIdentity> {
    let ret = SsIdentity::query(code, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let idty = ret.unwrap();
    if idty.is_none() {
      error!("can't find identity by code:{}", &code);
      return Err(anyhow!("can't find identity by code:{}", &code));
    }

    Ok(idty.unwrap())
  }

  pub async fn query_sys_infoconfigs(stype: i32, db: &DbConnection) -> Result<Vec<SsInfoconfig>> {
    let ret = SsInfoconfig::query_many(stype, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
}
