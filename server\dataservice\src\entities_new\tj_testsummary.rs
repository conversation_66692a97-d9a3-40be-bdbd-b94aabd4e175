//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_testsummary")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_testid: String,
  pub tj_deptid: String,
  pub tj_summary: String,
  pub tj_suggestion: String,
  pub tj_isfinished: i32,
  pub tj_doctorid: String,
  pub tj_doctor: String,
  pub tj_date: i64,
  pub tj_forceend: i32,
  pub tj_checkdoctor: String,
  pub tj_checkdate: i64,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
