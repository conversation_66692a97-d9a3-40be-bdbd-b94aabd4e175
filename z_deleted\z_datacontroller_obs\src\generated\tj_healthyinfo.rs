//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_healthyinfo")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub tj_testid: String,
    pub tj_pid: String,
    pub tj_surveydate: i32,
    pub tj_smoke: i32,
    pub tj_smokenum: String,
    pub tj_smokeyear: String,
    pub tj_drink: i32,
    pub tj_drinknum: String,
    pub tj_drinkyear: String,
    pub tj_childrennum: String,
    pub tj_abortionnum: String,
    pub tj_stillbirthnum: String,
    pub tj_prematurenum: String,
    pub tj_abnormalnum: String,
    pub tj_childrenhealthy: String,
    pub tj_menarcheage: String,
    pub tj_period: String,
    pub tj_cycle: String,
    pub tj_menopauseage: String,
    pub tj_modtime: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
