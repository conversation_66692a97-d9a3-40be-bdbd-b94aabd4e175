{"OrderInfo": {"physicalID": "**********", "ApplyNo ": "T**********300100", "PatientID": "20398319", "ChargeType": "", "Costs": "58", "VisitDate": "20250422 14:55:09", "OrderNo": "2025041810062657", "VisitNO": "20250418103184", "ClinDiag": "", "PhysSign": "", "ClinSymp": "", "RelevantDiag": "", "ExamReason": "", "Notice": "", "Status": "1", "ChargeIndicator": "1", "PatientSource": "P", "ExamClassCode": "DR", "ExamClassName": "DR", "ExamClass": "DR", "ReqDeptNo": "A01020420000", "ReqDeptName": "朝晖全科医学健康管理中心", "ReqPhysicianID": "00000154", "ReqPhysician": "陈瑜", "ReqDateTime": "2025-04-22 14:55:09", "ReqMemo": "", "Priority": "0", "SchDateTime": "2025-04-22 14:55:09", "SchMemo": "", "PerformedBy": "A01030010000", "PerformedByName": "", "TechnicianID": "", "Technician": "", "ExamDateTime": "2025-04-22 14:55:09", "VerifierDocNO": "", "VerifierDocName": "", "VerifierDocPhoneNo": "", "VerifierDateTime": "", "ExamSubClass": "DR", "ExamSubClassCode": "DR", "BabyNo": "0", "ExamItems": [{"ItemNo": "1", "ExamItem": "胸部正位片", "ExamItemCode": "8001", "ExamSubClass": "DR", "ExamThirdItemCode": "", "RefOrderNO": "", "RefOrderSubNo": "", "CollectionValume": "0", "SpecimenActionCode": "", "RelevantClinicalInfo": "", "SpecimenSourceName": "", "SpecimenSourcePart": "骨、关节系统", "NumbersOfSampleContainers": "0", "CollectorComment": "", "Costs": "58"}], "PhoneNo": "", "ChiefComplaint": "", "PhysicalExam": "", "DeliveryDate": "2025-04-22 14:55:09", "AttendingDate": "", "DiagnosisType": "", "SliceAmount": "", "Memo": "01", "LaboratoryExam": "", "ImagingExam": "", "PreoperativeRadiotherapyDate": "", "PostoperativeRadiotherapy": "", "PostoperativeRadiotherapyDate": "", "ExamPurpose": "", "SpecialExamItem": "", "SpecializedExam": "", "LeafageNodeID": "", "Empty": "", "SchTime": "", "ReqAreaCode": "01", "PerformedAreaCode": "01", "HistorySummary": "", "YJRemark": "", "PatientType": "T", "RegisterFlag": "0", "WardCode": "", "WardName": ""}, "PatientInfo": {"PatSex": "U", "Name": "沈巧文", "MaritalStatus": "已婚", "MaritalStatusCode": "M", "Address": "", "MailingAddress": "", "ZipCode": "", "BirthPlace": "", "BirthPlaceCode": "", "PresentAddressProvice": "", "PresentAddressCity": "", "PresentAddressCounty": "", "PresentAddressOthers": "", "PresentAddressZipcode": "", "IDNO": "", "PhoneNumber": "", "PhoneNumberBusiness": "", "PhoneNumberHome": "", "BirthPlaceName": "", "Nation": "族", "NationCode": "", "CityzenShipName": "中国", "CityzenShipCode": "CN", "UnitInContrName": "", "JobCode": "", "JobName": "", "NextOfKin": "", "NextOfKinPhone": "", "NextOfKinZipCode": "", "NextOfKinAddr": "", "RelationshipCode": "", "Relationship": "", "HealthCardType": "", "HealthCardNo": "", "PatAge": "125", "DOB": "1899-12"}, "PatVisitInfo": {"PatientClass": "T", "AttendingDoctorID": "", "AttendingDoctorName": "", "ReferringDoctorID": "", "ReferringDoctorName": "", "VIPIndicator": "", "AdmittingDoctorID": "", "AdmittingDoctorName": "", "DischargeDateTime": "", "BloodType": "", "BloodTypeRH": ""}, "GMSpecimen": {"ContaNo": "", "GMSpecimenNo": "", "SpecSource": "", "SpecPartCode": "", "Meno": "", "Amount": "", "TakeOutTime": "", "FixTime": "", "InspectionTime": "", "InspectionDeptCode": "", "InspectionDeptName": "", "InspectionDr": "", "InspectionDrName": ""}}