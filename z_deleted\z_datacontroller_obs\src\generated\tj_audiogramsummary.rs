//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_audiogramsummary")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_adsummary: String,
    pub tj_adsuggestion: String,
    pub tj_isabnormal: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
