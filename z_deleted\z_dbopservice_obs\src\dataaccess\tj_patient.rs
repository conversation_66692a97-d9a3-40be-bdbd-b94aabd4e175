use anyhow::{anyhow, Result};
use rbatis::{crud};
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;

#[derive(C<PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjPatient {
  pub id: i64,
  pub tj_pid: String,
  pub tj_pname: String,
  pub tj_psex: i32,
  pub tj_pmarriage: i32,
  pub tj_ptestnum: i32,
  pub tj_paddress: String,
  pub tj_pphone: String,
  pub tj_pemail: String,
  pub tj_pbirthday: String,
  pub tj_cardtype: i32,
  pub tj_pidcard: String,
  pub tj_pcareer: String,
  pub tj_pmobile: String,
  pub tj_photo: String,
  pub tj_cryptflag: i32,
  pub tj_popdate: i64,
  pub tj_staffid: i32,
  pub tj_pmemo: String,
  pub tj_syncflag: i32,
  pub tj_nation: String,
  pub tj_sign: String,
  pub p_wkid: i64,
  pub tj_patid: String,
}
crud!(TjPatient {}, "tj_patient");
rbatis::impl_select!(TjPatient{query(pid:&str) -> Option => "`where tj_pid = #{pid} `"});
rbatis::impl_select!(TjPatient{query_by_testid(testid:&str) -> Option => "`where tj_pid in (select tj_pid from tj_medexaminfo where tj_testid = #{testid}) `"});
rbatis::impl_select!(TjPatient{query_by_idcard(idcard:&str) -> Option => "`where tj_pidcard = #{idcard} `"});
rbatis::impl_select!(TjPatient{query_many(pids:&[&str], name:&str, idcards:&[&str], testids:&[&str]) => 
  "`where id > 0 `
  if !pids.is_empty():
    ` and tj_pid in ${pids.sql()} `
  if !idcards.is_empty():
    ` and tj_pidcard in ${idcards.sql()} `
  if !testids.is_empty():
    ` and tj_pid in (select tj_pid from tj_medexaminfo where tj_testid in ${testids.sql()}) `
  if name != '':
    ` and tj_pname like #{'%'+name+'%'} `"});
// rbatis::impl_update!(TjPatient{update_patid_by_pid(pid:&str) => "`where id = 1`"});

impl TjPatient {
  pub fn new(pid: &str) -> Self {
    TjPatient {
      tj_pid: pid.to_string(),
      ..Default::default()
    }
  }
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjPatient) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjPatient::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjPatient::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }

  #[rbatis::sql("update tj_patient set tj_patid = ? where tj_pid = ?")]
  async fn update_patid_by_pid(rb: &mut rbatis::RBatis, patid: &str, pid: &str) -> Result<()> {
    impled!()
  }
}
