use anyhow::{anyhow, Result};
use tracing::*;
// use dbopservice::{
//   dataaccess::{tj_instrumentinfo::TjInstrumentinfo, tj_itemrefinfo::TjItemrefinfo},
//   dbinit::DbConnection,
// };
use dataservice::{dbinit::DbConnection, entities::prelude::*};

pub struct InstrumentSvc;

impl InstrumentSvc {
  pub async fn query_instrumentinfos(db: &DbConnection) -> Result<Vec<TjInstrumentinfo>> {
    let ret = TjInstrumentinfo::query_many("", &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let infos = ret.unwrap();
    Ok(infos)
  }

  pub async fn save_instrumentinfo(info: &TjInstrumentinfo, db: &DbConnection) -> Result<i64> {
    let ret = TjInstrumentinfo::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    Ok(ret.unwrap())
  }
  pub async fn delete_instrumentinfo(id: i64, db: &DbConnection) -> Result<u64> {
    if id <= 0 {
      return Err(anyhow!("id <= 0, not allowed"));
    }
    let ret = TjInstrumentinfo::delete(&vec![id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    Ok(1 as u64)
  }

  pub async fn query_instrument_items(db: &DbConnection, struid: &String) -> Result<Vec<TjItemrefinfo>> {
    let mut struids: Vec<String> = vec![];
    if !struid.is_empty() {
      struids.push(struid.to_string());
    }
    let ret = TjItemrefinfo::query_many(&struids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let infos = ret.unwrap();
    Ok(infos)
  }
  pub async fn save_instrumentinfo_items(info: &TjItemrefinfo, db: &DbConnection) -> Result<i64> {
    let ret = TjItemrefinfo::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    Ok(ret.unwrap())
  }
  pub async fn delete_instrumentinfo_items(id: i64, db: &DbConnection) -> Result<u64> {
    if id <= 0 {
      return Err(anyhow!("id <= 0, not allowed"));
    }
    let ret = TjItemrefinfo::delete(&vec![id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    Ok(ret.unwrap())
  }
}
