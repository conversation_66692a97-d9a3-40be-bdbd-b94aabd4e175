use anyhow::{anyhow, Result};
use tracing::*;
// use dbopservice::{
//   dataaccess::{tj_audiogramdetail::TjAudiogramdetail, tj_audiogramresult::TjAudiogramresult, tj_audiogramsummary::TjAudiogramsummary, tj_testsummary::TjTestsummary},
//   dbinit::DbConnection,
// };

use crate::common::constant::{self, AUDIOGRAM_DEPTID};
use dataservice::{dbinit::DbConnection, entities::prelude::*};

pub struct AudiogramSvc;

impl AudiogramSvc {
  pub async fn query_audiogram_details(testid: &String, db: &DbConnection) -> Result<(Vec<TjAudiogramdetail>, TjAudiogramresult)> {
    let ret = TjAudiogramdetail::query_many(&vec![testid.to_owned()], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    //计算
    let infos = ret.unwrap();
    let result = AudiogramSvc::compute_audiogram_result(&testid, &infos);
    Ok((infos, result))
  }

  pub async fn save_audiogram_details(details: &Vec<TjAudiogramdetail>, db: &DbConnection) -> Result<(Vec<TjAudiogramdetail>, TjAudiogramresult)> {
    if details.len() <= 0 {
      return Err(anyhow!("details are empty"));
    }
    let mut details = details.to_owned();
    let testid = details.get(0).unwrap().tj_testid.to_string();
    if testid.is_empty() {
      return Err(anyhow!("testid is empty, not allowed"));
    }

    details.retain(|v| v.tj_result as f32 > constant::EMPTY_AUDIOGRAM_VALUE);
    // info!("保存电测听数据:{:?}", &details);
    if details.len() <= 0 {
      //相当于清除数据
      let ret = TjAudiogramdetail::delete(&vec![testid.clone()], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      return Ok((vec![], TjAudiogramresult { ..Default::default() }));
      // return Err(anyhow!("电测听结果数据为空......"));
    }

    let ret = TjAudiogramdetail::query_many(&vec![testid.clone()], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut infos = ret.unwrap();
    info!("已存在的电测听数据:{:?}", &infos);

    //to insert
    let insert_details: Vec<TjAudiogramdetail> = details
      .iter_mut()
      .filter(|f| {
        infos
          .iter()
          .find(|&f2| f2.tj_testid.eq_ignore_ascii_case(&f.tj_testid) && f2.tj_itemid.eq_ignore_ascii_case(&f.tj_itemid) && f2.tj_ear == f.tj_ear && f2.tj_adtype == f.tj_adtype)
          .is_none()
      })
      .map(|v| {
        v.id = 0;
        v.to_owned()
      })
      .collect();
    info!("需要新增的结果：{:?}", &insert_details);
    if insert_details.len() > 0 {
      let ret = TjAudiogramdetail::save_many(&insert_details, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    //to delete
    let delete_details: Vec<TjAudiogramdetail> = infos
      .iter()
      .filter(|&f| {
        details
          .iter()
          .find(|&f2| f2.tj_testid.eq_ignore_ascii_case(&f.tj_testid) && f2.tj_itemid.eq_ignore_ascii_case(&f.tj_itemid) && f2.tj_ear == f.tj_ear && f2.tj_adtype == f.tj_adtype)
          .is_none()
      })
      .map(|v| v.to_owned())
      .collect();
    info!("需要删除的结果：{:?}", &delete_details);
    if delete_details.len() > 0 {
      let del_ids = delete_details.iter().map(|v| v.id).collect::<Vec<i64>>();
      let ret = TjAudiogramdetail::delete_by_ids(&del_ids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    //to update
    let mut update_details: Vec<TjAudiogramdetail> = vec![];
    for val in infos.iter_mut() {
      let new_val = details
        .iter()
        .find(|&f2| f2.tj_testid.eq_ignore_ascii_case(&val.tj_testid) && f2.tj_itemid.eq_ignore_ascii_case(&val.tj_itemid) && f2.tj_ear == val.tj_ear && f2.tj_adtype == val.tj_adtype);
      if new_val.is_none() {
        continue;
      }
      let new_val = new_val.unwrap();
      val.tj_result = new_val.tj_result;
      val.tj_revise = new_val.tj_revise;
      val.tj_cover = new_val.tj_cover;
      update_details.push(val.to_owned());
    }
    info!("需要修改的结果：{:?}", &update_details);
    if update_details.len() > 0 {
      let ret = TjAudiogramdetail::save_many(&update_details, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }

    // let ret = TjAudiogramdetail::delete_by_column( "tj_testid", &testid).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }

    // let ret = TjAudiogramdetail::save_many( &details).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }

    // let ret = TjAudiogramdetail::query( &testid).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let infos = ret.unwrap();
    //计算
    let result = AudiogramSvc::compute_audiogram_result(&testid, &details);
    Ok((infos, result))
  }

  pub async fn delete_audiogram_details(testid: &String, db: &DbConnection) -> Result<()> {
    let ret = TjAudiogramdetail::delete(&vec![testid.to_owned()], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    //计算
    Ok(())
  }

  pub async fn auto_summary_audiogram(testid: &String, db: &DbConnection) -> Result<TjTestsummary> {
    if testid.is_empty() {
      return Ok(TjTestsummary::default());
    }
    let ret = TjMedexaminfo::query(testid, &db.get_connection()).await;
    if ret.is_err() {
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }
    let ret = ret.unwrap();
    if ret.is_none() {
      return Err(anyhow!("不能根据体检号{testid}找到体检信息"));
    }
    let medinfo = ret.unwrap();
    let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
    if ret.is_err() {
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }
    let ret = ret.unwrap();
    if ret.is_none() {
      return Err(anyhow!("不能根据体检号{testid}找到体检信息"));
    }
    let patient = ret.unwrap();
    AudiogramSvc::auto_summary_audiogram_details(&medinfo, &patient, db).await
  }

  pub async fn auto_summary_audiogram_details(medinfo: &TjMedexaminfo, patient: &TjPatient, db: &DbConnection) -> Result<TjTestsummary> {
    if medinfo.tj_testid.is_empty() {
      return Ok(TjTestsummary::default());
    }
    let testid = medinfo.tj_testid.to_string();
    let sex = patient.tj_psex;
    let age = medinfo.tj_age;

    let ret = TjTestsummary::query_one(&testid, AUDIOGRAM_DEPTID, &db.get_connection()).await;
    if ret.is_err() {
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }
    let mut summary = TjTestsummary {
      tj_testid: testid.to_string(),
      tj_deptid: AUDIOGRAM_DEPTID.to_string(),
      id: 0,
      ..Default::default()
    };
    let ret = ret.unwrap();
    if ret.is_some() {
      summary = ret.unwrap();
    }

    let mut summary_result: Vec<String> = vec![];
    // let mut summary_result = "".to_string();
    let mut lowval = -50;
    if patient.tj_psex == constant::Sex::Femal as i32 {
      lowval = -35;
    }
    let high_gaopin_avg = 40;
    let high_yupin_avg = 25;

    let details = TjAudiogramdetail::query(&medinfo.tj_testid, &db.get_connection()).await;
    if details.is_err() {
      return Err(anyhow!("{}", details.unwrap_err().to_string()));
    }
    let details = details.unwrap();

    let result = AudiogramSvc::compute_audiogram_result(&testid, &details);
    //高频
    if (result.tj_avgsgp as i32) >= high_gaopin_avg {
      summary_result.push(format!("双耳高频平均听阈: {} ↑", result.tj_avgsgp));
    }
    if (result.tj_avgsgp as i32) <= lowval {
      summary_result.push(format!("双耳高频平均听阈: {} ↓", result.tj_avgsgp));
    }

    //听阈加权
    if (result.tj_avgzty as i32) >= high_yupin_avg {
      summary_result.push(format!("左耳听阈加权值: {} ↑", result.tj_avgzty));
    }
    if (result.tj_avgzty as i32) <= lowval {
      summary_result.push(format!("左耳听阈加权值: {} ↓", result.tj_avgzty));
    }
    if (result.tj_avgyty as i32) >= high_yupin_avg {
      summary_result.push(format!("右耳听阈加权值: {} ↑", result.tj_avgyty));
    }
    if (result.tj_avgyty as i32) <= lowval {
      summary_result.push(format!("右耳听阈加权值: {} ↑", result.tj_avgyty));
    }
    //语频
    if (result.tj_avgsyp as i32) >= high_yupin_avg {
      summary_result.push(format!("双耳语频平均听阈: {} ↑", result.tj_avgsyp));
    }
    if (result.tj_avgsyp as i32) <= lowval {
      summary_result.push(format!("双耳语频平均听阈: {} ↓", result.tj_avgsyp));
    }
    if (result.tj_avgzyp as i32) >= high_yupin_avg {
      summary_result.push(format!("左耳语频平均听阈: {} ↑", result.tj_avgzyp));
    }
    if (result.tj_avgzyp as i32) <= lowval {
      summary_result.push(format!("左耳语频平均听阈: {} ↓", result.tj_avgzyp));
    }
    if (result.tj_avgyyp as i32) >= high_yupin_avg {
      summary_result.push(format!("右耳语频平均听阈: {} ↑", result.tj_avgyyp));
    }
    if result.tj_avgyyp as i32 <= lowval {
      summary_result.push(format!("右耳语频平均听阈: {} ↑", result.tj_avgyyp));
    }
    //以下不需要
    // if result.tj_avgzgp as i32 >= high_gaopin_avg {
    //   summary_result += format!("气导左耳高频平均听阈: {} ↑\r\n", result.tj_avgzgp).as_str();
    // }
    // if result.tj_avgzgp as i32 <= lowval {
    //   summary_result += format!("气导左耳高频平均听阈: {} ↓\r\n", result.tj_avgzgp).as_str();
    // }
    // if result.tj_avgygp as i32 >= high_gaopin_avg {
    //   summary_result += format!("气导右耳高频平均听阈: {} ↓\r\n", result.tj_avgygp).as_str();
    // }
    // if result.tj_avgygp as i32 <= lowval {
    //   summary_result += format!("气导右耳高频平均听阈: {} ↑\r\n", result.tj_avgygp).as_str();
    // }
    // if result.tj_avgsty as i32 >= high_yupin_avg {
    //   summary_result += format!("气导双耳听阈加权值: {} ↑\r\n", result.tj_avgsty).as_str();
    // }
    // if result.tj_avgsty as i32 <= lowval {
    //   summary_result += format!("气导双耳听阈加权值: {} ↓\r\n", result.tj_avgsty).as_str();
    // }
    //男性低值：-50 女性低值：-35
    //高频平均值高值：40，
    //语频平均值高值：25
    //结果值高值：25
    let mut details = details.to_owned();
    details.sort_by(|a, b| a.tj_ear.cmp(&b.tj_ear).then(a.tj_freq.cmp(&b.tj_freq)));
    for val in details.iter() {
      if val.tj_result as f32 == constant::EMPTY_AUDIOGRAM_VALUE {
        continue;
      }
      let detail_ret = AudiogramSvc::get_air_abnormal(sex, age, &val.tj_itemid, val.tj_ear, val.tj_freq, val.tj_adtype, val.tj_result, val.tj_revise, db).await;
      summary_result.extend(detail_ret);
    }
    let bone_ret = AudiogramSvc::get_bone_summaries(&details);
    summary_result.extend(bone_ret);
    // if !bone_ret.is_empty() {
    //   summary_result += bone_ret.as_str();
    // }
    summary.tj_summary = summary_result.join("\n");

    Ok(summary)
  }

  pub async fn query_audiogram_summaries(db: &DbConnection) -> Result<Vec<TjAudiogramsummary>> {
    let ret = TjAudiogramsummary::query_many(&db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_audiogram_summary(info: &TjAudiogramsummary, db: &DbConnection) -> Result<i64> {
    let ret = TjAudiogramsummary::save(info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn delete_audiogram_summary(info: &TjAudiogramsummary, db: &DbConnection) -> Result<i64> {
    let infoids: Vec<i64> = vec![info.id.into()];
    let ret = TjAudiogramsummary::delete(&infoids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap() as i64)
  }

  //计算结果
  pub fn compute_audiogram_result(testid: &String, infos: &Vec<TjAudiogramdetail>) -> TjAudiogramresult {
    let mut m_adresult = TjAudiogramresult {
      id: 0,
      tj_testid: testid.to_string(),
      tj_avgsgp: constant::EMPTY_AUDIOGRAM_VALUE,
      tj_avgyyp: constant::EMPTY_AUDIOGRAM_VALUE,
      tj_avgzyp: constant::EMPTY_AUDIOGRAM_VALUE,
      tj_avgsyp: constant::EMPTY_AUDIOGRAM_VALUE,
      tj_avgygp: constant::EMPTY_AUDIOGRAM_VALUE,
      tj_avgzgp: constant::EMPTY_AUDIOGRAM_VALUE,
      tj_avgyty: constant::EMPTY_AUDIOGRAM_VALUE,
      tj_avgzty: constant::EMPTY_AUDIOGRAM_VALUE,
      tj_avgsty: constant::EMPTY_AUDIOGRAM_VALUE,
    };

    //先计算右耳
    let v1 = infos
      .iter()
      .find(|&p| p.tj_freq == 500 && p.tj_ear == constant::TransEar::Right as i32 && p.tj_adtype == constant::TransType::Air as i32)
      .map_or(constant::EMPTY_AUDIOGRAM_VALUE, |f| f.tj_revise as f32);
    let v2 = infos
      .iter()
      .find(|&p| p.tj_freq == 1000 && p.tj_ear == constant::TransEar::Right as i32 && p.tj_adtype == constant::TransType::Air as i32)
      .map_or(constant::EMPTY_AUDIOGRAM_VALUE, |f| f.tj_revise as f32);
    let v3 = infos
      .iter()
      .find(|&p| p.tj_freq == 2000 && p.tj_ear == constant::TransEar::Right as i32 && p.tj_adtype == constant::TransType::Air as i32)
      .map_or(constant::EMPTY_AUDIOGRAM_VALUE, |f| f.tj_revise as f32);
    let v4 = infos
      .iter()
      .find(|&p| p.tj_freq == 3000 && p.tj_ear == constant::TransEar::Right as i32 && p.tj_adtype == constant::TransType::Air as i32)
      .map_or(constant::EMPTY_AUDIOGRAM_VALUE, |f| f.tj_revise as f32);
    let v5 = infos
      .iter()
      .find(|&p| p.tj_freq == 4000 && p.tj_ear == constant::TransEar::Right as i32 && p.tj_adtype == constant::TransType::Air as i32)
      .map_or(constant::EMPTY_AUDIOGRAM_VALUE, |f| f.tj_revise as f32);
    let v6 = infos
      .iter()
      .find(|&p| p.tj_freq == 6000 && p.tj_ear == constant::TransEar::Right as i32 && p.tj_adtype == constant::TransType::Air as i32)
      .map_or(constant::EMPTY_AUDIOGRAM_VALUE, |f| f.tj_revise as f32);

    let mut yyp_1 = 0_f32;
    if v1 > constant::EMPTY_AUDIOGRAM_VALUE && v2 > constant::EMPTY_AUDIOGRAM_VALUE && v3 > constant::EMPTY_AUDIOGRAM_VALUE && v5 > constant::EMPTY_AUDIOGRAM_VALUE {
      let right_avg = ((v1 + v2 + v3) / 3_f32) * 0.9 + v5 * 0.1; //右耳平均加权值
      m_adresult.tj_avgyty = right_avg.round();

      let yyp = (v1 + v2 + v3) / 3_f32; //右语频
      yyp_1 = v1 + v2 + v3;
      m_adresult.tj_avgyyp = yyp.round();
    }

    let mut ygp_1: f32 = 0_f32;
    if v4 > constant::EMPTY_AUDIOGRAM_VALUE && v5 > constant::EMPTY_AUDIOGRAM_VALUE && v6 > constant::EMPTY_AUDIOGRAM_VALUE {
      let ygp = (v4 + v5 + v6) / 3_f32; //右高频
      ygp_1 = v4 + v5 + v6;
      m_adresult.tj_avgygp = ygp.round();
    }

    //左耳
    let v1 = infos
      .iter()
      .find(|&p| p.tj_freq == 500 && p.tj_ear == constant::TransEar::Left as i32 && p.tj_adtype == constant::TransType::Air as i32)
      .map_or(constant::EMPTY_AUDIOGRAM_VALUE, |f| f.tj_revise as f32);
    let v2 = infos
      .iter()
      .find(|&p| p.tj_freq == 1000 && p.tj_ear == constant::TransEar::Left as i32 && p.tj_adtype == constant::TransType::Air as i32)
      .map_or(constant::EMPTY_AUDIOGRAM_VALUE, |f| f.tj_revise as f32);
    let v3 = infos
      .iter()
      .find(|&p| p.tj_freq == 2000 && p.tj_ear == constant::TransEar::Left as i32 && p.tj_adtype == constant::TransType::Air as i32)
      .map_or(constant::EMPTY_AUDIOGRAM_VALUE, |f| f.tj_revise as f32);
    let v4 = infos
      .iter()
      .find(|&p| p.tj_freq == 3000 && p.tj_ear == constant::TransEar::Left as i32 && p.tj_adtype == constant::TransType::Air as i32)
      .map_or(constant::EMPTY_AUDIOGRAM_VALUE, |f| f.tj_revise as f32);
    let v5 = infos
      .iter()
      .find(|&p| p.tj_freq == 4000 && p.tj_ear == constant::TransEar::Left as i32 && p.tj_adtype == constant::TransType::Air as i32)
      .map_or(constant::EMPTY_AUDIOGRAM_VALUE, |f| f.tj_revise as f32);
    let v6 = infos
      .iter()
      .find(|&p| p.tj_freq == 6000 && p.tj_ear == constant::TransEar::Left as i32 && p.tj_adtype == constant::TransType::Air as i32)
      .map_or(constant::EMPTY_AUDIOGRAM_VALUE, |f| f.tj_revise as f32);

    let mut zyp_1 = 0_f32;
    if v1 > constant::EMPTY_AUDIOGRAM_VALUE && v2 > constant::EMPTY_AUDIOGRAM_VALUE && v3 > constant::EMPTY_AUDIOGRAM_VALUE && v5 > constant::EMPTY_AUDIOGRAM_VALUE {
      let right_avg = ((v1 + v2 + v3) / 3 as f32) * 0.9 + v5 * 0.1; //左耳平均加权值
      m_adresult.tj_avgzty = right_avg.round();
      let zyp = (v1 + v2 + v3) / 3_f32; //左语频
      zyp_1 = v1 + v2 + v3;
      m_adresult.tj_avgzyp = zyp.round();
    }

    let mut zgp_1: f32 = 0_f32;
    if v4 > constant::EMPTY_AUDIOGRAM_VALUE && v5 > constant::EMPTY_AUDIOGRAM_VALUE && v6 > constant::EMPTY_AUDIOGRAM_VALUE {
      let zgp = (v4 + v5 + v6) / 3_f32; //左高频
      zgp_1 = v4 + v5 + v6;
      m_adresult.tj_avgzgp = zgp.round();
    }

    //双耳
    if m_adresult.tj_avgzgp > constant::EMPTY_AUDIOGRAM_VALUE && m_adresult.tj_avgygp > constant::EMPTY_AUDIOGRAM_VALUE {
      // let sgp = (m_adresult.tj_avgzgp + m_adresult.tj_avgygp) / 2_f32;
      let sgp = (zgp_1 + ygp_1) / 6_f32;
      m_adresult.tj_avgsgp = sgp.round();
    }
    if m_adresult.tj_avgzyp > constant::EMPTY_AUDIOGRAM_VALUE && m_adresult.tj_avgyyp > constant::EMPTY_AUDIOGRAM_VALUE {
      // let syp = (m_adresult.tj_avgzyp + m_adresult.tj_avgyyp) / 2_f32;
      let syp = (zyp_1 + yyp_1) / 6_f32;
      m_adresult.tj_avgsyp = syp.round();
    }
    if m_adresult.tj_avgyty > constant::EMPTY_AUDIOGRAM_VALUE && m_adresult.tj_avgzty > constant::EMPTY_AUDIOGRAM_VALUE {
      let sty = (m_adresult.tj_avgyty + m_adresult.tj_avgzty) / 2_f32;
      m_adresult.tj_avgsty = sty.round();
    }
    m_adresult
  }

  async fn get_air_abnormal(sex: i32, age: i32, itemid: &String, ear: i32, freq: i32, trans: i32, result: i32, revise: i32, db: &DbConnection) -> Vec<String> {
    let mut lowval = -50;
    if sex == constant::Sex::Femal as i32 {
      lowval = -35;
    }
    let mut highval = 25;

    let itemrange = crate::basic::itemsvc::ItemSvc::get_item_rangeinfo(itemid, age, sex, &db).await;
    if let Ok(lowval1) = itemrange.tj_lowvalue.parse::<i32>() {
      lowval = lowval1;
    }
    if let Ok(highval1) = itemrange.tj_uppervalue.parse::<i32>() {
      highval = highval1;
    }

    let mut abstr: Vec<String> = vec![];
    if trans == constant::TransType::Bone as i32 {
      return abstr;
    }
    // if trans == constant::TransType::Air as i32 {
    if revise >= highval || result >= highval {
      abstr.push(format!(
        "{}{}(dB):{}Hz {}({}) ↑",
        AudiogramSvc::get_ear_name(ear),
        AudiogramSvc::get_transtype_name(trans),
        freq,
        result,
        revise,
        // lowval,
        // highval
      ));
    }
    if revise <= lowval || result <= lowval {
      abstr.push(format!(
        "{}{}(dB):{}Hz {}({}) ↓",
        AudiogramSvc::get_ear_name(ear),
        AudiogramSvc::get_transtype_name(trans),
        freq,
        result,
        revise,
        // lowval,
        // highval
      ));
    }
    // }
    abstr
  }
  fn get_bone_summaries(details: &Vec<TjAudiogramdetail>) -> Vec<String> {
    // let mut boneret = "".to_string();
    let mut boneret: Vec<String> = vec![];
    let mut y_bone_details: Vec<TjAudiogramdetail> = details
      .iter()
      .filter(|&v| v.tj_adtype == constant::TransType::Bone as i32 && v.tj_ear == constant::TransEar::Right as i32 && v.tj_result > -100)
      .map(|v| v.to_owned())
      .collect();
    let mut z_bone_details: Vec<TjAudiogramdetail> = details
      .iter()
      .filter(|&v| v.tj_adtype == constant::TransType::Bone as i32 && v.tj_ear == constant::TransEar::Left as i32 && v.tj_result > -100)
      .map(|v| v.to_owned())
      .collect();
    y_bone_details.sort_by(|a, b| a.tj_freq.cmp(&b.tj_freq));
    z_bone_details.sort_by(|a, b| a.tj_freq.cmp(&b.tj_freq));

    if y_bone_details.len() > 0 {
      let mut boneret1 = format!("右耳(骨导)dB:");
      for val in y_bone_details.iter() {
        boneret1 += format!("{}Hz:{}({}); ", val.tj_freq, val.tj_result, val.tj_revise).as_str();
      }
      // boneret += "\r\n";
      boneret.push(boneret1);
    }
    if z_bone_details.len() > 0 {
      let mut boneret1 = format!("左耳(骨导)dB:");
      for val in z_bone_details.iter() {
        boneret1 += format!("{}Hz:{}({}); ", val.tj_freq, val.tj_result, val.tj_revise).as_str();
      }
      // boneret += "\r\n";
      boneret.push(boneret1);
    }
    boneret
  }
  fn get_ear_name(ear: i32) -> String {
    // x if x == constant::TransEar::Right as i32
    let ret = match ear {
      0 => "右耳".to_string(),
      1 => "左耳".to_string(),
      _ => "未知".to_string(),
    };
    ret
  }
  fn get_transtype_name(trans: i32) -> String {
    let ret = match trans {
      0 => "气导".to_string(),
      1 => "骨导".to_string(),
      _ => "未知".to_string(),
    };
    ret
  }
}
