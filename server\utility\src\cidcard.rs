use idcard::{fake, Gender, Identity};
pub struct IdCard {
  id: Identity,
}

impl IdCard {
  pub fn new(idnum: &String) -> Self {
    let id = Identity::new(&idnum);
    IdCard { id }
  }

  pub fn is_valid(&self) -> bool {
    self.id.is_valid()
  }
  pub fn get_gender(&self) -> i32 {
    // let id = Identity::new(&idnum);
    if !self.id.is_valid() {
      return 0;
    }
    let gender = self.id.gender();
    if gender.is_none() {
      return 0;
    }
    let gender = gender.unwrap();
    if gender == Gender::Male {
      return 1;
    } else {
      return 2;
    }
  }

  pub fn get_birthdate(&self) -> String {
    // let id = Identity::new(&idnum);
    if !self.id.is_valid() {
      return String::new();
    }
    let birth_date = self.id.birth_date();
    if birth_date.is_none() {
      return String::new();
    }
    birth_date.unwrap()
  }
  pub fn get_age(&self) -> i32 {
    if !self.id.is_valid() {
      return 0;
    }
    let age = self.id.age().unwrap_or_default();
    age as i32
  }
  pub fn get_region(&self) -> String {
    if !self.id.is_valid() {
      return String::new();
    }
    self.id.region().unwrap_or_default().to_string()
  }
  pub fn get_fakge_number(&self) -> String {
    return fake::rand().unwrap_or_default();
  }
}
