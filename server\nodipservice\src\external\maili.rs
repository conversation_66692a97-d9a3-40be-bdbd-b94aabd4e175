use anyhow::{anyhow, Result};
// use std::str::FromStr;
// use dbopservice::{
//   dataaccess::{tj_audiogramdetail::TjAudiogramdetail, tj_medexaminfo::TjMedexaminfo, tj_patient::TjPatient, tj_testsummary::TjTestsummary},
//   dbinit::DbConnection,
// };
use crate::common::constant;
use chrono::{Local, NaiveDateTime};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tokio::net::TcpStream;
use tracing::*;

// use crate::{common::constant::DatabaseType, config::settings::Settings};
// use serde::{Deserialize, Serialize};
// use tiberius::time::DateTime;

// #[derive(Clone, Debug)]
pub struct _TesterInfo {
  // pub id: i64,
  pub testid: String,
  pub tname: String,
  // pub subname: String,
  pub sex: String,
  pub birthdate: String,
  pub phone: String,
  pub address: String,
  pub idcard: String,
  pub guominshi: String,
  pub memo: String,
  pub createdate: String,
  pub completed: i32, //0 为未读取，1 为已读取
}

// rbatis::crud!(TesterInfo {}, "testerinfo");
// rbatis::impl_select!(TesterInfo{query(testid:&str) -> Option => "`where testid = #{testid} `"}, "testerinfo");

#[derive(Debug)]
pub struct TestResults {
  pub id: i64,
  pub testid: String,
  pub testnum: String,
  pub testdate: String,
  pub testtype: String,   //长度为 1 或以上的任意字符串类格式。当上传纯音听力报告时，该项为字符 1，当上传噪声聋报告时，该项为字符 2。
  pub testdev: String,    //测试设备
  pub testdoctor: String, //测试医师
  pub testdata: String,   //测试数据
  pub diagnosis: String,
  pub uploaddate: String,
  pub aux: String,
}
// rbatis::crud!(TestResults {}, "testresults");
// rbatis::impl_select!(TestResults{query(testid:&str)->Option => "`where testid = #{testid} `"}, "testresults");

// #[derive(Clone, Debug, Serialize, Deserialize)]
// struct FreqTestData {
//   pub testtype: i32, //测试类型：0-气导测试，1-骨导测试，2-声场测试。
//   pub freq: i32,     //测试频率：0-125Hz，1-250Hz，2-500Hz，3-750Hz，4-1000Hz，5-1500Hz，6-2000Hz，7-3000Hz，8-4000Hz，9-6000Hz，10-8000 Hz。
//   pub result: i32,   //测试分贝：实际分贝值
//   pub status: i32,   //测试状态：0-无掩蔽无响应，1-无掩蔽有响应，2-有掩蔽无响应，3-有掩蔽有响应
// }
pub struct MailiService;
impl MailiService {
  pub async fn send_testinfo(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, audiodburi: &String) -> Result<()> {
    if !audiodburi.is_empty() {
      // if config.audiogram.dbtype == DatabaseType::MsSql as i32 {
      let ret = MailiService::send_testinfo_with_tiberius(medinfo, ptinfo, audiodburi).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("send error:{:?}", ret.as_ref().unwrap_err().to_string()));
      }
      // }
    }

    Ok(())
  }

  pub async fn query_result_by_testid(testid: &String, dburi: &String, db: &DbConnection) -> Result<(Vec<TjAudiogramdetail>, TjTestsummary)> {
    let ret = TjTestsummary::query_one(&testid, crate::common::constant::AUDIOGRAM_DEPTID, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let tsum = ret.unwrap();
    if tsum.is_none() {
      return Err(anyhow!("没有该体检者在电测听科室的检查信息"));
    }
    let mut summary = tsum.unwrap();

    //有电测听数据
    // info!("开始电测听对接......");
    let ret = TjMedexaminfo::query(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let medinfo = ret.unwrap();
    if medinfo.is_none() {
      return Err(anyhow!("没有该体检编号的体检信息"));
    }
    let medinfo = medinfo.unwrap();
    if medinfo.tj_checkstatus >= constant::ExamStatus::Allchecked as i32 {
      return Err(anyhow!("该体检编号已经总检，不能获取电测听数据"));
    }

    let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ptret = ret.unwrap();
    if ptret.is_none() {
      return Err(anyhow!("没有该体检编号的人员信息"));
    }
    let ptinfo = ptret.unwrap();
    //query from database
    // let ret = TestResults::query(rb, &testid).await;
    // if ret.as_ref().is_err() {
    //   error!("query test results error:{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    let ret = MailiService::query_result_with_tiberius(&medinfo.tj_testid, dburi).await;
    if ret.as_ref().is_err() {
      error!("query test results error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let results = ret.unwrap();
    if results.is_none() {
      return Err(anyhow!("无该体检号的结果数据"));
    }
    let result = results.unwrap();
    summary.tj_doctor = result.testdoctor.to_string();
    if summary.tj_checkdoctor.is_empty() {
      summary.tj_checkdoctor = result.testdoctor.to_string();
    }
    summary.tj_doctorid = "".to_string();
    summary.tj_checkdate = utility::timeutil::convert_datetime_to_timestamp(&result.testdata, "%Y-%m-%d %H:%M:%S.%f"); //  result.testdate.timestamp();
    summary.tj_date = summary.tj_checkdate; //result.testdate.timestamp();
    summary.tj_summary = result.diagnosis.to_string();

    // let checkdate =
    let ret = MailiService::parse_test_result(&medinfo, &ptinfo, &result.testdata).await;
    if ret.as_ref().is_err() {
      error!("query test results error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let audio_details = ret.unwrap();
    Ok((audio_details, summary))
    // let aduio_result = AudiogramSvc::compute_audiogram_result(&medinfo.tj_testid, &audio_details);
    // Ok((audio_details, aduio_result))
  }

  async fn send_testinfo_with_tiberius(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, audiodburi: &String) -> Result<u64> {
    // let tconfig = tiberius::Config::from_jdbc_string(audiodburi);
    let tconfig = tiberius::Config::from_ado_string(&audiodburi);
    if tconfig.is_err() {
      error!("invalid url config:{:?}", &audiodburi);
      return Err(anyhow!("invalid config:{}", tconfig.unwrap_err().to_string()));
    }
    let tconfig = tconfig.unwrap();
    let tcp = TcpStream::connect(tconfig.get_addr()).await; //.expect("tcpstream connect error");
    if tcp.is_err() {
      error!("连接错误 {:?}", &tcp);
      return Err(anyhow!("invalid tcp:{}", tcp.unwrap_err().to_string()));
    }
    let tcp = tcp.unwrap();
    let ret = tcp.set_nodelay(true); //.expect("tcp stream set no delay error");
    if ret.as_ref().is_err() {
      return Err(anyhow!("set nodaly error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = tiberius::Client::connect(tconfig, tokio_util::compat::TokioAsyncWriteCompatExt::compat_write(tcp)).await; //.expect("connect with tiberium client error");
    if ret.as_ref().is_err() {
      return Err(anyhow!("connect error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut client = ret.unwrap();
    info!("start to process testerinfo......");

    info!("check if testid:{} existed", &medinfo.tj_testid);
    let sql = format!("select testid from dbo.testerinfo where testid = '{}'", &medinfo.tj_testid);
    let ret = client.query(&sql, &[]).await;
    info!("query result:{:?}", &ret);
    if ret.as_ref().is_err() {
      error!("select from testerinfo error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let stream = ret.unwrap();
    let ret = stream.into_row().await;
    if ret.as_ref().is_err() {
      error!("insert into hisjcrw error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    if let Ok(result) = ret {
      if let Some(_row) = result {
        let sql = format!("delete from dbo.testerinfo where testid = '{}'", &medinfo.tj_testid);
        let ret = client.query(&sql, &[]).await;
        if ret.as_ref().is_err() {
          error!("delete from testerinfo error:{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
    }

    let testid = medinfo.tj_testid.to_string();
    let tname = ptinfo.tj_pname.to_string();
    let sex = match ptinfo.tj_psex {
      1 => "男".to_string(),
      2 => "女".to_string(),
      _ => "".to_string(),
    };
    // let birthdate = match NaiveDateTime::parse_from_str(format!("{} 00:00:00", &ptinfo.tj_pbirthday).as_str(), "%Y-%m-%d %H:%M:%S") {
    //   Ok(v) => Local.from_local_datetime(&v).unwrap(),
    //   Err(_) => Local::now(),
    // };
    let birthdate = format!("{} 00:00:00", &ptinfo.tj_pbirthday);
    let phone = ptinfo.tj_pphone.to_string();
    let address = ptinfo.tj_paddress.to_string();
    let idcard = ptinfo.tj_pidcard.to_string();
    let guominshi = "".to_string();
    let memo = "".to_string();
    let createdate = Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
    let completed = 0;
    let sql = format!(
      r#"insert into dbo.testerinfo(testid, tname,sex,birthdate,phone,address,idcard,guominshi,memo,createdate,completed)
          values('{testid}','{tname}','{sex}','{birthdate}','{phone}','{address}','{idcard}','{guominshi}','{memo}','{createdate}',{completed})"#
    );
    info!("insert dbo.testerinfo sql string:{}", &sql);
    let ret = client.execute(&sql, &[]).await;
    if ret.as_ref().is_err() {
      error!("insert into dbo.testerinfo error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    // Ok(ret.unwrap().rows_affected().to_owned().get(0))
    let _ = client.close().await;

    Ok(1)
  }

  async fn query_result_with_tiberius(testid: &String, dburi: &String) -> Result<Option<TestResults>> {
    //
    let tconfig = tiberius::Config::from_jdbc_string(&dburi);
    if tconfig.is_err() {
      return Err(anyhow!("invalid config:{}", tconfig.unwrap_err().to_string()));
    }
    let tconfig = tconfig.unwrap();
    let tcp = TcpStream::connect(tconfig.get_addr()).await; //.expect("tcpstream connect error");
    if tcp.is_err() {
      return Err(anyhow!("invalid tcp:{}", tcp.unwrap_err().to_string()));
    }
    let tcp = tcp.unwrap();
    let ret = tcp.set_nodelay(true); //.expect("tcp stream set no delay error");
    if ret.as_ref().is_err() {
      return Err(anyhow!("set nodaly error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = tiberius::Client::connect(tconfig, tokio_util::compat::TokioAsyncWriteCompatExt::compat_write(tcp)).await; //.expect("connect with tiberium client error");
    if ret.as_ref().is_err() {
      return Err(anyhow!("connect error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut client = ret.unwrap();
    info!("start to process testerinfo......");

    let sql = format!(
      "select top 1 id,testid,testnum,testdate,testtype,testdev,testdoctor,testdata,diagnosis,uploaddate,aux from dbo.testresults where testid = '{}' order by id DESC",
      &testid
    );
    info!("start to query testresults with sql:{}", &sql);
    let ret = client.query(&sql, &[]).await;
    info!("query result:{:?}", &ret);
    if ret.as_ref().is_err() {
      error!("select from testresults error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let stream = ret.unwrap();
    let ret = stream.into_row().await;
    if ret.as_ref().is_err() {
      error!("stream into row error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = ret.unwrap();
    if let Some(row) = ret {
      let id = match row.try_get("id") {
        Ok(r) => match r {
          Some(v) => v,
          None => 0,
        },
        Err(_) => 0,
      };
      let testid: &str = match row.try_get("testid") {
        Ok(r) => match r {
          Some(v) => v,
          None => "",
        },
        Err(_) => "",
      };
      let testnum: &str = match row.try_get("testnum") {
        Ok(r) => match r {
          Some(v) => v,
          None => "",
        },
        Err(_) => "",
      };

      let testtype: &str = match row.try_get("testtype") {
        Ok(r) => match r {
          Some(v) => v,
          None => "",
        },
        Err(_) => "",
      };
      let testdev: &str = match row.try_get("testdev") {
        Ok(r) => match r {
          Some(v) => v,
          None => "",
        },
        Err(_) => "",
      };
      let testdoctor: &str = match row.try_get("testdoctor") {
        Ok(r) => match r {
          Some(v) => v,
          None => "",
        },
        Err(_) => "",
      };
      let testdata: &str = match row.try_get("testdata") {
        Ok(r) => match r {
          Some(v) => v,
          None => "",
        },
        Err(_) => "",
      };
      let diagnosis: &str = match row.try_get("diagnosis") {
        Ok(r) => match r {
          Some(v) => v,
          None => "",
        },
        Err(_) => "",
      };
      let testdate: Option<NaiveDateTime> = match row.try_get("testdate") {
        Ok(v) => v,
        Err(_) => None,
      };
      // info!("test date:{:?}", &testdate);
      let testdate_str = match testdate {
        Some(v) => v.format("%Y-%m-%d %H:%M:%S").to_string(),
        None => Local::now().format("%Y-%m-%d %H:%M:%S").to_string(),
      };
      let uploaddate: Option<NaiveDateTime> = match row.try_get("uploaddate") {
        Ok(v) => v,
        Err(_) => None,
      };
      let uploaddate_str = match uploaddate {
        Some(v) => v.format("%Y-%m-%d %H:%M:%S").to_string(),
        None => Local::now().format("%Y-%m-%d %H:%M:%S").to_string(),
      };
      info!("uploaddate date:{:?}", &uploaddate);
      let aux: &str = match row.try_get("aux") {
        Ok(r) => match r {
          Some(v) => v,
          None => "",
        },
        Err(_) => "",
      };
      let testresult = TestResults {
        id: id,
        testid: testid.to_string(),
        testnum: testnum.to_string(),
        testdate: testdate_str,
        testtype: testtype.to_string(),
        testdev: testdev.to_string(),
        testdoctor: testdoctor.to_string(),
        testdata: testdata.to_string(),
        diagnosis: diagnosis.to_string(),
        uploaddate: uploaddate_str,
        aux: aux.to_string(),
      };

      let _ = client.close().await;
      return Ok(Some(testresult));
    }

    let _ = client.close().await;
    Ok(None)
  }

  //测试数据字符串以“}{”分割左右耳数据，以“左耳数据}{右耳数据”的形式存放。
  //单耳测试数据由多个测试点组成，测试点以“|”分割。每个测试点中有四个描述项，
  // 描述项之间以“，”分割，分别描述测试类型、测试频率、测试分贝、测试状态。
  // 测试类型：0-气导测试，1-骨导测试，2-声场测试。
  // 测试频率：0-125Hz，1-250Hz，2-500Hz，3-750Hz，4-1000Hz，5-1500Hz，6-2000Hz，7-3000Hz，8-4000Hz，9-6000Hz，10-8000 Hz。
  // 测试分贝：实际分贝值
  // 测试状态：0-无掩蔽无响应，1-无掩蔽有响应，2-有掩蔽无响应，3-有掩蔽有响应
  //0,1,10,1|0,2,10,1|0,4,10,1|0,6,10,1|0,7,10,1|0,8,10,1|0,9,10,1|1,1,0,1|1,2,0,1|1,4,0,1|1,6,0,1|1,7,0,1|1,8,0,1|1,9,0,1}{0,2,20,1|0,4,20,1|0,6,20,1|0,7,20,1|0,8,20,1|0,9,20,1|1,2,-10,1|1,4,-10,1|1,6,-10,1|1,7,-10,1|1,8,-10,1|1,9,-10,1
  //Freq=250Hz, dB=10, state:有响应,无掩蔽[air L]
  //Freq=500Hz, dB=10, state:有响应,无掩蔽[air L]
  //Freq=250Hz, dB=0, state:有响应,无掩蔽[bone L]
  //Freq=500Hz, dB=0, state:有响应,无掩蔽[bone L]
  //Freq=500Hz, dB=-10, state:有响应,无掩蔽[bone R]
  //Freq=1000Hz, dB=10, state:有响应,无掩蔽[air L]
  //Freq=500Hz, dB=20, state:有响应,无掩蔽[air R]
  // Freq=1000Hz, dB=20, state:有响应,无掩蔽[air R]
  // Freq=1000Hz, dB=0, state:有响应,无掩蔽[bone L]
  // Freq=1000Hz, dB=-10, state:有响应,无掩蔽[bone R]
  // Freq=2000Hz, dB=10, state:有响应,无掩蔽[air L]
  // Freq=2000Hz, dB=20, state:有响应,无掩蔽[air R]
  // Freq=2000Hz, dB=0, state:有响应,无掩蔽[bone L]
  // Freq=2000Hz, dB=-10, state:有响应,无掩蔽[bone R]
  // Freq=3000Hz, dB=10, state:有响应,无掩蔽[air L]
  // Freq=3000Hz, dB=20, state:有响应,无掩蔽[air R]
  // Freq=3000Hz, dB=0, state:有响应,无掩蔽[bone L]
  // Freq=3000Hz, dB=-10, state:有响应,无掩蔽[bone R]
  // Freq=4000Hz, dB=10, state:有响应,无掩蔽[air L]
  // Freq=4000Hz, dB=20, state:有响应,无掩蔽[air R]
  // Freq=4000Hz, dB=0, state:有响应,无掩蔽[bone L]
  // Freq=4000Hz, dB=-10, state:有响应,无掩蔽[bone R]
  // Freq=6000Hz, dB=10, state:有响应,无掩蔽[air L]
  // Freq=6000Hz, dB=20, state:有响应,无掩蔽[air R]
  // Freq=6000Hz, dB=0, state:有响应,无掩蔽[bone L]
  // Freq=6000Hz, dB=-10, state:有响应,无掩蔽[bone R]
  async fn parse_test_result(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, result: &String) -> Result<Vec<TjAudiogramdetail>> {
    let results: Vec<&str> = result.split("}{").collect();
    if results.len() <= 0 {
      return Err(anyhow!("结果数据有误"));
    }
    let left_ear = results.get(0).map_or("", |&f| f);
    let right_ear = results.get(1).map_or("", |&f| f);
    info!("左耳数据:{left_ear}");
    info!("右耳数据:{right_ear}");
    let mut audio_details: Vec<TjAudiogramdetail> = vec![];
    if !left_ear.is_empty() {
      let left_details: Vec<&str> = left_ear.split('|').collect();
      for val in left_details.into_iter() {
        let adret = MailiService::parse_audiogram_detail(val, &medinfo.tj_testid, constant::TransEar::Left as i32, ptinfo.tj_psex, medinfo.tj_age).await;
        if adret.is_none() {
          continue;
        }
        audio_details.push(adret.unwrap());
      }
    }
    if !right_ear.is_empty() {
      let right_details: Vec<&str> = right_ear.split('|').collect();
      for val in right_details.into_iter() {
        let adret = MailiService::parse_audiogram_detail(val, &medinfo.tj_testid, constant::TransEar::Right as i32, ptinfo.tj_psex, medinfo.tj_age).await;
        if adret.is_none() {
          continue;
        }
        audio_details.push(adret.unwrap());
      }
    }
    Ok(audio_details)
  }

  async fn parse_audiogram_detail(val: &str, testid: &String, ear: i32, sex: i32, age: i32) -> Option<TjAudiogramdetail> {
    let details: Vec<i32> = val.split(',').map(|v| v.parse().unwrap_or_default()).collect();
    info!("检查数据：{details:?}");
    //0,1,10,1 =>Freq=250Hz, dB=10, state:有响应,无掩蔽[air L
    let adtype = details.get(0).map_or(0, |v| v.to_owned());
    let freq = MailiService::get_freq_by_idx(details.get(1).map_or(0, |f| f.to_owned()));
    if freq == 0 {
      error!("数据有误，不能得到频率数据");
      return None;
    }
    let itemid = MailiService::get_itemid_by_freq(freq);
    if itemid.is_empty() {
      return None;
    }
    let tret = details.get(2).map_or(0, |f| f.to_owned());
    let tscover = MailiService::get_mask(details.get(3).map_or(0, |f| f.to_owned()));
    let mut revise = tret;
    if adtype == constant::TransType::Air as i32 {
      let revise_val = crate::SYSCACHE.get().unwrap().get_audiogram_revise(freq, sex, age).await;
      info!("气导校正信息:{:?}", &revise_val);
      if revise_val.is_some() {
        revise = tret - revise_val.unwrap().tj_revise;
      }
    }

    let adt = TjAudiogramdetail {
      id: 0,
      tj_testid: testid.to_string(),
      tj_itemid: itemid,
      tj_freq: freq,
      tj_adtype: adtype,
      tj_ear: ear,
      tj_result: tret,
      tj_revise: revise,
      tj_cover: tscover,
    };
    return Some(adt);
  }

  fn get_freq_by_idx(idx: i32) -> i32 {
    match idx {
      0 => 125,
      1 => 250,
      2 => 500,
      3 => 750,
      4 => 1000,
      5 => 1500,
      6 => 2000,
      7 => 3000,
      8 => 4000,
      9 => 6000,
      10 => 8000,
      _ => 0,
    }
  }

  fn get_mask(idx: i32) -> i32 {
    match idx {
      0 | 1 => 0,
      _ => 1,
    }
  }
  fn get_itemid_by_freq(freq: i32) -> String {
    match freq {
      500 => "390007".to_string(),
      1000 => "390008".to_string(),
      2000 => "390009".to_string(),
      3000 => "390010".to_string(),
      4000 => "390011".to_string(),
      6000 => "390012".to_string(),
      _ => "".to_string(),
    }
  }
}
