python 版本：3.7.9

pip install reportlab -i <https://pypi.tuna.tsinghua.edu.cn/simple>
pip install sqlalchemy -i <https://pypi.tuna.tsinghua.edu.cn/simple>
pip install python-docx -i <https://pypi.tuna.tsinghua.edu.cn/simple>
pip install tomli -i <https://pypi.tuna.tsinghua.edu.cn/simple>
pip install pymysql -i <https://pypi.tuna.tsinghua.edu.cn/simple>
pip install exceptions -i <https://pypi.tuna.tsinghua.edu.cn/simple>
pip install pyinstaller -i <https://pypi.tuna.tsinghua.edu.cn/simple>

pip install reportlab sqlalchemy python-docx tomli pymysql exceptions pyinstaller fastapi uvicorn pydantic python-multipart pydantic-core

<!-- pipx install reportlab --include-deps -->

pipx install reportlab sqlalchemy python-docx tomli pymysql --include-deps

## env

### 创建 env

python3 -m venv myenv

### activate

source myenv/bin/activate

### install depentds

pip install -r requirements.txt

### install

pyinstaller -F report.py

pyinstaller --onefile --add-data "myenv/lib/python3.12/site-packages/docx/templates:docx/templates" report.py

### static package

执行参数：
第 1 个参数 报告编号
第 2 个参数 reporttype 1:docx 2:pdf
第 3 个参数 pagestyle
第 4 个参数 splitinrow
第 5 个参数 outdir

python .\python\report.py 12 1 1 1 ./reports

打包命令：

pyinstaller -F report.py

## 打包 pychartdir

pyinstaller.exe -F preport.py --paths=C:\Users\<USER>\rainerix\joesong\nodipservers\lib\pychartdir.py --collect-all pychartdir --collect-all pychartdir3x

### 添加 dll 文件

### 注意 python 和 chartdir.dll 的 64 位或者 32 位版本需要一致

#### windows

pyinstaller.exe -F preport.py --paths=./wlib --collect-all pychartdir --collect-all pychartdir3x --add-binary="chartdir.dll;."

pyinstaller.exe -F preport.py --paths=./wlib --collect-all pychartdir --add-binary "./wlib/pychartdir3x.pyd;." --add-binary "./wlib/chartdir.dll;."

### Linux

pyinstaller -F preport.py --paths=./llib --collect-all pychartdir --collect-all pychartdir3x --add-binary="libchartdir.so:." --add-binary="./llib/libchartdir.so:."

# 生成个人报告命令

python.exe python\preport.py 10016609 2 0 1 ./reports

# Report Generator Executable

This document explains how to build and use the standalone executable version of the report generator.

## Building the Executable

1. Make sure you have PyInstaller installed:

```bash
pip install pyinstaller
```

2. Build the executable using the spec file:

```bash
pyinstaller report.spec
```

The executable will be created in the `dist` directory.

## Directory Structure

Make sure your project directory has the following structure before building:

```
python/
├── fonts/
│   ├── simhei.ttf
│   └── simsun.ttc
├── images/
├── config/
├── report.py
├── report.spec
└── requirements.txt
```

## Running the Executable

The executable accepts the same command-line arguments as the Python script:

```bash
report.exe <rptid> <reporttype> <pagestyle> <splitinrow> <outdir>
```

Example:

```bash
report.exe 123 0 1 1 ./reports
```

Parameters:

- `rptid`: Report ID (integer)
- `reporttype`: Report type (0 for PDF, 1 for DOCX)
- `pagestyle`: Page style
- `splitinrow`: Split in row
- `outdir`: Output directory for generated reports

## Notes

1. The executable includes all necessary dependencies and data files
2. Make sure the output directory exists before running
3. The executable requires the same database configuration as the Python script
4. All fonts and images are bundled with the executable

## Troubleshooting

If you encounter any issues:

1. Check if all required directories (fonts, images, config) are present
2. Verify database connection settings
3. Ensure the output directory is writable
4. Check the application logs for detailed error messages

#### 解决打包 python-docx 报错 no default-header.xml 错误：找到报错的代码 docx/parts/hdrftr.py 的第 53 行和 31 行

```
    @classmethod
    def _default_header_xml(cls):
        """Return bytes containing XML for a default header part."""
        path = os.path.join(
            os.path.split(__file__)[0], '..', 'templates', 'default-header.xml'
        )
        path = path.replace('parts/../','') --
        with open(path, 'rb') as f:
            xml_bytes = f.read()
        return xml_bytes
```
