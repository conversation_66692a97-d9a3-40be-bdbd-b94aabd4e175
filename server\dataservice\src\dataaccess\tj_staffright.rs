use crate::entities::{prelude::*, tj_staffright};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter};
use serde_json::json;

impl TjStaffright {
  pub async fn query_many(staffnos: &Vec<i64>, db: &DatabaseConnection) -> Result<Vec<TjStaffright>> {
    if staffnos.len() <= 0 {
      return Err(anyhow!("conditions are empty, not allowed"));
    }
    let mut conditions = Condition::all();
    // if staffids.len() > 0 {
    //   conditions = conditions.add(tj_staffright::Column::Id.is_in(staffids.to_owned()));
    // }
    if !staffnos.is_empty() {
      conditions = conditions.add(tj_staffright::Column::TjStaffid.is_in(staffnos.to_owned()));
    }

    let ret = TjStaffrightEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn insert_many(info: &Vec<TjStaffright>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }

    let mut active_values: Vec<tj_staffright::ActiveModel> = Vec::new();
    for val in info {
      let ret = tj_staffright::ActiveModel::from_json(json!(val));
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      active_values.push(ret.unwrap());
    }
    let ret = TjStaffrightEntity::insert_many(active_values).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().last_insert_id)
  }

  pub async fn delete(staffids: &Vec<i64>, db: &DatabaseConnection) -> Result<()> {
    let ret = TjStaffrightEntity::delete_many()
      .filter(Condition::all().add(tj_staffright::Column::TjStaffid.is_in(staffids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(())
  }

  pub async fn delete_by_ids(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<()> {
    let ret = TjStaffrightEntity::delete_many()
      .filter(Condition::all().add(tj_staffright::Column::Id.is_in(ids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(())
  }
}
