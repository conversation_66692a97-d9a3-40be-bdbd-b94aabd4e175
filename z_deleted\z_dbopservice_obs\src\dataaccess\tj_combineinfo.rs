use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjCombineinfo {
  pub id: i64,
  pub tj_combid: String,
  pub tj_itemid: String,
  pub tj_showorder: i32,
}
crud!(TjCombineinfo {}, "tj_combineinfo");
rbatis::impl_select!(TjCombineinfo{query(combid:&String) => "`where tj_combid = #{combid} `"});
rbatis::impl_select!(TjCombineinfo{query_many(codes:&[&str]) => 
  "`where id > 0 `
  if !codes.is_empty():
    ` and tj_combid in ${codes.sql()} `
  "});

rbatis::impl_delete!(TjCombineinfo{delete(id:i64) => "`where id = #{id} `"});

impl TjCombineinfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjCombineinfo) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjCombineinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjCombineinfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
}
