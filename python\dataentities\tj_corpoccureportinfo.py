from sqlalchemy import Column, Integer, String
from dataentities.dbconn import Base


class TjCorpoccureportinfo(Base):
    # 数据库中存储的表名
    __tablename__ = "tj_corpoccureport_info"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    tj_report_id = Column(Integer, nullable=False)
    tj_test_id = Column(String(256), nullable=False, unique=True)
    tj_rptnum = Column(String(256), nullable=False)
