use chrono::{DateTime, Datelike, Duration, Local, NaiveDate, NaiveDateTime, TimeZone};
use std::time::{SystemTime, UNIX_EPOCH};

pub fn get_current_year_month_day() -> (i32, u32, u32) {
  let current_date = chrono::Local::now(); //.year();
  (current_date.year(), current_date.month(), current_date.day())
}

// pub fn timestamp_to_naive_datetime(t: i64) -> NaiveDateTime {
//   // Create a NaiveDateTime from the timestamp
//   let naive = NaiveDateTime::from_timestamp_opt(t, 0);

//   naive.unwrap_or_default()
// }

pub fn current_timestamp() -> i64 {
  chrono::offset::Local::now().timestamp()
  // SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs() as i64
}

pub fn current_timestamp_millis() -> u128 {
  SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis()
}

pub fn current_timestamp_with_millis() -> i64 {
  chrono::offset::Local::now().timestamp_millis()
  // SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs() as i64
}

pub fn timestamp_add_days(ts: i64, days: i64) -> i64 {
  // let naive = NaiveDateTime::from_timestamp_opt(ts, 0).unwrap_or_default();
  // let naive = DateTime::from_timestamp(ts, 0).unwrap_or_default();
  let date_time = get_local_date_time_from_timestamp(ts);
  let naive = date_time.naive_utc();

  let new_date = naive.checked_add_signed(Duration::days(days));
  if new_date.is_none() {
    return 0;
  }
  let new_date = new_date.unwrap();
  new_date.and_utc().timestamp()
}
//return date with 2022-01-01 10:10:10
pub fn format_timestamp(timestamp: i64) -> String {
  let date_time = get_local_date_time_from_timestamp(timestamp);
  return date_time.format("%Y-%m-%d %H:%M:%S").to_string();
}

pub fn format_timestamp_with_format(timestamp: i64, fmt: &str) -> String {
  let date_time = get_local_date_time_from_timestamp(timestamp);
  return date_time.format(fmt).to_string();
}

pub fn format_timestamp_date_only(timestamp: i64) -> String {
  let date_time = get_local_date_time_from_timestamp(timestamp);
  return date_time.format("%Y-%m-%d").to_string();
}

pub fn get_local_date_time_from_timestamp(timestamp: i64) -> DateTime<Local> {
  let (secs, nanos) = match timestamp.abs().to_string().len() {
    0..=12 => (timestamp, 0),                                                   // 秒
    13..=15 => (timestamp / 1_000, (timestamp % 1_000 * 1_000_000) as u32),     // 毫秒
    16..=18 => (timestamp / 1_000_000, (timestamp % 1_000_000 * 1_000) as u32), // 微秒
    _ => (current_timestamp(), 0),
  };

  let dt = DateTime::from_timestamp(secs, nanos).unwrap_or_default();
  let naive = dt.naive_utc();
  let date_time: DateTime<Local> = Local.from_utc_datetime(&naive);
  date_time
}

pub fn format_timestamp_date_only_without_sep(dt: i64) -> String {
  let date_time = get_local_date_time_from_timestamp(dt);
  return date_time.format("%Y%m%d").to_string();
}

//return date with 20220101
pub fn current_date() -> i32 {
  let dnow = Local::now();
  // let dtstr = format!("{}{}{}", dnow.year(), dnow.month(), dnow.day());
  let dtstr = dnow.format("%Y%m%d");
  // SystemTime::now().
  // let dtstr = dtstr.to_string();
  dtstr.to_string().parse::<i32>().unwrap_or_default()
}

//return date with 20220101101010
pub fn current_datetime() -> String {
  let dnow = Local::now();
  // let dtstr = format!("{}{}{}", dnow.year(), dnow.month(), dnow.day());
  let dtstr = dnow.format("%Y%m%d%H%M%S");
  // SystemTime::now().
  // let dtstr = dtstr.to_string();
  dtstr.to_string() //.parse::<i32>().unwrap_or_default()
}

pub fn convert_date_to_timestamp(dt: &str, fmt: &str) -> i64 {
  if dt.is_empty() {
    return chrono::offset::Local::now().timestamp();
  }
  let mut fmt = fmt.to_string();
  if fmt.is_empty() {
    fmt = "%Y-%m-%d".to_string();
  }
  let naive_date = NaiveDate::parse_from_str(dt, &fmt);
  if naive_date.as_ref().is_err() {
    // error!("convert string to navie date error:{}", naive_date.err().unwrap().to_string());
    return chrono::offset::Local::now().timestamp() as i64;
  }
  let naive_date = naive_date.unwrap();
  let naive_datetime: NaiveDateTime = naive_date.and_hms_opt(0, 0, 0).unwrap_or_default();
  let datetime = Local.from_local_datetime(&naive_datetime);
  datetime.unwrap().timestamp()
}

pub fn convert_datetime_to_timestamp(dt: &str, fmt: &str) -> i64 {
  let naive_date = NaiveDateTime::parse_from_str(dt, fmt);
  if naive_date.as_ref().is_err() {
    return chrono::offset::Local::now().timestamp();
  }
  let naive_datetime = naive_date.unwrap();
  let datetime = Local.from_local_datetime(&naive_datetime);
  datetime.unwrap().timestamp()
}

pub fn current_naive_time() -> NaiveDateTime {
  chrono::Local::now().naive_local()
}

pub fn get_age_from_birthdate(birthdate: &str, destdate: i64, fmt: &str) -> i32 {
  let mut date_fmt = fmt.to_owned();
  if date_fmt.is_empty() {
    date_fmt = "%Y-%m-%d".to_string();
  }
  // let naive_date = NaiveDate::parse_from_str(birthdate, &date_fmt);
  // if naive_date.is_err() {
  //   // error!("get age error:{}", naive_date.err().unwrap().to_string());
  //   return 0;
  // }
  // let birth_datetime = naive_date.unwrap().and_hms_opt(0, 0, 0).unwrap_or_default();
  // let current = Local::now().naive_local();
  // let ret = current.signed_duration_since(birth_datetime);
  // (ret.num_days() / 365) as i32

  let birth_date = NaiveDate::parse_from_str(birthdate, &date_fmt).ok().unwrap_or_default();

  // Get the current date
  // let today = Local::now().date_naive();
  let today = DateTime::from_timestamp(destdate, 0).unwrap_or_default().naive_local().date();

  // Calculate the difference in years
  let mut age = today.year() - birth_date.year();

  // Adjust age if the birthday hasn't occurred this year
  if today.month() < birth_date.month() || (today.month() == birth_date.month() && today.day() < birth_date.day()) {
    age -= 1;
  }

  age
}

///2021-08-0914:55:48
pub fn current_naive_time_iso_with_seperator(hassep: bool) -> String {
  let dt = chrono::Local::now().naive_local();
  if hassep {
    dt.format("%Y-%m-%d %H:%M:%S").to_string()
  } else {
    dt.format("%Y-%m-%d%H:%M:%S").to_string()
  }
}

/// convert from 20210809 to 2021-08-09
pub fn get_iso_date(ddate: i32) -> String {
  let date_str = format!("{}", ddate);
  let naive_date = NaiveDate::parse_from_str(date_str.as_str(), "%Y%m%d");
  if naive_date.as_ref().is_err() {
    return "".to_string();
  } else {
    return naive_date.unwrap().format("%Y-%m-%d").to_string();
  }
}

/// convert from 20210809 to 2021-08-09
pub fn get_date_only(ddate: &String) -> String {
  // let date_str = format!("{}", ddate);
  let naive_date = NaiveDate::parse_from_str(ddate.as_str(), "%Y-%m-%d");
  if naive_date.as_ref().is_err() {
    return "0".to_string();
  } else {
    return naive_date.unwrap().format("%Y%m%d").to_string();
  }
}

pub fn check_timestamp(timestamp: i64) -> &'static str {
  // 假设时间戳可能是秒或毫秒，尝试两种情况
  let seconds = timestamp;
  // let milliseconds = timestamp * 1000;

  // 检查秒级时间戳
  if let Some(naive) = DateTime::from_timestamp(seconds, 0) {
    let datetime: DateTime<Local> = naive.with_timezone(&Local);
    // 验证时间戳是否在合理范围内（例如，1970 年后且不过于未来）
    if datetime.year() >= 1970 && datetime.year() <= 2100 {
      return "seconds";
    }
  }

  // 检查毫秒级时间戳
  if let Some(naive) = DateTime::from_timestamp(timestamp / 1000, (timestamp % 1000 * 1_000_000) as u32) {
    let datetime: DateTime<Local> = naive.with_timezone(&Local);
    if datetime.year() >= 1970 && datetime.year() <= 2100 {
      return "milliseconds";
    }
  }

  "unknown"
}
