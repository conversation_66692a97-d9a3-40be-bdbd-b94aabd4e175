<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:s0="http://tempuri.org" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" targetNamespace="http://tempuri.org">
	<types>
		<s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org">
			<s:element name="Sample1">
				<s:complexType>
					<s:sequence>
						<s:element minOccurs="0" name="method" type="s:string"/>
						<s:element minOccurs="0" name="StrJson" type="s:string"/>
					</s:sequence>
				</s:complexType>
			</s:element>
			<s:element name="Sample1Response">
				<s:complexType>
					<s:sequence>
						<s:element name="Sample1Result" type="s:string"/>
					</s:sequence>
				</s:complexType>
			</s:element>
			<s:element name="Sample2">
				<s:complexType>
					<s:sequence>
						<s:element minOccurs="0" name="pInput" type="s:string"/>
					</s:sequence>
				</s:complexType>
			</s:element>
			<s:element name="Sample2Response">
				<s:complexType>
					<s:sequence>
						<s:element name="Sample2Result" type="s:string"/>
					</s:sequence>
				</s:complexType>
			</s:element>
		</s:schema>
	</types>
	<message name="Sample1SoapIn">
		<part name="parameters" element="s0:Sample1"/>
	</message>
	<message name="Sample1SoapOut">
		<part name="parameters" element="s0:Sample1Response"/>
	</message>
	<message name="Sample2SoapIn">
		<part name="parameters" element="s0:Sample2"/>
	</message>
	<message name="Sample2SoapOut">
		<part name="parameters" element="s0:Sample2Response"/>
	</message>
	<portType name="ZYBCommonWSSoap">
		<operation name="Sample1">
			<input message="s0:Sample1SoapIn"/>
			<output message="s0:Sample1SoapOut"/>
		</operation>
		<operation name="Sample2">
			<input message="s0:Sample2SoapIn"/>
			<output message="s0:Sample2SoapOut"/>
		</operation>
	</portType>
	<binding name="ZYBCommonWSSoap" type="s0:ZYBCommonWSSoap">
		<soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document"/>
		<operation name="Sample1">
			<soap:operation soapAction="http://tempuri.org/HEREN.ZYB.BS.ZYBCommonWS.Sample1" style="document"/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
		</operation>
		<operation name="Sample2">
			<soap:operation soapAction="http://tempuri.org/HEREN.ZYB.BS.ZYBCommonWS.Sample2" style="document"/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
		</operation>
	</binding>
	<service name="ZYBCommonWS">
		<port name="ZYBCommonWSSoap" binding="s0:ZYBCommonWSSoap">
			<soap:address location="http://***********/csp/healthshare/srmyyhip/HEREN.ZYB.BS.ZYBCommonWS.cls"/>
		</port>
	</service>
</definitions>