use anyhow::{anyhow, Result};
use dataservice::entities::prelude::*;

// "server=tcp:localhost,1433;IntegratedSecurity=true;TrustServerCertificate=true".to_owned()
#[derive(Debug)]
pub struct RecvOracle;

impl RecvOracle {
  pub async fn query_from_external_labresult(conn: &oracle::Connection, tjbh: &str) -> Result<Vec<VTjLisresult>> {
    let sql = format!(
      "select tjbh, brxm, xmxh, xmmc, xmdw, xmjg, gdbj, ckdz, ckgz, ckfw, jyys, bgrq, bgys,sfyc from v_tj_lisresult where tjbh = '{}'",
      tjbh
    );
    let ret = conn.statement(&sql).build();
    if ret.as_ref().is_err() {
      return Err(anyhow!("Prepare sql error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut stmt = ret.unwrap();
    let rows = stmt.query(&[])?;
    let mut results: Vec<VTjLisresult> = vec![];
    for row_result in rows {
      let row = row_result?;
      let tjbh: String = row.get(0)?; // index by 0-based position
      let brxm: Option<String> = row.get(1)?; // nullable column must be get as Option<...> to avoid panic.
      let xmxh: Option<String> = row.get(2)?; //
      let xmmc: Option<String> = row.get(3)?; //
      let xmdw: Option<String> = row.get(4)?; //
      let xmjg: Option<String> = row.get(5)?; //
      let gdbj: Option<String> = row.get(6)?; //
      let ckdz: Option<String> = row.get(7)?; //
      let ckgz: Option<String> = row.get(8)?; //
      let ckfw: Option<String> = row.get(9)?; //
      let jyys: Option<String> = row.get(10)?; //
      let bgrq: Option<String> = row.get(11)?; //
      let bgys: Option<String> = row.get(12)?; //
      let sfyc: Option<i32> = row.get(13)?; //
      let labret = VTjLisresult {
        id: 0,
        tjbh,
        brxm: brxm.unwrap_or_default(),
        xmxh: xmxh.unwrap_or_default(),
        xmmc: xmmc.unwrap_or_default(),
        xmdw: xmdw.unwrap_or_default(),
        xmjg: xmjg.unwrap_or_default(),
        sfyc: sfyc.unwrap_or_default(),
        gdbj: gdbj.unwrap_or_default(),
        ckdz: ckdz.unwrap_or_default(),
        ckgz: ckgz.unwrap_or_default(),
        ckfw: ckfw.unwrap_or_default(),
        jyys: jyys.unwrap_or_default(),
        bgrq: bgrq.unwrap_or_default(),
        bgys: bgys.unwrap_or_default(),
      };
      results.push(labret);
    }
    if results.len() <= 0 {
      return Err(anyhow!("lis系统没有结果数据"));
    }
    // self.conn.close();
    Ok(results)
  }

  pub async fn query_from_external_pacsresult(conn: &oracle::Connection, tjbh: &str, hassfyc: i32, version: i32) -> Result<Vec<VTjPacsresult>> {
    let mut results: Vec<VTjPacsresult> = vec![];
    let sql: String;
    if hassfyc == 1 {
      sql = format!(
        "select tjbh,brxm,jclx,jcxm,jcmc,imagesight,imagediagnosis,jcys,sxys,bgys,bgrq,sfyc from v_tj_pacsresult where tjbh = '{}'",
        tjbh
      );
    } else {
      sql = format!(
        "select tjbh,brxm,jclx,jcxm,jcmc,imagesight,imagediagnosis,jcys,sxys,bgys,bgrq from v_tj_pacsresult where tjbh = '{}'",
        tjbh
      );
    }
    let ret = conn.statement(&sql).build();
    if ret.as_ref().is_err() {
      return Err(anyhow!("Prepare sql error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut stmt = ret.unwrap();
    let rows = stmt.query(&[])?;
    for row_result in rows {
      let row = row_result?;
      let tjbh: Option<String> = row.get(0)?; // index by 0-based position
      let brxm: Option<String> = row.get(1)?; // nullable column must be get as Option<...> to avoid panic.
      let jclx: Option<String> = row.get(2)?; //
      let jcxm: Option<String> = row.get(3)?; //
      let jcmc: Option<String> = row.get(4)?; //
      let imagesight: Option<String> = row.get(5)?; //
      let imagediagnosis: Option<String> = row.get(6)?; //
      let jcys: Option<String> = row.get(7)?; //

      let sxys: Option<String> = row.get(8)?; //
      let bgys: Option<String> = row.get(9)?; //
      let bgrq: Option<String> = row.get(10)?; //
      let mut sfyc = 0;
      if hassfyc == 1 {
        if version == 1 {
          let sfyci: Option<i32> = row.get(11)?; //
          sfyc = sfyci.unwrap_or_default();
        } else {
          let sfs: Option<String> = row.get(11)?;
          if sfs.is_some() {
            sfyc = sfs.unwrap().parse().unwrap_or_default();
          }
        }
      }
      let imagepath = Some("".to_string());
      let datas = Some("".to_string());

      let pret: VTjPacsresult = VTjPacsresult {
        id: 0,
        tjbh: tjbh.unwrap_or_default(),
        brxm: brxm.unwrap_or_default(),
        jclx: jclx.unwrap_or_default(),
        jcxm: jcxm.unwrap_or_default(),
        jcmc: jcmc.unwrap_or_default(),
        imagesight: imagesight.unwrap_or_default(),
        imagediagnosis: imagediagnosis.unwrap_or_default(),
        jcys: jcys.unwrap_or_default(),
        sxys: sxys.unwrap_or_default(),
        bgys: bgys.unwrap_or_default(),
        bgrq: bgrq.unwrap_or_default(),
        sfyc: sfyc,
        imagepath,
        datas,
      };
      results.push(pret);
    }
    Ok(results)
  }

  pub async fn query_lis_xmxx(conn: &oracle::Connection) -> Result<Vec<VLisXmxx>> {
    let sql = "select itemid, chinesename, englishab, unit from v_lis_xmxx";
    let ret = conn.statement(sql).build();
    if ret.as_ref().is_err() {
      return Err(anyhow!("Prepare sql error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut xmxxs: Vec<VLisXmxx> = vec![];
    let mut stmt = ret.unwrap();
    let rows = stmt.query(&[])?;
    for row_result in rows {
      let row = row_result?;
      let itemid: String = row.get(0)?; // index by 0-based position
      let itemname: Option<String> = row.get(1)?; // nullable column must be get as Option<...> to avoid panic.
      let engname: Option<String> = row.get(2)?; //
      let unit: Option<String> = row.get(3)?; //
      let xmxx: VLisXmxx = VLisXmxx {
        itemid: itemid,
        chinesename: itemname.unwrap_or_default(),
        englishab: engname.unwrap_or_default(),
        unit: unit.unwrap_or_default(),
      };
      xmxxs.push(xmxx);
    }
    Ok(xmxxs)
  }
}
