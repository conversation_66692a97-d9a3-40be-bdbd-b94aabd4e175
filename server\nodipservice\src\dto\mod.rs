// use dbopservice::dataaccess::prelude::*;
use dataservice::entities::prelude::*;
use serde::{Deserialize, Serialize};
// use utility::serde::{deserialize_string_to_i64, serialize_i64_to_string};
#[derive(Debug, Serialize, Deserialize)]
pub struct LoginDto {
  #[serde(rename = "username")]
  pub userid: String,
  pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MultiKeyDto {
  pub keys_str1: Vec<String>,
  pub keys_str2: Vec<String>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct CorpReportDTO {
  pub rptid: i64,
  pub pagestyle: i32,
  pub outdir: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LogoutDto {
  #[serde(rename = "username")]
  pub userid: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct KeysDto {
  pub keys: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct KeyIntsDto {
  pub keys: Vec<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct KeyDto {
  pub key: String,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct KeyIntDto {
  pub key: i64,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct MKeyDto {
  pub key_str: String,
  pub key_int: i64,
}
#[derive(Debug, Serialize, Deserialize)]
pub struct MultiKeysDto {
  pub keys_str: Vec<String>,
  pub keys_int: Vec<i64>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct MultipleKeyDto {
  pub keystr1: String,
  pub keystr2: String,
  pub keyint1: i64,
  pub keyint2: i64,
  pub keystr3: Option<Vec<String>>,
  pub keyint3: Option<i32>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct MulKeysDto {
  pub keyi1: Vec<i64>,
  pub keyi2: Option<Vec<i64>>,
  pub keyi3: Option<Vec<i64>>,
  pub keyi4: Option<Vec<i64>>,
  pub keys1: Vec<String>,
  pub keys2: Option<Vec<String>>,
  pub keys3: Option<Vec<String>>,
  pub keys4: Option<Vec<String>>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct MultipleKeysDto {
  pub keystr1: Vec<String>,
  pub keystr2: Vec<String>,
  pub keyint1: i64,
  pub keyint2: i64,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct ExtTypeDTO {
  pub testid: String,
  pub exttype: i32,
  pub newinfos: Option<Vec<ExtCheckiteminfo>>,
  pub existinfos: Option<Vec<ExtCheckiteminfo>>,
}

//ExternalDTO
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct ExternalDTO {
  pub testid: String,
  pub exttype: i32,
  pub additional: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ExamStatusDto {
  pub medinfo: TjMedexaminfo,
  pub updown: i32,
  pub newstatus: i32,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct TestSummaryQueryDto {
  pub testids: Vec<String>,
  pub deptids: Vec<String>,
  pub status: i64,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct AutoSummaryDto {
  pub medinfo: TjMedexaminfo,
  pub summary: TjTestsummary,
  pub ciinfos: Vec<TjCheckiteminfo>,
  pub pthazards: Vec<TjPatienthazards>,
  pub saveci: i32, //是否保存结果信息 0：否 1：是
  pub ignore: i32, //是否忽略已经小结的，0：否 1：是
  pub pacssum: String,
}

///自动小结的返回结果
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct SummaryResponseDto {
  pub code: i32,   //小结状态
  pub msg: String, //提示
  pub medinfo: TjMedexaminfo,
  pub summary: TjTestsummary,
  pub disinfos: Vec<TjDiseaseinfo>,
}

// pub deptid: String,
// pub chkdate: i64,
// pub chkdoctorid: String,
// pub chkdoctorname: String,
// pub recheckdoctorname: String,

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct MedQueryDto {
  pub dtstart: i64,
  pub dtend: i64,
  pub testids: Vec<String>,
  pub corpid: i64,
  pub testtypes: Vec<i32>,
  pub status: Vec<i32>,
  pub pids: Vec<String>,
  pub pname: String,
  pub isrecheck: i32,
  pub oldtestids: Vec<String>,
  pub rptid: i64,
  pub deptids: Vec<String>,
  pub deptstatus: i32,
  pub filter: i32, //是否过滤没有任何体检的人员信息 0：否 1：是
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct CorpQueryDto {
  pub reportids: Vec<i64>,
  pub dtstart: i64,
  pub dtend: i64,
  pub corpid: i64,
  pub testtype: i32,
  pub reportnum: String,
  pub pname: String,
  pub testid: String,
  pub reporttype: i32,
  pub orptid: i64, //原报告编号
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct MedexamResultDetail {
  pub idx: i64,
  pub medinfo: TjMedexaminfo,
  pub patient: TjPatient,
  pub checkall: TjCheckallnew,
  pub summaries: Vec<TjTestsummary>,
  pub pthazards: Vec<TjPatienthazards>,
  pub diseases: Vec<TjDiseaseinfo>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QuickRegisterDto {
  pub medinfo: TjMedexaminfo,
  pub ptinfo: TjPatient,
  pub pkginfo: TjPackageinfo,
  pub hdinfos: Vec<TjHazardinfo>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MedexamRegisterDto {
  pub medinfo: TjMedexaminfo,
  pub ptinfo: TjPatient,
  pub iteminfos: Vec<TjIteminfo>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct RegisterResponseDto {
  pub medinfo: TjMedexaminfo,
  pub ptinfo: TjPatient,
  pub checkitems: Vec<TjCheckiteminfo>,
}
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct PatientQueryDto {
  pub pids: Vec<String>,
  pub pname: String,
  pub idcards: Vec<String>,
  pub testids: Vec<String>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct PackageDto {
  pub pkginfo: TjPackageinfo,
  pub details: Vec<TjPackagedetail>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct CheckallQueryDto {
  pub testids: Vec<String>,
  pub stdate: i64,
  pub enddate: i64,
  pub castatus: i32,
  pub typeids: Vec<i32>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct CheckitemUpdateDto {
  pub medinfo: TjMedexaminfo,
  pub ptinfo: TjPatient,
  pub items: Vec<TjIteminfo>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct CheckitemsQueryDto {
  pub testids: Vec<String>,
  pub deptids: Vec<String>,
  pub flag: i32,
  pub combined: i32,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct CheckitemResultFlagDto {
  pub checkitem: TjCheckiteminfo,
  pub age: i32,
  pub sex: i32,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct IteminfoQueryDto {
  pub itemids: Vec<String>,
  pub okflag: i32,
  pub combineflag: i32,
  pub itemtype: i32,
  pub lisnum: Vec<String>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct SaveItemDto {
  pub iteminfo: TjIteminfo,
  pub combines: Vec<TjCombineinfo>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct QuerySummaryDto {
  pub testids: Vec<String>,
  pub deptids: Vec<String>,
  pub stdate: i64,
  pub enddate: i64,
  pub status: i32,
  pub staffid: String,
  pub forceend: i32,
}
//StatSummaryDto

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct StatSummaryDto {
  pub deptids: Vec<String>,
  pub stdate: i64,
  pub enddate: i64,
  pub status: i32,
  pub staffid: String,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct StatSummaryResponse {
  pub deptid: String,
  pub completed: i64,
  pub uncompleted: i64,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct QuestionnairDto {
  pub medinfo: TjMedexaminfo,
  pub patient: TjPatient,
  pub healthyinfo: TjHealthyinfo,
  pub diseases: Vec<TjDiseasehistory>,
  pub occupations: Vec<TjOccupationhistory>,
  pub marriages: Vec<TjMarriagehistory>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct ExternalDto {
  pub testid: String,
  pub barcode: String,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct ExternalLisDto {
  pub testid: String,
  pub testername: String,
  pub itemid: String,
  pub itemidtype: i32,
  pub itemname: String,
  pub itemunit: String,
  pub result: String,
  pub range_l: String,
  pub range_h: String,
  pub refrange: String,
  pub abnormal: i32,        //是否异常，0：正常 1：异常
  pub abnormalshow: String, //异常显示，比如高低箭头
  pub checkdoctor: String,
  pub checkdate: String,
  pub recheckdoctor: String,
  pub recheckdate: String,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct ExternalPacsDto {
  pub testid: String,
  pub brxm: String,
  pub jclx: String,
  pub bmlx: i32,
  pub jcmc: String,
  pub jcxm: String,
  pub jcys: String,
  pub bgys: String,
  pub bgrq: String,
  pub sfyc: i32, //是否异常，0：正常 1：异常
  pub imagesight: String,
  pub imagediagnosis: String,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct ResultMessage {
  pub code: i32,
  pub message: String,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct EgoDto {
  pub testid: String,
  pub extkey: String,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct MedexamHazardUpdateDto {
  pub medinfos: Vec<TjMedexaminfo>,
  pub hdinfos: Vec<TjHazardinfo>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct AudiogramResponse {
  pub testid: String,
  pub addetails: Vec<TjAudiogramdetail>,
  pub adresult: TjAudiogramresult,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct HazardConnectDto {
  pub hdids: Vec<i64>,
  pub refid: i64,
  pub conitems: i32,
  pub conlaw: i32,
  pub condis: i32,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct StaffRightsDto {
  pub staff: TjStaffadmin,
  pub rights: Vec<TjStaffright>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct GroupRightsDto {
  pub group: TjGroupinfo,
  pub rights: Vec<TjGroupright>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct HazardDto {
  pub hdinfo: TjHazardinfo,
  pub hdlaws: Vec<TjHazardlaw>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct FeeSummaryDetails {
  pub deptname: String,
  pub itemname: String,
  pub itemprice: f32,
  pub medtype: i32, //1:个人 2:团体
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct CheckallOutputDto {
  pub checkall: TjCheckallnew,
  pub pthazards: Vec<TjPatienthazards>,
}
