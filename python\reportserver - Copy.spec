# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['reportserver.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('fonts/*', 'fonts'),  # Include fonts directory
        ('images/*', 'images'),  # Include images directory
        ('config/*', 'config'),  # Include config directory
    ],
    hiddenimports=[
        'reportlab',
        'sqlalchemy',
        'pymysql',
        'dataentities',
        'corpreport',
        'personalreport',
        'common',
        'config',
        'fastapi',
        'fastapi.applications',
        'fastapi.routing',
        'fastapi.openapi',
        'fastapi.openapi.utils',
        'fastapi.middleware',
        'fastapi.middleware.cors',
        'fastapi.responses',
        'fastapi.requests',
        'fastapi.encoders',
        'fastapi.dependencies',
        'fastapi.dependencies.utils',
        'fastapi.security',
        'fastapi.security.oauth2',
        'fastapi.security.api_key',
        'starlette',
        'starlette.applications',
        'starlette.routing',
        'starlette.middleware',
        'starlette.middleware.cors',
        'starlette.responses',
        'starlette.requests',
        'starlette.staticfiles',
        'starlette.templating',
        'starlette.websockets',
        'starlette.background',
        'starlette.concurrency',
        'starlette.datastructures',
        'starlette.exceptions',
        'starlette.middleware.base',
        'starlette.middleware.exceptions',
        'starlette.middleware.sessions',
        'starlette.middleware.trustedhost',
        'starlette.types',
        'uvicorn.logging',
        'uvicorn.loops',
        'uvicorn.loops.auto',
        'uvicorn.protocols',
        'uvicorn.protocols.http',
        'uvicorn.protocols.http.auto',
        'uvicorn.protocols.websockets',
        'uvicorn.protocols.websockets.auto',
        'uvicorn.lifespan',
        'uvicorn.lifespan.on',
        'pydantic',
        'pydantic.fields',
        'pydantic.main',
        'pydantic.types',
        'pydantic.validators',
        'pydantic.error_wrappers',
        'pydantic.networks',
        'pydantic.json',
        'pydantic.datetime_parse',
        'pydantic.typing',
        'email_validator',
        'python-multipart',
        'importlib.metadata',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['pkg_resources'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='reportserver',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
) 