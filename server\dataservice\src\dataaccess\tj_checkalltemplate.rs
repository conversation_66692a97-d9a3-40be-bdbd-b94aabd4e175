use crate::entities::{prelude::*, tj_checkalltemplate};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter};

impl TjCheckalltemplate {
  pub async fn query(id: i64, db: &DatabaseConnection) -> Result<Option<TjCheckalltemplate>> {

    let ret = TjCheckalltemplateEntity::find().filter(tj_checkalltemplate::Column::Id.eq(id)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many( db: &DatabaseConnection) -> Result<Vec<TjCheckalltemplate>> {

    let ret = TjCheckalltemplateEntity::find().all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
