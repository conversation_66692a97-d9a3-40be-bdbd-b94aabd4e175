//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_organization")]
pub struct Model {
  #[sea_orm( primary_key)]
  pub id: i32,
  #[sea_orm(primary_key, auto_increment = false)]
  pub tj_orgcode: String,
  pub tj_orgname: String,
  pub tj_orgabbrname: String,
  pub tj_orglphone: String,
  pub tj_orgzphone: String,
  pub tj_orgcontactor: String,
  pub tj_orgweb: String,
  pub tj_orgaddress: String,
  pub tj_orgpublicbus: String,
  pub tj_orgreportnum: String,
  pub tj_orgnreportnum: String,
  pub tj_orgemail: String,
  pub tj_orgpostcode: String,
  pub tj_orgshowlogo: i32,
  pub tj_orglogo: String,
  pub tj_orgfax: String,
  pub tj_orgdjh: String,
  pub tj_orgtjyj: String,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
