use serde::{Deserialize, Serialize};
use yaserde::*;
// // SData  xml data
// #[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
// #[yaserde(rename = "data")]
// pub struct HealthyData {
//     #[yaserde(rename = "header")]
//     pub header: Header, //`xml:"header"`
//     #[yaserde(rename = "body")]
//     pub body: HealthyBody, //  , //`xml:"body"`
// }

// #[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
// #[yaserde(rename = "data")]
// pub struct EmployerData {
//     #[yaserde(rename = "header")]
//     pub header: Header, //      , //`xml:"header"`
//     #[yaserde(rename = "body")]
//     pub body: EmployerBody, //, //`xml:"body"`
// }

// SHeader  header
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "header")]
pub struct Header {
  #[yaserde(rename = "eventId")]
  pub eventid: String, //, //`xml:"eventId"`
  #[yaserde(rename = "userId")]
  pub userid: String, //, //`xml:"userId"`
  #[yaserde(rename = "operationType")]
  pub operationtype: Option<String>, //, //`xml:"operationType"`
  #[yaserde(rename = "requestTime")]
  pub requesttime: String, //`xml:"requestTime"`
  #[yaserde(rename = "headSign")]
  pub headsign: String, //`xml:"headSign"`
  #[yaserde(rename = "bodySign")]
  pub bodysign: String, //`xml:"bodySign"`
}

// Totalbody
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "body")]
pub struct TotalBody {
  #[yaserde(rename = "employerList")]
  pub employerlist: EmployerList, //`xml:"employerList"`
  #[yaserde(rename = "reportCardList")]
  pub reportcardlist: ReportCardList, //`xml:"reportCardList"`
}

// Body body
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "body")]
pub struct HealthyBody {
  #[yaserde(rename = "reportCardList")]
  pub reportcardlist: ReportCardList, //`xml:"reportCardList"`
}

// SEmployerBody body
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "body")]
pub struct EmployerBody {
  #[yaserde(rename = "employerList")]
  pub employerlist: EmployerList, //`xml:"employerList"`
}

// SEmployerBody body
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "body")]
pub struct EmployerDeleteBody {
  #[yaserde(rename = "orgCode")]
  pub org_code: String,
  #[yaserde(rename = "creditCodeSet")]
  pub employerdeletelist: EmployerDeleteList, //`xml:"employerList"`
}

// SReportCardList  reportCardList节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "creditCodeSet")]
pub struct EmployerDeleteList {
  #[yaserde(rename = "creditCode")]
  pub creditcodesets: Vec<String>, //`xml:"reportCard"`
}

// SEmployerBody body
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "body")]
pub struct ReportCardDeleteBody {
  #[yaserde(rename = "orgCode")]
  pub org_code: String,
  #[yaserde(rename = "codeSet")]
  pub carddeletelist: ReportCardDeleteList, //`xml:"employerList"`
}

// SReportCardList  reportCardList节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "codeSet")]
pub struct ReportCardDeleteList {
  #[yaserde(rename = "code")]
  pub code: Vec<String>, //`xml:"reportCard"`
}

// SReportCardList  reportCardList节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "reportCardList")]
pub struct ReportCardList {
  #[yaserde(rename = "reportCard")]
  pub reportcards: Vec<ReportCard>, //`xml:"reportCard"`
}

// SReportCard reportCard节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "reportCard")]
pub struct ReportCard {
  //人员信息
  #[yaserde(rename = "userInfo")]
  pub userinfo: UserInfo, //`xml:"userInfo"`
  //用人单位信息(企业信息)
  #[yaserde(rename = "empInfo")]
  pub empinfo: EmpInfo, //`xml:"empInfo"`
  //用工单位信息
  #[yaserde(rename = "empInfoEmployer")]
  pub empinfoemployer: EmpInfoEmployer, //`xml:"empInfoEmployer"`
  //机构信息
  #[yaserde(rename = "orgInfo")]
  pub orginfo: OrgInfo, //`xml:"orgInfo"`
  //体检信息
  #[yaserde(rename = "cardInfo")]
  pub cardinfo: CardInfo, //`xml:"cardInfo"`
  //毒害因素
  #[yaserde(rename = "contactHazardFactorList")]
  pub contacthazardfactorlist: ContactHazardFactorList, //`xml:"contactHazardFactorList"`
  #[yaserde(rename = "hazardFactorList")]
  pub hazardfactorlist: HazardFactorList, //`xml:"hazardFactorList"`
  //体检项目
  #[yaserde(rename = "itemList")]
  pub itemlist: ItemList, //`xml:"itemList"`
  //诊断结论
  #[yaserde(rename = "diagnosisList")]
  pub diagnosislist: DiagnosisList, //`xml:"diagnosisList"`
  //审核信息开始节点
  #[yaserde(rename = "auditInfo")]
  pub auditinfo: Option<AuditInfo>, //`xml:"auditInfo"`
  //调查问卷节点
  #[yaserde(rename = "healthSurvey")]
  pub healthsurvey: Option<HealthSurvey>, //`xml:"healthSurvey"`
}

// SUserInfo userInfo节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "userInfo")]
pub struct UserInfo {
  pub name: String, //`xml:"name"`
  #[yaserde(rename = "idcardType")]
  pub idcardtype: String, //`xml:"idcardtype"`
  #[yaserde(rename = "idcardCode")]
  pub idcardcode: String, //`xml:"idcardCode"`
  #[yaserde(rename = "sexCode")]
  pub sexcode: i32, //`xml:"sexCode"`
  #[yaserde(rename = "birthday")]
  pub birthday: String, //`xml:"birthday"`
  #[yaserde(rename = "telPhone")]
  pub telphone: String, //`xml:"telPhone"`
  #[yaserde(rename = "emergencyContactPerson")]
  pub emergencycontactperson: String, //`xml:"emergencyContactPerson"`
  #[yaserde(rename = "emergencyContactPhone")]
  pub mmergencycontactphone: String, //`xml:"emergencyContactPhone"`  //nb
  #[yaserde(rename = "workshop")]
  pub workshop: String, //`xml:"workshop"`               //nb
  #[yaserde(rename = "jobCode")]
  pub jobcode: String, //`xml:"jobCode"`                //nb
  #[yaserde(rename = "otherJobName")]
  pub otherjobname: String, //`xml:"otherJobName"`           //nb
  #[yaserde(rename = "radiationType")]
  pub radiationtype: String, //`xml:"radiationType"`          //nbradiationType
  #[yaserde(rename = "maritalStatusCode")]
  pub maritalstatuscode: i32, //`xml:"maritalStatusCode"`
  #[yaserde(rename = "jobNumber")]
  pub jobnumber: String, //`xml:"jobNumber"`              //nb
}

// SEmpInfo empInfo节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "empInfo")]
pub struct EmpInfo {
  #[yaserde(rename = "creditCode")]
  pub creditcode: String, //`xml:"creditCode"`
  #[yaserde(rename = "employerName")]
  pub employername: String, //`xml:"employerName"`
  #[yaserde(rename = "economicTypeCode")]
  pub economictypecode: String, //`xml:"economicTypeCode"`     //nb
  #[yaserde(rename = "industryCategoryCode")]
  pub industrycategorycode: String, //`xml:"industryCategoryCode"` //nb
  #[yaserde(rename = "enterpriseSizeCode")]
  pub enterprisesizecode: String, //`xml:"enterpriseSizeCode"`   //nb
  #[yaserde(rename = "areaCode")]
  pub areacode: String, //`xml:"areaCode"`             //nb
  #[yaserde(rename = "areaName")]
  pub areaname: String, //`xml:"areaName"`             //nb
  #[yaserde(rename = "address")]
  pub address: String, //`xml:"address"`              //nb
  #[yaserde(rename = "addressZipCode")]
  pub addresszipcode: String, //`xml:"addressZipCode"`       //nb
  #[yaserde(rename = "contactPerson")]
  pub contactperson: String, //`xml:"contactPerson"`        //nb
  #[yaserde(rename = "contactPersonJob")]
  pub contactpersonjob: String, //`xml:"contactPerson"`
  #[yaserde(rename = "employerPhone")]
  pub employerphone: String, //`xml:"employerPhone"`        //nb
}

// SEmpInfoEmployer empInfoEmployer
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "empInfoEmployer")]
pub struct EmpInfoEmployer {
  #[yaserde(rename = "creditCodeEmployer")]
  pub creditcodeemployer: String, //`xml:"creditCodeEmployer"`
  #[yaserde(rename = "employerNameEmployer")]
  pub employernameemployer: String, //`xml:"employerNameEmployer"`
  #[yaserde(rename = "economicTypeCodeEmployer")]
  pub economictypecodeemployer: String, //`xml:"economicTypeCodeEmployer"`
  #[yaserde(rename = "industryCategoryCodeEmployer")]
  pub industrycategorycodeemployer: String, //`xml:"industryCategoryCodeEmployer"`
  #[yaserde(rename = "enterpriseSizeCodeEmployer")]
  pub enterprisesizecodeemployer: String, //`xml:"enterpriseSizeCodeEmployer"`
  #[yaserde(rename = "areaCodeEmployer")]
  pub areacodeemployer: String, //`xml:"areaCodeEmployer"`
  #[yaserde(rename = "areaNameEmployer")]
  pub areanameemployer: String, //`xml:"areaNameEmployer"`
}

// SOrgInfo orgInfo节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "orgInfo")]
pub struct OrgInfo {
  #[yaserde(rename = "orgCode")]
  pub orgcode: String, //`xml:"orgCode"`
  #[yaserde(rename = "orgName")]
  pub orgname: String, //`xml:"orgName"`
}

// SCardInfo cardInfo节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "cardInfo")]
pub struct CardInfo {
  #[yaserde(rename = "code")]
  pub code: String, //`xml:"code"`
  #[yaserde(rename = "type")]
  pub cardtype: String, //`xml:"type"`
  #[yaserde(rename = "seniorityYear")]
  pub seniorityyear: i32, //`xml:"seniorityYear"`  //nb
  #[yaserde(rename = "seniorityMonth")]
  pub senioritymonth: i32, //`xml:"seniorityMonth"` //nb
  #[yaserde(rename = "exposureYear")]
  pub exposureyear: i32, //`xml:"exposureYear"`   //nb
  #[yaserde(rename = "exposureMonth")]
  pub exposuremonth: i32, //`xml:"exposureMonth"`  //nb
  #[yaserde(rename = "checkType")]
  pub checktype: i32, //`xml:"checkType"`
  #[yaserde(rename = "bodyCheckType")]
  pub bodychecktype: String, //`xml:"bodyCheckType"`
  #[yaserde(rename = "protectiveEquipmentCode")]
  pub protectiveequipmentcode: String, //`xml:"protectiveEquipmentCode"`
  #[yaserde(rename = "previousCardId")]
  pub previouscardid: String, //`xml:"previousCardId"`
  #[yaserde(rename = "checkTime")]
  pub checktime: String, //`xml:"checkTime"` //D8
  #[yaserde(rename = "writePerson")]
  pub writeperson: String, //`xml:"writePerson"`    //nb
  #[yaserde(rename = "writePersonTel")]
  pub writepersontel: String, //`xml:"writePersonTel"` //nb
  #[yaserde(rename = "checkOrgName")]
  pub checkorgname: String, //`xml:"checkOrgName"`
  #[yaserde(rename = "writeDate")]
  pub writedate: String, //`xml:"writeDate"`       //d8
  #[yaserde(rename = "checkResultCode")]
  pub checkresultcode: String, //`xml:"checkResultCode"`
  #[yaserde(rename = "suggest")]
  pub suggest: String, //`xml:"suggest"`
  #[yaserde(rename = "radiationResult")]
  pub radiationresult: String, //`xml:"radiationResult"`//放射工作人员职业健康检查适任性评价编码
  #[yaserde(rename = "checkDoctor")]
  pub checkdoctor: String, //`xml:"checkDoctor"`
  #[yaserde(rename = "monitorTypeCode")]
  pub monitortypecode: String, //`xml:"monitorTypeCode"`
  #[yaserde(rename = "reportUnit")]
  pub reportunit: String, //`xml:"reportUnit"`      //nb
  #[yaserde(rename = "reportPerson")]
  pub reportperson: String, //`xml:"reportPerson"`    //nb
  #[yaserde(rename = "reportPersonTel")]
  pub reportpersontel: String, //`xml:"reportPersonTel"` //nb
  #[yaserde(rename = "remark")]
  pub remark: String, //`xml:"remark"`          //nb
}

#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "contactHazardFactorList")]
pub struct ContactHazardFactorList {
  #[yaserde(rename = "contactHazardFactor")]
  pub contacthazardfactors: Vec<ContactHazardFactor>, //`xml:"contactHazardFactor"`
}

// SContactHazardFactor contactHazardFactor
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "contactHazardFactor")]
pub struct ContactHazardFactor {
  #[yaserde(rename = "hazardCode")]
  pub hazardcode: String, //`xml:"hazardCode"`
  #[yaserde(rename = "otherHazardName")]
  pub otherhazardname: String, //`xml:"otherHazardName"` //nb
}

// SHazardFactorList hazardFactorList节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "hazardFactorList")]
pub struct HazardFactorList {
  #[yaserde(rename = "hazardFactor")]
  pub hazardfactors: Vec<HazardFactor>, //`xml:"hazardFactor"`
}

// SHazardFactor hazardFactor节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "hazardFactor")]
pub struct HazardFactor {
  #[yaserde(rename = "hazardCode")]
  pub hazardcode: String, //`xml:"hazardCode"`
  #[yaserde(rename = "otherHazardName")]
  pub otherhazardname: String, //`xml:"otherHazardName"` //nb
  #[yaserde(rename = "hazardStartDate")]
  pub hazardstartdate: Option<String>, //`xml:"hazardStartDate"` //nb
  #[yaserde(rename = "hazardYear")]
  pub hazardyear: Option<i32>, //`xml:"hazardYear"`
  #[yaserde(rename = "hazardMonth")]
  pub hazardmonth: Option<i32>, //`xml:"hazardMonth"`
}

// SItemList itemList节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "itemList")]
pub struct ItemList {
  #[yaserde(rename = "item")]
  pub itemlist: Vec<Item>, //`xml:"item"`
}

// SItem item节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "item")]
pub struct Item {
  #[yaserde(rename = "itemId")]
  pub itemid: String, //`xml:"itemId"`
  #[yaserde(rename = "otherItemName")]
  pub otheritemname: String, //`xml:"otherItemName"`
  #[yaserde(rename = "itemGroupName")]
  pub itemgroupname: String, //`xml:"itemGroupName"` //nb
  #[yaserde(rename = "department")]
  pub department: String, //`xml:"department"`
  #[yaserde(rename = "result")]
  pub result: String, //`xml:"result"`
  #[yaserde(rename = "type")]
  pub itemtype: i32, //`xml:"type"`
  #[yaserde(rename = "unit")]
  pub unit: String, //`xml:"unit"`
  #[yaserde(rename = "max")]
  pub max: String, //`xml:"max"`
  #[yaserde(rename = "min")]
  pub min: String, //`xml:"min"`
  #[yaserde(rename = "characterCode")]
  pub charactercode: String, //`xml:"characterCode"` //条件必填,1: <，2:>，当检查结果类别为半定量时，该项必填
  #[yaserde(rename = "checkResult")]
  pub checkresult: String, //`xml:"checkResult"`
  #[yaserde(rename = "mark")]
  pub mark: String, //`xml:"mark"`
  #[yaserde(rename = "checkDate")]
  pub checkdate: String, //`xml:"checkDate"`
  #[yaserde(rename = "checkDoctor")]
  pub checkdoctor: String, //`xml:"checkDoctor"`
}

// SDiagnosisList diagnosisList节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "diagnosisList")]
pub struct DiagnosisList {
  #[yaserde(rename = "diagnosis")]
  pub diagnosises: Vec<Diagnosis>, //`xml:"diagnosis"`
}

// SDiagnosis diagnosis节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "diagnosis")]
pub struct Diagnosis {
  #[yaserde(rename = "hazardCode")]
  pub hazardcode: String, //`xml:"hazardCode"`
  #[yaserde(rename = "conclusion")]
  pub conclusion: String, //`xml:"conclusion"`对照字典6
  #[yaserde(rename = "repeatItemList")]
  pub repeatitemlist: RepeatItemList, //`xml:"repeatItemList"` //复查时必填
  #[yaserde(rename = "cdtList")]
  pub cdtlist: CdtList, //`xml:"cdtList"`        //职业禁忌时必填
  #[yaserde(rename = "sptList")]
  pub sptlist: SptList, //`xml:"sptList"`        //疑似职业病时必填
  #[yaserde(rename = "otherList")]
  pub otherlist: OtherList, //`xml:"otherList"`      // 其他疾病时必填
}

// SRepeatItemList repeatItemList节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "repeatItemList")]
pub struct RepeatItemList {
  #[yaserde(rename = "repeatItem")]
  pub repeatitems: Vec<RepeatItem>, //`xml:"repeatItem"`
}

// SRepeatItem repeatItem节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "repeatItem")]
pub struct RepeatItem {
  #[yaserde(rename = "repeatItemId")]
  pub repeatitemid: String, //`xml:"repeatItemId"`
  #[yaserde(rename = "otherItemName")]
  pub otheritemname: String, //`xml:"otherItemName"`
}

// SCdtList cdtList节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "cdtList")]
pub struct CdtList {
  #[yaserde(rename = "cdt")]
  pub cdts: Vec<Cdt>, //`xml:"cdt"`
}

// SCdt cdt节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "cdt")]
pub struct Cdt {
  // CdtHazardCode :String, //`xml:"cdtHazardCode"`
  #[yaserde(rename = "cdtId")]
  pub cdtid: String, //`xml:"cdtId"`
}

// SSptList sptList节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "sptList")]
pub struct SptList {
  #[yaserde(rename = "spt")]
  pub spts: Vec<Spt>, //`xml:"spt"`
}

// SSpt spt节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "spt")]
pub struct Spt {
  // SptHazardCode :String, //`xml:"sptHazardCode"`
  #[yaserde(rename = "sptId")]
  pub sptid: String, //`xml:"sptId"`
  #[yaserde(rename = "empName")]
  pub empname: String, //`xml:"empName"`
}

// SOtherList otherList节点内容
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "otherList")]
pub struct OtherList {
  #[yaserde(rename = "otherDisease")]
  pub otherdiseases: Vec<String>, //`xml:"otherDisease"`
}

// ----------------------------- 企业信息 --------------------------
// SEmployerList employerList
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "employerList")]
pub struct EmployerList {
  #[yaserde(rename = "employer")]
  pub employers: Vec<Employer>, //`xml:"employer"`
}

// SEmployer employer
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "employer")]
pub struct Employer {
  #[yaserde(rename = "creditCode")]
  pub creditcode: String, //`xml:"creditCode"`
  #[yaserde(rename = "employerName")]
  pub employername: String, //`xml:"employerName"`
  #[yaserde(rename = "orgCode")]
  pub orgcode: String, //`xml:"orgCode"`
  #[yaserde(rename = "orgName")]
  pub orgname: String, //`xml:"orgName"`
  #[yaserde(rename = "areaCode")]
  pub areacode: String, //`xml:"areaCode"`
  #[yaserde(rename = "economicTypeCode")]
  pub economictypecode: String, //`xml:"economicTypeCode"`
  #[yaserde(rename = "industryCategoryCode")]
  pub industrycategorycode: String, //`xml:"industryCategoryCode"`
  #[yaserde(rename = "enterpriseSizeCode")]
  pub enterprisesizecode: String, //`xml:"enterpriseSizeCode"`
  #[yaserde(rename = "address")]
  pub address: String, //`xml:"address"`
  #[yaserde(rename = "addressZipCode")]
  pub addresszipcode: String, //`xml:"addressZipCode"`
  #[yaserde(rename = "contactPerson")]
  pub contactperson: String, //`xml:"contactPerson"`
  #[yaserde(rename = "employerPhone")]
  pub employerphone: String, //`xml:"employerPhone"`
  #[yaserde(rename = "statffNum")]
  pub statffnum: String, //职工总人数
  #[yaserde(rename = "productioStaffNum")]
  pub productiostaffnum: String, //其中生产工人总数
  #[yaserde(rename = "externalStaffNum ")]
  pub externalstaffnum: String, //外委人员数
  #[yaserde(rename = "contactHazardStaffNum ")]
  pub contacthazardstaffnum: String, //接触有毒有害作业人数
  #[yaserde(rename = "isSubsidiary")]
  pub issubsidiary: bool, //`xml:"isSubsidiary"`
  #[yaserde(rename = "secondEmployerCode")]
  pub secondemployercode: String, //`xml:"secondEmployerCode"`
  #[yaserde(rename = "createAreaCode")]
  pub createareacode: String, //`xml:"createAreaCode"`
  #[yaserde(rename = "writeUnit")]
  pub writeunit: String, //`xml:"writeUnit"`//填表单位名称
  #[yaserde(rename = "writePerson")]
  pub writeperson: String, //`xml:"writePerson"`
  #[yaserde(rename = "writePersonTel")]
  pub writepersontel: String, //`xml:"writePersonTel"`
  #[yaserde(rename = "writeDate")]
  pub writedate: i32, //`xml:"writeDate"`
  #[yaserde(rename = "reportUnit")]
  pub reportunit: String, //`xml:"reportUnit"`
  #[yaserde(rename = "reportPerson")]
  pub reportperson: String, //`xml:"reportPerson"`
  #[yaserde(rename = "reportPersonTel")]
  pub reportpersontel: String, //`xml:"reportPersonTel"`
  #[yaserde(rename = "auditStatus")]
  pub auditstatus: String, //`xml:"auditStatus"`
  #[yaserde(rename = "auditInfo")]
  pub auditdesc: String, //`xml:"auditInfo"`
  #[yaserde(rename = "auditTime")]
  pub auditdate: String, //`xml:"auditTime"`
  #[yaserde(rename = "auditorName")]
  pub auditorname: String, //`xml:"auditorName"`
}
// ----------------------------- end --------------------------------

//SAuditInfo auditInfo
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "auditInfo")]
pub struct AuditInfo {
  #[yaserde(rename = "auditStatus")]
  pub auditstatus: String, //`xml:"auditStatus"`
  #[yaserde(rename = "auditDesc")]
  pub auditdesc: String, //`xml:"auditDesc"`
  #[yaserde(rename = "auditDate")]
  pub auditdate: String, //`xml:"auditDate"`
  #[yaserde(rename = "auditorName")]
  pub auditorname: String, //`xml:"auditorName"`
}

// ************************************************* //
// 以下是新增的调查问卷内容 //
// ************************************************ //

// SHealthSurvey healthSurvey
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "healthSurvey")]
pub struct HealthSurvey {
  #[yaserde(rename = "questionnaire")]
  pub querstionnair: QuestionNaire, //`xml:"questionnaire"`
}

//SQuestionNaire questionnaire
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "questionnaire")]
pub struct QuestionNaire {
  pub id: String, //`xml:"id"`                //问卷表编号
  #[yaserde(rename = "surveyDate")]
  pub surveydate: String, //`xml:"surveyDate"`        //填表日期
  #[yaserde(rename = "smkSituation")]
  pub smksituation: i32, //`xml:"smkSituation"`      //烟酒史-吸烟情况
  #[yaserde(rename = "smkNum")]
  pub smknum: String, //`xml:"smkNum"`            //烟酒史-吸烟数（包/天）
  #[yaserde(rename = "smkYear")]
  pub smkyear: String, //`xml:"smkYear"`           //烟酒史-吸烟多少年
  #[yaserde(rename = "smkMonth")]
  pub smkmonth: String, //`xml:"smkMonth"`           //烟酒史-吸烟多少月
  #[yaserde(rename = "drkSituation")]
  pub drksituation: i32, //`xml:"drkSituation"`      //烟酒史-饮酒情况
  #[yaserde(rename = "drkNum")]
  pub drknum: String, //`xml:"drkNum"`            //烟酒史-饮酒量ml/日
  #[yaserde(rename = "drkYear")]
  pub drkyear: String, //`xml:"drkYear"`           //烟酒史-饮酒多少年
  #[yaserde(rename = "childrenNum")]
  pub childrennum: String, //`xml:"childrenNum"`       //生育史-现有子女数
  #[yaserde(rename = "abortionNum")]
  pub abortionnum: String, //`xml:"abortionNum"`       //生育史-流产数
  #[yaserde(rename = "stillbirthNum")]
  pub stillbirthnum: String, //`xml:"stillbirthNum"`     //生育史-死产数
  #[yaserde(rename = "prematureBirthNum")]
  pub prematurebirthnum: String, //`xml:"prematureBirthNum"` //生育史-早产数
  #[yaserde(rename = "abnormalFetalNum")]
  pub abnormalfetalnum: String, //`xml:"abnormalFetalNum"`  //生育史-异常胎数
  #[yaserde(rename = "childrenCondition")]
  pub childrencondition: String, //`xml:"childrenCondition"` //生育史-子女健康状况
  #[yaserde(rename = "menarcheAge")]
  pub menarcheage: String, //`xml:"menarcheAge"`       //月经史-初潮年龄
  #[yaserde(rename = "period")]
  pub period: String, //`xml:"period"`            //月经史-经期（天）
  #[yaserde(rename = "cycle")]
  pub cycle: String, //`xml:"cycle"`             //月经史-周期（天）
  #[yaserde(rename = "menopauseAge")]
  pub menopauseage: String, //`xml:"menopauseAge"`      //月经史-绝经年龄
  #[yaserde(rename = "occupationHistoryList")]
  pub occupationhistorylist: OccupationHistoryList, //`xml:"occupationHistoryList"`
  #[yaserde(rename = "diseaseHistoryList")]
  pub diseasehistorylist: DiseaseHistoryList, //`xml:"diseaseHistoryList"`
  #[yaserde(rename = "occupationDieaseHistoryList")]
  pub occupationdieasehistorylist: OccupationDieaseHistoryList, //`xml:"occupationDieaseHistoryList"` //
  #[yaserde(rename = "marriageHistoryList")]
  pub marriagehistorylist: MarriageHistoryList, //`xml:"marriageHistoryList"`         //
  #[yaserde(rename = "symptomList")]
  pub symptomlist: SymptomList, //`xml:"symptomList"`                 //symptomList
}

//SOccupationHistoryList occupationHistoryList
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "occupationHistoryList")]
pub struct OccupationHistoryList {
  #[yaserde(rename = "occupationHostory")]
  pub ocuhistories: Vec<OccupationHistory>, //`xml:"occupationHostory"`
}

//SOccupationHostory occupationHostory
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "occupationHostory")]
pub struct OccupationHistory {
  #[yaserde(rename = "startDate")]
  pub startdate: String, //`xml:"startDate"`             //
  #[yaserde(rename = "endDate")]
  pub enddate: String, //`xml:"endDate"`               //
  #[yaserde(rename = "depWorkShop")]
  pub depworkshop: String, //`xml:"depWorkShop"`           //
  #[yaserde(rename = "jobCode")]
  pub jobcode: String, //`xml:"jobCode"`               //对照字典19
  #[yaserde(rename = "otherJobName")]
  pub otherjobname: String, //`xml:"otherJobName"`          //工种选择其他时，该项必填
  #[yaserde(rename = "harmful")]
  pub harmful: String, //`xml:"harmful"`               //对照字典13 多危害因素用英文（,）分隔
  #[yaserde(rename = "protectiveMeasures")]
  pub protectivemeasures: String, //`xml:"protectiveMeasures"`    //
  #[yaserde(rename = "dayNum")]
  pub daynum: String, //`xml:"dayNum"`                //
  #[yaserde(rename = "totalNum")]
  pub totalnum: String, //`xml:"totalNum"`              //
  #[yaserde(rename = "overdose")]
  pub overdose: String, //`xml:"overdose"`              //
  #[yaserde(rename = "occuExposure")]
  pub occuexposure: String, //`xml:"occuExposure"`          //对照字典22
  #[yaserde(rename = "occuExposureCode")]
  pub occuexposurecode: String, //`xml:"occuExposureCode"`      //对照字典22 职业史(放射)种类编码与职业史(放射)种类名称要一一对应
  #[yaserde(rename = "radiationtype")]
  pub radiationtype: String, //`xml:"radiationtype"`         //
  #[yaserde(rename = "radProtectiveMeasures")]
  pub radprotectivemeasures: String, //`xml:"radProtectiveMeasures"` //
}

//SDiseaseHistoryList diseaseHistoryList
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "diseaseHistoryList")]
pub struct DiseaseHistoryList {
  #[yaserde(rename = "diseaseHistory")]
  pub diseasehistories: Vec<DiseaseHistory>, //`xml:"diseaseHistory"` //
}

//SDiseaseHistory diseaseHistory
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "diseaseHistory")]
pub struct DiseaseHistory {
  #[yaserde(rename = "diseaseName")]
  pub diseasename: String, //`xml:"diseaseName"`       //
  #[yaserde(rename = "diagnosisDate")]
  pub diagnosisdate: String, //`xml:"diagnosisDate"`     //
  #[yaserde(rename = "diagnosisOrg")]
  pub diagnosisorg: String, //`xml:"diagnosisOrg"`      //
  #[yaserde(rename = "treatmentProcess")]
  pub treatmentprocess: String, //`xml:"treatmentProcess"`  //
  #[yaserde(rename = "dieaseOutcomeCode")]
  pub dieaseoutcomecode: String, //`xml:"dieaseOutcomeCode"` //转归编码 对照字典26
}

//SOccupationDieaseHistoryList
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "occupationDieaseHistoryList")]
pub struct OccupationDieaseHistoryList {
  #[yaserde(rename = "occupationDieaseHistory")]
  pub occupationdieasehistory: Vec<OccupationDieaseHistory>, //`xml:"occupationDieaseHistory"` //
}

//SOccupationDieaseHistory occupationDieaseHistory
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "occupationDieaseHistory")]
pub struct OccupationDieaseHistory {
  #[yaserde(rename = "diseaseName")]
  pub diseasename: String, //`xml:"diseaseName"`   //疾病名称
  #[yaserde(rename = "diagnosisDate")]
  pub diagnosisdate: String, //`xml:"diagnosisDate"` //诊断日期
  #[yaserde(rename = "diagnosisOrg")]
  pub diagnosisorg: String, //`xml:"diagnosisOrg"`  //诊断机构名称
  #[yaserde(rename = "isRecovery")]
  pub isrecovery: i32, //`xml:"isRecovery"`    //N 对照字典2
}

//SMarriageHistoryList
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "marriageHistoryList")]
pub struct MarriageHistoryList {
  #[yaserde(rename = "marriageHistory")]
  pub marriagehistories: Vec<MarriageHistory>, //`xml:"marriageHistory"` //
}

//SMarriageHistory marriageHistory
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "marriageHistory")]
pub struct MarriageHistory {
  #[yaserde(rename = "marriageDate")]
  pub marriagedate: String, //`xml:"marriageDate"`    //
  #[yaserde(rename = "spouseRadiation")]
  pub spouseradiation: String, //`xml:"spouseRadiation"` //配偶接触放射线情况
  #[yaserde(rename = "spouseOcpt")]
  pub spouseocpt: String, //`xml:"spouseOcpt"`      //配偶职业及健康情况
}

//SSymptomList
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "symptomList")]
pub struct SymptomList {
  #[yaserde(rename = "symptom")]
  pub symptoms: Vec<Symptom>, //`xml:"symptom"` //
}

//SSymptom symptom
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "symptom")]
pub struct Symptom {
  #[yaserde(rename = "symptomCode")]
  pub symptomcode: String, //`xml:"symptomCode"` //对照字典12
  #[yaserde(rename = "otherDesc")]
  pub otherdesc: String, //`xml:"otherDesc"`   //
  #[yaserde(rename = "result")]
  pub result: String, //`xml:"result"`
  #[yaserde(rename = "checkDoctor")]
  pub checkdoctor: String, //`xml:"checkDoctor"` //
  #[yaserde(rename = "checkDate")]
  pub checkdate: String, //`xml:"checkDate"`   //D8
}
// ----------------------------------------------------------------------------------------------------------------
// fefused card query
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "body")]
pub struct CardQueryBody {
  #[yaserde(rename = "reportCardList")]
  pub cardlistqueryparm: CardQueryList, //`xml:"employerList"`
}

#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
// #[yaserde(rename = "reportCardList")]
pub struct CardQueryList {
  #[yaserde(rename = "reportCard")]
  pub cardlistqueryparm: Cardlistqueryparam, //`xml:"employerList"`
}

#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "data")]
pub struct RefusedcardsQuery {
  #[yaserde(rename = "header")]
  pub header: Header, //`xml:"diseaseName"`       //
  #[yaserde(rename = "body")]
  pub body: Cardlistqueryparam, //`xml:"diagnosisDate"`     //
}

// 审核不通过的职业健康档案查找参数
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
// #[yaserde(rename = "reportCard")]
pub struct Cardlistqueryparam {
  #[yaserde(rename = "pageNum")]
  pub pagenum: String, //`xml:"diseaseName"`       //
  #[yaserde(rename = "pageSize")]
  pub pagesize: String, //`xml:"diagnosisDate"`     //
  #[yaserde(rename = "writeDateBegin")]
  pub writedatebegin: String, //`xml:"diagnosisOrg"`      //
  #[yaserde(rename = "writeDateEnd")]
  pub writedateend: String, //`xml:"treatmentProcess"`  //
  #[yaserde(rename = "checkTimeBegin")]
  pub checktimebegin: String, //`xml:"dieaseOutcomeCode"` //转归编码 对照字典26
  #[yaserde(rename = "checkTimeEnd")]
  pub checktimeend: String, //`xml:"dieaseOutcomeCode"` //转归编码 对照字典26
}

// 审核不通过的职业健康档案查找结果
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "data")]
pub struct Refusedreportcardresponse {
  #[yaserde(rename = "returnCode")]
  pub returncode: String, //`xml:"diseaseName"`       //
                          // #[yaserde(rename = "message")]
                          // pub message: String, //`xml:"diagnosisDate"`     //
                          // #[yaserde(rename = "reportCardList")]
                          // pub reportcards: Vec<Refusedreportcard>, //`xml:"symptom"` //
}


// //////////////////////response/////////////////////
// #[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
// #[yaserde(rename = "DATAS")]
// pub struct ResponstMessage {
//     pub returncode: String,
//     pub returnmessage: String,
//     pub returndata: String,
// }

// //RData response data
// #[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
// #[yaserde(rename = "DATAS")]
// pub struct RData {
//     pub returncode: String,          //`xml:"returnCode"`
//     pub message: String,             //`xml:"message"`
//     pub errorlist: RErrorList,       //`xml:"errorList"`
//     pub emperrorlist: REmpErrorList, //`xml:"empErrorList"`
//     pub submessage: String,          //`xml:"-"`
// }

// //RErrorList errorDatas
// #[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
// #[yaserde(rename = "DATAS")]
// pub struct RErrorList {
//     pub error: Vec<RError>, //`xml:"error"`
// }

// //RError error
// #[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
// #[yaserde(rename = "DATAS")]
// pub struct RError {
//     pub code: String,    //`xml:"code"`
//     pub orgcode: String, //`xml:"orgCode"`
//     pub message: String, //`xml:"message"`
// }

// //REmpErrorList errorDatas
// #[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
// #[yaserde(rename = "DATAS")]
// pub struct REmpErrorList {
//     pub error: Vec<REmpError>, //`xml:"empError"`
// }

// //REmpError error
// #[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
// #[yaserde(rename = "DATAS")]
// pub struct REmpError {
//     pub creditcode: String, //`xml:"creditCode"`
//     pub empname: String,    //`xml:"empName"`
//     pub message: String,    //`xml:"message"`
// }
