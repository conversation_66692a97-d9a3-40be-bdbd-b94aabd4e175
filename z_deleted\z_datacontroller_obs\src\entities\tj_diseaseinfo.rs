use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_diseaseinfo")]
#[derive(<PERSON><PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct TjDiseaseinfo {
    pub id: i64,
    pub tj_testid: String,
    pub tj_disid: i32,
    pub tj_diseasenum: String,
    pub tj_diseasename: String,
    pub tj_suggestion: String,
    pub tj_deptid: String,
    pub tj_isdisease: i32,
    pub tj_isoccu: i32,
    pub tj_typeid: i32,
    pub tj_opinion: String,
    pub tj_showorder: i32,
}
