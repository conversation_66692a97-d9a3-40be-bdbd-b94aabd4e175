//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_corpinfo")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    #[sea_orm(unique)]
    pub tj_corpid: String,
    pub tj_corpname: String,
    pub tj_contactor: String,
    pub tj_principle: String,
    pub tj_phone: String,
    pub tj_fax: String,
    pub tj_areacode: String,
    pub tj_address: String,
    pub tj_postcode: String,
    pub tj_industry2: String,
    pub tj_economic2: String,
    pub tj_pyjm: String,
    pub tj_zdym: String,
    pub tj_operator: i32,
    pub tj_adddate: i64,
    pub tj_testtype: i32,
    pub tj_password: String,
    pub tj_mail: String,
    pub tj_memo: String,
    pub tj_orgcode: String,
    pub tj_gbcode: String,
    #[sea_orm(column_name = "tj_ecotypeH")]
    pub tj_ecotype_h: i32,
    pub tj_ecotype: i32,
    #[sea_orm(column_name = "tj_industryH")]
    pub tj_industry_h: i32,
    pub tj_industry: i32,
    pub tj_secondcode: String,
    pub tj_secondname: String,
    pub tj_corpscale: i32,
    pub tj_total: i32,
    #[sea_orm(column_name = "tj_totalF")]
    pub tj_total_f: i32,
    pub tj_operationer: i32,
    #[sea_orm(column_name = "tj_operationerF")]
    pub tj_operationer_f: i32,
    pub tj_hazarders: i32,
    #[sea_orm(column_name = "tj_hazardersF")]
    pub tj_hazarders_f: i32,
    pub tj_syncflag: i8,
    pub tj_status: i8,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
