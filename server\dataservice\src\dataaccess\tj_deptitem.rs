use crate::entities::{prelude::*, tj_deptitem};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjDeptitem {
  pub async fn query_many(itemids: &Vec<String>, deptids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjDeptitem>> {
    let mut conditions = Condition::all();

    if itemids.len() > 0 {
      conditions = conditions.add(tj_deptitem::Column::TjItemid.is_in(itemids.to_owned()));
    }
    if deptids.len() > 0 {
      conditions = conditions.add(tj_deptitem::Column::TjDeptid.is_in(deptids.to_owned()));
    }
    let ret = TjDeptitemEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjDeptitem, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_deptitem::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    Ok(val_id.unwrap())
  }

  pub async fn save_many(info: &Vec<TjDeptitem>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = info.len();
    let (insert_vals, update_vals): (Vec<TjDeptitem>, Vec<TjDeptitem>) = info.to_owned().into_iter().partition(|f| f.id == 0);

    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_deptitem::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_deptitem::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjDeptitemEntity::insert_many(active_insert_values).exec(db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_deptitem::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    Ok(total as i64)
  }

  pub async fn delete_by_itemid_deptid(itemid: &String, deptid: &String, db: &DatabaseConnection) -> Result<u64> {
    if itemid.is_empty() && deptid.is_empty() {
      return Err(anyhow!("itemid and deptid is empty, not allowed"));
    }
    let mut conditions = Condition::all();
    if !itemid.is_empty() {
      conditions = conditions.add(tj_deptitem::Column::TjItemid.eq(itemid));
    }
    if !deptid.is_empty() {
      conditions = conditions.add(tj_deptitem::Column::TjDeptid.eq(deptid));
    }
    let ret = TjDeptitemEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let conditions = Condition::all().add(tj_deptitem::Column::Id.is_in(ids.to_owned()));
    let ret = TjDeptitemEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
