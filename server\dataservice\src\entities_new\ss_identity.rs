//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "ss_identity")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub id_name: String,
  pub id_cvalue: i32,
  pub id_ovalue: i32,
  pub id_incvalue: i32,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
