use config::{Config, ConfigError, File};
use serde::Deserialize;

#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct Application {
  pub apphost: String,
  pub appport: i32,
}

#[derive(Debug, <PERSON>lone, Deserialize)]
pub struct Database {
  pub uri: String,
}

#[derive(Debug, Clone, Deserialize)]
#[allow(unused)]
pub struct System {
  pub platform: String,
  pub upload_mode: i32,
  pub file_path: String,
  pub area_code: String,
  pub area_name: String,
  pub dr_deptid: String,
  // pub use_https: i32,
  // pub cdc: String,
  pub area: String,
  pub other_hdcode: String,
  pub other_items: String,
  pub rangeval: i32,
  pub multiitem: i32,
  pub additems: Vec<String>,
}

#[derive(Debug, Clone, Deserialize)]
pub struct Organization {
  pub org_code: String,
  pub password: String,
  pub org_name: String,
  pub key_file: String,
  pub server_url: String,
  pub server_url2: String,
}

#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct Settings {
  pub application: Application,
  pub system: System,
  pub database: Database,
  pub organization: Organization,
}

impl Settings {
  pub fn init() -> Result<Self, ConfigError> {
    let s = Config::builder()
      // Start off by merging in the "default" configuration file
      .add_source(File::with_name("config/cdcuploader.toml"))
      // // Add in the current environment file
      // // Default to 'development' env
      // // Note that this file is _optional_
      // .add_source(File::with_name(&format!("examples/hierarchical-env/config/{}", run_mode)).required(false))
      // // Add in a local configuration file
      // // This file shouldn't be checked in to git
      // .add_source(File::with_name("examples/hierarchical-env/config/local").required(false))
      // // Add in settings from the environment (with a prefix of APP)
      // // Eg.. `APP_DEBUG=1 ./target/app` would set the `debug` key
      // .add_source(Environment::with_prefix("app"))
      // // You may also programmatically change settings
      // .set_override("database.url", "postgres://")?
      .build()
      .expect("build config file error");

    s.try_deserialize()
  }
}
