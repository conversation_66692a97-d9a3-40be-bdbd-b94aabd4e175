//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, Default, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "ss_monitor")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub ss_jobcode: String,
  pub ss_jobname: String,
  pub ss_industrycode: String,
  pub ss_industryname: String,
  pub ss_memo: String,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
