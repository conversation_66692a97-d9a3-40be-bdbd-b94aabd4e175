use crate::app;
use anyhow::Result;
// use dbopservice::dataaccess::prelude::*;
use dataservice::entities::prelude::*;
use nodipservice::{
  common::constant::*,
  common::{constant, itemservice::ItemService},
};

pub const HEALTHYEVENTID: &str = "A57";
pub const CORPYEVENTID: &str = "E10";
pub const FENCHEN: &str = "11";
pub const REFUSEDQUERYEVENTID: &str = "S83";

pub fn get_corp_scale(scale: i32) -> String {
  match scale {
    1 => "10000".to_string(),
    2 => "10001".to_string(),
    3 => "10002".to_string(),
    _ => "10004".to_string(),
  }
}

pub fn get_operation_type(optype: app::OperationType) -> String {
  match optype {
    app::OperationType::ADD => "add".to_string(),
    app::OperationType::UPDATE => "update".to_string(),
    app::OperationType::DELETE => "delete".to_string(),
    // _ => "add".to_string(),
  }
}

pub fn get_item_check_result(itemid: &String, checkitem: &TjCheckiteminfo, iteminfo: &TjIteminfo) -> Result<String> {
  if itemid.eq_ignore_ascii_case("11247") {
    if checkitem.tj_abnormalflag == 1 {
      if checkitem.tj_result.contains("尘肺") {
        return Ok("2".to_string());
      } else {
        return Ok("3".to_string());
      }
    } else {
      return Ok("1".to_string());
    }
  }

  let ret = ItemService::get_item_check_result(checkitem, iteminfo);
  return ret;
}

pub fn get_check_result(ttype: i32) -> String {
  match ttype {
    0 => "1".to_string(),
    1 => "2".to_string(),
    2 => "4".to_string(),
    3 => "3".to_string(), //
    4 => "5".to_string(), //
    5 => "1".to_string(), //
    _ => "".to_string(),
  }
}

pub fn get_check_type(sffc: i32) -> i32 {
  match sffc {
    0 => 11,
    1 => 21,
    _ => 11,
  }
}
pub fn get_item_type(typestr: &str) -> i32 {
  if typestr.is_empty() {
    return 3;
  }
  if typestr.eq_ignore_ascii_case("0") {
    return 3;
  }
  let itype = typestr.parse::<i32>();
  if itype.is_err() {
    return 3;
  }

  return itype.unwrap_or(3);
  // match typestr {
  //     "N" => 2,
  //     _ => 1,
  // }
}

// pub fn get_qualified_flag(ttype: i32) -> i32 {
//     match ttype {
//         0 => 1,
//         1 => 0,
//         _ => 0,
//     }
// }

pub fn get_qualified_mark(ttype: i32, drret: i32, itemcode: &String, drcode: &String) -> i32 {
  let drids: Vec<String> = drcode.split(",").map(|x| x.to_string()).collect();
  // info!("当前项目:{},胸片项目:{:?}", &itemcode, &drids);
  let dr_code = drids.iter().find(|&x| x.eq_ignore_ascii_case(itemcode.as_str()));
  if dr_code.is_none() {
    let ret = match ttype {
      0 => 1,
      1 => 0,
      _ => 0,
    };
    return ret;
  } else {
    let mut drret = drret;
    if ttype != 0 && drret == 1 {
      drret = 3; //其他异常
    }
    return drret;
    // let ret = match ttype {
    //   0 => 1,
    //   _ => 3,
    // };
    // return ret;
  }
}

pub fn get_testtype_status(ttype: i32) -> String {
  match ttype {
    // 2 => "".to_string(),
    3 => "1".to_string(), //sg
    4 => "2".to_string(), //zg
    5 => "3".to_string(), //lg
    6 => "5".to_string(), //yj
    _ => "".to_string(),
  }
}

pub fn get_marriage_status(marriage: i32) -> i32 {
  match marriage {
    1 => 1,
    2 => 0,
    3 => 2,
    4 => 3,
    _ => 4,
  }
}

#[derive(Clone, Debug)]
#[allow(unused)]
pub struct AudioDetail {
  pub extcode: String,
  pub extname: String,
  pub freq: i32,
  pub transear: i32,
  pub transtype: i32,
  pub lowvalue: i32,
  pub highvalue: i32,
}

pub fn get_card_type(pthds: &Vec<TjPatienthazards>, hdinfos: &Vec<TjHazardinfo>) -> String {
  let mut card_type = "101".to_string();
  let mut has_radio = false;
  let mut has_occu = false;

  for hd in pthds.iter() {
    if let Some(hdinfo) = hdinfos.iter().find(|v| v.id == hd.tj_hid) {
      if hdinfo.tj_extcode.starts_with("14") {
        has_radio = true;
      } else {
        has_occu = true;
      }
    }
  }

  if has_occu && has_radio {
    card_type = "301".to_string()
  } else if !has_occu && has_radio {
    card_type = "201".to_string()
  }

  card_type
}

//放射工作人员职业健康检查适任性评价编码
pub fn get_radiation_result(medinfo: &TjMedexaminfo, checkall: &TjCheckallnew) -> String {
  let ret = match medinfo.tj_testtype {
    x if x == constant::TestType::SG as i32 => {
      if checkall.tj_typeid == constant::CheckResultType::Oculike as i32 || checkall.tj_typeid == constant::CheckResultType::Forbidden as i32 {
        "103".to_string()
      } else {
        "101".to_string()
      }
    }
    x if x == constant::TestType::ZG as i32 => {
      if checkall.tj_typeid == constant::CheckResultType::Oculike as i32 || checkall.tj_typeid == constant::CheckResultType::Forbidden as i32 {
        "203".to_string()
      } else {
        "201".to_string()
      }
    }
    x if x == constant::TestType::LG as i32 => {
      if checkall.tj_typeid == constant::CheckResultType::Oculike as i32 || checkall.tj_typeid == constant::CheckResultType::Forbidden as i32 {
        "302".to_string()
      } else {
        "301".to_string()
      }
    }
    _ => "".to_string(),
  };
  ret
}

pub fn get_audio_detail(val: &TjAudiogramdetail, sex: i32) -> Option<AudioDetail> {
  let details = init_audio_details(sex);
  let dt = details
    .iter()
    .find(|x| x.freq == val.tj_freq && x.transear == val.tj_ear && x.transtype == val.tj_adtype)
    .map(|x| x.clone());
  dt
}

pub fn get_audio_result(rttype: i32, sex: i32) -> Option<AudioDetail> {
  let adrets = init_audio_result(sex);
  adrets.iter().find(|x| x.freq == rttype).map(|x| x.clone())
}

pub fn init_audio_details(sex: i32) -> Vec<AudioDetail> {
  let yupin_highval = 25;
  let gaopin_maxval = 25;
  let mut lowval = -50;
  if sex == 2 {
    lowval = -35;
  }

  let mut details: Vec<AudioDetail> = Vec::new();
  let yq500 = AudioDetail {
    extcode: "11231".to_string(),
    extname: "左耳500Hz（气导）".to_string(),
    freq: 500,
    transear: TransEar::Right as i32,
    transtype: TransType::Air as i32,
    lowvalue: lowval,
    highvalue: yupin_highval,
  };
  details.push(yq500);
  let yg500 = AudioDetail {
    extcode: "11432".to_string(),
    extname: "右耳500Hz(骨导)".to_string(),
    freq: 500,
    transear: TransEar::Right as i32,
    transtype: TransType::Bone as i32,
    lowvalue: lowval,
    highvalue: yupin_highval,
  };
  details.push(yg500);
  let yq1000 = AudioDetail {
    extcode: "11234".to_string(),
    extname: "右耳1000Hz（气导）".to_string(),
    freq: 1000,
    transear: TransEar::Right as i32,
    transtype: TransType::Air as i32,
    lowvalue: lowval,
    highvalue: yupin_highval,
  };
  details.push(yq1000);
  let yg1000 = AudioDetail {
    extcode: "11433".to_string(),
    extname: "右耳1000Hz(骨导)".to_string(),
    freq: 1000,
    transear: TransEar::Right as i32,
    transtype: TransType::Bone as i32,
    lowvalue: lowval,
    highvalue: yupin_highval,
  };
  details.push(yg1000);

  let yq2000 = AudioDetail {
    extcode: "11235".to_string(),
    extname: "右耳2000Hz".to_string(),
    freq: 2000,
    transear: TransEar::Right as i32,
    transtype: TransType::Air as i32,
    lowvalue: lowval,
    highvalue: yupin_highval,
  };
  details.push(yq2000);

  let yg2000 = AudioDetail {
    extcode: "11434".to_string(),
    extname: "右耳2000Hz(骨导)".to_string(),
    freq: 2000,
    transear: TransEar::Right as i32,
    transtype: TransType::Bone as i32,
    lowvalue: lowval,
    highvalue: yupin_highval,
  };
  details.push(yg2000);

  let yq3000 = AudioDetail {
    extcode: "11237".to_string(),
    extname: "右耳3000Hz".to_string(),
    freq: 3000,
    transear: TransEar::Right as i32,
    transtype: TransType::Air as i32,
    lowvalue: lowval,
    highvalue: gaopin_maxval,
  };
  details.push(yq3000);

  let yg3000 = AudioDetail {
    extcode: "11435".to_string(),
    extname: "右耳3000Hz(骨导)".to_string(),
    freq: 3000,
    transear: TransEar::Right as i32,
    transtype: TransType::Bone as i32,
    lowvalue: lowval,
    highvalue: gaopin_maxval,
  };
  details.push(yg3000);

  let yq4000 = AudioDetail {
    extcode: "11239".to_string(),
    extname: "右耳4000Hz".to_string(),
    freq: 4000,
    transear: TransEar::Right as i32,
    transtype: TransType::Air as i32,
    lowvalue: lowval,
    highvalue: gaopin_maxval,
  };
  details.push(yq4000);

  let yg4000 = AudioDetail {
    extcode: "11436".to_string(),
    extname: "右耳4000Hz(骨导)".to_string(),
    freq: 4000,
    transear: TransEar::Right as i32,
    transtype: TransType::Bone as i32,
    lowvalue: lowval,
    highvalue: gaopin_maxval,
  };
  details.push(yg4000);

  let yq6000 = AudioDetail {
    extcode: "11241".to_string(),
    extname: "右耳6000Hz".to_string(),
    freq: 6000,
    transear: TransEar::Right as i32,
    transtype: TransType::Air as i32,
    lowvalue: lowval,
    highvalue: gaopin_maxval,
  };
  details.push(yq6000);

  let yg6000 = AudioDetail {
    extcode: "11437".to_string(),
    extname: "右耳6000Hz(骨导)".to_string(),
    freq: 6000,
    transear: TransEar::Right as i32,
    transtype: TransType::Bone as i32,
    lowvalue: lowval,
    highvalue: gaopin_maxval,
  };
  details.push(yg6000);
  // ========LEFT========================================
  let zq500 = AudioDetail {
    extcode: "11230".to_string(),
    extname: "左耳500Hz".to_string(),
    freq: 500,
    transear: TransEar::Left as i32,
    transtype: TransType::Air as i32,
    lowvalue: lowval,
    highvalue: 25,
  };
  details.push(zq500);
  let zg500 = AudioDetail {
    extcode: "11426".to_string(),
    extname: "左耳500Hz(骨导)".to_string(),
    freq: 500,
    transear: TransEar::Left as i32,
    transtype: TransType::Bone as i32,
    lowvalue: lowval,
    highvalue: yupin_highval,
  };
  details.push(zg500);

  let zq1000 = AudioDetail {
    extcode: "11232".to_string(),
    extname: "左耳1000Hz".to_string(),
    freq: 1000,
    transear: TransEar::Left as i32,
    transtype: TransType::Air as i32,
    lowvalue: lowval,
    highvalue: yupin_highval,
  };
  details.push(zq1000);
  let zg1000 = AudioDetail {
    extcode: "11427".to_string(),
    extname: "左耳1000Hz(骨导)".to_string(),
    freq: 1000,
    transear: TransEar::Left as i32,
    transtype: TransType::Bone as i32,
    lowvalue: lowval,
    highvalue: yupin_highval,
  };
  details.push(zg1000);

  let zq2000 = AudioDetail {
    extcode: "11233".to_string(),
    extname: "左耳2000Hz".to_string(),
    freq: 2000,
    transear: TransEar::Left as i32,
    transtype: TransType::Air as i32,
    lowvalue: lowval,
    highvalue: yupin_highval,
  };
  details.push(zq2000);

  let zg2000 = AudioDetail {
    extcode: "11428".to_string(),
    extname: "左耳2000Hz(骨导)".to_string(),
    freq: 2000,
    transear: TransEar::Left as i32,
    transtype: TransType::Bone as i32,
    lowvalue: lowval,
    highvalue: yupin_highval,
  };
  details.push(zg2000);

  let zq3000 = AudioDetail {
    extcode: "11236".to_string(),
    extname: "左耳3000Hz".to_string(),
    freq: 3000,
    transear: TransEar::Left as i32,
    transtype: TransType::Air as i32,
    lowvalue: lowval,
    highvalue: gaopin_maxval,
  };
  details.push(zq3000);

  let zg3000 = AudioDetail {
    extcode: "11429".to_string(),
    extname: "左耳3000Hz(骨导)".to_string(),
    freq: 3000,
    transear: TransEar::Left as i32,
    transtype: TransType::Bone as i32,
    lowvalue: lowval,
    highvalue: gaopin_maxval,
  };
  details.push(zg3000);

  let zq4000 = AudioDetail {
    extcode: "11238".to_string(),
    extname: "左耳4000Hz".to_string(),
    freq: 4000,
    transear: TransEar::Left as i32,
    transtype: TransType::Air as i32,
    lowvalue: lowval,
    highvalue: gaopin_maxval,
  };
  details.push(zq4000);

  let zg4000 = AudioDetail {
    extcode: "11430".to_string(),
    extname: "左耳4000Hz(骨导)".to_string(),
    freq: 4000,
    transear: TransEar::Left as i32,
    transtype: TransType::Bone as i32,
    lowvalue: lowval,
    highvalue: gaopin_maxval,
  };
  details.push(zg4000);

  let zq6000 = AudioDetail {
    extcode: "11240".to_string(),
    extname: "左耳6000Hz".to_string(),
    freq: 6000,
    transear: TransEar::Left as i32,
    transtype: TransType::Air as i32,
    lowvalue: lowval,
    highvalue: gaopin_maxval,
  };
  details.push(zq6000);

  let zg6000 = AudioDetail {
    extcode: "11431".to_string(),
    extname: "左耳6000Hz(骨导)".to_string(),
    freq: 6000,
    transear: TransEar::Left as i32,
    transtype: TransType::Bone as i32,
    lowvalue: lowval,
    highvalue: gaopin_maxval,
  };
  details.push(zg6000);

  details
}

pub enum AdResultType {
  SYP = 0,
  ZYP = 1,
  YYP = 2,
  SGP = 3,
  YJQ = 4,
  ZJQ = 5,
}

fn init_audio_result(sex: i32) -> Vec<AudioDetail> {
  let mut lowval = -50;
  if sex == 2 {
    lowval = -35;
  }
  let gaopin_avg = 40;
  let yupin_avg = 25;
  let mut details: Vec<AudioDetail> = Vec::new();
  let syp = AudioDetail {
    extcode: "11245".to_string(),
    extname: "双耳语频平均听阈".to_string(),
    freq: 0,
    transear: 0,
    transtype: 0,
    lowvalue: lowval,
    highvalue: yupin_avg,
  };
  details.push(syp);
  let zyp = AudioDetail {
    extcode: "11242".to_string(),
    extname: "左耳语频平均听阈".to_string(),
    freq: 1,
    transear: 0,
    transtype: 0,
    lowvalue: lowval,
    highvalue: yupin_avg,
  };
  details.push(zyp);

  let yyp = AudioDetail {
    extcode: "11243".to_string(),
    extname: "右耳语频平均听阈".to_string(),
    freq: 2,
    transear: 0,
    transtype: 0,
    lowvalue: lowval,
    highvalue: yupin_avg,
  };
  details.push(yyp);

  let sgp = AudioDetail {
    extcode: "11244".to_string(),
    extname: "双耳高频平均听阈".to_string(),
    freq: 3,
    transear: 0,
    transtype: 0,
    lowvalue: lowval,
    highvalue: gaopin_avg,
  };
  details.push(sgp);

  let yjq = AudioDetail {
    extcode: "11505".to_string(),
    extname: "右耳听阈加权".to_string(),
    freq: 4,
    transear: 0,
    transtype: 0,
    lowvalue: lowval,
    highvalue: yupin_avg,
  };
  details.push(yjq);

  let zjq = AudioDetail {
    extcode: "11504".to_string(),
    extname: "左耳听阈加权值".to_string(),
    freq: 5,
    transear: 0,
    transtype: 0,
    lowvalue: lowval,
    highvalue: yupin_avg,
  };
  details.push(zjq);

  details
}
