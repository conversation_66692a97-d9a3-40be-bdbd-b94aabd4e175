//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_corpmedexaminfo")]
pub struct Model {
  #[sea_orm(column_name = "ID", primary_key)]
  pub id: i32,
  pub tj_corpnum: i32,
  pub tj_corpname: String,
  pub tj_pyjm: String,
  pub tj_testtype: i32,
  pub tj_status: i32,
  pub tj_testers: i32,
  pub tj_checked: i32,
  pub tj_reportnum: String,
  pub tj_staffid: i32,
  pub tj_aptnum: i64,
  pub tj_notifystate: i8,
  pub tj_testdate: i64,
  pub tj_recdate: i64,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
