use anyhow::{anyhow, Result};
use mpart_async::client::MultipartRequest;
use reqwest::{header::CONTENT_TYPE, Body, Client};
// use serde::{Deserialize, Serialize};

pub struct HttpClient;

impl HttpClient {
  pub async fn send_request(parm: &str, orginfo: &str, uri: &str, server: &str) -> Result<String> {
    if server.is_empty() {
      return Ok("".to_string());
    }
    let client = Client::new();
    let server_uri = format!("{}{}", server, uri);
    // info!("start to send request to server:{},parameters:{}", &server_uri, parm);
    let resp = client
      .post(server_uri)
      .timeout(std::time::Duration::from_secs(600))
      .header(CONTENT_TYPE, "application/json; charset=utf-8")
      .header("orginfo", orginfo.to_owned())
      .body(parm.to_owned())
      .send()
      .await;

    // info!("resp is:{:?}", &resp);

    if resp.is_err() {
      return Err(anyhow!("{}", resp.err().unwrap().to_string()));
    }
    let ret = resp.unwrap().text().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let response_text = ret.unwrap();
    info!("response text is:{:?}", &response_text);
    // let response_value: Result<ResponseBody> = serde_json::from_str::<T>(response_text.as_str());
    Ok(response_text)
  }

  pub async fn send_upload_request(filename: &str, medid: i64, orginfo: &str, uri: &str, server: &str) -> Result<String> {
    if server.is_empty() {
      return Ok("".to_string());
    }
    let mut mpart = MultipartRequest::default();
    mpart.add_file(medid.to_string(), filename);

    // info!("MultiPart:{:?}", &mpart);
    let server_uri = format!("{}{}", server, uri);
    info!("upload server uri is:{}", &server_uri);
    let resp = Client::new()
      .post(server_uri)
      .timeout(std::time::Duration::from_secs(600))
      .header("orginfo", orginfo.to_owned())
      .header(CONTENT_TYPE, format!("multipart/form-data; boundary={}", mpart.get_boundary()))
      .body(Body::wrap_stream(mpart))
      .send()
      .await;
    if resp.is_err() {
      return Err(anyhow!("{}", resp.err().unwrap().to_string()));
    }
    let ret = resp.unwrap().text().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let response_text = ret.unwrap();
    Ok(response_text)
  }
}
