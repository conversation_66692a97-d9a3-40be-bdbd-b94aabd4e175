use super::zjresponse::ZjResponse;
use crate::app::Refusedcard;
use crate::client::zwxresponse::ZwxResponse;
use crate::{app, config::settings::Settings};
use anyhow::{anyhow, Result};
// use log::*;
use tracing::*;
// use hyper::StatusCode;
use mpart_async::client::MultipartRequest;
use reqwest::header::CONTENT_TYPE;
use reqwest::{
  // multipart::{Form, Part},
  Body,
  Client,
};
pub struct TransClient {}
impl TransClient {
  //
  pub async fn upload(content: &String, server: &str, settings: &Settings) -> Result<String> {
    if settings.system.platform == app::Platform::Shanxi.to_string() {
      return TransClient::zwx_upload(&content, server, settings).await;
    } else if settings.system.platform == app::Platform::Zhongweixin.to_string() {
      return TransClient::zwx_upload(&content, server, settings).await;
    } else {
      return TransClient::zj_upload(content.to_owned(), server, settings).await;
    }
    // Ok("".to_string())
  }

  pub async fn query(content: &String, server: &str, settings: &Settings) -> Result<Vec<Refusedcard>> {
    if settings.system.platform == app::Platform::Zhejiang.to_string() {
      return TransClient::zj_query(content.to_owned(), server, settings).await;
    } else {
      return Err(anyhow!("platform:{} not supported", &settings.system.platform));
    }
  }

  async fn zwx_upload(filename: &String, server: &str, settings: &Settings) -> Result<String> {
    info!("uploading to zwx servers......");
    let client = Client::new();

    // ===================方法 1 ======================
    // let content: Vec<u8> = Vec::new(); // Read in file content
    // let part = Part::bytes(content).file_name(filename.to_owned());
    // // let file = reqwest::multipart::Form::new().part("dataFile", part);

    // let form = Form::new()
    //     .text("unitCode", settings.organization.org_code.clone())
    //     .text("password", settings.organization.password.clone())
    //     .part("dataFile", part);

    // let resp = client
    //     .post(settings.organization.server_url.as_str())
    //     .basic_auth(settings.organization.org_code.to_owned(), Some(settings.organization.password.to_owned()))
    //     .multipart(form)
    //     .send()
    //     .await;

    // ====================== 方法 2 ===================

    let mut mpart = MultipartRequest::default();
    // let filename = "./files/abc.zip";
    mpart.add_field("unitCode", settings.organization.org_code.as_str());
    mpart.add_field("password", settings.organization.password.as_str());
    mpart.add_file("dataFile", filename);

    // info!("MultiPart:{:?}", &mpart);

    let resp = client
      .post(server)
      .timeout(std::time::Duration::from_secs(600))
      .basic_auth(
        format!("unitCode={}", settings.organization.org_code.to_owned()),
        Some(format!("password={}", settings.organization.password.to_owned())),
      )
      .header(CONTENT_TYPE, format!("multipart/form-data; boundary={}", mpart.get_boundary()))
      .body(Body::wrap_stream(mpart))
      .send()
      .await;

    //===========================================================
    // info!("RESP:{:?}", &resp);
    if resp.is_err() {
      return Err(anyhow!("upload error:{}", resp.as_ref().err().unwrap().to_string()));
    }
    let resp = resp.unwrap();
    // if resp.status().eq(&hyper::StatusCode::UNAUTHORIZED) {
    //   return Err(anyhow!("UNAUTHORIZED......"));
    // }
    // resp.t
    let ret = resp.text().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let res_ret = ret.unwrap();
    info!("text result: {:?}", &res_ret);

    let response = ZwxResponse::decode_response(res_ret.as_str());
    if response.as_ref().is_err() {
      return Err(anyhow!("{}", response.as_ref().err().unwrap().to_string()));
    }
    Ok("上报成功".to_string())
  }

  async fn zj_upload(content: String, server: &str, setting: &Settings) -> Result<String> {
    // info!("uploading to zj servers ......{}", &server);
    // info!("CONTENT:{}", &content);
    let client;
    if setting.system.platform == app::Platform::Zhejiang.to_string() || setting.system.platform == app::Platform::Wenzhou.to_string() {
      // client = Client::new();
      info!(server, "uploading to zhejiang servers ......");
      client = Client::builder().connect_timeout(std::time::Duration::from_secs(600)).build().unwrap();
    } else if setting.system.platform == app::Platform::Ningbo.to_string() {
      info!(server, "uploading to ningbo servers ......");
      client = reqwest::Client::builder()
        .danger_accept_invalid_certs(true)
        .connect_timeout(std::time::Duration::from_secs(600))
        .build()
        .unwrap();
    } else {
      client = Client::builder().connect_timeout(std::time::Duration::from_secs(600)).build().unwrap();
    }

    let resp = client
      .post(server)
      .timeout(std::time::Duration::from_secs(600))
      .header(CONTENT_TYPE, "application/soap+xml; charset=utf-8")
      .body(content)
      .send()
      .await;

    // info!("{:?}", &resp);
    if resp.as_ref().is_err() {
      error!("{}", resp.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", resp.as_ref().err().unwrap().to_string()));
    }
    let ret = resp.unwrap().text().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let res_ret = ret.unwrap();
    info!("text result: {:?}", &res_ret);

    let response = ZjResponse::decode_response(res_ret.as_str(), &setting);
    if response.as_ref().is_err() {
      return Err(anyhow!("{}", response.as_ref().err().unwrap().to_string()));
    }
    Ok("上报成功".to_string())
  }

  async fn zj_query(content: String, server: &str, setting: &Settings) -> Result<Vec<Refusedcard>> {
    let client;
    if setting.system.platform == app::Platform::Zhejiang.to_string() || setting.system.platform == app::Platform::Wenzhou.to_string() {
      // client = Client::new();
      info!(server, content, "uploading to zhejiang servers ......");
      client = Client::builder().connect_timeout(std::time::Duration::from_secs(600)).build().unwrap();
    } else if setting.system.platform == app::Platform::Ningbo.to_string() {
      info!(server, "uploading to ningbo servers ......");
      client = reqwest::Client::builder()
        .danger_accept_invalid_certs(true)
        .connect_timeout(std::time::Duration::from_secs(600))
        .build()
        .unwrap();
    } else {
      client = Client::builder().connect_timeout(std::time::Duration::from_secs(600)).build().unwrap();
    }

    let resp = client
      .post(server)
      .timeout(std::time::Duration::from_secs(600))
      .header(CONTENT_TYPE, "application/soap+xml; charset=utf-8")
      .body(content)
      .send()
      .await;

    // info!("{:?}", &resp);
    if resp.as_ref().is_err() {
      error!("{}", resp.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", resp.as_ref().err().unwrap().to_string()));
    }
    let ret = resp.unwrap().text().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let res_ret = ret.unwrap();
    info!("text result: {:?}", &html_escape::decode_html_entities(&res_ret));

    // let response: Result<crate::service::zjservice::datastructs::Refusedreportcardresponse, serde_json::Error> = serde_json::from_str(&res_ret);
    let response = ZjResponse::decode_reportcard_response(res_ret.as_str(), &setting);
    if response.as_ref().is_err() {
      return Err(anyhow!("{}", response.as_ref().err().unwrap().to_string()));
    }
    let response = response.unwrap();
    if response.is_some() {
      return Ok(
        response
          .unwrap()
          .into_iter()
          .map(|card| Refusedcard {
            reportcode: card.reportcode,
            code: card.code,
            auditlevel: card.auditlevel,
            auditorname: card.auditorname,
            auditdate: card.auditdate,
            auditdesc: card.auditdesc,
          })
          .collect(),
      );
    } else {
      return Ok(vec![]);
    }
    // if response.as_ref().is_err() {
    //   return Err(anyhow!("{}", response.as_ref().err().unwrap().to_string()));
    // }
    // let response = response.unwrap();
    // if response.returncode != "0" {
    //   return Err(anyhow!("{}", response.message));
    // }
    // let cardlist = response.reportcards;

    // Ok(
    //   cardlist
    //     .into_iter()
    //     .map(|card| Refusedcard {
    //       reportcode: card.reportcode,
    //       code: card.code,
    //       auditlevel: card.auditlevel,
    //       auditorname: card.auditorname,
    //       auditdate: card.auditdate,
    //       auditdesc: card.auditdesc,
    //     })
    //     .collect(),
    // )
    // Ok(vec![])
  }
}
