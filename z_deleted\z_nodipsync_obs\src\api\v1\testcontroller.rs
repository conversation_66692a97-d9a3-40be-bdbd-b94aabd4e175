use crate::nxmiddleware::token::{self, Auth<PERSON>ody, AuthError, AuthPayload, Claims};
use axum::Json;
use datacontroller::entities::tj_staffadmin::TjStaffadmin;
use serde_json::Value;

pub async fn test_put() -> Json<Value> {
  Json(serde_json::json!("test_put"))
}

pub async fn test_post() -> Json<Value> {
  Json(serde_json::json!("test_post"))
}

pub async fn test_get(claims: Claims) -> Json<Value> {
  Json(serde_json::json!("test_get"))
}

pub async fn test_delete() -> Json<Value> {
  Json(serde_json::json!("test_delete"))
}
pub async fn protected(claims: Claims) -> Result<String, AuthError> {
  // Send the protected data to the user
  Ok(format!("Welcome to the protected area :)\nYour data:\n{:?}", claims))
}

pub async fn authorize(Json(payload): Json<AuthPayload>) -> Result<Json<AuthBody>, AuthError> {
  // Check if the user sent the credentials
  if payload.client_id.is_empty() || payload.client_secret.is_empty() {
    return Err(AuthError::MissingCredentials);
  }
  // Here you can check the user credentials from a database
  // if payload.client_id != "foo" || payload.client_secret != "bar" {
  //   return Err(AuthError::WrongCredentials);
  // }
  // Create the authorization token
  //   let token = encode(&Header::default(), &claims, &EncodingKey::from_secret(&KEY)).map_err(|_| AuthError::TokenCreation)?;
  let user = TjStaffadmin {
    id: 1,
    tj_staffno: "admin".to_string(),
    tj_staffname: "admin".to_string(),
    tj_sex: 1,
    tj_deptid: 1,
    tj_groupid: 1,
    tj_password: "".to_string(),
    tj_role: 1,
    tj_checkallflag: 1,
    tj_title: "a".to_string(),
    tj_status: 1,
    tj_operator: "1".to_string(),
    tj_moddate: 1,
    tj_memo: "1".to_string(),
    tj_isadmin: 1,
    login_session: "".to_string(),
    tj_esign: "todo!()".to_string(),
  };
  let token = token::create(user);
  if token.as_ref().is_err() {
    return Err(AuthError::TokenCreation);
  }
  // Send the authorized token
  Ok(Json(AuthBody::new(token.unwrap())))
}
