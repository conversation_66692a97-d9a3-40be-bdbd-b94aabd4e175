//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_corpoccureport_info")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub tj_report_id: i64,
    pub tj_test_id: String,
    pub tj_rptnum: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
