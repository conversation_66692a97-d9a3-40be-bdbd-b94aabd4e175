//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_medexamtasks")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_mdid: i32,
    pub tj_staffid: Option<i32>,
    pub tj_tasktype: i32,
    pub tj_deptid: Option<i32>,
    pub tj_taskdate: Option<DateTime>,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::tj_corpmedexaminfo::Entity",
        from = "Column::TjMdid",
        to = "super::tj_corpmedexaminfo::Column::Id",
        on_update = "Restrict",
        on_delete = "Restrict"
    )]
    TjCorpmedexaminfo,
    #[sea_orm(
        belongs_to = "super::tj_departinfo::Entity",
        from = "Column::TjDeptid",
        to = "super::tj_departinfo::Column::Id",
        on_update = "Restrict",
        on_delete = "Restrict"
    )]
    TjDepartinfo,
}

impl Related<super::tj_corpmedexaminfo::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjCorpmedexaminfo.def()
    }
}

impl Related<super::tj_departinfo::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjDepartinfo.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
