# coding=utf-8

import os
import sys
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from werkzeug.exceptions import BadRequest, NotFound, InternalServerError

# Add the current directory to Python path to import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from report import main as generate_report
import constant
from api_models import ReportGenerationRequest, ReportGenerationResponse, ErrorResponse
from api_handlers import ReportHandler
from config import APIConfig
from dataentities.dbconn import create_mysql_engine
from db_manager import initialize_database, get_db_session, close_database, database_health_check

def initialize_database_connection():
    """Initialize global database connection using the database manager"""
    try:
        logger.info("Initializing database connection...")

        # Get database URI from config
        from config import APIConfig
        config = APIConfig()
        database_uri = config.get_database_uri()

        if not database_uri:
            # Fallback to create_mysql_engine
            engine = create_mysql_engine()
            database_uri = str(engine.url)

        # Initialize using the database manager
        success = initialize_database(database_uri)

        if success:
            # Update the global session in dbconn module for backward compatibility
            import dataentities.dbconn as dbconn
            dbconn.session = get_db_session()

        return success

    except Exception as e:
        logger.error(f"✗ Failed to initialize database connection: {e}")
        logger.error("Please check your database configuration in config/nodipexam.toml")
        return False

# Configure logging
os.makedirs('./log', exist_ok=True)  # Ensure log directory exists
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./log/reportserver.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Load configuration
config = APIConfig()
report_handler = ReportHandler(config)

@app.errorhandler(BadRequest)
def handle_bad_request(e):
    """Handle 400 Bad Request errors"""
    logger.error(f"Bad request: {e.description}")
    return jsonify(ErrorResponse(
        error="Bad Request",
        message=str(e.description),
        status_code=400
    ).__dict__), 400

@app.errorhandler(NotFound)
def handle_not_found(e):
    """Handle 404 Not Found errors"""
    logger.error(f"Not found: {e.description}")
    return jsonify(ErrorResponse(
        error="Not Found",
        message=str(e.description),
        status_code=404
    ).__dict__), 404

@app.errorhandler(InternalServerError)
def handle_internal_error(e):
    """Handle 500 Internal Server errors"""
    logger.error(f"Internal server error: {e.description}")
    return jsonify(ErrorResponse(
        error="Internal Server Error",
        message="An unexpected error occurred",
        status_code=500
    ).__dict__), 500

@app.errorhandler(Exception)
def handle_generic_exception(e):
    """Handle all other exceptions"""
    logger.error(f"Unhandled exception: {str(e)}", exc_info=True)
    return jsonify(ErrorResponse(
        error="Internal Server Error",
        message="An unexpected error occurred",
        status_code=500
    ).__dict__), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint with database status"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

    # Get detailed database health check
    db_health = database_health_check()
    health_status["database"] = db_health

    # Determine overall status based on database health
    if db_health["status"] == "healthy":
        health_status["status"] = "healthy"
    elif db_health["status"] == "degraded":
        health_status["status"] = "degraded"
    else:
        health_status["status"] = "unhealthy"

    # Set HTTP status code based on health
    status_code = 200 if health_status["status"] == "healthy" else 503

    return jsonify(health_status), status_code

@app.route('/api/database/info', methods=['GET'])
def database_info():
    """Get database connection information"""
    try:
        from db_manager import get_database_info
        info = get_database_info()
        return jsonify(info), 200
    except Exception as e:
        logger.error(f"Error getting database info: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/reports/types', methods=['GET'])
def get_report_types():
    """Get available report types and formats"""
    return jsonify({
        "report_types": [
            {"id": 0, "name": "职业健康报告", "description": "Occupational health report"},
            {"id": 1, "name": "普通体检报告", "description": "Normal health examination report"},
            {"id": 2, "name": "放射报告", "description": "Radiation report"}
        ],
        "report_formats": [
            {"id": constant.ReportFormat.Docx.value, "name": "DOCX", "description": "Microsoft Word document"},
            {"id": constant.ReportFormat.Pdf.value, "name": "PDF", "description": "Portable Document Format"}
        ],
        "page_styles": [
            {"id": constant.PageStyle.Portrait.value, "name": "Portrait", "description": "Portrait orientation"},
            {"id": constant.PageStyle.Landscape.value, "name": "Landscape", "description": "Landscape orientation"}
        ]
    })

@app.route('/api/reports/generate', methods=['POST'])
def generate_report_api():
    """Generate a report based on the provided parameters"""
    try:
        # Validate request data
        if not request.is_json:
            raise BadRequest("Request must be JSON")

        data = request.get_json()
        if not data:
            raise BadRequest("Request body is empty")

        # Parse and validate request
        try:
            req = ReportGenerationRequest(**data)
        except TypeError as e:
            raise BadRequest(f"Invalid request format: {str(e)}")

        # Validate required parameters
        if req.report_id <= 0:
            raise BadRequest("report_id must be a positive integer")

        if req.report_format not in [constant.ReportFormat.Docx.value, constant.ReportFormat.Pdf.value]:
            raise BadRequest("Invalid report_format. Must be 1 (DOCX) or 2 (PDF)")

        if req.page_style not in [constant.PageStyle.Portrait.value, constant.PageStyle.Landscape.value]:
            raise BadRequest("Invalid page_style. Must be 0 (Portrait) or 1 (Landscape)")

        # Generate report
        result = report_handler.generate_report(req)

        return jsonify(result.__dict__), 200

    except BadRequest:
        raise
    except Exception as e:
        logger.error(f"Error generating report: {str(e)}", exc_info=True)
        raise InternalServerError(f"Failed to generate report: {str(e)}")

@app.route('/api/reports/status/<int:report_id>', methods=['GET'])
def get_report_status(report_id: int):
    """Get the status of a report generation request"""
    try:
        if report_id <= 0:
            raise BadRequest("report_id must be a positive integer")

        status = report_handler.get_report_status(report_id)
        return jsonify(status), 200

    except BadRequest:
        raise
    except Exception as e:
        logger.error(f"Error getting report status: {str(e)}", exc_info=True)
        raise InternalServerError(f"Failed to get report status: {str(e)}")

@app.route('/api/reports/download/<path:filename>', methods=['GET'])
def download_report(filename: str):
    """Download a generated report file"""
    try:
        logger.info(f"Downloading report: {filename}")
        # Validate filename to prevent directory traversal
        if '..' in filename or filename.startswith('/'):
            raise BadRequest("Invalid filename")

        file_path = report_handler.get_report_file_path(filename)

        if not file_path or not os.path.exists(file_path):
            raise NotFound(f"Report file '{filename}' not found")

        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename
        )

    except (BadRequest, NotFound):
        raise
    except Exception as e:
        logger.error(f"Error downloading report: {str(e)}", exc_info=True)
        raise InternalServerError(f"Failed to download report: {str(e)}")

@app.route('/api/reports/list', methods=['GET'])
def list_reports():
    """List available report files"""
    try:
        reports = report_handler.list_available_reports()
        return jsonify({"reports": reports}), 200

    except Exception as e:
        logger.error(f"Error listing reports: {str(e)}", exc_info=True)
        raise InternalServerError(f"Failed to list reports: {str(e)}")

if __name__ == '__main__':
    logger.info("Starting Report Server...")
    logger.info(f"Report output directory: {config.output_directory}")
    logger.info(f"Server host: {config.host}")
    logger.info(f"Server port: {config.port}")

    # Ensure output directory exists
    os.makedirs(config.output_directory, exist_ok=True)

    # Initialize database connection
    if not initialize_database_connection():
        logger.error("Failed to initialize database connection. Server will start but may not function properly.")
        logger.error("Please check your database configuration and try again.")

    try:
        app.run(
            host=config.host,
            port=config.port,
            debug=config.debug
        )
    finally:
        # Clean up database connection on shutdown
        logger.info("Shutting down server...")
        close_database()