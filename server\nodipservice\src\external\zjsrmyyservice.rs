use std::collections::HashMap;

use crate::{common::constant::YesOrNo, dto::ExternalDTO};
use anyhow::{anyhow, Result};
use calamine::{open_workbook, Data, Reader, Xlsx};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use itertools::Itertools;
use tracing::*;

use super::herenkj::*;

// use super::Herenkj;

pub struct ZjsrmyyService;

impl ZjsrmyyService {
  pub async fn do_upload(
    dto: &ExternalDTO,
    medinfo: &TjMedexaminfo,
    new_extitems: &Vec<ExtCheckiteminfo>,
    exist_items: &Vec<ExtCheckiteminfo>,
    server: &String,
    // uid: &mut UidgenService,
    force: i32,
    db: &DbConnection,
  ) -> Result<String> {
    let ret = Herenkj::upload_medexaminfo(dto, medinfo, new_extitems, exist_items, server, force, db).await;
    if ret.as_ref().is_err() {
      error!("upload_medexaminfo error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok("".to_string())
  }

  pub async fn update_charge_flag(datas: &str, db: &DbConnection) -> OutboundResponseDto<String> {
    let mut resp = OutboundResponseDto {
      result_code: CODE_OK.to_string(),
      desc_message: "".to_string(),
      ..Default::default()
    };

    let ret: Result<InboundRequestDto<ChargeStatus>, _> = serde_json::from_str(datas); //.unwrap_or_default();

    // let ret = serde_json::from_str::<InboundRequestDto<ChargeStatus>>(datas); //.unwrap_or_default();
    if ret.is_err() {
      resp.result_code = CODE_ERROR.to_string();
      resp.desc_message = format!("数据格式错误:{}", ret.unwrap_err().to_string());
      return resp;
    }
    let args = ret.unwrap().params.args;
    let ret = Herenkj::update_charge_flag(&args, db).await;
    resp.result_code = ret.result_code;
    resp.desc_message = ret.desc_message;
    resp
  }

  pub async fn receive_lis_result(datas: &str, db: &DbConnection) -> OutboundResponseDto<String> {
    let mut resp = OutboundResponseDto {
      result_code: CODE_OK.to_string(),
      desc_message: "".to_string(),
      ..Default::default()
    };
    let ret = serde_json::from_str::<OutboundRequestDto<LabResultRecord>>(datas); //.unwrap_or_default();
    if ret.is_err() {
      resp.result_code = CODE_ERROR.to_string();
      resp.desc_message = format!("数据格式错误:{}", ret.unwrap_err().to_string());
      return resp;
    }
    let args = ret.unwrap().params.args;
    let ret = Herenkj::update_lab_results(&args, db).await;
    resp.result_code = ret.result_code;
    resp.desc_message = ret.desc_message;
    resp
  }

  pub async fn update_lab_status(datas: &str, db: &DbConnection) -> OutboundResponseDto<String> {
    let mut resp = OutboundResponseDto {
      result_code: CODE_OK.to_string(),
      desc_message: "".to_string(),
      ..Default::default()
    };
    let ret = serde_json::from_str::<OutboundRequestDto<LabStatusRecord>>(datas); //.unwrap_or_default();
    if ret.is_err() {
      resp.result_code = CODE_ERROR.to_string();
      resp.desc_message = format!("数据格式错误:{}", ret.unwrap_err().to_string());
      return resp;
    }
    let args = ret.unwrap().params.args;
    let ret = Herenkj::update_lab_status(&args, db).await;
    resp.result_code = ret.result_code;
    resp.desc_message = ret.desc_message;
    resp
  }

  pub async fn receive_pacs_result(datas: &str, db: &DbConnection) -> OutboundResponseDto<String> {
    let mut resp = OutboundResponseDto {
      result_code: CODE_OK.to_string(),
      desc_message: "".to_string(),
      ..Default::default()
    };
    let ret = serde_json::from_str::<OutboundRequestDto<PacsResultRecord>>(datas); //.unwrap_or_default();
    if ret.is_err() {
      resp.result_code = CODE_ERROR.to_string();
      resp.desc_message = format!("数据格式错误:{}", ret.unwrap_err().to_string());
      return resp;
    }
    let args = ret.unwrap().params.args;
    let ret = Herenkj::update_function_exam_reports(&args, db).await;
    resp.result_code = ret.result_code;
    resp.desc_message = ret.desc_message;
    resp
  }

  pub async fn update_function_exam_status(datas: &str, db: &DbConnection) -> OutboundResponseDto<String> {
    let mut resp = OutboundResponseDto {
      result_code: CODE_OK.to_string(),
      desc_message: "".to_string(),
      ..Default::default()
    };
    let ret = serde_json::from_str::<OutboundRequestDto<ExamStatus>>(datas); //.unwrap_or_default();
    if ret.is_err() {
      resp.result_code = CODE_ERROR.to_string();
      resp.desc_message = format!("数据格式错误:{}", ret.unwrap_err().to_string());
      return resp;
    }
    let args = ret.unwrap().params.args;
    let ret = Herenkj::update_function_exam_status(&args, db).await;
    resp.result_code = ret.result_code;
    resp.desc_message = ret.desc_message;
    resp
  }

  pub async fn import_lis_results(filename: &str, db: &DbConnection) -> Result<()> {
    if filename.is_empty() {
      return Err(anyhow!("filename is empty"));
    }

    let mut workbook: Xlsx<_> = open_workbook(filename)?;
    // let range = workbook.worksheet_range("Sheet1")?;
    let range = workbook.worksheet_range_at(0).unwrap()?;

    // let mut labresults: Vec<TjLabresult> = vec![];
    let mut vtjlisresults: Vec<VTjLisresult> = vec![];
    // Iterate over rows
    let mut row_xmxh: HashMap<usize, String> = HashMap::new();
    let mut row_xmmc: HashMap<usize, String> = HashMap::new();
    let mut testids: Vec<String> = vec![];
    let mut analytes: Vec<String> = vec![];
    for (row_idx, row) in range.rows().enumerate() {
      if row_idx == 0 {
        for (col_idx, cell) in row.iter().enumerate() {
          let value = ZjsrmyyService::get_cell_value(cell);
          // println!("  Cell ({}, {}): {}", row_idx, col_idx, value);
          row_xmmc.insert(col_idx, value);
        }
      }
      if row_idx == 1 {
        //第二行
        for (col_idx, cell) in row.iter().enumerate() {
          let value = ZjsrmyyService::get_cell_value(cell);
          // println!("  Cell ({}, {}): {}", row_idx, col_idx, value);
          row_xmxh.insert(col_idx, value);
        }
      }

      //第三行开始
      // for row in range.rows() {
      let idcard = ZjsrmyyService::get_cell_value(&row[1]);
      let pname = ZjsrmyyService::get_cell_value(&row[2]);
      // let psex = ZjsrmyyService::get_cell_value(&row[3]);
      let testdate = ZjsrmyyService::get_cell_value(&row[5]);

      if idcard.is_empty() || pname.is_empty() || testdate.is_empty() {
        continue;
      }
      // info!("身份证号:{}", &idcard);
      // let mut testid = "".to_string();
      let medinfo = TjMedexaminfo::query_many_by_idcard_testtype(&idcard, -1, -1, &db.get_connection()).await;
      if medinfo.is_err() {
        info!("不能根据身份证号:{}找到体检信息，错误:{}", &idcard, medinfo.unwrap_err().to_string());
        continue;
      }
      let medinfos = medinfo.unwrap();
      if medinfos.len() <= 0 {
        info!("该身份证号:{}没有体检信息", &idcard);
        continue;
      }

      let ret = TjPatient::query_by_idcard(&idcard, &db.get_connection()).await;
      if ret.is_err() {
        info!("不能根据身份证号:{}找到体检人员信息，错误:{}", &idcard, ret.unwrap_err().to_string());
        continue;
      }
      let ptinfo = ret.unwrap();
      if ptinfo.is_none() {
        info!("不能根据身份证号:{}找到体检人员信息", &idcard);
        continue;
      }
      let ptinfo = ptinfo.unwrap();

      let medinfo = medinfos.iter().sorted_by(|a, b| b.id.cmp(&a.id)).next().unwrap();
      testids.push(medinfo.tj_testid.to_string());
      //具体项目结果
      for (col_idx, cell) in row.iter().enumerate() {
        if col_idx <= 5 {
          continue;
        }
        let value = ZjsrmyyService::get_cell_value(cell);

        let xmxh = row_xmxh.get(&col_idx).map_or("".to_string(), |s| s.to_string());
        if xmxh.is_empty() {
          continue;
        }
        analytes.push(xmxh.to_string());
        let xmmc = row_xmmc.get(&col_idx).map_or("".to_string(), |s| s.to_string());
        // info!("{} Cell ({}, {}): {}", xmmc, row_idx, col_idx, value);
        if value.is_empty() {
          continue;
        }
        let iteminfos = crate::SYSCACHE.get().unwrap().get_iteminfo_by_lisnum(&xmxh).await;
        if iteminfos.len() <= 0 {
          error!("不能根据对接编号:{}找到项目信息", xmxh);
          continue;
        }
        let iteminfo = &iteminfos[0];
        let mut ckdz = iteminfo.tj_lowvalue.to_string();
        let mut ckgz = iteminfo.tj_uppervalue.to_string();
        let mut gdbj = "".to_string();
        let mut ckfw = "".to_string();
        let itemrangeinfos = crate::SYSCACHE.get().unwrap().get_item_rangeinfo(&iteminfo.tj_itemid, medinfo.tj_age, ptinfo.tj_psex).await;
        if itemrangeinfos.len() > 0 {
          ckdz = itemrangeinfos[0].tj_lowvalue.to_owned();
          ckgz = itemrangeinfos[0].tj_uppervalue.to_owned();
        }
        // if iteminfo.tj_itemid.eq_ignore_ascii_case("120011") && medinfo.tj_testid.eq_ignore_ascii_case("10000938") {
        //   info!("120011 参考值范围信息:{:?}", &itemrangeinfos);
        //   info!("120011 参考值:{}-{}", ckdz, ckgz);
        // }
        let mut sfyc = 0;
        if iteminfo.tj_valuetype.eq_ignore_ascii_case(&crate::common::constant::ValueType::DingLiang.to_string()) {
          //判断是否异常
          let isval_ok = value.parse::<f64>();
          if isval_ok.is_err() {
            error!("定量类型，但是非数字结果......:{}", &value);
            if value.trim().eq_ignore_ascii_case("阴性(-)")
              || value.trim().eq_ignore_ascii_case("未见")
              || value.trim().eq_ignore_ascii_case("未见异常")
              || value.trim().eq_ignore_ascii_case("阴性")
            {
              sfyc = 0;
            } else {
              sfyc = 1;
            }
          } else {
            let ret_val = isval_ok.unwrap();
            match iteminfo.tj_reftype.to_uppercase().as_str() {
              "X-Y" | "X_Y" => {
                ckfw = format!("{}-{}", &ckdz, &ckgz);
                if !ckdz.is_empty() {
                  if let Ok(lowval) = ckdz.parse::<f64>() {
                    if ret_val < lowval {
                      sfyc = 1;
                      gdbj = iteminfo.tj_lowoffset.to_owned();
                    }
                  }
                }
                if !ckgz.is_empty() {
                  if let Ok(highval) = ckgz.parse::<f64>() {
                    if ret_val > highval {
                      sfyc = 1;
                      gdbj = iteminfo.tj_highoffset.to_owned();
                    }
                  }
                }
              }
              "<Y" => {
                ckfw = format!("<{}", &ckgz);
                if !ckgz.is_empty() {
                  if let Ok(highval) = ckgz.parse::<f64>() {
                    if ret_val >= highval {
                      sfyc = 1;
                      gdbj = iteminfo.tj_highoffset.to_owned();
                    }
                  }
                }
              }
              "<=Y" => {
                ckfw = format!("≤{}", &ckgz);
                if !ckgz.is_empty() {
                  if let Ok(highval) = ckgz.parse::<f64>() {
                    if ret_val > highval {
                      sfyc = 1;
                      gdbj = iteminfo.tj_highoffset.to_owned();
                    }
                  }
                }
              }
              ">X" => {
                ckfw = format!(">{}", &ckdz);
                if !ckdz.is_empty() {
                  if let Ok(lowval) = ckdz.parse::<f64>() {
                    if ret_val <= lowval {
                      sfyc = 1;
                      gdbj = iteminfo.tj_lowoffset.to_owned();
                    }
                  }
                }
              }
              ">=X" => {
                ckfw = format!("≥{}", &ckdz);
                if !ckdz.is_empty() {
                  if let Ok(lowval) = ckdz.parse::<f64>() {
                    if ret_val < lowval {
                      sfyc = 1;
                      gdbj = iteminfo.tj_lowoffset.to_owned();
                    }
                  }
                }
              }
              _ => {
                let itemresults = crate::SYSCACHE.get().unwrap().get_itemresultinfos(&iteminfo.tj_itemid, db).await;
                for pv in itemresults.into_iter() {
                  if pv.tj_itemresult.is_empty() {
                    continue;
                  }
                  if value.contains(&pv.tj_itemresult) && pv.tj_sumflag == YesOrNo::Yes as i32 {
                    // tr.tj_abnormalflag = 1;
                    sfyc = 1;
                    break;
                  }
                }
              }
            }
          }
        } else {
          if value.trim().is_empty()
            || value.trim().eq_ignore_ascii_case("正常")
            || value.trim().eq_ignore_ascii_case("阴性(-)")
            || value.trim().eq_ignore_ascii_case("阴性")
            || value.trim().eq_ignore_ascii_case("未见")
          {
            sfyc = 0;
          } else {
            let itemresults = crate::SYSCACHE.get().unwrap().get_itemresultinfos(&iteminfo.tj_itemid, db).await;
            for pv in itemresults.into_iter() {
              if pv.tj_itemresult.is_empty() {
                continue;
              }
              if value.contains(&pv.tj_itemresult) && pv.tj_sumflag == YesOrNo::Yes as i32 {
                // tr.tj_abnormalflag = 1;
                sfyc = 1;
                break;
              }
            }
          }
          gdbj = "".to_string();
        }

        let vtjlisresult = VTjLisresult {
          id: 0,
          tjbh: medinfo.tj_testid.to_string(),
          brxm: pname.to_string(),
          xmxh,
          xmmc,
          xmdw: iteminfo.tj_itemunit.to_string(),
          xmjg: value.to_string(),
          sfyc,
          gdbj,
          ckdz,
          ckgz,
          ckfw,
          jyys: "".to_string(),
          bgrq: testdate.to_string(),
          bgys: testdate.to_string(),
        };
        vtjlisresults.push(vtjlisresult);
        // };
        // let labresult = TjLabresult {
        //   id: 0,
        //   tj_clinicid: medinfo.tj_testid.to_string(),
        //   tj_testid: medinfo.tj_testid.to_string(),
        //   tj_patientname: pname.to_string(),
        //   tj_sex: psex.to_string(),
        //   tj_origrec: xmxh.to_string(),
        //   tj_itemid: iteminfo.tj_itemid.to_string(),
        //   tj_analyte: xmmc.to_string(),
        //   tj_shortname: xmmc.to_string(),
        //   tj_units: "".to_string(),
        //   tj_final: value,
        //   tj_rn10: "".to_string(),
        //   tj_ckfw_l: "".to_string(),
        //   tj_ckfw_h: "".to_string(),
        //   tj_ckfw: "".to_string(),
        //   tj_abnormalflag: 0,
        //   tj_displowhigh: "".to_string(),
        //   tj_senddate: 0,
        //   tj_ordno: "".to_string(),
        //   // tj_testgroup: todo!(),
        //   // tj_checkdoctor: todo!(),
        //   // tj_recheckdoctor: todo!(),
        //   // tj_importer: todo!(),
        //   // tj_importdate: todo!(),
        //   // tj_isreceived: todo!(),
        //   // tj_receivdate: todo!(),
        //   tj_checkdate: testdate.to_string(),
        //   tj_recheckdate: testdate.to_string(),
        //   ..Default::default()
        // };
        // labresults.push(labresult);
      }
      // }
    }
    // info!("labresults are:{:?}", &labresults);
    //read from excel file;
    if vtjlisresults.len() <= 0 {
      return Ok(());
    }

    let ret = VTjLisresult::delete_many_by_xmxh(&testids, &analytes, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }

    let labrets: Vec<Vec<VTjLisresult>> = vtjlisresults.into_iter().chunks(500).into_iter().map(|c| c.collect()).collect();
    for val in labrets.into_iter() {
      let ret = VTjLisresult::save_many(&val, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
    }

    // let ret = TjLabresult::delete_by_analytes(&testids, &analytes, &db.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    // }

    // let labrets: Vec<Vec<TjLabresult>> = labresults.into_iter().chunks(500).into_iter().map(|c| c.collect()).collect();
    // for val in labrets.into_iter() {
    //   let ret = TjLabresult::save_many(&val, &db.get_connection()).await;
    //   if ret.as_ref().is_err() {
    //     error!("{}", ret.as_ref().unwrap_err().to_string());
    //     return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    //   }
    // }

    Ok(())
  }

  fn get_cell_value(cell: &Data) -> String {
    match cell {
      Data::String(s) => s.to_string(),
      Data::Float(f) => f.to_string(),
      Data::Int(i) => i.to_string(),
      Data::Bool(b) => b.to_string(),
      Data::DateTime(dt) => dt.to_string(),
      Data::DateTimeIso(dt) => dt.to_string(),
      Data::DurationIso(d) => d.to_string(),
      Data::Empty => "".to_string(),
      Data::Error(e) => format!("Error: {:?}", e),
    }
  }
}
