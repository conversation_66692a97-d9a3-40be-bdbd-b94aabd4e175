use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"ext_checkiteminfo")]
#[derive(C<PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct ExtCheckiteminfo {
  pub id: i64,
  pub testid: String,
  pub tid: String,
  pub testername: String,
  pub idcard: String,
  pub psex: i32,
  pub csex: String,
  pub birthdate: String,
  pub phone: String,
  pub age: i32,
  pub itemname: String,
  pub itemid: String,
  pub itemid2: String,
  pub deptid: String,
  pub deptname: String,
  pub depttype: i32,
  pub requesterid: String,
  pub requestername: String,
  pub requestdate: i64,
  pub requestdate2: String,
  pub paytype: i32,
  pub packagename: String,
  pub zdym: String,
  pub sampletype: String,
  pub corpnum: i64,
  pub syncstatus: i32,
  pub uid: i64,
  pub lis: i32,
  pub pacs: i32,
}
