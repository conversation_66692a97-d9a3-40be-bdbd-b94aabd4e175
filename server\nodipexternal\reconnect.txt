 if !external_server.is_empty() {
    tokio::spawn(async move {
      loop {
        match TcpStream::connect(&external_server).await {
          Ok(stream) => {
            info!("connected to server......");
            let transport = Framed::new(stream, MllpCodec::new());
            let (mut sink, mut stream) = transport.split();
            tokio::select! {
              msg = rx.next()=>{
                info!("received message:{:?}",&msg);
                if let Some(msginfo) = msg{
                  let ret = sink.send(BytesMut::from(msginfo.as_str())).await;
                  if ret.as_ref().is_err() {
                    error!("send error：{}", ret.as_ref().unwrap_err().to_string());
                  }
                }
              }
              recvdata = stream.next()=>{
                info!("stream next received:{:?}",&recvdata);
                if let Some(result) = recvdata {
                  match result{
                    Ok(msg)=>{
                      let str_msg = String::from_utf8_lossy(&msg);
                      info!("Got message: {}", str_msg);
                      if let Ok(message) = Message::try_from(str_msg.as_ref()) {
                        let msg_type = message.query("MSH.F8");
                        info!("message type from msh.f8 is:{}", &msg_type);
                        match msg_type {
                          "ORU^R01" => {
                            //报告发布
                            if let Err(e) = InternalService::handl_lab_result(&message, &dbconn_clone).await {
                              error!("handle result error:{}", e.to_string());
                            }
                          }
                          _ => {
                            info!("message do not need to be handled......");
                          }
                        }
                      }
                      let ack_msg = MessageHelper::generate_ack_message();
                      let _ = tx_clone.unbounded_send(ack_msg);
                    }
                    Err(e)=>{
                      error!("receive error:{:?}",&e);
                    }
                  }
                }
              }
            }
          }
          Err(e) => {
            error!("connect to server error:{:?}", &e)
          }
        }
        info!("disconnected.... will try to reconnect in 10 seconds");
        tokio::time::sleep(core::time::Duration::from_secs(10)).await;
      }
    });
  }