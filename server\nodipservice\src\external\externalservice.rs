use crate::external::{dianservice::DianService, jhtsservice::JhtsService, layyservice::LayyService, localservice::LocalExternal, wyhsservice::WyhsService, NbHsxxService, ZjsrmyyService};
use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::tj_medexaminfo::TjMedexaminfo, dbinit::DbConnection};
use crate::{dto::ExternalDTO, medexam::checkitemsvc::CheckitemSvc};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;
use utility::uidservice::UidgenService;
pub struct ExtService;

impl ExtService {
  pub async fn do_external_upload(
    db: &DbConnection,
    exttype: &String,
    /* server: &String,  */
    extserver: &String,
    dburi: &String,
    uid: &mut UidgenService,
    force: i32,
    dto: &ExternalDTO,
  ) -> Result<String> {
    info!("开始外部对接，参数:{:?}", &dto);

    if dto.testid.is_empty() {
      return Ok("".to_string());
    }

    let ret = TjMedexaminfo::query(&dto.testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      info!("query medinfo error:{:?}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let meddet = ret.unwrap();
    if meddet.is_none() {
      info!("Can't find medinfo by testid:{}", &dto.testid);
      return Err(anyhow!("Can't find medinfo by testid:{}", &dto.testid));
    }
    let medinfo = meddet.unwrap();
    if medinfo.tj_checkstatus == crate::common::constant::ExamStatus::Appoint as i32 {
      info!("{},该体检号处于预约状态，不需要外部对接......", &dto.testid);
      return Ok("".to_string());
    }
    //query from ext checkiteminfos
    let ret = CheckitemSvc::query_ext_checktitems(&dto.testid, &db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut exist_infos = ret.unwrap();
    info!("当前在ext_checkiteminfo表中的数据:{}", &exist_infos.len());

    let ret = LocalExternal::do_upload(&dto, &medinfo, uid, &mut exist_infos, &db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    // let new_infos = ret.unwrap();
    // info!("新的项目信息:{:#?}", &new_infos);

    //本地对接完成，数据库里的应该是全部项目了。
    let ret = CheckitemSvc::query_ext_checktitems(&dto.testid, &db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let new_extitems = ret.unwrap();
    // info!("new extist items:{:#?}", &new_extitems);
    // if new_extitems.len() <= 0 && exist_infos.len() <= 0 && dto.exttype != crate::common::constant::ExtOpType::DEL as i32 {
    //   error!("体检号：{}没有需要新增也没有需要删除的项目，不需要外部对接", &medinfo.tj_testid);
    //   return Ok("".to_string());
    // }
    // let mut uids = uid.write().await;
    // info!("external type is:{}", &config.external.exttype);

    // match config.external.exttype.to_lowercase().as_str() {
    match exttype.to_lowercase().as_str() {
      "dian" => {
        info!("start to do dian upload");
        let ret = DianService::do_upload(&dto, extserver, dburi, /*&config.application.proxyserver, &config.audiogram.dburi,*/ &db).await;
        // info!("result is:{:?}", &ret);
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
      "jhts" => {
        info!("start to do jhts upload");
        let ret = JhtsService::do_upload(&dto, extserver).await;
        // info!("result is:{:?}", &ret);
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
      "layy" => {
        info!("start to do layy upload");
        let ret = LayyService::do_upload(&dto, &new_extitems, &exist_infos, extserver, &db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
      "wyhs" => {
        info!("开始金华武义壶山医院对接......");
        // if dto.exttype == crate::common::constant::ExtOpType::DEL as i32 {
        //   //删除
        // } else {
        let ret = WyhsService::do_upload(&dto, &medinfo, &new_extitems, &exist_infos, extserver, force, &db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
        // }
      }
      "hsxx" => {
        info!("开始宁波海曙新欣医院对接......");
        let ret = NbHsxxService::do_upload(&dto, extserver, dburi, &db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
      "zjsrmyy" => {
        info!("开始浙江省人民医院的外部对接......");
        let ret = ZjsrmyyService::do_upload(&dto, &medinfo, &new_extitems, &exist_infos, extserver, force, &db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
      _ => {
        info!("do NOT need do external upload");
      }
    }
    Ok("".to_string())
  }
}
