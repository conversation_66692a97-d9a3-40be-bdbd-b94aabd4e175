//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_corpoccureport_file_med")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub report_file_id: i64,
  pub med_id: i64,
  pub create_date: i64,
  pub is_saas: i8,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
