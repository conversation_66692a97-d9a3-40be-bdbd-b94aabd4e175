use crate::{
  api::httpresponse::{response_json_value_ok, response_value_error, response_value_ok},
  auth::auth::Claims,
  // common::constant::{self},
  // config::settings::Settings,
};
// use anyhow::{anyhow, Result};
use axum::{Extension, Json};
// use dbopservice::{
//   dataaccess::{tj_departinfo::TjDepartinfo, tj_staffadmin::TjStaffadmin, tj_staffdept::TjStaffdept, tj_staffright::TjStaffright},
//   dbinit::DbConnection,
// };
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{dto::*, medexam::accountsvc::AccountSvc};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

#[derive(Serialize, Default, Deserialize, Debug)]
pub struct LoginResponse {
  pub irt: i32,
  pub token_type: String,
  pub token: String,
  pub user: TjStaffadmin,
  pub depts: Vec<TjDepartinfo>,
  pub rights: Vec<TjStaffright>,
}

pub async fn user_login(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<LoginDto>) -> Json<Value> {
  info!("user login:{:?}", &dto);
  let mut res = LoginResponse {
    irt: 0,
    token: "".to_string(),
    token_type: "bearer".to_string(),
    user: TjStaffadmin { ..Default::default() },
    ..Default::default()
  };

  let ret = AccountSvc::user_login(&dto.userid, &dto.password, &db).await;
  if ret.as_ref().is_err() {
    error!("login error:{}", &ret.as_ref().unwrap_err().to_string());
    return response_value_error(&ret.as_ref().unwrap_err().to_string(), res);
  }
  let staff = ret.unwrap();
  //generate token
  let token = Claims::generate_token(&staff);
  if token.as_ref().is_err() {
    error!("login error:{}", &token.as_ref().unwrap_err().to_string());
    return response_value_error(token.as_ref().unwrap_err().to_string().as_str(), res);
  }
  let token = token.unwrap();
  res.token = token;
  res.irt = 1;
  if staff.tj_isadmin == nodipservice::common::constant::YesOrNo::Yes as i32 {
    //管理员
    // let ret = TjDepartinfo::query_many( &vec![], &vec![], -1).await;
    // if ret.as_ref().is_err() {
    //   error!("login error:{}", &ret.as_ref().unwrap_err().to_string());
    //   return response_value_error(&ret.as_ref().unwrap_err().to_string(), res);
    // }
    // res.depts = ret.unwrap();
    res.depts = vec![];
  } else {
    let ret = TjStaffdept::query_many(&vec![staff.id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("login error:{}", &ret.as_ref().unwrap_err().to_string());
      return response_value_error(&ret.as_ref().unwrap_err().to_string(), res);
    }
    let deptids: Vec<String> = ret.unwrap().into_iter().map(|v| v.tj_deptid).collect();
    let ret = TjDepartinfo::query_many(&vec![], &deptids, -1, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("login error:{}", &ret.as_ref().unwrap_err().to_string());
      return response_value_error(&ret.as_ref().unwrap_err().to_string(), res);
    }
    res.depts = ret.unwrap();
  }
  if staff.tj_isadmin == nodipservice::common::constant::YesOrNo::Yes as i32 {
    res.rights = vec![];
  } else {
    let ret = TjStaffright::query_many(&vec![staff.id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("login error:{}", &ret.as_ref().unwrap_err().to_string());
      return response_value_error(&ret.as_ref().unwrap_err().to_string(), res);
    }
    res.rights = ret.unwrap();
  }
  res.user = staff;
  // info!("login result is:{:?}", &res);
  response_value_ok("登录成功！", res)
}

pub async fn change_password(Extension(_db): Extension<Arc<DbConnection>>, Json(_dto): Json<LoginDto>) -> Json<Value> {
  response_json_value_ok(1, 1)
}
