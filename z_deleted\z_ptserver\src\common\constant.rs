pub const EMPTY_STR: &str = "";
pub const MESSAGE_OK: &str = "";

// pub const IGNORE_ROUTES: [&str; 3] = ["/api/ping", "/api/v1/login", "/api/pinyin"];
// pub(crate) const TOKEN: &str = "icryrainix";

#[derive(PartialEq, Clone, Debug)]
pub enum HttpCode {
  OK = 200,
  Error = 201,
}

//1:oracle 2:mssql 3:mysql 4:postgresql
#[derive(PartialEq)]
pub enum DatabaseType {
  _Oracle = 1,
  MsSql = 2,
  MySql = 3,
  _Postgress = 4,
}
