import os
from datetime import datetime
from PIL import Image
from reportlab.platypus import (
    BaseDocTemplate,
    Spacer,
    PageBreak,
    NextPageTemplate,
    PageTemplate,
    Frame,
)
from reportlab.platypus import Paragraph, Table, HRFlowable
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape, portrait
from reportlab.lib.units import cm, inch
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.lib.styles import getSampleStyleSheet
from corpreport.numberedcanvas import NumberedCanvas
from functools import partial
import constant

from dataentities.dbconn import session
from dataentities.tj_staffadmin import TjStaffadmin


def generate_pdf_corp_occureport(
    rptinfo,
    checkallinfos,
    medinfos,
    patients,
    corpinfo,
    dicts,
    customer,
    pagestyle,
    splitinrow,
    outdir,
    areaname,
):
    # 生成pdf文件 # 职业健康报告
    c_dict = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictTesttype.value
        and dict.ss_pid == rptinfo.tj_testtype
    )
    output = "{}-{}-{}-{}.pdf".format(
        rptinfo.tj_corpname, "职业", c_dict.ss_name, rptinfo.tj_reportnumint
    )
    rpt = corpoccu_report(
        pagestyle,
        splitinrow,
        rptinfo,
        outdir,
        output,
    )

    oculike_data = []
    forbidden_data = []
    recheck_data = []
    other_data = []
    add_data = []
    for ca in checkallinfos:
        # print("ca typeid:",)
        if ca.tj_typeid == constant.CheckResultType.Oculike.value:  # 疑似
            oculike_data.append(ca)
        elif ca.tj_typeid == constant.CheckResultType.Forbidden.value:  # 禁忌
            forbidden_data.append(ca)
        elif ca.tj_typeid == constant.CheckResultType.Recheck.value:  # 需复查
            recheck_data.append(ca)
        elif ca.tj_typeid == constant.CheckResultType.Addional.value:  # 需补检
            add_data.append(ca)
        else:  # biao 3
            other_data.append(ca)
        continue
    # for ca in checkallinfos:
    #     # print("ca typeid:",)
    #     some_oculike = next((x for x in pthazards if x.tj_typeid == constant.CheckResultType.Oculike.value and x.tj_testid == ca.tj_testid), None)
    #     some_forbidden = next((x for x in pthazards if x.tj_typeid == constant.CheckResultType.Forbidden.value and x.tj_testid == ca.tj_testid), None)
    #     some_recheck = next((x for x in pthazards if x.tj_typeid == constant.CheckResultType.Recheck.value and x.tj_testid == ca.tj_testid), None)
    #     some_addition = next((x for x in pthazards if x.tj_typeid == constant.CheckResultType.Addional.value and x.tj_testid == ca.tj_testid), None)
    #     some_others = next((x for x in pthazards if x.tj_typeid == constant.CheckResultType.Other.value and x.tj_testid == ca.tj_testid), None)
    #     # print("Oculine:",some_oculike)
    #     if ca.tj_typeid == constant.CheckResultType.Oculike.value or some_oculike:  # 疑似
    #         ca_clone = copy.deepcopy(ca)
    #         ca_clone.tj_typeid = constant.CheckResultType.Oculike.value
    #         oculike_data.append(ca_clone)
    #     if ca.tj_typeid == constant.CheckResultType.Forbidden.value or some_forbidden:  # 禁忌
    #         ca_clone = copy.deepcopy(ca)
    #         ca_clone.tj_typeid = constant.CheckResultType.Forbidden.value
    #         forbidden_data.append(ca_clone)
    #     if ca.tj_typeid == constant.CheckResultType.Recheck.value or some_recheck:  # 需复查
    #         ca_clone = copy.deepcopy(ca)
    #         ca_clone.tj_typeid = constant.CheckResultType.Recheck.value
    #         recheck_data.append(ca_clone)
    #     if ca.tj_typeid == constant.CheckResultType.Addional.value or some_addition:  # 需补检
    #         ca_clone = copy.deepcopy(ca)
    #         ca_clone.tj_typeid = constant.CheckResultType.Addional.value
    #         add_data.append(ca_clone)
    #     # else:  # biao 3
    #     if ca.tj_typeid == constant.CheckResultType.Other.value or some_others:  # 其他
    #         ca_clone = copy.deepcopy(ca)
    #         ca_clone.tj_typeid = constant.CheckResultType.Other.value
    #         other_data.append(ca_clone)
    # continue

    # print("疑似的人数:", len(oculike_data))
    # print("禁忌的人数:", len(forbidden_data))
    # print("复查的人数:", len(recheck_data))
    # print("其他的人数:", len(other_data))
    # sort by tj_testid
    oculike_data.sort(key=lambda x: x.tj_testid)
    forbidden_data.sort(key=lambda x: x.tj_testid)
    recheck_data.sort(key=lambda x: x.tj_testid)
    other_data.sort(key=lambda x: x.tj_testid)
    add_data.sort(key=lambda x: x.tj_testid)

    rpt.add_front_page(corpinfo, dicts, customer, areaname)

    if customer == constant.Customer.WZDAMS.value:
        summary = "本次职业健康检查发现：疑似职业病{}人，职业禁忌证{}人，需要复查人员{}人。详见附表：".format(
            len(oculike_data), len(forbidden_data), len(recheck_data)
        )
        rpt.set_page_portrait()
        rpt.add_back_page(dicts)
        rpt.add_page_break()
        rpt.add_sumamry_page(
            corpinfo=corpinfo, dicts=dicts, summary=summary, areaname=areaname
        )
        if pagestyle == 1:
            rpt.set_page_landscape()
        else:
            rpt.set_page_portrait()
        rpt.add_page_break()
        idx = 0
        # 疑似
        if len(oculike_data) > 0:
            idx += 1
            title = "<b>表{}、疑似职业病人员名单</b>".format(idx)
            if idx != 1:
                rpt.add_spacer(0.8)
            rpt.add_table_t1t2(title, oculike_data, medinfos, patients, dicts)
        if len(forbidden_data) > 0:
            idx += 1
            title = "<b>表{}、职业禁忌证人员名单</b>".format(idx)
            if idx != 1:
                rpt.add_spacer(0.8)
            rpt.add_table_t1t2(title, forbidden_data, medinfos, patients, dicts)
        if len(recheck_data) > 0:
            idx += 1
            title = "<b>表{}、 需复查人员名单</b>".format(idx)
            if idx != 1:
                rpt.add_spacer(0.8)
            rpt.add_table_t1t2(title, recheck_data, medinfos, patients, dicts)
        if len(other_data) > 0:
            idx += 1
            if (
                len(oculike_data) <= 0
                and len(forbidden_data) <= 0
                and len(recheck_data) <= 0
            ):
                title = "<b>表{}、受检人员名单</b>".format(idx)
            else:
                title = "<b>表{}、其他人员名单</b>".format(idx)
            if idx != 1:
                rpt.add_spacer(0.8)
            rpt.add_table_t3(title, other_data, medinfos, patients, dicts)
        if len(add_data) > 0:
            idx += 1
            title = "<b>表{}、需补检人员名单</b>".format(idx)
            if idx != 1:
                rpt.add_spacer(0.8)
            rpt.add_table_t4(title, add_data, medinfos, patients, dicts)
        rpt.add_spacer(0.8)
        rpt.add_sign_page(dicts, customer)
    else:
        rpt.set_page_portrait()
        rpt.add_page_break()
        rpt.add_back_page(dicts)
        rpt.add_page_break()
        rpt.add_sumamry_page(
            corpinfo=corpinfo, dicts=dicts, summary="", areaname=areaname
        )
        if pagestyle == 1:
            rpt.set_page_landscape()
        else:
            rpt.set_page_portrait()
        rpt.add_page_break()
        t1_data = oculike_data + forbidden_data
        title = "<b>表 1、疑似职业病和职业禁忌证人员名单</b>"
        rpt.add_table_t1t2(title, t1_data, medinfos, patients, dicts)
        rpt.add_spacer(0.8)
        title = "<b>表 2、需复查人员名单</b>"
        rpt.add_table_t1t2(title, recheck_data, medinfos, patients, dicts)
        rpt.add_spacer(0.8)
        title = "<b>表 3、其他人员名单（表 1-2 所列人员以外的受检人员）</b>"
        rpt.add_table_t3(title, other_data, medinfos, patients, dicts)
        if len(add_data) > 0:
            title = "<b>表 4、需补检人员名单</b>"
            rpt.add_spacer(0.8)
            rpt.add_table_t4(title, add_data, medinfos, patients, dicts)
        rpt.add_spacer(0.8)
        rpt.add_sign_page(dicts, customer)
        rpt.set_page_portrait()
    rpt.build()
    return os.path.join(outdir, output)


# 	a4:21.0 x 29.7 cm
class corpoccu_report:
    def __init__(self, pagestyle, splitinrow, rptinfo, rptdir, output):
        self.rptinfo = rptinfo
        self.font_name = "SimSun"
        self.splitinrow = splitinrow
        self.contents = []
        stylesheet = getSampleStyleSheet()
        self.title_style = stylesheet["Title"]
        self.normal_style = stylesheet["Normal"]
        self.body_style = stylesheet["BodyText"]
        self.doc = BaseDocTemplate(
            os.path.join(rptdir, output),
            pagesize=A4,
            leftMargin=1.5 * cm,
            rightMargin=1.2 * cm,
            topMargin=2.3 * cm,
            bottomMargin=1.2 * cm,
        )
        self.line_height = 0.8
        self.result_row_height = 0.9 * cm
        self.portrait_frame = Frame(
            1.5 * cm,  # self.doc.leftMargin,
            1.2 * cm,  # self.doc.bottomMargin,
            self.doc.width,
            self.doc.height,
            id="portrait_frame",
        )
        self.landscape_frame = Frame(
            1.6 * cm,  # self.doc.leftMargin,
            1.2 * cm,  # self.doc.bottomMargin,
            self.doc.height,
            self.doc.width,
            topPadding=1.1 * cm,
            id="landscape_frame",
        )
        self.doc.addPageTemplates(
            [
                PageTemplate(
                    id="portrait",
                    frames=self.portrait_frame,
                    onPage=partial(
                        header_portrait, rptnumber=self.rptinfo.tj_reportnum
                    ),
                    pagesize=portrait(A4),
                ),
                PageTemplate(
                    id="landscape",
                    onPage=partial(
                        header_landscape, rptnumber=self.rptinfo.tj_reportnum
                    ),
                    frames=self.landscape_frame,
                    pagesize=landscape(A4),
                ),
                # PageTemplate(id="header", onPage=header, frames=self.portrait_frame),
            ]
        )
        self.pagestyle = pagestyle

    def on_first_page(self, canvas, doc):
        canvas.saveState()
        # style = getSampleStyleSheet()["Heading3"]
        # style.alignment = TA_RIGHT
        # style.fontName =  self.font_name
        # style.fontSize = 15
        # style.leading = 70
        # style.wordWrap = "CJK"  # 设置自动换行
        # style.spaceBefore = 20
        # canvas.drawRightString(10 * cm, 20 * cm, "(杭发医院) 职检字第 (2023-0215) 号")

        # style2 = getSampleStyleSheet()["Heading2"]
        # style2.leading = 80
        # style2.fontName =  self.font_name
        # style2.fontSize = 32
        # style2.alignment = TA_CENTER
        # canvas.drawRightString(10 * cm, 20 * cm, "职业健康检查报告书")
        # # self.contents.append(Paragraph("职业健康检查报告书", style2))

        textobject = canvas.beginText()
        textobject.setTextOrigin(inch, 2.5 * inch)
        textobject.setFont("Helvetica-Oblique", 14)
        # for line in lyrics:
        # textobject.textLine("caseSensitive")
        textobject.setFillGray(0.4)
        textobject.textLines("")
        canvas.drawText(textobject)

        canvas.restoreState()

    def on_later_pages(self, canvas, doc):
        canvas.saveState()
        canvas.setFont(self.font_name, 13)
        canvas.drawCentredString(A4[0] // 2, 29 * cm, "职 业 健 康 检 查 报 告 书")
        canvas.setFont(self.font_name, 12)
        canvas.drawString(doc.leftMargin, 28.3 * cm, self.rptinfo.tj_reportnum)
        # canvas.drawRightString(
        #     A4[0] - doc.rightMargin,
        #     28.3 * cm,
        #     "第{:d}页 ".format(doc.page),
        # )
        canvas.setLineWidth(0.5)
        canvas.line(doc.leftMargin, 28 * cm, A4[0] - doc.rightMargin, 28 * cm)

        # canvas.setLineWidth(0.5)
        # canvas.line(doc.leftMargin, 1.5 * cm, A4[0] - doc.rightMargin, 1.5 * cm)
        # canvas.drawString(doc.leftMargin, 0.8 * cm, "(杭发医院) 职检字第 (2023-0215) 号")
        # canvas.drawCentredString(A4[0] // 2, 0.8 * cm, "{:d}".format(doc.page))
        # canvas.drawRightString(A4[0] - doc.rightMargin, 0.8 * cm, "{}".format(self.tag))
        canvas.restoreState()
        # self.contents.append(Spacer(1, 0.5 * cm))

    # def get_section_number(self):
    #     self.current_section += 1
    #     return self.current_section

    def set_page_header(self):
        self.contents.append(NextPageTemplate("header"))

    def set_page_portrait(self):
        self.contents.append(NextPageTemplate("portrait"))

    def set_page_landscape(self):
        self.contents.append(NextPageTemplate("landscape"))

    def add_front_page(self, corpinfo, dicts, customer, areaname):
        # self.contents.append()
        # self.story.append(Paragraph("", self.heading3_style))
        # self.contents.append(Spacer(1, 1 * cm))
        style = getSampleStyleSheet()["Heading3"]
        style.alignment = TA_RIGHT
        style.fontName = self.font_name
        style.fontSize = 15
        style.leading = 70
        style.wordWrap = "CJK"  # 设置自动换行
        style.spaceBefore = 20
        self.contents.append(Paragraph(self.rptinfo.tj_reportnum, style))

        style2 = getSampleStyleSheet()["Heading2"]
        style2.leading = 80
        style2.fontName = self.font_name
        style2.fontSize = 32
        style2.alignment = TA_CENTER
        self.contents.append(Paragraph("职业健康检查报告书", style2))

        self.contents.append(Spacer(1, 1 * cm))

        style3 = getSampleStyleSheet()["Heading3"]
        style3.alignment = TA_LEFT
        style3.leading = 17
        style3.fontName = self.font_name
        style3.fontSize = 15
        style3.wordWrap = "CJK"  # 设置自动换行
        # style3.underlineOffset = 30
        # col_width = 200
        data = [
            ("用人单位:", Paragraph(self.rptinfo.tj_corpname, style3)),
            ("地    址:", Paragraph(areaname + corpinfo.tj_address, style3)),
            ("联系电话:", Paragraph(corpinfo.tj_phone, style3)),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            # ("FONTSIZE", (0, 0), (-1, 0), 12),  # 第一行的字体大小
            ("FONTSIZE", (0, 0), (-1, -1), 15),  # 第二行到最后一行的字体大小
            # ("BACKGROUND", (0, 0), (-1, 0), "#d5dae6"),  # 设置第一行背景颜色
            # ("ALIGN", (0, 0), (-1, -1), "CENTER"),  # 第一行水平居中
            ("ALIGN", (0, 1), (-1, -1), "LEFT"),  # 第二行到最后一行左右左对齐
            ("VALIGN", (0, 0), (-1, -1), "BOTTOM"),  # 所有表格上下居中对齐
            ("LINEBELOW", (1, 0), (-1, -1), 1, colors.black),
            # ("BOTTOMPADDING", (1, 0), (-1, -1), 5),
            # ("TEXTCOLOR", (0, 0), (-1, -1), colors.darkslategray),  # 设置表格内文字颜色
            # ("GRID", (0, 0), (-1, -1), 0.5, colors.grey),  # 设置表格框线为grey色，线宽为0.5
            # ('SPAN', (0, 1), (0, 2)),  # 合并第一列二三行
            # ('SPAN', (0, 3), (0, 4)),  # 合并第一列三四行
            # ('SPAN', (0, 5), (0, 6)),  # 合并第一列五六行
            # ('SPAN', (0, 7), (0, 8)),  # 合并第一列五六行
        ]
        table = Table(
            data,
            colWidths=[3 * cm, 10 * cm],
            style=style,
            minRowHeights=[1.0 * cm, 1.0 * cm, 1.0 * cm],
        )

        self.contents.append(table)
        self.contents.append(Spacer(1, 2 * cm))

        style3 = getSampleStyleSheet()["BodyText"]
        style3.alignment = TA_LEFT
        style3.leading = 15
        style3.fontName = self.font_name
        style3.fontSize = 15
        data_testtypes = []

        c_dict = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictTesttype.value
            and dict.ss_pid == constant.TestType.SG.value
        )
        if c_dict is None:
            dcitname = "上岗前"
        else:
            dcitname = c_dict.ss_name

        shanggang = dcitname
        if self.rptinfo.tj_testtype == constant.TestType.SG.value:
            shanggang = (
                '<img src="./images/yes.png" width="15" height="15" valign="middle"/> '
            ) + shanggang
        else:
            shanggang = (
                '<img src="./images/no.png" width="15" height="15" valign="middle"/> '
            ) + shanggang
        data_testtypes.append(
            Paragraph(
                shanggang,
                style3,
            )
        )
        zaigang = "在岗期间"
        c_dict = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictTesttype.value
            and dict.ss_pid == constant.TestType.ZG.value
        )
        if c_dict is not None:
            zaigang = c_dict.ss_name
        if self.rptinfo.tj_testtype == constant.TestType.ZG.value:
            zaigang = (
                '<img src="./images/yes.png" width="15" height="15" valign="middle"/> '
            ) + zaigang
        else:
            zaigang = (
                '<img src="./images/no.png" width="15" height="15" valign="middle"/> '
            ) + zaigang
        data_testtypes.append(
            Paragraph(
                zaigang,
                style3,
            )
        )
        ligang = "离岗时"
        c_dict = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictTesttype.value
            and dict.ss_pid == constant.TestType.LG.value
        )
        if c_dict is not None:
            ligang = c_dict.ss_name
        if self.rptinfo.tj_testtype == constant.TestType.LG.value:
            ligang = (
                '<img src="./images/yes.png" width="15" height="15" valign="middle"/> '
            ) + ligang
        else:
            ligang = (
                '<img src="./images/no.png" width="15" height="15" valign="middle"/> '
            ) + ligang
        data_testtypes.append(
            Paragraph(
                ligang,
                style3,
            )
        )
        # yingji = "应急"
        # c_dict = next(
        #     dict
        #     for dict in dicts
        #     if dict.ss_typeid == constant.DictType.DictTesttype.value
        #     and dict.ss_pid == constant.TestType.YJ.value
        # )
        # if c_dict is not None:
        #     yingji = c_dict.ss_name
        # if self.rptinfo.tj_testtype == constant.TestType.YJ.value:
        #     yingji = (
        #         ' <img src="./images/yes.png" width="15" height="15" valign="middle"/> '
        #     ) + yingji
        # else:
        #     yingji = (
        #         ' <img src="./images/no.png" width="15" height="15" valign="middle"/> '
        #     ) + yingji
        # data_testtypes.append(
        #     Paragraph(
        #         yingji,
        #         style3,
        #     )
        # )
        # 温州迪安复查跟体检类别放一起
        if customer == constant.Customer.WZDAMS.value:
            recheck = "复查"
            if self.rptinfo.tj_isrecheck == constant.YesOrNo.Yes.value:
                recheck = (
                    ' <img src="./images/yes.png" width="15" height="15" valign="middle"/> '
                ) + recheck
            else:
                recheck = (
                    ' <img src="./images/no.png" width="15" height="15" valign="middle"/> '
                ) + recheck
            data_testtypes.append(
                Paragraph(
                    recheck,
                    style3,
                )
            )

        data_testtype = [
            ("体检类型:", data_testtypes),
        ]
        style_2 = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 15),  # 第二行到最后一行的字体大小
            ("ALIGN", (0, 1), (-1, -1), "LEFT"),  # 第二行到最后一行左右左对齐
            ("VALIGN", (0, 0), (-1, -1), "TOP"),  # 所有表格上下居中对齐
        ]
        tablea_testtype = Table(
            data_testtype,
            colWidths=[3 * cm, 10 * cm],
            style=style_2,
        )
        self.contents.append(tablea_testtype)

        self.contents.append(Spacer(1, 1 * cm))

        if customer != constant.Customer.WZDAMS.value:
            style3 = getSampleStyleSheet()["BodyText"]
            style3.alignment = TA_LEFT
            style3.leading = 15
            style3.fontName = self.font_name
            style3.fontSize = 15
            data_testtypes = []
            datas = "复查"
            if self.rptinfo.tj_isrecheck == constant.YesOrNo.Yes.value:
                datas = (
                    '<img src="./images/yes.png" width="15" height="15" valign="middle"/> '
                ) + datas
            else:
                datas = (
                    ' <img src="./images/no.png" width="15" height="15" valign="middle"/> '
                ) + datas
            data_testtypes.append(
                Paragraph(
                    datas,
                    style3,
                )
            )

            data_recheck = [
                ("复    查:", data_testtypes),
            ]
            table_recheck = Table(
                data_recheck,
                colWidths=[3 * cm, 10 * cm],
                style=style_2,
                minRowHeights=[1.5 * cm, 1.5 * cm, 1.5 * cm, 1.5 * cm],
            )
            self.contents.append(table_recheck)

        self.contents.append(Spacer(1, 5 * cm))
        style2 = getSampleStyleSheet()["Heading2"]
        # style2.leading = 20
        style2.fontName = self.font_name
        style2.fontSize = 20
        style2.alignment = TA_CENTER

        # 盖章
        dwgz = Paragraph("", style2)
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictSysparm.value
            and dict.ss_pid == constant.SysParm.Stample.value
        )
        if dict_info is None:
            pass
        else:
            if dict_info.ss_short == "1" or dict_info.ss_short == "2":
                filename = "./sign/{}".format(dict_info.ss_name)
                # print("corp stamp file name is:", filename)
                if os.path.exists(filename) and os.path.isfile(filename):
                    img = Image.open(filename)
                    ratio = img.width / img.height
                    width = 100 * ratio
                    dwgz = Paragraph(
                        '<img src={} width="{}" height="100" valign="middle"/>'.format(
                            filename, width
                        ),
                        style2,
                    )
                else:
                    dwgz = Paragraph("", style2)
        self.contents.append(dwgz)

        orginfo = next(
            filter(
                lambda dict: dict.ss_typeid == constant.DictType.DictCustomer.value
                and dict.ss_pid == constant.CustomerInfo.CustomerName.value,
                dicts,
            ),
            None,
        )
        if orginfo is None:
            orgname = ""
        else:
            orgname = orginfo.ss_name
        self.contents.append(Paragraph(orgname, style2))
        # 日期
        date_time_obj = datetime.fromtimestamp(self.rptinfo.tj_createdate).strftime(
            "%Y-%m-%d"
        )
        self.contents.append(Paragraph(date_time_obj, style2))

    def add_sumamry_page(self, corpinfo, dicts, summary, areaname):
        # self.contents.append(Spacer(1, 0.5 * cm))
        # style3 = self.heading4_style
        style3 = getSampleStyleSheet()["BodyText"]
        style3.alignment = TA_LEFT
        style3.fontName = self.font_name
        style3.leading = 17
        style3.fontSize = 12
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.strikeWidth = 10
        # col_width = 200
        testtype_name = self.rptinfo.tj_typename
        testtype_dict = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictTesttype.value
            and dict.ss_pid == self.rptinfo.tj_testtype
        )
        if testtype_dict is not None:
            testtype_name = testtype_dict.ss_name
        shanggang = "上岗"
        c_dict = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictTesttype.value
            and dict.ss_pid == constant.TestType.SG.value
        )
        if c_dict is not None:
            shanggang = c_dict.ss_name
        if self.rptinfo.tj_testtype == constant.TestType.SG.value:
            shanggang = (
                '<img src="./images/yes.png" width="15" height="15" valign="middle"/> '
            ) + shanggang
        else:
            shanggang = (
                '<img src="./images/no.png" width="15" height="15" valign="middle"/> '
            ) + shanggang
        zaigang = "在岗"
        c_dict = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictTesttype.value
            and dict.ss_pid == constant.TestType.ZG.value
        )
        if c_dict is not None:
            zaigang = c_dict.ss_name
        if self.rptinfo.tj_testtype == constant.TestType.ZG.value:
            zaigang = (
                '<img src="./images/yes.png" width="15" height="15" valign="middle"/> '
            ) + zaigang
        else:
            zaigang = (
                '<img src="./images/no.png" width="15" height="15" valign="middle"/> '
            ) + zaigang
        ligang = "离岗"
        c_dict = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictTesttype.value
            and dict.ss_pid == constant.TestType.LG.value
        )
        if c_dict is not None:
            ligang = c_dict.ss_name
        if self.rptinfo.tj_testtype == constant.TestType.LG.value:
            ligang = (
                '<img src="./images/yes.png" width="15" height="15" valign="middle"/> '
            ) + ligang
        else:
            ligang = (
                '<img src="./images/no.png" width="15" height="15" valign="middle"/> '
            ) + ligang
        yingji = "应急"
        c_dict = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictTesttype.value
            and dict.ss_pid == constant.TestType.YJ.value
        )
        if c_dict is not None:
            yingji = c_dict.ss_name
        if self.rptinfo.tj_testtype == constant.TestType.YJ.value:
            yingji = (
                ' <img src="./images/yes.png" width="15" height="15" valign="middle"/> '
            ) + yingji
        else:
            yingji = (
                ' <img src="./images/no.png" width="15" height="15" valign="middle"/> '
            ) + yingji
        test_type = (
            shanggang
            + " &nbsp;&nbsp;"
            + zaigang
            + " &nbsp;&nbsp;"
            + ligang
            # + " &nbsp;&nbsp;"
            # + yingji
        )
        if summary == "":
            summary = self.rptinfo.tj_result
        # print("评价依据：", self.rptinfo.tj_evalaw.replace("\n", "<br/>\n"))
        data = [
            (
                "用人单位:",
                Paragraph(self.rptinfo.tj_corpname, style3),
                "联系电话:",
                Paragraph(corpinfo.tj_phone, style3),
            ),
            (
                "单位地址:",
                Paragraph(areaname + corpinfo.tj_address, style3),
            ),
            (
                "体检日期:",
                Paragraph(self.rptinfo.tj_testdate, style3),
            ),
            (
                "体检类别:",
                Paragraph(test_type, style3),
            ),
            (
                "体检地点:",
                Paragraph(self.rptinfo.tj_testaddress, style3),
            ),
            (
                "应检人数:",
                Paragraph(str(self.rptinfo.tj_apeoplenum), style3),
                "受检人数:",
                Paragraph(str(self.rptinfo.tj_peoplenum), style3),
            ),
            (
                "接触职业病\n危害因素名称:",
                Paragraph(self.rptinfo.tj_poisions, style3),
            ),
            (
                "体检项目:",
                Paragraph(
                    self.rptinfo.tj_testitems,
                    style3,
                ),
            ),
            (
                "评价依据:",
                Paragraph(
                    self.rptinfo.tj_evalaw.replace("\n", "<br/>\n"),
                    style3,
                ),
            ),
            (
                "备注说明:",
                Paragraph(
                    self.rptinfo.tj_memo.replace("\n", "<br/>\n"),
                    style3,
                ),
            ),
        ]
        # 坐标是以(列，行)的形式给出的 （0,0),第一列第一行
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            # ("FONTSIZE", (0, 0), (-1, 0), 12),  # 第一行的字体大小
            ("FONTSIZE", (0, 0), (-1, -1), 12),  # 第二行到最后一行的字体大小
            # ("BACKGROUND", (0, 0), (-1, 0), "#d5dae6"),  # 设置第一行背景颜色
            # ("ALIGN", (0, 0), (-1, -1), "CENTER"),  # 第一行水平居中
            ("ALIGN", (0, 1), (-1, -1), "LEFT"),  # 第二行到最后一行左右左对齐
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            ("LINEBELOW", (1, 0), (1, 0), self.line_height, colors.black),
            ("LINEBELOW", (3, 0), (3, 0), self.line_height, colors.black),
            ("TOPPADDING", (0, 0), (-1, -1), 5),
            # ("TEXTCOLOR", (0, 0), (-1, -1), colors.darkslategray),  # 设置表格内文字颜色
            # ("GRID", (0, 0), (-1, -1), 0.5, colors.grey),  # 设置表格框线为grey色，线宽为0.5
            ("SPAN", (1, 1), (3, 1)),  # 合并第二行第一列到三列
            ("LINEBELOW", (1, 1), (3, 1), self.line_height, colors.black),
            ("SPAN", (1, 2), (3, 2)),  # 合并第3行第一列到三列
            ("LINEBELOW", (1, 2), (1, 2), self.line_height, colors.black),
            ("SPAN", (1, 3), (3, 3)),  # 合并第二行第一列到三列
            # ("LINEBELOW", (1, 3), (3, 3), self.line_height, colors.black),
            ("SPAN", (1, 4), (3, 4)),
            ("LINEBELOW", (1, 4), (3, 4), self.line_height, colors.black),
            # ("SPAN", (1, 5), (3, 5)),
            ("LINEBELOW", (1, 5), (1, 5), self.line_height, colors.black),
            ("LINEBELOW", (3, 5), (3, 5), self.line_height, colors.black),
            ("SPAN", (1, 6), (3, 6)),
            ("LINEBELOW", (1, 6), (3, 6), self.line_height, colors.black),
            ("SPAN", (1, 7), (3, 7)),
            ("LINEBELOW", (1, 7), (3, 7), self.line_height, colors.black),
            ("SPAN", (1, 8), (3, 8)),
            ("LINEBELOW", (1, 8), (3, 8), self.line_height, colors.black),
            ("SPAN", (1, 9), (3, 9)),
            ("LINEBELOW", (1, 9), (3, 9), self.line_height, colors.black),
        ]
        table = Table(
            data,
            colWidths=[3.0 * cm, 9 * cm, 2.5 * cm, 4.5 * cm],
            style=style,
            minRowHeights=[1.0 * cm],
        )

        self.contents.append(table)
        self.contents.append(Spacer(1, 1 * cm))
        style2 = getSampleStyleSheet()["Heading2"]
        style2.leading = 25
        style2.fontName = self.font_name
        style2.fontSize = 20
        style2.alignment = TA_LEFT
        self.contents.append(Paragraph("体检结论与处理意见/医学建议:", style2))

        style = getSampleStyleSheet()["Heading2"]
        # style2.leading = 20
        style.fontName = self.font_name
        style.fontSize = 12
        style.alignment = TA_LEFT
        style.wordWrap = "CJK"  # 设置自动换行
        # style.strikeWidth = 300
        # style.firstLineIndent = 4
        self.contents.append(
            Paragraph(
                "&nbsp;&nbsp;&nbsp;&nbsp;" + summary.replace("\n", "<br/>\n"), style
            )
        )

    def add_table_t1t2(self, title, t1_data, medinfos, patients, dicts):
        style = getSampleStyleSheet()["Normal"]
        style.alignment = TA_LEFT
        style.leading = 20
        style.fontName = "SimHei"
        style.fontSize = 14
        style.wordWrap = "CJK"  # 设置自动换行
        # if t1t2 == 1:
        #     title = "<b>表 1、疑似职业病和职业禁忌证人员名单</b>"
        # else:
        #     title = "<b>表 2、需复查人员名单</b>"
        self.contents.append(Paragraph(title, style))
        self.contents.append(Spacer(1, 0.2 * cm))

        style3 = getSampleStyleSheet()["Normal"]
        style3.alignment = TA_CENTER
        style3.leading = 10
        style3.fontName = self.font_name
        style3.fontSize = 10
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.underlineOffset = 20

        style3_left = getSampleStyleSheet()["Normal"]
        style3_left.alignment = TA_LEFT
        style3_left.leading = 10
        style3_left.fontName = self.font_name
        style3_left.fontSize = 10
        style3_left.wordWrap = "CJK"  # 设置自动换行
        style3_left.underlineOffset = 20
        data = [
            (
                Paragraph("序号", style3),
                Paragraph("体检编号", style3),
                Paragraph("姓名", style3),
                Paragraph("性别", style3),
                Paragraph("年龄", style3),
                Paragraph("接害工龄", style3),
                Paragraph("工种", style3),
                Paragraph("接触职业病危害因素名称", style3),
                Paragraph("异常指标", style3_left),
                Paragraph("结论", style3),
                Paragraph("处理意见", style3),
                Paragraph("医学建议", style3_left),
            ),
        ]

        if len(t1_data) > 0:
            idx = 1
            for ca in t1_data:
                medinfo = next((med for med in medinfos if med.tj_testid == ca.tj_testid),None)
                if medinfo is None:
                    continue
                ptinfo = next((pt for pt in patients if pt.tj_pid == medinfo.tj_pid),None)
                if ptinfo is None:
                    continue
                x = ca.tj_othabnormal.split("\n")
                str_list = list(filter(None, x))
                othabnormals = "<br/>".join(str_list)
                x = ca.tj_othsuggestion.split("\n")
                str_list = list(filter(None, x))
                othsuggestion = "<br/>".join(str_list)
                x = ca.tj_ocuabnormal.split("\n")
                str_list = list(filter(None, x))
                ocuabnormals = "<br/>".join(str_list)
                x = ca.tj_ocusuggestion.split("\n")
                str_list = list(filter(None, x))
                ocusuggestion = "<br/>".join(str_list)
                data.append(
                    (
                        idx,
                        Paragraph(ca.tj_testid, style3),
                        Paragraph(ptinfo.tj_pname, style3),
                        Paragraph(constant.get_sex(ptinfo.tj_psex), style3),
                        Paragraph(str(medinfo.tj_age), style3),
                        Paragraph(medinfo.tj_poisionage, style3),
                        Paragraph(medinfo.tj_worktype, style3),
                        Paragraph(medinfo.tj_poisionfactor, style3_left),
                        Paragraph(ocuabnormals if ocuabnormals else "-", style3_left),
                        Paragraph(
                            constant.get_dict_name(
                                dicts,
                                constant.DictType.DictCheckall.value,
                                ca.tj_typeid,
                            ),
                            style3,
                        ),
                        Paragraph(ca.tj_ocuopinion.replace("\n", "<br/>") if ca.tj_ocuopinion else "-", style3),
                        Paragraph(ocusuggestion if ocusuggestion else "-", style3_left),#为空则用“--”
                    ),
                )
                data.append(
                    (
                        idx,
                        Paragraph(ca.tj_testid, style3),
                        Paragraph(ptinfo.tj_pname, style3),
                        Paragraph(constant.get_sex(ptinfo.tj_psex), style3),
                        Paragraph(str(medinfo.tj_age), style3),
                        Paragraph(medinfo.tj_poisionage, style3),
                        Paragraph(medinfo.tj_worktype, style3),
                        Paragraph(medinfo.tj_poisionfactor, style3_left),
                        Paragraph(othabnormals if othabnormals else "-", style3_left),
                        Paragraph(
                            "-",
                            style3,
                        ),
                        Paragraph(ca.tj_othopinion.replace("\n", "<br/>") if ca.tj_othopinion else "-", style3),
                        Paragraph(othsuggestion if othsuggestion else "-", style3_left),
                    ),
                )
                idx += 1
        else:
            data.append(
                (
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3_left),
                    Paragraph("无", style3_left),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3_left),
                )
            )
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), "#eeeeee"),  # 设置第一行背景颜色
            # ("ALIGN", (0, 0), (-1, -1), "CENTER"),  # 第一行水平居中
            ("ALIGN", (0, 1), (-1, -1), "LEFT"),  # 第二行到最后一行左右左对齐
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            # ("LINEBELOW", (1, 0), (-1, -1), 1, colors.black),
            # ("BOTTOMPADDING", (1, 0), (-1, -1), 5),
            # ("TEXTCOLOR", (0, 0), (-1, -1), colors.darkslategray),  # 设置表格内文字颜色
            (
                "GRID",
                (0, 0),
                (-1, -1),
                0.5,
                colors.grey,
            ),  # 设置表格框线为grey色，线宽为0.5
            # ("SPAN", (1, 1), (1, 2)),
            # ("SPAN", (0, 1), (0, 2)),
            # ("SPAN", (0, 3), (0, 4)),
            # ('SPAN', (0, 3), (0, 4)),  # 合并第一列三四行
            # ('SPAN', (0, 5), (0, 6)),  # 合并第一列五六行
            # ('SPAN', (0, 7), (0, 8)),  # 合并第一列五六行
        ]
        # 合并表格
        if len(t1_data) > 0:
            for col in range(0, 8):
                for row in range(1, len(data), 2):
                    style.append(("SPAN", (col, row), (col, row + 1)))
        else:
            style.append(("SPAN", (0, 1), (11, 1)))

        repeatedrow = 1
        if self.splitinrow == 1:
            repeatedrow = 0

        ratio = 1
        if self.pagestyle == 1:
            ratio = 1.4
        w0 = 0.5 * ratio
        w1 = 0.8 * ratio
        w2 = 1.5 * ratio
        w3 = 1.8 * ratio
        w4 = 4.3 * ratio
        w5 = 1.0 * ratio
        w6 = 1.5 * ratio
        table = Table(
            data,
            colWidths=[
                w0 * cm,
                w5 * cm,
                w6 * cm,
                w1 * cm,
                w1 * cm,
                w5 * cm,
                w1 * cm,
                w2 * cm,
                w4 * cm,
                w5 * cm,
                w3 * cm,
                w4 * cm,
            ],
            style=style,
            splitInRow=self.splitinrow,
            repeatRows=repeatedrow,
            # hAlign="CENTER"
            # repeatRows=1
            # minRowHeights=[0.8 * cm, 0.8 * cm, 0.8 * cm],
        )
        self.contents.append(table)

    def add_table_t3(self, title, t3_data, medinfos, patients, dicts):
        style = getSampleStyleSheet()["Normal"]
        style.alignment = TA_LEFT
        style.leading = 20
        style.fontName = "SimHei"
        style.fontSize = 14
        style.wordWrap = "CJK"  # 设置自动换行
        # title = "<b>表 3、其他人员名单（表 1-2 所列人员以外的受检人员）</b>"
        self.contents.append(Paragraph(title, style))
        self.contents.append(Spacer(1, 0.2 * cm))

        style3 = getSampleStyleSheet()["Normal"]
        style3.alignment = TA_CENTER
        style3.leading = 10
        style3.fontName = self.font_name
        style3.fontSize = 10
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.underlineOffset = 20

        style3_left = getSampleStyleSheet()["Normal"]
        style3_left.alignment = TA_LEFT
        style3_left.leading = 10
        style3_left.fontName = self.font_name
        style3_left.fontSize = 10
        style3_left.wordWrap = "CJK"  # 设置自动换行
        style3_left.underlineOffset = 20
        data = [
            (
                Paragraph("序号", style3),
                Paragraph("体检编号", style3),
                Paragraph("姓名", style3),
                Paragraph("性别", style3),
                Paragraph("年龄", style3),
                Paragraph("接害工龄", style3),
                Paragraph("工种", style3),
                Paragraph("接触职业病危害因素名称", style3),
                Paragraph("异常指标", style3_left),
                Paragraph("结论", style3),
                Paragraph("医学建议", style3_left),
            ),
        ]

        if len(t3_data) > 0:
            idx = 1
            for ca in t3_data:
                medinfo = next((med for med in medinfos if med.tj_testid == ca.tj_testid),None)
                if medinfo is None:
                    continue
                ptinfo = next((pt for pt in patients if pt.tj_pid == medinfo.tj_pid),None)
                if ptinfo is None:
                    continue
                x = ca.tj_othabnormal.split("\n")
                str_list = list(filter(None, x))
                othabnormals = "<br/>".join(str_list)
                x = ca.tj_othsuggestion.split("\n")
                str_list = list(filter(None, x))
                othsuggestion = "<br/>".join(str_list)
                data.append(
                    (
                        idx,
                        Paragraph(ca.tj_testid, style3),
                        Paragraph(ptinfo.tj_pname, style3),
                        Paragraph(constant.get_sex(ptinfo.tj_psex), style3),
                        Paragraph(str(medinfo.tj_age), style3),
                        Paragraph(medinfo.tj_poisionage, style3),
                        Paragraph(medinfo.tj_worktype, style3),
                        Paragraph(medinfo.tj_poisionfactor, style3_left),
                        Paragraph(othabnormals if othabnormals else "-", style3_left),
                        # Paragraph(ca.tj_othconclusion.replace("\n", "<br/>") if ca.tj_othconclusion else "-", style3),
                        Paragraph(
                            constant.get_dict_name(
                                dicts,
                                constant.DictType.DictCheckall.value,
                                ca.tj_typeid,
                            ),
                            style3,
                        ),
                        Paragraph(othsuggestion if othsuggestion else "-", style3_left),
                    ),
                )
                idx += 1
        else:
            data.append(
                (
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3_left),
                    Paragraph("无", style3_left),
                    Paragraph("无", style3),
                    # Paragraph("无", style3),
                    Paragraph("无", style3_left),
                )
            )
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), "#eeeeee"),  # 设置第一行背景颜色
            # ("ALIGN", (0, 0), (-1, -1), "CENTER"),  # 第一行水平居中
            ("ALIGN", (0, 1), (0, -1), "CENTER"),  # 第二行到最后一行左右左对齐
            ("ALIGN", (1, 1), (-1, -1), "LEFT"),  # 第二行到最后一行左右左对齐
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            # ("LINEBELOW", (1, 0), (-1, -1), 1, colors.black),
            # ("BOTTOMPADDING", (1, 0), (-1, -1), 5),
            # ("TEXTCOLOR", (0, 0), (-1, -1), colors.darkslategray),  # 设置表格内文字颜色
            (
                "GRID",
                (0, 0),
                (-1, -1),
                0.5,
                colors.grey,
            ),  # 设置表格框线为grey色，线宽为0.5
            # ("SPAN", (1, 1), (1, 2)),
            # ("SPAN", (0, 1), (0, 2)),
            # ("SPAN", (0, 3), (0, 4)),
            # ('SPAN', (0, 3), (0, 4)),  # 合并第一列三四行
            # ('SPAN', (0, 5), (0, 6)),  # 合并第一列五六行
            # ('SPAN', (0, 7), (0, 8)),  # 合并第一列五六行
        ]
        # 合并表格
        if len(t3_data) <= 0:
            style.append(("SPAN", (0, 1), (10, 1)))
        # style.append(("SPAN", (1, 1), (1, 2)))
        # col, row = 0, 1
        # style.append(("SPAN", (col, row), (col, row + 1)))
        repeatedrow = 1
        if self.splitinrow == 1:
            repeatedrow = 0
        ratio = 1
        if self.pagestyle == 1:
            ratio = 1.4
        w0 = 0.5 * ratio
        w1 = 1.0 * ratio
        w2 = 1.5 * ratio
        # w3 = 1.6 * ratio
        w4 = 4.9 * ratio
        w5 = 1.5 * ratio
        w6 = 0.8 * ratio
        table = Table(
            data,
            colWidths=[
                w0 * cm,
                w1 * cm,
                w5 * cm,
                w6 * cm,
                w6 * cm,
                w1 * cm,
                w1 * cm,
                w2 * cm,
                w4 * cm,
                w2 * cm,
                w4 * cm,
            ],
            style=style,
            splitInRow= self.splitinrow,
            repeatRows=repeatedrow,
            # minRowHeights=[0.8 * cm, 0.8 * cm, 0.8 * cm],
        )
        self.contents.append(table)

    def add_table_t4(self, title, t4_data, medinfos, patients, dicts):
        style = getSampleStyleSheet()["Normal"]
        style.alignment = TA_LEFT
        style.leading = 20
        style.fontName = "SimHei"
        style.fontSize = 14
        style.wordWrap = "CJK"  # 设置自动换行
        # title = "<b>表 4、需补检人员名单</b>"
        self.contents.append(Paragraph(title, style))
        self.contents.append(Spacer(1, 0.2 * cm))

        style3 = getSampleStyleSheet()["Normal"]
        style3.alignment = TA_CENTER
        style3.leading = 10
        style3.fontName = self.font_name
        style3.fontSize = 10
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.underlineOffset = 20

        style3_left = getSampleStyleSheet()["Normal"]
        style3_left.alignment = TA_LEFT
        style3_left.leading = 10
        style3_left.fontName = self.font_name
        style3_left.fontSize = 10
        style3_left.wordWrap = "CJK"  # 设置自动换行
        style3_left.underlineOffset = 20
        data = [
            (
                Paragraph("序号", style3),
                Paragraph("体检编号", style3),
                Paragraph("姓名", style3),
                Paragraph("性别", style3),
                Paragraph("年龄", style3),
                Paragraph("接害工龄", style3),
                Paragraph("工种", style3),
                Paragraph("接触职业病危害因素名称", style3),
                Paragraph("异常指标", style3_left),
                Paragraph("结论", style3),
                Paragraph("处理意见", style3),
                Paragraph("医学建议", style3_left),
            ),
        ]

        if len(t4_data) > 0:
            idx = 1
            for ca in t4_data:
                medinfo = next((med for med in medinfos if med.tj_testid == ca.tj_testid),None)
                if medinfo is None:
                    continue
                ptinfo = next((pt for pt in patients if pt.tj_pid == medinfo.tj_pid),None)
                if ptinfo is None:
                    continue
                x = ca.tj_othabnormal.split("\n")
                str_list = list(filter(None, x))
                othabnormals = "<br/>".join(str_list)
                x = ca.tj_othsuggestion.split("\n")
                str_list = list(filter(None, x))
                othsuggestion = "<br/>".join(str_list)
                data.append(
                    (
                        idx,
                        Paragraph(ca.tj_testid, style3),
                        Paragraph(ptinfo.tj_pname, style3),
                        Paragraph(constant.get_sex(ptinfo.tj_psex), style3),
                        Paragraph(str(medinfo.tj_age), style3),
                        Paragraph(medinfo.tj_poisionage, style3),
                        Paragraph(medinfo.tj_worktype, style3),
                        Paragraph(medinfo.tj_poisionfactor, style3_left),
                        Paragraph(othabnormals if othabnormals else "-", style3_left),
                        Paragraph(
                            constant.get_dict_name(
                                dicts,
                                constant.DictType.DictCheckall.value,
                                ca.tj_typeid,
                            ),
                            style3,
                        ),
                        Paragraph(ca.tj_ocuopinion.replace("\n", "<br/>") if ca.tj_ocuopinion else "-", style3),
                        Paragraph(othsuggestion if othsuggestion else "-", style3_left),
                    ),
                )   
                idx += 1
        else:
            data.append(
                (
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3_left),
                    Paragraph("无", style3_left),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3_left),
                )
            )
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("NOSPLIT", (0, 0), (10, 1)),
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), "#eeeeee"),  # 设置第一行背景颜色
            ("ALIGN", (0, 1), (-1, -1), "LEFT"),  # 第二行到最后一行左右左对齐
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            (
                "GRID",
                (0, 0),
                (-1, -1),
                0.5,
                colors.grey,
            ),  # 设置表格框线为grey色，线宽为0.5
        ]
        # 合并表格
        if len(t4_data) <= 0:
            style.append(("SPAN", (0, 1), (11, 1)))
        # style.append(("SPAN", (1, 1), (1, 2)))
        # col, row = 0, 1
        # style.append(("SPAN", (col, row), (col, row + 1)))
        ratio = 1
        if self.pagestyle == 1:
            ratio = 1.4
        w0 = 0.5 * ratio
        w1 = 0.8 * ratio
        w2 = 1.5 * ratio
        w3 = 1.8 * ratio
        w4 = 4.3 * ratio
        w5 = 1.0 * ratio
        w6 = 1.5 * ratio
        table = Table(
            data,
            colWidths=[
                w0 * cm,
                w5 * cm,
                w6 * cm,
                w1 * cm,
                w1 * cm,
                w5 * cm,
                w1 * cm,
                w2 * cm,
                w4 * cm,
                w5 * cm,
                w3 * cm,
                w4 * cm,
            ],
            style=style,
            # splitInRow=1,
            repeatRows=1,
            minRowHeights=[0.8 * cm],
        )
        # col, row = 0, 1
        # tTableStyles = []

        # table.setStyle(tTableStyles)
        self.contents.append(table)

    def add_sign_page(self, dicts, customer):
        if customer != constant.Customer.WZDAMS:
            self.contents.append(
                HRFlowable(
                    width="100%",
                    thickness=1,
                    color="#000000",
                    spaceAfter=2,
                    vAlign="MIDDLE",
                    lineCap="square",
                )
            )
        self.add_spacer(0.5)

        style3 = getSampleStyleSheet()["Heading3"]
        style3.alignment = TA_LEFT
        style3.fontName = self.font_name
        style3.fontSize = 10
        style3.leftIndent = 0.5 * cm

        # style3.leading = 70
        # style3.wordWrap = "CJK"  # 设置自动换行

        zjys = ""

        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictSysparm.value
            and dict.ss_pid == constant.SysParm.EZJeSign.value
        )
        if dict_info is None:
            pass
        else:
            if dict_info.ss_short == "1":  # 电子签名
                # 先检查该主检医生的签名
                staff = (
                    session.query(TjStaffadmin)
                    .filter(TjStaffadmin.id == self.rptinfo.tj_creator)
                    .first()
                )
                if staff is not None:
                    staff_esign = staff.tj_esign
                    if staff_esign != "":
                        esign = "./sign/{}".format(staff_esign)
                        if os.path.exists(esign) and os.path.isfile(esign):
                            img = Image.open(esign)
                            ratio = img.width / img.height
                            width = 25 * ratio
                            zjys = '<img src={} width="{}" height="30" valign="middle"/>'.format(
                                esign,width
                            )
                # print("人员信息配置了主检医生，但是没有签名信息:", zjys)
                if zjys == "":  # 再检查系统配置的总检医生
                    # print("开始根据系统配置的主检医生处理：", dict_info.ss_name)
                    esign = "./sign/{}".format(dict_info.ss_name)
                    if os.path.exists(esign)  and os.path.isfile(esign):
                        img = Image.open(esign)
                        ratio = img.width / img.height
                        width = 25 * ratio
                        zjys = '<img src={} width="{}" height="30" valign="middle"/>'.format(
                            esign,width
                        )
            elif dict_info.ss_short == "2":
                staff = (
                    session.query(TjStaffadmin)
                    .filter(TjStaffadmin.id == self.rptinfo.tj_creator)
                    .first()
                )
                if staff is None:
                    zjys = dict_info.ss_name
                else:
                    zjys = staff.tj_staffname
            else:
                zjys = ""

        shys = Paragraph("")
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictSysparm.value
            and dict.ss_pid == constant.SysParm.ESHYiSheng.value
        )
        if dict_info is None:
            pass
        else:
            if dict_info.ss_short == "1":  # esign
                esign = "./sign/{}".format(dict_info.ss_name)
                if os.path.exists(esign) and os.path.isfile(esign):
                    img = Image.open(esign)
                    ratio = img.width / img.height
                    width = 25 * ratio
                    shys = Paragraph(
                        '<img src={} width="{}" height="30" valign="middle"/>'.format(
                            esign,width
                        )
                    )
            elif dict_info.ss_short == "2":
                shys = Paragraph(dict_info.ss_name, style3)
            else:
                shys = Paragraph("")

        pzys = Paragraph("")
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictSysparm.value
            and dict.ss_pid == constant.SysParm.PZeSign.value
        )
        if dict_info is None:
            pass
        else:
            if dict_info.ss_short == "1":  # esign
                esign = "./sign/{}".format(dict_info.ss_name)
                if os.path.exists(esign) and os.path.isfile(esign):
                    img = Image.open(esign)
                    ratio = img.width / img.height
                    width = 25 * ratio
                    pzys = Paragraph(
                        '<img src={} width="{}" height="30" valign="middle"/>'.format(
                            esign,width
                        )
                    )
            elif dict_info.ss_short == "2":
                pzys = Paragraph(dict_info.ss_name, style3)
            else:
                pzys = Paragraph("")

        # /////////zbr/////////
        zbr = Paragraph("")
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictSysparm.value
            and dict.ss_pid == constant.SysParm.ZBRSign.value
        )
        if dict_info is None:
            pass
        else:
            if dict_info.ss_short == "1":  # esign
                esign = "./sign/{}".format(dict_info.ss_name)
                if os.path.exists(esign) and os.path.isfile(esign):                   
                    img = Image.open(esign)
                    ratio = img.width / img.height
                    width = 40 * ratio
                    zbr = Paragraph(
                        '<img src={} width="{}" height="30" valign="middle"/>'.format(
                            esign,width
                        ),
                        style3,
                    )
            elif dict_info.ss_short == "2":
                zbr = Paragraph(dict_info.ss_name, style3)
            else:
                zbr = Paragraph("")

        # 体检单位名称
        customer_name = ""
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictCustomer.value
            and dict.ss_pid == constant.CustomerInfo.CustomerName.value
        )
        if dict_info is None:
            pass
        else:
            customer_name = dict_info.ss_name
        # 体检单位是否显示电子章
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictSysparm.value
            and dict.ss_pid == constant.SysParm.Stample.value
        )
        # dwgz = Paragraph(customer_name, style3)
        dwgz = Paragraph("", style3)
        if dict_info is None:
            pass
        else:
            if dict_info.ss_short == "1" or dict_info.ss_short == "2":
                filename = "./sign/{}".format(dict_info.ss_name)
                print("corp stamp file name is:", filename)
                if os.path.exists(filename) and os.path.isfile(filename):
                    img = Image.open(filename)
                    ratio = img.width / img.height
                    width = 100 * ratio
                    dwgz = Paragraph(
                        '<img src={} width="{}" height="100" valign="middle"/>'.format(
                            filename, width
                        ),
                        style3,
                    )
                else:
                    dwgz = Paragraph("", style3)
        pzrq = datetime.fromtimestamp(self.rptinfo.tj_createdate).strftime("%Y-%m-%d")
        if customer == constant.Customer.WZDAMS:
            pzrq = ""
        data = [
            ("编 制 人:", zbr,"主检医师:", Paragraph(zjys, style3)),
            ("审 核 人:", shys,"批 准 人:", pzys),
            ("批准日期:", pzrq, "体检单位(盖章):", customer_name),
            ("", "", "", dwgz),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            ("ALIGN", (0, 0), (0, -1), "RIGHT"),  # 第一列右对齐
            ("ALIGN", (2, 0), (2, -1), "RIGHT"),  # 第三列列右对齐
            ("ALIGN", (1, 0), (1, -1), "LEFT"),  # 第二列左对齐
            ("ALIGN", (3, 0), (3, -1), "LEFT"),  # 第四列左对齐
            # ("VALIGN", (3, 0), (3, -1), "MIDDLE"),  # 第四列上下居中对齐对齐
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            # ("SPAN", (1, 3), (3, 3)), #第四行合并1-3单元格
            # ("ALIGN", (1, 3), (3, 3), "RIGHT"),  # 第一列右对齐
        ]
        table = Table(
            data,
            colWidths=[3.2 * cm, 6 * cm, 3 * cm, 6 * cm],
            style=style,
            minRowHeights=[1.5 * cm, 1.5 * cm, 1.5 * cm],
        )

        self.contents.append(table)
        self.add_spacer(1.0)

        # style4 = getSampleStyleSheet()["Heading3"]
        # style4.alignment = TA_LEFT
        # style4.fontName = self.font_name
        # style4.fontSize = 15
        # style4.leftIndent = 0.5 * cm
        # # style3.leading = 70
        # style4.wordWrap = "CJK"  # 设置自动换行
        # if self.pagestyle == 1:
        #     style4.leftIndent = 4.3 * cm

        # dwgz = "体检单位(盖章)：" + customer_name
        # self.contents.append(Paragraph(dwgz, style4))
        # self.contents.append(Paragraph(zjys, style))

    def add_back_page(self, dicts):
        self.add_spacer(2)
        self.add_spacer(1)
        style3 = getSampleStyleSheet()["Heading1"]
        style3.alignment = TA_CENTER
        style3.fontName = self.font_name
        style3.fontSize = 30
        style3.leftIndent = 0.5 * cm
        style3.leading = 80
        style3.wordWrap = "CJK"  # 设置自动换行
        # style3.spaceBefore = 1 * cm
        self.contents.append(Paragraph("职业健康检查报告书说明", style3))

        style1 = getSampleStyleSheet()["Heading1"]
        style1.fontName = self.font_name
        style1.leading = 25
        style1.wordWrap = "CJK"  # 设置自动换行
        style1.alignment = TA_LEFT
        style1.fontSize = 15
        style1.leftIndent = 0.5 * cm
        style1.strikeWidth = 10
        self.contents.append(
            Paragraph(
                "一、对本报告书有异议的，请于收到之日起十五日内向本单位提出。", style1
            )
        )
        self.contents.append(
            Paragraph(
                "二、本报告书无主检医师、审核人及批准人签字无效，本报告书无本单位盖章无效。",
                style1,
            )
        )
        self.contents.append(Paragraph("三、本报告书涂改无效。", style1))
        self.contents.append(
            Paragraph("四、本报告书不得部分复制，不得作广告宣传。", style1)
        )
        self.contents.append(
            Paragraph(
                "五、本报告书一式三份（用人单位和用人单位所在地卫生行政部门各一份，职业健康检查机构存档一份）。",
                style1,
            )
        )

        style2 = getSampleStyleSheet()["Heading1"]
        style2.fontName = self.font_name
        style2.leading = 18
        style2.wordWrap = "CJK"  # 设置自动换行
        style2.alignment = TA_LEFT
        style2.fontSize = 12
        style2.leftIndent = 0.5 * cm
        style2.strikeWidth = 8

        self.add_spacer(1)
        addict_dict = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictSysparm.value
            and dict.ss_pid == constant.SysParm.ReportIntro.value
        )
        if addict_dict is None:
            self.add_spacer(1)
            pass
        else:
            if addict_dict.ss_short == "1":
                self.contents.append(
                    Paragraph(
                        "&nbsp;&nbsp;&nbsp;&nbsp;" + addict_dict.ss_name,
                        style2,
                    )
                )
        self.add_spacer(1)
        style4 = getSampleStyleSheet()["Heading3"]
        style4.fontName = self.font_name
        style4.leading = 25
        style4.wordWrap = "CJK"  # 设置自动换行
        style4.alignment = TA_LEFT
        style4.fontSize = 15
        style4.leftIndent = 0.5 * cm
        style4.strikeWidth = 8
        self.contents.append(Paragraph("本单位联系方式：", style4))

        style5 = getSampleStyleSheet()["BodyText"]
        style5.fontName = self.font_name
        style5.leading = 15
        style5.wordWrap = "CJK"  # 设置自动换行
        style5.alignment = TA_LEFT
        style5.fontSize = 12
        style5.leftIndent = 0.5 * cm
        style5.strikeWidth = 8
        customer_name = ""
        customer_no = ""
        customer_add = ""
        customer_postcode = ""
        customer_phone = ""
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictCustomer.value
            and dict.ss_pid == constant.CustomerInfo.CustomerName.value
        )
        if dict_info is None:
            pass
        else:
            customer_name = dict_info.ss_name
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictCustomer.value
            and dict.ss_pid == constant.CustomerInfo.CustomerDJH.value
        )
        if dict_info is None:
            pass
        else:
            customer_no = dict_info.ss_name
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictCustomer.value
            and dict.ss_pid == constant.CustomerInfo.CustomerAddress.value
        )
        if dict_info is None:
            pass
        else:
            customer_add = dict_info.ss_name
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictCustomer.value
            and dict.ss_pid == constant.CustomerInfo.CustomerPostcode.value
        )
        if dict_info is None:
            pass
        else:
            customer_postcode = dict_info.ss_name
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictCustomer.value
            and dict.ss_pid == constant.CustomerInfo.CustomerPhone.value
        )
        if dict_info is None:
            pass
        else:
            customer_phone = dict_info.ss_name
        self.contents.append(
            Paragraph("职业健康检查机构名称：" + customer_name, style5)
        )
        self.contents.append(
            Paragraph("职业健康检查机构备案号：" + customer_no, style5)
        )
        self.contents.append(Paragraph("地址：" + customer_add, style5))
        self.contents.append(Paragraph("邮编：" + customer_postcode, style5))
        self.contents.append(Paragraph("联系电话：" + customer_phone, style5))

    def add_page_break(self):
        self.contents.append(PageBreak())

    def add_spacer(self, size):
        self.contents.append(Spacer(1, size * cm))

    def build(self):
        self.doc.build(
            self.contents,
            # onFirstPage=self.on_first_page,
            # onLaterPages=self.on_later_pages,
            canvasmaker=NumberedCanvas,
        )


def header_portrait(canvas, doc, rptnumber):
    if doc.page == 1:
        return
    canvas.saveState()
    canvas.setFont("SimSun", 13)
    canvas.drawCentredString(A4[0] // 2, 28.5 * cm, "职 业 健 康 检 查 报 告 书")
    canvas.setFont("SimSun", 12)
    canvas.drawString(doc.leftMargin, 27.7 * cm, rptnumber)
    canvas.setLineWidth(0.5)
    canvas.line(doc.leftMargin, 27.5 * cm, A4[0] - doc.rightMargin, 27.5 * cm)
    # self.contents.append(Spacer(1, 0.2 * cm))
    # canvas.
    canvas.restoreState()


def header_landscape(canvas, doc, rptnumber):
    # print("start to draw header:", rptnumber)
    canvas.saveState()
    canvas.setFont("SimSun", 13)
    canvas.drawCentredString(A4[1] // 2, 19.5 * cm, "职 业 健 康 检 查 报 告 书")
    canvas.setFont("SimSun", 12)
    canvas.drawString(doc.leftMargin, 19.0 * cm, rptnumber)
    canvas.setLineWidth(0.5)
    canvas.line(doc.leftMargin, 18.8 * cm, A4[1] - doc.rightMargin, 18.8 * cm)

    # canvas.setLineWidth(0.5)
    # canvas.line(doc.leftMargin, 1.5 * cm, A4[0] - doc.rightMargin, 1.5 * cm)
    # canvas.drawString(doc.leftMargin, 0.8 * cm, "(杭发医院) 职检字第 (2023-0215) 号")
    # canvas.drawCentredString(A4[0] // 2, 0.8 * cm, "{:d}".format(doc.page))
    # canvas.drawRightString(A4[0] - doc.rightMargin, 0.8 * cm, "{}".format(self.tag))
    canvas.restoreState()
