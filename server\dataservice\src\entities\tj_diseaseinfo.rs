//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Default, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_diseaseinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_testid: String,
  pub tj_disid: i64,
  pub tj_diseasenum: String,
  pub tj_diseasename: String,
  pub tj_suggestion: String,
  pub tj_deptid: String,
  pub tj_isdisease: i32,
  pub tj_isoccu: i32,
  pub tj_typeid: i32,
  pub tj_opinion: String,
  pub tj_showorder: i64,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
