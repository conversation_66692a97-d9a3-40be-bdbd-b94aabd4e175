use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use rust_decimal::prelude::ToPrimitive;
use tracing::*;

pub struct PackageSvc;

impl PackageSvc {
  pub async fn save_packageinfos(pkginfo: &TjPackageinfo, pkgdetails: &Vec<TjPackagedetail>, db: &DbConnection) -> Result<TjPackageinfo> {
    //先保存packageinfo
    let mut pkginfo = pkginfo.to_owned();
    let ret = TjPackageinfo::save(&pkginfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    pkginfo.id = ret.unwrap();
    info!("package info id:{:?}", &pkginfo);
    let mut pkgdetails: Vec<TjPackagedetail> = pkgdetails.iter().filter(|f| f.id <= 0).map(|v| v.to_owned()).collect();

    if pkgdetails.len() > 0 {
      pkgdetails.iter_mut().for_each(|v| v.tj_pnum = pkginfo.id);
      info!("pkg details:{:?}", &pkgdetails);
      let ret = TjPackagedetail::save_many(&pkgdetails, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    Ok(pkginfo)
  }
  pub async fn query_packageinfos(db: &DbConnection) -> Result<Vec<TjPackageinfo>> {
    let ret = TjPackageinfo::query_many(&db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn query_packageinfo(id: i64, db: &DbConnection) -> Result<Option<TjPackageinfo>> {
    let ret = TjPackageinfo::query(id, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn delete_packageinfos(ids: &Vec<i64>, db: &DbConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Ok(0);
    }
    let ret = TjPackageinfo::delete(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = TjPackagedetail::delete(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ids.len() as u64)
  }
  pub async fn query_package_details(ids: &Vec<i64>, db: &DbConnection) -> Result<Vec<TjPackagedetail>> {
    let ret = TjPackagedetail::query_many(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn delete_package_details(ids: &Vec<i64>, db: &DbConnection) -> Result<i64> {
    if ids.len() <= 0 {
      return Ok(0);
    }
    // let ids = details.into_iter().map(|v| v.id).collect::<Vec<i64>>();
    let ret = TjPackagedetail::delete(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap().to_i64().unwrap_or_default())
  }
}
