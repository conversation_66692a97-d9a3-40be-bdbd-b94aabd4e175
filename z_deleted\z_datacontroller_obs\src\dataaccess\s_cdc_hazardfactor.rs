use crate::{datasetup::DbConnection, entities::prelude::*};
use anyhow::{anyhow, Result};
use rbatis::crud::CRUD;

impl SCdcHazardfactor {
  pub async fn query_many_by_code(hdcode: &str, db: &DbConnection) -> Result<Vec<SCdcHazardfactor>> {
    let w = db.get_connection().new_wrapper().eq("cdc_code", hdcode); //
    let ret = db.get_connection().fetch_list_by_wrapper(w).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_by_name(hdname: &str, db: &DbConnection) -> Option<SCdcHazardfactor> {
    if hdname.is_empty() {
      return None;
    }
    let w = db.get_connection().new_wrapper().eq("cdc_hazardname", hdname); //

    let query_ret = db.get_connection().fetch_by_wrapper(w).await;

    match query_ret {
      Ok(v) => v,
      Err(e) => {
        error!("query error:{}", e);
        None
      }
    }
  }
}
