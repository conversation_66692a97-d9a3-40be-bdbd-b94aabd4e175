use crate::entities::{prelude::*, tj_diseases};
use anyhow::{anyhow, Result};
use sea_orm::{sea_query::Expr, ActiveModelTrait, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, ActiveValue::NotSet, Set};
use serde_json::json;

impl TjDiseases {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<TjDiseases>> {
    let ret = TjDiseasesEntity::find().filter(tj_diseases::Column::TjDiscode.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(disids: &Vec<i64>, db: &DatabaseConnection) -> Result<Vec<TjDiseases>> {
    let mut conditions = Condition::all();
    if disids.len() > 0 {
      conditions = conditions.add(tj_diseases::Column::Id.is_in(disids.to_owned()));
    }
    let ret = TjDiseasesEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save(info: &TjDiseases, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_diseases::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    Ok(val_id.unwrap())
  }
  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let ret = TjDiseasesEntity::update_many()
      .col_expr(tj_diseases::Column::TjStatus, Expr::value(-1))
      .filter(Condition::all().add(tj_diseases::Column::Id.is_in(ids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
}
