use anyhow::{anyhow, Result};
use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjDiseasehistory {
  pub id: i64,
  pub tj_testid: String,
  pub tj_disname: String,
  pub tj_date: i32,
  pub tj_orgname: String,
  pub tj_curve: String,
  pub tj_isrecover: i32,
  pub tj_finalcode: String,
  pub tj_isoccu: i32,
}
crud!(TjDiseasehistory {}, "tj_diseasehistory");
rbatis::impl_select!(TjDiseasehistory{query(testid:&str) => "`where tj_testid = #{testid} `"});
rbatis::impl_delete!(TjDiseasehistory{delete(testid:&str) => "`where tj_testid = #{testid} `"});

impl TjDiseasehistory {
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<TjDiseasehistory>) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<TjDiseasehistory>, Vec<TjDiseasehistory>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = TjDiseasehistory::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = TjDiseasehistory::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }
}
