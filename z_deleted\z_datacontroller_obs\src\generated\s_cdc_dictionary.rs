//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "s_cdc_dictionary")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub dictionary_no: i32,
    pub lemma_item: i32,
    pub item_content: Option<String>,
    pub allow_edit: i8,
    pub item_status: i8,
    pub sort_no: Option<i32>,
    pub item_remark: Option<String>,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
