use axum::Json;
use serde_json::Value;

use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok},
  service::externalservice::ExternalService,
};

// pub async fn test_put() -> Json<Value> {
//   Json(serde_json::json!("test_put"))
// }

pub async fn test_post() -> Json<Value> {
  info!("test post");
  let ret = ExternalService::query_dictions().await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  // let results = ret.unwrap();
  response_json_value_ok(1, ret.unwrap())
}

// pub async fn test_get() -> Json<Value> {
//   Json(serde_json::json!("test_get"))
// }

// pub async fn test_delete() -> <PERSON><PERSON><Value> {
//   Json(serde_json::json!("test_delete"))
// }
