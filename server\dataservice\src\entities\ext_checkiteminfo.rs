//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(C<PERSON>, Debug, Default, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "ext_checkiteminfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub testid: String,
  pub tid: String,
  pub testername: String,
  pub idcard: String,
  pub psex: i32,
  pub csex: String,
  pub birthdate: String,
  pub phone: String,
  pub age: i32,
  pub itemname: String,
  pub price: String,
  pub itemid: String,
  pub itemid2: String,
  pub deptid: String,
  pub deptname: String,
  pub depttype: i32,
  pub requesterid: String,
  pub requestername: String,
  pub requestdate: i64,
  pub requestdate2: String,
  pub paytype: i32,
  pub packagename: String,
  pub zdym: String,
  pub sampletype: String,
  pub corpnum: i64,
  pub syncstatus: i32,
  pub uid: i64,
  pub lis: i32,
  pub pacs: i32,
  pub extsn: String,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
