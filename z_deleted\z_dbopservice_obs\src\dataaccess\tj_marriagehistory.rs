use anyhow::{anyhow, Result};
use rbatis::crud;
// use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjMarriagehistory {
  pub id: i64,
  pub tj_testid: String,
  pub tj_date: i32,
  pub tj_spouseradiation: String,
  pub tj_spouseoccu: String,
}
crud!(TjMarriagehistory {}, "tj_marriagehistory");
rbatis::impl_select!(TjMarriagehistory{query(testid:&str) => "`where tj_testid = #{testid} `"});
rbatis::impl_delete!(TjMarriagehistory{delete(testid:&str) => "`where tj_testid = #{testid} `"});

impl TjMarriagehistory {
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<TjMarriagehistory>) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<TjMarriagehistory>, Vec<TjMarriagehistory>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = TjMarriagehistory::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = TjMarriagehistory::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }
}
