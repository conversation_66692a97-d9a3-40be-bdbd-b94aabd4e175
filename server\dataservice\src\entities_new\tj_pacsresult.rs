//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_pacsresult")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_testid: String,
  pub tj_patientname: String,
  pub tj_itemid: String,
  pub tj_itemname: String,
  pub tj_jclx: String,
  pub tj_jcxm: String,
  pub tj_jcmc: String,
  pub tj_jcys: String,
  pub tj_sxys: String,
  pub tj_bgys: String,
  pub tj_sfyc: i32,
  pub tj_importer: String,
  pub tj_imagesight: String,
  pub tj_imagediagnosis: String,
  pub tj_bgrq: i64,
  pub tj_importdate: i64,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
