// use anyhow::{anyhow, Result};
// use rbatis::{crud, sql::IntoSql};
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjAudiogramresult {
  pub id: i64,
  pub tj_testid: String,
  pub tj_avgsgp: f32,
  pub tj_avgyyp: f32,
  pub tj_avgzyp: f32,
  pub tj_avgsyp: f32,
  pub tj_avgygp: f32,
  pub tj_avgzgp: f32,
  pub tj_avgyty: f32,
  pub tj_avgzty: f32,
  pub tj_avgsty: f32,
}
// crud!(TjAudiogramresult {}, "tj_audiogramresult");
// rbatis::impl_select!(TjAudiogramresult{query_many(testids:&[&str]) => "`where tj_testid in ${testids.sql()} `"});
// rbatis::impl_select!(TjAudiogramresult{query(testid:&str) ->Option => "`where tj_testid = #{testid} limit 1`"});
// rbatis::impl_delete!(TjAudiogramresult{delete(testids:&[&str]) => "`where tj_testid in ${testids.sql()} `"});

// impl TjAudiogramresult {
//   pub async fn save(rb: &mut rbatis::RBatis, info: &TjAudiogramresult) -> Result<i64> {
//     // let mut rb = db.get_connection_clone();
//     if info.id <= 0 {
//       let ret = TjAudiogramresult::insert(rb, &info).await;
//       if ret.as_ref().is_err() {
//         return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
//       }
//       return Ok(ret.unwrap().last_insert_id.as_i64().unwrap_or_default());
//     } else {
//       let ret = TjAudiogramresult::update_by_column(rb, &info, "id").await;
//       if ret.as_ref().is_err() {
//         return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
//       }
//       return Ok(ret.unwrap().last_insert_id.as_i64().unwrap_or_default());
//     }
//   }
// }
