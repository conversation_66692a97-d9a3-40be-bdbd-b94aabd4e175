use crate::entities::{
  prelude::{Tj<PERSON><PERSON><PERSON><PERSON>, Tj<PERSON>azardinfoEntity},
  tj_hazardinfo,
};
use anyhow::{anyhow, Result};
use log::*;
use sea_orm::{
  sea_query::Expr, ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, ConnectionTrait, DatabaseBackend, DatabaseConnection, EntityTrait, QueryFilter, Set, Statement,
};
use serde_json::json;

impl TjHazardinfo {
  pub async fn query(code: i64, db: &DatabaseConnection) -> Result<Option<TjHazardinfo>> {
    let ret = TjHazardinfoEntity::find().filter(tj_hazardinfo::Column::Id.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(typeids: &Vec<i64>, hdids: &Vec<i64>, hdnames: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjHazardinfo>> {
    let mut conditions = Condition::all().add(tj_hazardinfo::Column::TjStatus.gt(-1));
    if typeids.len() > 0 {
      conditions = conditions.add(tj_hazardinfo::Column::TjTid.is_in(typeids.to_owned()))
    }
    if hdids.len() > 0 {
      conditions = conditions.add(tj_hazardinfo::Column::Id.is_in(hdids.to_owned()))
    }
    if hdnames.len() > 0 {
      conditions = conditions.add(tj_hazardinfo::Column::TjHname.is_in(hdnames.to_owned()))
    }

    let ret = TjHazardinfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjHazardinfo, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_hazardinfo::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn save_many(info: &Vec<TjHazardinfo>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = info.len();
    let (insert_vals, update_vals): (Vec<TjHazardinfo>, Vec<TjHazardinfo>) = info.to_owned().into_iter().partition(|f| f.id == 0);

    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_hazardinfo::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_hazardinfo::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjHazardinfoEntity::insert_many(active_insert_values).exec(db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_hazardinfo::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    Ok(total as i64)
  }

  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjHazardinfoEntity::update_many()
      .col_expr(tj_hazardinfo::Column::TjStatus, Expr::value(-1))
      .filter(Condition::all().add(tj_hazardinfo::Column::Id.is_in(ids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }

  pub async fn update_type(oldtype: i64, newtype: i64, db: &DatabaseConnection) -> Result<u64> {
    let sqlstr = format!("update tj_hazardinfo set tj_tid = {newtype} where tj_tid = {oldtype}");

    let ret = db.execute(Statement::from_string(DatabaseBackend::MySql, sqlstr.to_owned())).await;
    info!("execute sql ret:{:?}, sql:{}", ret, sqlstr);
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap().rows_affected())
  }
}
