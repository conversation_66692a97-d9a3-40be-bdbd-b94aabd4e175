//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_itemtype")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  #[sea_orm(unique)]
  pub tj_typeid: i32,
  pub tj_typename: String,
  pub tj_deptid: String,
  pub tj_type: i32,
  pub tj_outsource: i32,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_showorder: i32,
  pub tj_operator: String,
  pub tj_moddate: i64,
  pub tj_status: i32,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
