use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};
#[derive(<PERSON><PERSON>, Default, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjGroupinfo {
  pub id: i64,
  pub tj_gname: String,
  pub tj_gmemo: String,
  pub tj_goperator: String,
  pub tj_gmoddate: i64,
  pub tj_status: i32,
}
crud!(TjGroupinfo {}, "tj_groupinfo");
rbatis::impl_select!(TjGroupinfo{query_many(ids:&[i64]) => 
  "`where id > 0 `
  if !ids.is_empty():
    ` and id in ${ids.sql()} `
 "});

impl TjGroupinfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjGroupinfo) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjGroupinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      // ret.unwrap().rows_affected
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjGroupinfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }

  // #[py_sql("update tj_departinfo set tj_flag = 1 where id in ${ids.sql()} ")]
  // pub async fn delete(rb: &mut rbatis::RBatis, ids: &[i64]) -> Result<ExecResult, rbatis::Error> {
  //   impled!()
  // }
}
