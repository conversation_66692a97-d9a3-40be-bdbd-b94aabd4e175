use serde::{Deserialize, Serialize};

//调用服务更新检查申请状态
#[derive(Debug, Serialize, Deserialize)]
pub struct InboundResponseDto {
  #[serde(rename = "resultCode")]
  pub result_code: String, //0表示成功，107表示安全访问令牌过期，非0表示失败
  #[serde(rename = "descMessage")]
  pub desc_message: String, //成功返回成功；失败返回失败原因。
  #[serde(rename = "patientID")]
  pub patient_id: Option<String>, //填写病人id
  pub result: Option<String>, //结果信息
}

//调用服务更新检查申请状态
#[derive(Debug, Serialize, Deserialize)]
pub struct InboundResponseResult {
  #[serde(rename = "orderId")]
  pub orderid: Option<String>, //医嘱号
  #[serde(rename = "examNo")]
  pub examno: Option<String>, //申请单号
  #[serde(rename = "labelNo")]
  pub labelno: Option<String>, //标签号
}

//调用服务更新检查申请状态
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct InboundRequestDto<T> {
  #[serde(rename = "accessToken")]
  pub accesstoken: String,
  #[serde(rename = "serviceName")]
  pub servicename: String,
  #[serde(rename = "serviceVersion")]
  pub serviceversion: String,
  // #[serde(rename = "params")]
  pub params: Params<T>,
}

// Params struct with generic args
#[derive(Serialize, Default, Deserialize, Debug)]
// #[serde(rename = "args")]
pub struct Params<T> {
  pub args: T,
}

//收费通知（体检系统提供，集成平台调用）
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct ChargeStatus {
  #[serde(rename = "physicalID")]
  pub physicalid: String, // 体检系统的体检号
  #[serde(rename = "chargeFlag")]
  pub chargeflag: String, //收退费标识 0-收费,1-退费
}

//调用服务更新检查申请状态
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct ExamStatus {
  #[serde(rename = "patientID")]
  pub patient_id: String, // 姓名，长度20，不可为空
  #[serde(rename = "visitNO")]
  pub visit_no: String, // 就诊号，长度20，不可为空
  #[serde(rename = "patientClass")]
  pub patient_class: String, // 病人类型，长度2，不可为空
  #[serde(rename = "orderID")]
  pub order_id: String, // 医嘱号，长度20，不可为空
  #[serde(rename = "examNO")]
  pub exam_no: Option<String>, // 检查单号，长度20，可为空
  #[serde(rename = "examStatus")]
  pub exam_status: String, // 检查状态，长度2，不可为空
  #[serde(rename = "operatorID")]
  pub operator_id: String, // 操作者ID，长度20，不可为空
  #[serde(rename = "operatorName")]
  pub operator_name: String, // 操作者姓名，长度20，不可为空
  #[serde(rename = "operationTime")]
  pub operation_time: String, // 操作时间，长度20，不可为空
}

//2.4.4.2.	调用服务写检查报告
#[derive(Debug, Default, Clone, Serialize, Deserialize)]
pub struct PacsResultRecord {
  #[serde(rename = "patientID")]
  pub patient_id: String, // 编号，长度20，不可为空
  #[serde(rename = "visitNO")]
  pub visit_no: String, // 就诊号，长度20，不可为空
  #[serde(rename = "patientClass")]
  pub patient_class: String, // 病人类型，长度2，不可为空
  #[serde(rename = "orderID")]
  pub order_id: String, // 医嘱号，长度20，不可为空
  #[serde(rename = "examNO")]
  pub exam_no: String, // 检查单号，长度20，不可为空（此处传空）
  #[serde(rename = "examStatus")]
  pub exam_status: String, // 检查申请状态，长度2，不可为空
  #[serde(rename = "reportDoctorID")]
  pub report_doctorid: String, // 报告医生ID，长度20，不可为空
  #[serde(rename = "reportDoctorName")]
  pub report_doctor_name: String, // 报告医生姓名，长度20，不可为空
  #[serde(rename = "reportTime")]
  pub report_time: String, // 报告时间，长度20，不可为空
  #[serde(rename = "verifierDoctorID")]
  pub verifier_doctorid: String, // 审核报告医生ID
  #[serde(rename = "verifierDoctorName")]
  pub verifier_doctor_name: String, // 审核报告医生姓名
  #[serde(rename = "verifierTime")]
  pub verifier_time: String, // 审核报告时间，长度20，不可为空
  #[serde(rename = "operatorID")]
  pub operator_id: String, // 操作者ID，长度20，不可为空
  #[serde(rename = "operatorName")]
  pub operator_name: String, // 操作者姓名，长度20，不可为空
  #[serde(rename = "operationTime")]
  pub operation_time: String, // 操作时间，长度20，不可为空
  // #[serde(rename = "pacsLocallId")]
  // pub pacs_local_id: Option<String>, // 检查系统检查单号，可为空
  #[serde(rename = "reports")]
  pub reports: Vec<Report>, // 报告列表
  #[serde(rename = "dicoms")]
  pub dicoms: Vec<Dicom>, // DICOM列表
  #[serde(rename = "images")]
  pub images: Vec<Image>, // 图像列表
  #[serde(rename = "technicianID")]
  pub technicianid: Option<String>, // technician id
  #[serde(rename = "technician")]
  pub technician: String, // technician
}

// 报告子结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Report {
  #[serde(rename = "examPara")]
  pub exam_para: String, // 检查参数，长度1000，不可为空
  #[serde(rename = "description")]
  pub description: String, // 检查所见，长度1000，不可为空
  #[serde(rename = "impression")]
  pub impression: String, // 印象，长度2000，不可为空
  #[serde(rename = "recommendation")]
  pub recommendation: String, // 建议，长度100，不可为空
  #[serde(rename = "isAbnormal")]
  pub is_abnormal: String, // 是否阳性，长度1，不可为空
  // #[serde(rename = "device")]
  // pub device: Option<String>, // 使用仪器，长度20，可为空
  #[serde(rename = "useImage")]
  pub use_image: Option<String>, // 报告中图象编号，长度20，可为空
  #[serde(rename = "memo")]
  pub memo: Option<String>, // 备注，长度40，可为空
  #[serde(rename = "reportID")]
  pub report_id: String, // 医技科室生成的报告ID，长度36，不可为空
  #[serde(rename = "criticalFlag")]
  pub critical_flag: String, // 危急值标志，长度1，不可为空
  #[serde(rename = "infectionFlag")]
  pub infection_flag: String, // 传染病标识，长度1，不可为空
  #[serde(rename = "urlPath")]
  pub urlpath: Option<String>, // 报告路径
}

// DICOM子结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Dicom {
  #[serde(rename = "acceNo")]
  pub acce_no: Option<String>, // 影像号，长度20，可为空
  #[serde(rename = "examNo")]
  pub exam_no: Option<String>, // 申请单号，长度20，可为空
  #[serde(rename = "reportId")]
  pub report_id: Option<String>, // 报告号，长度20，可为空
}

// 图像子结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Image {
  #[serde(rename = "reportId")]
  pub report_id: Option<String>, // 报告号，长度20，可为空
  #[serde(rename = "orderId")]
  pub order_id: Option<String>, // 医嘱号，长度20，可为空
  #[serde(rename = "url")]
  pub url: Option<String>, // 图像路径，长度30，可为空
}

//2.4.4.3.	调用服务更新检验申请状态
#[derive(Debug, Serialize, Deserialize)]
pub struct LabStatusRecord {
  #[serde(rename = "patientID")]
  pub patient_id: String, // 姓名，长度20，不可为空
  #[serde(rename = "visitNO")]
  pub visit_no: String, // 就诊号，长度20，不可为空
  #[serde(rename = "patientClass")]
  pub patient_class: String, // 病人类型，长度2，不可为空
  #[serde(rename = "orderID")]
  pub order_id: String, // 医嘱号，长度20，不可为空
  #[serde(rename = "applyNO")]
  pub apply_no: String, // 检验单号，长度20，不可为空（此处传空）
  #[serde(rename = "labStatus")]
  pub lab_status: String, // 检验状态，长度2，不可为空
  #[serde(rename = "operatorID")]
  pub operator_id: String, // 操作者ID，长度20，不可为空
  #[serde(rename = "operatorName")]
  pub operator_name: String, // 操作者姓名，长度20，不可为空
  #[serde(rename = "operationTime")]
  pub operation_time: String, // 操作时间，长度20，不可为空
  #[serde(rename = "barCode")]
  pub bar_code: Option<String>, // 条码号，可为空
}

//2.4.4.4.	调用服务更新检验报告
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct LabResultRecord {
  #[serde(rename = "patientID")]
  pub patient_id: String, // 姓名，长度20，不可为空
  #[serde(rename = "visitNO")]
  pub visit_no: String, // 就诊号，长度20，不可为空
  #[serde(rename = "patientClass")]
  pub patient_class: String, // 病人类型，长度2，不可为空
  #[serde(rename = "orderID")]
  pub order_id: String, // 医嘱号，长度20，不可为空
  #[serde(rename = "applyNO")]
  pub apply_no: String, // 检验单号，长度20，不可为空（此处传空）
  #[serde(rename = "labStatus")]
  pub lab_status: String, // 检验申请状态，长度2，不可为空
  #[serde(rename = "spcmSampleDateTime")]
  pub spcm_sample_date_time: String, // 标本采集时间，长度20，可为空
  #[serde(rename = "spmRecvedDateTime")]
  pub spm_recved_date_time: String, // 标本接收时间，长度20，可为空
  #[serde(rename = "executeDoctorID")]
  pub execute_doctor_id: String, // 执行者ID，长度20，可为空
  #[serde(rename = "executeDoctorName")]
  pub execute_doctor_name: String, // 执行者姓名，长度20，可为空
  #[serde(rename = "executeDate")]
  pub execute_date: String, // 执行时间，长度20，可为空
  #[serde(rename = "reportDoctorID")]
  pub report_doctor_id: String, // 报告者ID，长度20，可为空
  #[serde(rename = "reportDoctorName")]
  pub report_doctor_name: String, // 报告医生姓名，长度20，可为空
  #[serde(rename = "reportTime")]
  pub report_time: String, // 报告时间，长度20，可为空
  #[serde(rename = "verifierDoctorID")]
  pub verifier_doctor_id: String, // 审核者ID，长度20，可为空
  #[serde(rename = "verifierDoctorName")]
  pub verifier_doctor_name: String, // 审核报告医生姓名，长度20，可为空
  #[serde(rename = "verifierTime")]
  pub verifier_time: String, // 审核报告时间，长度20，可为空
  #[serde(rename = "operatorID")]
  pub operator_id: String, // 操作者ID，长度20，可为空
  #[serde(rename = "operatorName")]
  pub operator_name: String, // 操作者姓名，长度20，可为空
  #[serde(rename = "operationTime")]
  pub operation_time: String, // 操作时间，长度20，可为空
  #[serde(rename = "applyPurpose")]
  pub apply_purpose: String, // 检验目的(报告名称)，长度50，可为空
  #[serde(rename = "note")]
  pub note: String, // 备注，长度500，可为空
  #[serde(rename = "advice")]
  pub advice: String, // 解释信息(临床提示)，长度500，可为空
  #[serde(rename = "sampleDetail")]
  pub sample_detail: String, // 样本性状，长度100，可为空
  // #[serde(rename = "reportUrl")]
  // pub report_url: String, // Pdf路径，长度100，可为空
  // #[serde(rename = "images")]
  // pub images: Vec<String>, // 图像路径，可循环，可为空
  #[serde(rename = "results")]
  pub results: Vec<LisResult>, // 检验结果列表
}

// 检验结果子结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct LisResult {
  #[serde(rename = "itemNO")]
  pub item_no: String, // 项目序号，长度20，不可为空
  #[serde(rename = "itemCode")]
  pub item_code: String, // 检验报告项目代码，长度20，不可为空
  #[serde(rename = "itemName")]
  pub item_name: String, // 检验报告项目名称，长度20，不可为空
  #[serde(rename = "result")]
  pub result: String, // 检验结果值，长度20，不可为空
  #[serde(rename = "units")]
  pub units: String, // 检验结果单位，长度20，不可为空
  #[serde(rename = "upperAndlowerLimits")]
  pub upper_and_lower_limits: String, // 结果参考值，长度20，不可为空
  #[serde(rename = "abnormalIndicator")]
  pub abnormal_indicator: String, // 结果正常标志，长度2，不可为空
  #[serde(rename = "resultDateTime")]
  pub result_date_time: String, // 检验日期及时间，长度20，不可为空
  // #[serde(rename = "instrument")]
  // pub instrument: String, // 检验仪器或编号，长度20，可为空
  #[serde(rename = "crisisValue")]
  pub crisis_value: String, // 危机值，长度2，不可为空
}
