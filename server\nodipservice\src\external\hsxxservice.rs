use anyhow::{anyhow, Result};
// use dbopservice::{
//   dataaccess::{tj_audiogramdetail::TjAudiogramdetail, tj_audiogramresult::TjAudiogramresult, tj_medexaminfo::TjMedexaminfo, tj_patient::TjPatient, tj_testsummary::TjTestsummary},
//   dbinit::DbConnection,
// };
use dataservice::{dbinit::DbConnection, entities::prelude::*};
// use hyper::Method;
use crate::{common::constant, dto::ExternalDTO, medexam::audiogramsvc::AudiogramSvc};
use serde::{Deserialize, Serialize};
use tracing::*;

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct OrderInfo {
  pub order_no: String,
  pub order_name: String,
}

pub struct NbHsxxService;

impl NbHsxxService {
  pub async fn do_upload(dto: &ExternalDTO, _server: &String, audiodburi: &String, db: &DbConnection) -> Result<()> {
    let testid = dto.testid.to_string();
    // let server = config.application.proxyserver.to_string();
    let mut error_msg = "".to_string();
    //同步电测听
    if !audiodburi.is_empty() {
      let ret = TjTestsummary::query_one(&testid, crate::common::constant::AUDIOGRAM_DEPTID, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
        // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        error_msg = format!("{}{}", error_msg, ret.as_ref().unwrap_err().to_string());
      }
      if let Some(summary) = ret.unwrap() {
        //有电测听数据
        if summary.tj_isfinished == constant::YesOrNo::No as i32 {
          // info!("开始电测听对接......");
          let ret = TjMedexaminfo::query(&testid, &db.get_connection()).await;
          if ret.as_ref().is_err() {
            error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
            // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
            error_msg = format!("{}{}", error_msg, ret.as_ref().unwrap_err().to_string());
          }
          if let Some(medinfo) = ret.unwrap() {
            let pid = medinfo.tj_pid.to_string();
            let ret = TjPatient::query(&pid, &db.get_connection()).await;
            if ret.as_ref().is_err() {
              error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
              // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
              error_msg = format!("{}{}", error_msg, ret.as_ref().unwrap_err().to_string());
            }
            if let Some(ptinfo) = ret.unwrap() {
              //开始保存到电测听的数据表
              // info!("找到体检信息跟人员信息，开始")
              let ret = super::maili::MailiService::send_testinfo(&medinfo, &ptinfo, &audiodburi).await;
              if ret.as_ref().is_err() {
                // return Err(anyhow!("{}", ret.unwrap_err().to_string()));
                error_msg = format!("{}{}", error_msg, ret.as_ref().unwrap_err().to_string());
              }
            } else {
              error!("不能根据pid：{pid}找到体检人员信息");
              error_msg = format!("{}{}", error_msg, "不能根据pid找到体检人员信息");
            }
          } else {
            // error!("不能根据体检号：{testid}找到体检信息");
            error_msg = format!("{}{}", error_msg, "不能根据体检号找到体检信息");
          }
        }
      }
    }
    if error_msg.is_empty() {
      return Ok(());
    } else {
      return Err(anyhow!(error_msg));
    }
    // Ok(())
  }

  // //下载电测听结果数据
  pub async fn receive_audiogram(testid: &String, dburi: &String, db: &DbConnection) -> Result<(Vec<TjAudiogramdetail>, TjAudiogramresult)> {
    if dburi.is_empty() {
      return Err(anyhow!("没有配置电测听的连接信息，无法获取数据"));
    }

    let ret = super::maili::MailiService::query_result_by_testid(testid, dburi, db).await;
    if ret.as_ref().is_err() {
      error!("获取电测听数据错误:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let aud_result = ret.unwrap();
    let sumamry = aud_result.1;
    //update summary
    let ret = TjTestsummary::save(&sumamry, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("获取电测听数据错误:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let audio_details = aud_result.0;
    let audio_result = AudiogramSvc::compute_audiogram_result(&testid, &audio_details);
    Ok((audio_details, audio_result))
  }
}
