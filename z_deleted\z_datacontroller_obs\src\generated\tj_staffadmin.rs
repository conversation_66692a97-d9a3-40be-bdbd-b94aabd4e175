//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_staffadmin")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(unique)]
    pub tj_staffno: String,
    pub tj_staffname: String,
    pub tj_sex: i32,
    pub tj_deptid: i32,
    pub tj_groupid: i32,
    pub tj_password: String,
    pub tj_role: i32,
    pub tj_checkallflag: i32,
    pub tj_title: String,
    pub tj_status: i32,
    pub tj_operator: String,
    pub tj_moddate: i64,
    pub tj_memo: String,
    pub tj_isadmin: u8,
    pub login_session: String,
    pub tj_esign: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::tj_staffdept::Entity")]
    TjStaffdept,
}

impl Related<super::tj_staffdept::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjStaffdept.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
