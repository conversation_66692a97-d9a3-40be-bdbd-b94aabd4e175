//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, De<PERSON>ult,PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "sys_hazard_checkitem")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub check_item_name: String,
  pub check_item_description: String,
  pub local_tj_itemid: String,
  pub result_type: i8,
  pub result_unit_name_value: String,
  pub result_refer_min_value: String,
  pub result_refer_max_value: String,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
