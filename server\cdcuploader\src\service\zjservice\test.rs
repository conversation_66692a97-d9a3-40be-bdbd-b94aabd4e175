pub fn test() {

    /* test begin */
    // // let employer = Employer { ..Default::default() };
    // // let str = serde_xml_rs2::to_string(&employer).unwrap();
    // // info!("employer:{:?}", &str);

    // // let emplist = zjservice::datastructs::EmployerList { employer: vec![employer] };

    // // let serde_xml_str2 = serde_xml_rs2::to_string(&emplist).unwrap();
    // // info!("serde xml 22 result: {:?}", &serde_xml_str2);

    // let userinfo = UserInfo { ..Default::default() };
    // let se_ret = serde_xml_rs2::to_string(&userinfo);
    // if se_ret.as_ref().is_err() {
    //     error!("serialize error:{}", se_ret.as_ref().err().unwrap().to_string());
    // }
    // info!("userinfo string:{:?}", se_ret.unwrap());

    // let reportcard = ReportCard { userinfo, ..Default::default() };
    // let se_ret = serde_xml_rs2::to_string(&reportcard);
    // if se_ret.as_ref().is_err() {
    //     error!("serialize error:{}", se_ret.as_ref().err().unwrap().to_string());
    // }
    // info!("reportcard string:{:?}", se_ret.unwrap());

    // let reportcardlist = ReportCardList { reportcard: vec![reportcard] };

    // // let se_ret = serde_xml_rs2::to_string(&reportcardlist);
    // // if se_ret.as_ref().is_err() {
    // //     error!("serialize error:{}", se_ret.as_ref().err().unwrap().to_string());
    // // }
    // // info!("reportcardlist string:{:?}", se_ret.unwrap());

    // let healthybody = HealthyBody { reportcardlist };

    // // let quick_ret = quick_xml::se::to_string(&reportcardlist);
    // // if quick_ret.as_ref().is_err() {
    // //     error!("serialize quick error:{}", quick_ret.as_ref().err().unwrap().to_string());
    // // }
    // // info!("reportcardlist quick string:{:?}", quick_ret.unwrap());

    // // let body = HealthyBody { reportcardlist };
    // // let ret = quick_xml::se::to_string(&body).unwrap();
    // // info!("quick ret:{:?}", &ret);
    // // let header = Header {
    // //     eventid: "id".to_string(),
    // //     operationtype: "".to_string(),
    // //     ..Default::default()
    // // };

    // // let healthydata = HealthyData { header, body };
    // // let str = quick_xml::se::to_string(&healthydata).unwrap();
    // // info!(" result: {:?}", &str);
    // let str = quick_xml::se::to_string(&healthybody).unwrap();
    // // let str = serde_xml_rs2::to_string(&healthybody);
    // info!(" result: {:#?}", &str);
    // info!("================================================");
    /* test end */
}
