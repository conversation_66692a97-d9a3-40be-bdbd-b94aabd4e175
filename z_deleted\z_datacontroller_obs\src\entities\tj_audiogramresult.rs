use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_audiogramresult")]
#[derive(<PERSON><PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct TjAudiogramresult {
    pub id: i64,
    pub tj_testid: String,
    pub tj_avgsgp: f32,
    pub tj_avgyyp: f32,
    pub tj_avgzyp: f32,
    pub tj_avgsyp: f32,
    pub tj_avgygp: f32,
    pub tj_avgzgp: f32,
    pub tj_avgyty: f32,
    pub tj_avgzty: f32,
    pub tj_avgsty: f32,
}
