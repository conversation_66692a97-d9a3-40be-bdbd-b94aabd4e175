use crate::entities::{prelude::*, tj_baritems};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjBaritems {
  pub async fn query_many(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<Vec<TjBaritems>> {
    let mut conditions = Condition::all();
    if ids.len() > 0 {
      conditions = conditions.add(tj_baritems::Column::TjBarnum.is_in(ids.to_owned()));
    }
    let ret = TjBaritemsEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save(info: &TjBaritems, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_baritems::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }
  pub async fn delete(id: i64, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjBaritemsEntity::delete_many().filter(tj_baritems::Column::Id.eq(id)).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
