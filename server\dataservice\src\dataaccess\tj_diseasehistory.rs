use crate::entities::{prelude::*, tj_diseasehistory};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjDiseasehistory {
  pub async fn query_many(code: &str, db: &DatabaseConnection) -> Result<Vec<TjDiseasehistory>> {
    let ret = TjDiseasehistoryEntity::find().filter(tj_diseasehistory::Column::TjTestid.eq(code)).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_many(info: &Vec<TjDiseasehistory>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = info.len();
    let (insert_vals, update_vals): (Vec<TjDiseasehistory>, Vec<TjDiseasehistory>) = info.to_owned().into_iter().partition(|f| f.id == 0);

    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_diseasehistory::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_diseasehistory::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjDiseasehistoryEntity::insert_many(active_insert_values).exec(db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_diseasehistory::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    Ok(total as i64)
  }

  pub async fn insert_many(info: &Vec<TjDiseasehistory>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      // return Err(anyhow!("empty data to insert checkitems, not allowed"));
      return Ok(0);
    }
    let mut active_values: Vec<tj_diseasehistory::ActiveModel> = Vec::new();
    for val in info {
      let ret = tj_diseasehistory::ActiveModel::from_json(json!(val));
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      active_values.push(ret.unwrap());
    }
    let ret = TjDiseasehistoryEntity::insert_many(active_values).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().last_insert_id)
  }
  pub async fn delete(ids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjDiseasehistoryEntity::delete_many()
      .filter(tj_diseasehistory::Column::TjTestid.is_in(ids.to_owned()))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
}
