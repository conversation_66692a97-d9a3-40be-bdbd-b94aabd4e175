use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_departinfo")]
#[derive(<PERSON><PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct TjDepartinfo {
    pub id: i64,
    pub tj_deptid: String,
    pub tj_deptname: String,
    pub tj_showorder: i32,
    pub tj_depttype: i32,
    pub tj_deptinfo: String,
    pub tj_pricinple: i32,
    pub tj_deptaddr: String,
    pub tj_pyjm: String,
    pub tj_zdym: String,
    pub tj_flag: i32,
    pub tj_operator: i32,
    pub tj_moddate: i64,
    pub tj_diagtype: i32,
    pub tj_reportmode: i32,
    pub tj_reportorder: i32,
    pub tj_sex: i32,
}
