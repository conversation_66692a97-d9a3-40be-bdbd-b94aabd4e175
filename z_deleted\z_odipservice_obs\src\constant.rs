// use std::collections::HashMap;
// use strum_macros::EnumString;
use num_enum::TryFromPrimitive;
// use std::string::ToString;
use strum_macros::EnumString;

pub const TESTID: &str = "tjbh";
pub const PID: &str = "pid";
pub const API_MEDEXAM_SYNC: &str = "/v1/sync/medquery";
// pub const API_MEDEXAM_UPDATE: &str = "/v1/sync/medupdate";
pub const API_MEDSTATUS_UPDATE: &str = "/v1/sync/medstatus";
pub const API_MEDEXAM_RESULT: &str = "/v1/sync/medresult";
pub const API_MEDEXAM_RESULT_REPORT: &str = "/v1/sync/medreport";
pub const API_MEDEXAM_REPORT_UPLOAD: &str = "/v1/sync/medreportfile";
pub const API_MEDEXAM_PHOTO_UPLOAD: &str = "/v1/sync/medphoto";
pub const API_MEDEXAM_REPORT: &str = "/v1/sync/medreport";
pub const API_CORP_UPDATE: &str = "/v1/sync/corpupdate";
pub const API_CORP_REPORT_UPLOAD: &str = "/v1/sync/corpreportfile";

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(u8)]
pub enum YesOrNo {
  No = 0,
  Yes = 1,
}

#[derive(Debug, Clone)]
pub enum InfoType {
  EconomicType = 1,
  WorkType = 2,
  AreaCode = 3,
}

#[derive(Debug, Clone)]
pub enum TestType {
  PT = 1,
  TZ = 2,
  SG = 3,
  ZG = 4,
  LG = 5,
  YJ = 6,
  JKZ = 7,
}

//体检状态
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[repr(i32)]
pub enum ExamStatus {
  Noprogress = 0,  //体检状态0
  Appoint = 1,     //预约 1
  Register = 2,    //登记 2
  Examining = 3,   //正在体检 3
  Examined = 4,    //体检结束 4
  Allchecked = 5,  //已总检 5
  Reported = 6,    //已报告 6
  CDCUploaded = 7, //CDC已上报 7
  Syncd = 8,       //已同步 8
  Printed = 9,     //已打印 9
  MaxStatus,       //最大值
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[repr(i32)]
pub enum PAYMETHOD {
  Personal = 1, //个人
  Company = 2,  //企业
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CheckResultType {
  Normal = 0,    //正常
  Recheck = 1,   //复查
  Forbidden = 2, //禁忌
  Oculike = 3,   //疑似
  Other = 4,     //其他疾病或异常
  Addional = 5,  //需补检
}

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(i32)]
pub enum DICTTYPE {
  DictAll = 0,
  DictSex = 1,
  DictMarriage = 2,
  DictCategory = 3,
  DictSource = 4,
  DictTesttype = 5,
  DictItemtype = 6,
  DictCheckall = 8, //总检结果
  DictCustomer = 10,
  DictCPMEStatus = 11,
  DictStatus = 12,
  DictYesORNo = 13,
  DicttAskType = 14,
  DictGuiMo = 17,
  DictGuide = 18,
  DictSysparm = 19,
  DictDisRet = 20, //疾病转归代码
}

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(i32)]
pub enum CUSTOMER {
  CustomerAll = 0,
  CustomerName = 1,
  CustomerPhone = 2,
  CustomerConsultPhone = 3,
  CustomerUrl = 4,
  CustomerAddress = 5,
  CustomerBus = 6,
  CustomerBead = 7,
  CustomerReportNum = 8,
  CustomerContactor = 9,
  CustomerEmail = 10,
  CustomerPostcode = 11,
  CustomerShowLog = 12,
  CustomerLogo = 13,
  CustomerFax = 14,
  CustomerDJH = 15,
  CustomerTJYJ = 16,
  CustomerNreportnum = 17,
  CustomerMax = 100,
}

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(i32)]
#[derive(strum_macros::Display, EnumString)]
pub enum ValueType {
  /// Random Docs
  #[strum(to_string = "1")]
  DingXing,
  #[strum(to_string = "2")]
  DingLiang,
  #[strum(to_string = "3")]
  MiaoShu,
  #[strum(to_string = "4")]
  BanDingLiang,
}

#[derive(PartialEq, Clone, Debug)]
pub enum TransType {
  Air = 0,
  Bone = 1,
}

#[derive(PartialEq, Clone, Debug)]
pub enum TransEar {
  Right = 0,
  Left = 1,
}
#[derive(PartialEq, Clone, Debug)]
pub enum CorpStatus {
  DEL = 0,
  OK = 1,
}
