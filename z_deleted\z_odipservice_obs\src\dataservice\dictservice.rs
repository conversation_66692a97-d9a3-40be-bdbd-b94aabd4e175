use anyhow::{anyhow, Result};
use datacontroller::{datasetup::DbConnection, entities::prelude::*};

#[derive(Debug, Default)]
pub struct DictService {
  itemunits: Vec<ScdcItemunit>,
  incfgs: Vec<SsInfoconfig>,
  areas: Vec<SsArea>,
  cdchazards: Vec<SCdcHazardfactor>,
  diseases: Vec<TjDiseases>,
  // pub staffs: Vec<TjStaffadmin>,
}

impl DictService {
  pub async fn init(area_codes: &str, dbconn: &DbConnection) -> Self {
    let ids: Vec<String> = Vec::new();
    let incfgs = SsInfoconfig::query_many(&ids, 0, dbconn).await.expect("query info configs error");
    let areacodes: Vec<String> = area_codes.split(",").map(|x| x.to_string()).collect();
    let areas = SsArea::query_many_by_dto(&areacodes, dbconn).await.expect("query code error");
    let itemunits = ScdcItemunit::query_many(&ids, &dbconn).await.expect("init item units error");
    let cdchazards = SCdcHazardfactor::query_many_by_code("", &dbconn).await.expect("init hazards error");
    let dids: Vec<i64> = Vec::new();
    let diseases = TjDiseases::query_many(&dids, &dbconn).await.expect("query disease error");
    DictService {
      incfgs,
      areas,
      itemunits,
      cdchazards,
      diseases,
    }
  }
  pub async fn query_many(tid: i32, pid: i32, dbconn: &DbConnection) -> Result<Vec<SsDictionary>> {
    let ret = SsDictionary::query_many(tid, pid, dbconn).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("error:{}", ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub fn find_info_config(&self, ccode: &String, ptype: i32) -> Option<SsInfoconfig> {
    if ccode.is_empty() && ptype <= 0 {
      return None;
    }
    let ret = self.incfgs.iter().find(|&x| x.ss_code == ccode.to_owned() && x.ss_type == ptype).cloned();

    ret
  }

  pub async fn find_cdc_hazard(&self, hdname: &str) -> Option<&SCdcHazardfactor> {
    if hdname.is_empty() {
      return None;
    }
    let ret = self.cdchazards.iter().find(|&x| x.cdc_hazardname.eq_ignore_ascii_case(hdname));
    ret
  }

  pub async fn find_item_units(&self, itemcode: &String) -> Option<&ScdcItemunit> {
    if itemcode.is_empty() {
      return None;
    }
    let ret = self.itemunits.iter().find(|&x| x.item_code.eq_ignore_ascii_case(itemcode.as_str()));

    ret
  }

  pub async fn find_diseases_by_id(&self, did: i64) -> Option<&TjDiseases> {
    if did <= 0 {
      return None;
    }

    let ret = self.diseases.iter().find(|&x| x.id == did);
    ret
  }

  pub async fn find_diseases_by_code(&self, code: &str) -> Option<&TjDiseases> {
    if code.is_empty() {
      return None;
    }

    let ret = self.diseases.iter().find(|&x| x.tj_discode.eq_ignore_ascii_case(code));
    ret
  }
  pub async fn find_area_info(&self, areacode: &String) -> Option<SsArea> {
    if areacode.is_empty() {
      return None;
    }
    let ret = self.areas.iter().find(|&x| x.area_code.eq_ignore_ascii_case(areacode.as_str()));
    if ret.is_none() {
      return None;
    }
    Some(ret.unwrap().clone())
  }

  //生成新的id
  pub async fn generate_new_id(&self, key: &str, dbconn: &DbConnection) -> Result<String> {
    let ret = SsIdentity::query_by_name(key, &dbconn).await;
    if ret.is_none() {
      return Err(anyhow!("can't find ss_identity by {}", key));
    }
    let mut idx = ret.unwrap();
    let testid = idx.id_cvalue.to_string();
    idx.id_cvalue = idx.id_cvalue + idx.id_incvalue;
    let ret = SsIdentity::update(&idx, &dbconn).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    Ok(testid)
  }
}
