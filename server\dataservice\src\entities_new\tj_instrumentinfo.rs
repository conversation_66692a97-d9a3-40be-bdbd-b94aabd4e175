//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_instrumentinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  #[sea_orm(unique)]
  pub tj_struid: String,
  pub tj_struname: String,
  pub tj_typename: String,
  pub tj_deptid: String,
  pub tj_memo: String,
  pub tj_com: String,
  #[sea_orm(column_type = "Decimal(Some((20, 10)))")]
  pub tj_lowvalue: Decimal,
  #[sea_orm(column_type = "Decimal(Some((20, 10)))")]
  pub tj_upvalue: Decimal,
  pub tj_vlimited: i32,
  pub tj_status: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
