//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, De<PERSON>ult,PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "s_cdc_itemunit")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub item_type: String,
  pub item_code: String,
  pub item_name: String,
  pub item_unit: String,
  pub item_unitcode: String,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
