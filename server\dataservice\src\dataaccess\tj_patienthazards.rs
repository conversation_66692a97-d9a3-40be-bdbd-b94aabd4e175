use crate::entities::{prelude::*, tj_patienthazards};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, Set, TransactionTrait};
use serde_json::json;

impl TjPatienthazards {
  pub async fn query(testid: &str, db: &DatabaseConnection) -> Result<Vec<TjPatienthazards>> {
    let ret = TjPatienthazardsEntity::find().filter(tj_patienthazards::Column::TjTestid.eq(testid)).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_many(testids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjPatienthazards>> {
    let ret = TjPatienthazardsEntity::find().filter(tj_patienthazards::Column::TjTestid.is_in(testids.to_owned())).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_many(infos: &Vec<TjPatienthazards>, db: &DatabaseConnection) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len();
    let (insert_vals, update_vals): (Vec<TjPatienthazards>, Vec<TjPatienthazards>) = infos.to_owned().into_iter().partition(|f| f.id == 0);
    let txn = db.begin().await.expect("start transaction error");
    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_patienthazards::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_patienthazards::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjPatienthazardsEntity::insert_many(active_insert_values).exec(&txn).await;
      if ret.as_ref().is_err() {
        let _ = txn.rollback();
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_patienthazards::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_patienthazards::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(&txn).await;
        if ret.as_ref().is_err() {
          let _ = txn.rollback();
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    let ret = txn.commit().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(total as i64)
  }

  pub async fn insert_many(info: &Vec<TjPatienthazards>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    // let testids: Vec<String> = info.iter().map(|f| f.tj_testid.to_owned()).collect();
    // let ret = TjPatienthazards::delete(&testids, &db).await;
    // if ret.as_ref().is_err() {
    //   return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    // }
    let mut active_values: Vec<tj_patienthazards::ActiveModel> = Vec::new();
    for val in info {
      let ret = tj_patienthazards::ActiveModel::from_json(json!(val));
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      active_values.push(ret.unwrap());
    }
    let ret = TjPatienthazardsEntity::insert_many(active_values).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().last_insert_id)
  }

  pub async fn delete(testids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    if testids.len() <= 0 {
      return Err(anyhow!("testids are empty"));
    }
    let ret = TjPatienthazardsEntity::delete_many()
      .filter(tj_patienthazards::Column::TjTestid.is_in(testids.to_owned()))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn delete_by_ids(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty"));
    }
    let ret = TjPatienthazardsEntity::delete_many().filter(tj_patienthazards::Column::Id.is_in(ids.to_owned())).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
