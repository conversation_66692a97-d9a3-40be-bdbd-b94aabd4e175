import os, sys
from datetime import datetime

# import matplotlib.pyplot as plt;
# from matplotlib.font_manager import FontProperties
# from matplotlib.offsetbox import OffsetImage, AnnotationBbox
# import numpy  as np
from PIL import Image
from reportlab.platypus import (
    BaseDocTemplate,
    Spacer,
    PageBreak,
    NextPageTemplate,
    TableStyle,
    PageTemplate,
    Frame,
)
from reportlab.platypus import Paragraph, Table, HRFlowable
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape, portrait
from reportlab.lib.units import cm, inch
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.lib.styles import getSampleStyleSheet
from personalreport.numberedcanvas import PersonalNumberedCanvas
from personalreport.audiogram import TjAudiogramresult
from personalreport.audiochart import AudioChart
from functools import partial
import constant

# from dataentities.dbconn import session
from dataentities.tj_staffadmin import TjStaffadmin


def generate_pdf_personal_occureport(
    medinfo,
    patient,
    summaries,
    checkitems,
    audiogramdetails,
    checkallinfo,
    corpinfo,
    deptinfos,
    dicts,
    customer,
    staffs,
    pagestyle,
    splitinrow,
    outdir,
    areaname,
):
    # 生成pdf文件 # 职业健康报告
    output = "{}-1.pdf".format(
        medinfo.tj_testid,
    )

    rpt = personaloccu_report(
        medinfo,
        patient,
        pagestyle,
        splitinrow,
        outdir,
        output,
    )

    rpt.add_spacer(0.8)
    rpt.add_front_page(checkallinfo, corpinfo, dicts)
    rpt.add_page_break()
    rpt.add_detail_pages(
        summaries, checkitems, deptinfos, audiogramdetails, staffs, dicts
    )
    rpt.add_checkall_page(checkallinfo, dicts)
    rpt.add_spacer(1.0)
    rpt.add_sign_page(checkallinfo, dicts)
    rpt.add_spacer(1.0)
    rpt.add_info_page(dicts)
    # rpt.add_spacer(0.1)
    rpt.build()
    return os.path.join(outdir, output)


# 	a4:21.0 x 29.7 cm
class personaloccu_report:
    def __init__(self, medinfo, patient, pagestyle, splitinrow, rptdir, output):
        self.medinfo = medinfo
        # print("============Self Patient info:",patient.tj_pname)
        self.patient = patient
        self.font_name = "SimSun"
        self.font_hei = "SimHei"
        self.splitinrow = splitinrow
        self.contents = []
        stylesheet = getSampleStyleSheet()
        self.title_style = stylesheet["Title"]
        self.normal_style = stylesheet["Normal"]
        self.body_style = stylesheet["BodyText"]
        self.doc = BaseDocTemplate(
            os.path.join(rptdir, output),
            pagesize=A4,
            leftMargin=1.2 * cm,
            rightMargin=1.2 * cm,
            topMargin=2.0 * cm,
            bottomMargin=1.2 * cm,
        )
        self.line_height = 0.8
        self.result_row_height = 0.9 * cm
        self.portrait_frame = Frame(
            1.2 * cm,  # self.doc.leftMargin,
            1.2 * cm,  # self.doc.bottomMargin,
            self.doc.width,
            self.doc.height,
            id="portrait_frame",
        )
        self.landscape_frame = Frame(
            1.6 * cm,  # self.doc.leftMargin,
            1.2 * cm,  # self.doc.bottomMargin,
            self.doc.height,
            self.doc.width,
            topPadding=0.8 * cm,
            id="landscape_frame",
        )
        self.doc.addPageTemplates(
            [
                PageTemplate(
                    id="portrait",
                    frames=self.portrait_frame,
                    onPage=partial(
                        # header_portrait, rptnumber="体检编号："+self.medinfo.tj_testid, ptname= "姓名："+self.patient.tj_pname
                        footer_portrait,
                        rptnumber="体检编号：" + self.medinfo.tj_testid,
                        ptname="姓名：" + self.patient.tj_pname,
                    ),
                    pagesize=portrait(A4),
                ),
                PageTemplate(
                    id="landscape",
                    onPage=partial(
                        header_landscape,
                        rptnumber="体检编号：" + self.medinfo.tj_testid,
                        ptname="姓名：" + self.patient.tj_pname,
                    ),
                    frames=self.landscape_frame,
                    pagesize=landscape(A4),
                ),
                # PageTemplate(id="header", onPage=header, frames=self.portrait_frame),
            ]
        )
        self.pagestyle = pagestyle
        self.graybg = "#888888"
        self.whitebg = "#ffffff"

    def add_front_page(self, checkall, corpinfo, dicts):

        orginfo = next(
            filter(
                lambda dict: dict.ss_typeid == constant.DictType.DictCustomer.value
                and dict.ss_pid == constant.CustomerInfo.CustomerName.value,
                dicts,
            ),
            None,
        )
        if orginfo is None:
            orgname = ""
        else:
            orgname = orginfo.ss_name

        style2 = getSampleStyleSheet()["Heading2"]
        style2.leading = 30
        style2.fontName = self.font_hei
        style2.fontSize = 35
        style2.alignment = TA_CENTER
        self.contents.append(Paragraph(orgname, style2))
        self.contents.append(Paragraph("职业健康检查表", style2))

        self.contents.append(Spacer(1, 3 * cm))

        # first page content
        style3 = getSampleStyleSheet()["Heading3"]
        style3.alignment = TA_LEFT
        style3.leading = 20
        style3.fontName = self.font_name
        style3.fontSize = 15
        style3.wordWrap = "CJK"  # 设置自动换行
        # style3.underlineOffset = 30
        # col_width = 200
        # poisionfactor = "南方的朋友都在用“大火收汁”戏称，梅雨一结束，高温就像开了辆没带刹车的跑车，从“水深”丝滑转场“火热”；开始适应湿热天气的北方朋友，则感觉像进了蒸笼一般，调侃自己就像个肉包子。"
        poisionfactor = self.medinfo.tj_poisionfactor
        jclb = constant.get_dict_name(
            dicts, constant.DictType.DictTesttype.value, self.medinfo.tj_testtype
        )
        empid = ""
        data = [
            ("单    位:", Paragraph(corpinfo.tj_corpname, style3)),
            ("检查类别:", Paragraph(jclb, style3)),
            ("姓    名:", Paragraph(self.patient.tj_pname, style3)),
            ("性    别:", Paragraph(constant.get_sex(self.patient.tj_psex), style3)),
            ("年    龄:", Paragraph(str(self.medinfo.tj_age), style3)),
            ("身份证号:", Paragraph(self.patient.tj_pidcard, style3)),
            ("部门工号:", Paragraph(empid, style3)),
            ("体检编号:", Paragraph(self.medinfo.tj_testid, style3)),
            ("接害工龄:", Paragraph(self.medinfo.tj_poisionage, style3)),
            ("毒害因素:", Paragraph(poisionfactor, style3)),
            (
                "总检日期:",
                Paragraph(constant.get_local_date(checkall.tj_checkdate), style3),
            ),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            # ("FONTSIZE", (0, 0), (-1, 0), 12),  # 第一行的字体大小
            ("FONTSIZE", (0, 0), (-1, -1), 15),  # 第二行到最后一行的字体大小
            # ("BACKGROUND", (0, 0), (-1, 0), "#d5dae6"),  # 设置第一行背景颜色
            # ("ALIGN", (0, 0), (-1, -1), "CENTER"),  # 第一行水平居中
            ("ALIGN", (0, 1), (-1, -1), "LEFT"),  # 第二行到最后一行左右左对齐
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            ("LINEBELOW", (1, 0), (-1, -1), 1, colors.black),
            ("BOTTOMPADDING", (0, 0), (-1, -1), 2),
            # ("TEXTCOLOR", (0, 0), (-1, -1), colors.darkslategray),  # 设置表格内文字颜色
            # ("GRID", (0, 0), (-1, -1), 0.5, colors.grey),  # 设置表格框线为grey色，线宽为0.5
            # ('SPAN', (0, 1), (0, 2)),  # 合并第一列二三行
            # ('SPAN', (0, 3), (0, 4)),  # 合并第一列三四行
            # ('SPAN', (0, 5), (0, 6)),  # 合并第一列五六行
            # ('SPAN', (0, 7), (0, 8)),  # 合并第一列五六行
        ]
        table = Table(
            data,
            colWidths=[3 * cm, 10 * cm],
            style=style,
            # rowHeights=0.9*cm,
            minRowHeights=[
                1.0 * cm,
                1.0 * cm,
                1.0 * cm,
                1.0 * cm,
                1.0 * cm,
                1.0 * cm,
                1.0 * cm,
                1.0 * cm,
                1.0 * cm,
                1.0 * cm,
                1.0 * cm,
            ],
        )

        self.contents.append(table)

        self.contents.append(Spacer(1, 4 * cm))
        # style2 = getSampleStyleSheet()["Heading2"]
        # style2.leading = 35
        # style2.fontName = self.font_hei
        # style2.fontSize = 25
        # style2.alignment = TA_CENTER#
        # self.contents.append(Paragraph('国家卫生健康委员会监制', style2))

    def add_detail_pages(
        self, summaries, checkitems, deptinfos, audiogramdetails, staffs, dicts
    ):
        style3_left = getSampleStyleSheet()["Normal"]
        style3_left.alignment = TA_LEFT
        style3_left.leading = 10
        style3_left.fontName = self.font_name
        style3_left.fontSize = 10
        style3_left.wordWrap = "CJK"  # 设置自动换行
        style3_left.underlineOffset = 20

        style3 = getSampleStyleSheet()["Normal"]
        style3.alignment = TA_CENTER
        style3.leading = 10
        style3.fontName = self.font_name
        style3.fontSize = 10
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.underlineOffset = 20

        deptinfos.sort(key=lambda x: x.tj_showorder, reverse=False)
        # for val in range(0,len(deptinfos),3):
        #     print("val is:",val)
        #     print(deptinfos[val].tj_deptname+"-"+deptinfos[val+1].tj_deptname+"-"+deptinfos[val+2].tj_deptname)

        question_dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictSysparm.value
            and dict.ss_pid == constant.SysParm.Symptom.value
        )
        symptom_item = question_dict_info.ss_name
        symptom_deptid = ""
        for item in checkitems:
            if item.tj_synid == symptom_item:
                symptom_deptid = item.tj_deptid
                break
            else:
                continue
        # print("合并症状的科室:",symptom_deptid)

        colwiths = [6.0 * cm, 6.7 * cm, 1.8 * cm, 2.5 * cm, 1.2 * cm]
        colwidths2 = [6.0 * cm, 12.2 * cm]
        for dept in deptinfos:
            sums = [
                summary for summary in summaries if summary.tj_deptid == dept.tj_deptid
            ]
            if len(sums) <= 0:
                continue
            sum = sums[0]

            deptresults = []
            for ca in checkitems:
                if ca.tj_deptid == sum.tj_deptid:
                    deptresults.append(ca)
            if dept.tj_deptid == constant.AUDIOGRAM_DEPTID:
                self.add_audiogram_page(sum, audiogramdetails, staffs, dept)
                self.contents.append(Spacer(1, 0.5 * cm))
                continue
            if dept.tj_depttype == constant.DepartType.Check.value:
                if dept.tj_deptid == symptom_deptid:
                    self.add_question_page(sum, deptresults, dept, staffs, dicts)
                else:
                    self.add_check_pages(sum, deptresults, dept, staffs, colwiths)
            elif dept.tj_depttype == constant.DepartType.Lab.value:
                self.add_labret_pages(sum, deptresults, dept, staffs, colwiths)
            elif dept.tj_depttype == constant.DepartType.Func.value:
                self.add_func_pages(sum, deptresults, dept, staffs, colwidths2)
            else:
                self.add_labret_pages(sum, deptresults, staffs, dept)
            self.add_spacer(0.5)
            # style3_left.fontSize = 12
            # style3_left.leading = 14
            # self.contents.append(
            #     Paragraph(
            #         "小结：" + sum.tj_summary.replace("\n", "<br/>\n"), style3_left
            #     )
            # )
            self.add_summary_data(sum.tj_summary)
            self.add_spacer(1)

    def add_check_pages(self, sum, deptresults, dept, staffs, colwiths):
        style3 = getSampleStyleSheet()["Normal"]
        # style3.alignment = TA_CENTER
        style3.leading = 10
        style3.fontName = self.font_name
        style3.fontSize = 10
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.underlineOffset = 20

        style3_right = getSampleStyleSheet()["Normal"]
        style3_right.alignment = TA_RIGHT
        style3_right.leading = 10
        style3_right.fontName = self.font_name
        style3_right.fontSize = 10
        style3_right.wordWrap = "CJK"  # 设置自动换行
        style3_right.underlineOffset = 20

        style3_center = getSampleStyleSheet()["Normal"]
        style3_center.alignment = TA_CENTER
        style3_center.leading = 10
        style3_center.fontName = self.font_name
        style3_center.fontSize = 10
        style3_center.wordWrap = "CJK"  # 设置自动换行
        style3_center.underlineOffset = 20

        bgcolor = self.graybg
        checkdoctor = Paragraph(sum.tj_checkdoctor, style3_right)

        sign = constant.get_staff_sign(staffs, sum.tj_checkdoctor)
        # print("sign is:",sign)
        if os.path.exists(sign):
            bgcolor = self.whitebg
            checkdoctor = Paragraph(
                '<img src={} width="70" height="25" valign="middle"/>'.format(sign)
            )

        data = [
            (
                Paragraph(dept.tj_deptname, style3),
                Paragraph("检查者：", style3_right),
                checkdoctor,
            ),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 13),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), bgcolor),  # 设置第一行背景颜色
            ("LINEABOVE", (0, 0), (-1, 0), 0.5, colors.black),
            ("LINEBELOW", (0, 0), (-1, 0), 0.5, colors.black),
            ("BOTTOMPADDING", (0, -1), (-1, -1), 8),
            ("TOPPADDING", (0, -1), (-1, -1), 8),
            ("LEFTPADDING", (0, -1), (-1, -1), 5),
            ("RIGHTPADDING", (0, -1), (-1, -1), 5),
            ("ALIGN", (0, 0), (0, 0), "LEFT"),  #
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
        ]
        table = Table(data, style=style)
        self.contents.append(table)
        deptresults.sort(key=lambda x: x.tj_showorder)
        data = [
            (
                Paragraph("项目名称", style3),
                Paragraph("检查结果", style3),
                Paragraph("单位", style3_center),
                Paragraph("参考范围", style3_center),
                Paragraph("提示", style3_center),
            ),
        ]
        for ca in deptresults:
            if ca.tj_combineflag == 1:
                continue
            data.append(
                (
                    Paragraph(ca.tj_itemname, style3),
                    Paragraph(ca.tj_result, style3),
                    Paragraph(ca.tj_itemunit, style3_center),
                    Paragraph(ca.tj_itemrange, style3_center),
                    Paragraph(ca.tj_abnormalshow, style3_center),
                )
            )

        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), "#dddddd"),  # 设置第一行背景颜色
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            ("BOTTOMPADDING", (0, 0), (-1, 0), 5),
            ("TOPPADDING", (0, 0), (-1, 0), 5),
        ]
        repeatedrow = 1
        # col_widths = [6.6*cm,6.7*cm,1.6*cm,2.0*cm,1.3*cm]
        table = Table(data, style=style, colWidths=colwiths, repeatRows=repeatedrow)
        data_len = len(data)
        for each in range(data_len):
            if each == 0:
                continue
            if each % 2 == 1:
                bg_color = colors.white
            else:
                bg_color = colors.whitesmoke
            table.setStyle(
                TableStyle([("BACKGROUND", (0, each), (-1, each), bg_color)])
            )
        self.add_spacer(0.01)
        self.contents.append(table)
        # self.add_spacer(0.5)
        # style3.fontSize = 12
        # self.contents.append(Paragraph("小结："+sum.tj_summary,style3))

    def add_labret_pages(self, sum, deptresults, dept, staffs, colwidths):
        style3 = getSampleStyleSheet()["Normal"]
        # style3.alignment = TA_CENTER
        style3.leading = 10
        style3.fontName = self.font_name
        style3.fontSize = 10
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.underlineOffset = 20

        style3_right = getSampleStyleSheet()["Normal"]
        style3_right.alignment = TA_RIGHT
        style3_right.leading = 10
        style3_right.fontName = self.font_name
        style3_right.fontSize = 10
        style3_right.wordWrap = "CJK"  # 设置自动换行
        style3_right.underlineOffset = 20

        style3_center = getSampleStyleSheet()["Normal"]
        style3_center.alignment = TA_CENTER
        style3_center.leading = 10
        style3_center.fontName = self.font_name
        style3_center.fontSize = 10
        style3_center.wordWrap = "CJK"  # 设置自动换行
        style3_center.underlineOffset = 20

        bgcolor = self.graybg
        checkdoctor = Paragraph(sum.tj_checkdoctor, style3_right)
        doctor = Paragraph(sum.tj_doctor, style3_right)

        sign = constant.get_staff_sign(staffs, sum.tj_checkdoctor)
        print("sign is:", sign)
        if os.path.exists(sign):
            bgcolor = self.whitebg
            checkdoctor = Paragraph(
                '<img src={} width="70" height="25" valign="middle"/>'.format(sign)
            )
        sign2 = constant.get_staff_sign(staffs, sum.tj_doctor)
        print("sign is:", sign2)
        if os.path.exists(sign2):
            bgcolor = self.whitebg
            doctor = Paragraph(
                '<img src={} width="70" height="25" valign="middle"/>'.format(sign2)
            )

        data = [
            (
                Paragraph(dept.tj_deptname, style3),
                Paragraph("审核者：", style3_right),
                checkdoctor,
                Paragraph("检查者：", style3_right),
                doctor,
            ),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 13),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), bgcolor),  # 设置第一行背景颜色
            ("LINEABOVE", (0, 0), (-1, 0), 0.5, colors.black),
            ("LINEBELOW", (0, 0), (-1, 0), 0.5, colors.black),
            ("BOTTOMPADDING", (0, -1), (-1, -1), 8),
            ("TOPPADDING", (0, -1), (-1, -1), 8),
            ("LEFTPADDING", (0, -1), (-1, -1), 5),
            ("RIGHTPADDING", (0, -1), (-1, -1), 5),
            ("ALIGN", (0, 0), (0, 0), "LEFT"),  #
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
        ]
        table = Table(
            data,
            style=style,
            colWidths=[8.2 * cm, 2.0 * cm, 3.0 * cm, 2.0 * cm, 3.0 * cm],
        )
        self.contents.append(table)
        deptresults.sort(key=lambda x: x.tj_showorder)
        data = [
            (
                Paragraph("项目名称", style3),
                Paragraph("检查结果", style3),
                Paragraph("单位", style3_center),
                Paragraph("参考范围", style3_center),
                Paragraph("提示", style3_center),
            ),
        ]
        for ca in deptresults:
            if ca.tj_combineflag == 1:
                continue
            data.append(
                (
                    Paragraph(ca.tj_itemname, style3),
                    Paragraph(ca.tj_result, style3),
                    Paragraph(ca.tj_itemunit, style3_center),
                    Paragraph(ca.tj_itemrange, style3_center),
                    Paragraph(ca.tj_abnormalshow, style3_center),
                )
            )

        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), "#dddddd"),  # 设置第一行背景颜色
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            ("BOTTOMPADDING", (0, 0), (-1, 0), 5),
            ("TOPPADDING", (0, 0), (-1, 0), 5),
            # ('LEFTPADDING', (0,0), (-1,-1), 3),
            # (
            #     "GRID",
            #     (0, 0),
            #     (-1, -1),
            #     0.5,
            #     colors.grey,
            # ),  # 设置表格框线为grey色，线宽为0.5
        ]
        repeatedrow = 1
        table = Table(data, style=style, colWidths=colwidths, repeatRows=repeatedrow)
        data_len = len(data)
        for each in range(data_len):
            if each == 0:
                continue
            if each % 2 == 0:
                bg_color = colors.white
            else:
                bg_color = colors.whitesmoke
            table.setStyle(
                TableStyle([("BACKGROUND", (0, each), (-1, each), bg_color)])
            )
        self.add_spacer(0.01)
        self.contents.append(table)
        # self.add_spacer(0.5)
        # style3.fontSize = 12
        # self.contents.append(Paragraph("小结："+sum.tj_summary,style3))

    def add_func_pages(self, sum, deptresults, dept, staffs, colwidths):
        style3 = getSampleStyleSheet()["Normal"]
        # style3.alignment = TA_CENTER
        style3.leading = 10
        style3.fontName = self.font_name
        style3.fontSize = 10
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.underlineOffset = 20

        style3_right = getSampleStyleSheet()["Normal"]
        style3_right.alignment = TA_LEFT
        style3_right.leading = 10
        style3_right.fontName = self.font_name
        style3_right.fontSize = 10
        style3_right.wordWrap = "CJK"  # 设置自动换行
        style3_right.underlineOffset = 20

        bgcolor = self.graybg
        checkdoctor = Paragraph(sum.tj_checkdoctor, style3_right)
        doctor = Paragraph(sum.tj_doctor, style3_right)

        sign = constant.get_staff_sign(staffs, sum.tj_checkdoctor)
        print("sign is:", sign)
        if os.path.exists(sign):
            bgcolor = self.whitebg
            checkdoctor = Paragraph(
                '<img src={} width="70" height="25" valign="middle"/>'.format(sign)
            )
        sign2 = constant.get_staff_sign(staffs, sum.tj_doctor)
        print("sign is:", sign2)
        if os.path.exists(sign2):
            bgcolor = self.whitebg
            doctor = Paragraph(
                '<img src={} width="70" height="25" valign="middle"/>'.format(sign2)
            )

        data = [
            (
                Paragraph(dept.tj_deptname, style3),
                Paragraph("审核者：", style3_right),
                checkdoctor,
                Paragraph("检查者：", style3_right),
                doctor,
            ),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 13),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), bgcolor),  # 设置第一行背景颜色
            ("LINEABOVE", (0, 0), (-1, 0), 0.5, colors.black),
            ("LINEBELOW", (0, 0), (-1, 0), 0.5, colors.black),
            ("BOTTOMPADDING", (0, -1), (-1, -1), 8),
            ("TOPPADDING", (0, -1), (-1, -1), 8),
            ("LEFTPADDING", (0, -1), (-1, -1), 5),
            ("RIGHTPADDING", (0, -1), (-1, -1), 5),
            ("ALIGN", (0, 0), (0, 0), "LEFT"),  #
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
        ]
        table = Table(
            data,
            style=style,
            colWidths=[8.2 * cm, 2.0 * cm, 3.0 * cm, 2.0 * cm, 3.0 * cm],
        )
        self.contents.append(table)
        deptresults.sort(key=lambda x: x.tj_showorder)
        data = [
            (
                Paragraph("项目名称", style3),
                Paragraph("检查结果", style3),
            ),
        ]
        for ca in deptresults:
            if ca.tj_combineflag == 1:
                continue
            data.append(
                (
                    Paragraph(ca.tj_itemname, style3),
                    Paragraph(ca.tj_result.replace("\n", "<br/>\n"), style3),
                )
            )

        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), "#dddddd"),  # 设置第一行背景颜色
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            ("BOTTOMPADDING", (0, 0), (-1, 0), 5),
            ("TOPPADDING", (0, 0), (-1, 0), 5),
        ]
        repeatedrow = 1
        table = Table(data, style=style, colWidths=colwidths, repeatRows=repeatedrow)
        data_len = len(data)
        for each in range(data_len):
            if each == 0:
                continue
            if each % 2 == 0:
                bg_color = colors.white
            else:
                bg_color = colors.whitesmoke
            table.setStyle(
                TableStyle([("BACKGROUND", (0, each), (-1, each), bg_color)])
            )
        self.add_spacer(0.01)
        self.contents.append(table)
        # self.add_spacer(0.5)
        # style3.fontSize = 12
        # self.contents.append(Paragraph("小结："+sum.tj_summary,style3))

    def add_question_page(self, sum, deptresults, dept, staffs, dicts):
        style3 = getSampleStyleSheet()["Normal"]
        # style3.alignment = TA_CENTER
        style3.leading = 10
        style3.fontName = self.font_name
        style3.fontSize = 10
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.underlineOffset = 20

        style3_right = getSampleStyleSheet()["Normal"]
        style3_right.alignment = TA_RIGHT
        style3_right.leading = 10
        style3_right.fontName = self.font_name
        style3_right.fontSize = 10
        style3_right.wordWrap = "CJK"  # 设置自动换行
        style3_right.underlineOffset = 20
        bgcolor = self.graybg
        checkdoctor = Paragraph(sum.tj_checkdoctor, style3_right)

        sign = constant.get_staff_sign(staffs, sum.tj_checkdoctor)
        print("sign is:", sign)
        if os.path.exists(sign):
            bgcolor = self.whitebg
            checkdoctor = Paragraph(
                '<img src={} width="70" height="25" valign="middle"/>'.format(sign)
            )

        # 添加科室信息
        data = [
            (
                Paragraph(dept.tj_deptname, style3),
                Paragraph("检查者：", style3_right),
                checkdoctor,
            ),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 13),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), bgcolor),  # 设置第一行背景颜色
            ("LINEABOVE", (0, 0), (-1, 0), 0.5, colors.black),
            ("LINEBELOW", (0, 0), (-1, 0), 0.6, colors.black),
            ("BOTTOMPADDING", (0, -1), (-1, -1), 8),
            ("TOPPADDING", (0, -1), (-1, -1), 8),
            ("LEFTPADDING", (0, -1), (-1, -1), 5),
            ("RIGHTPADDING", (0, -1), (-1, -1), 5),
            ("ALIGN", (0, 0), (0, 0), "LEFT"),  #
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
        ]
        table = Table(data, style=style)
        self.contents.append(table)
        # self.contents.append(Spacer(1, 0.1 * cm))
        # 获取项目内容
        deptresults.sort(key=lambda x: x.tj_showorder)
        data = [
            (
                Paragraph("检查项目", style3),
                Paragraph("结果", style3),
                Paragraph("检查项目", style3),
                Paragraph("结果", style3),
                Paragraph("检查项目", style3),
                Paragraph("结果", style3),
                Paragraph("检查项目", style3),
                Paragraph("结果", style3),
            ),
        ]
        # for ca in deptresults:
        #     data.append((Paragraph(ca.tj_itemname,style3_left),Paragraph(ca.tj_result,style3_left)))
        data_len = len(deptresults)
        for ca in range(0, data_len, 4):
            d1 = ""
            d2 = ""
            d3 = ""
            d4 = ""
            d5 = ""
            d6 = ""
            d7 = ""
            d8 = ""
            if ca < data_len:
                d1 = deptresults[ca].tj_itemname
                d2 = deptresults[ca].tj_result
            if ca + 1 < data_len:
                d3 = deptresults[ca + 1].tj_itemname
                d4 = deptresults[ca + 1].tj_result
            if ca + 2 < data_len:
                d5 = deptresults[ca + 2].tj_itemname
                d6 = deptresults[ca + 2].tj_result
            if ca + 3 < data_len:
                d7 = deptresults[ca + 3].tj_itemname
                d8 = deptresults[ca + 3].tj_result
            data.append(
                (
                    Paragraph(d1, style3),
                    Paragraph(d2, style3),
                    Paragraph(d3, style3),
                    Paragraph(d4, style3),
                    Paragraph(d5, style3),
                    Paragraph(d6, style3),
                    Paragraph(d7, style3),
                    Paragraph(d8, style3),
                )
            )

        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), "#dddddd"),  # 设置第一行背景颜色
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            ("BOTTOMPADDING", (0, 0), (-1, -1), 3),
            (
                "GRID",
                (0, 0),
                (-1, -1),
                0.5,
                colors.grey,
            ),  # 设置表格框线为grey色，线宽为0.5
        ]
        repeatedrow = 1

        table = Table(data, style=style, repeatRows=repeatedrow)
        data_len = len(data)
        for each in range(data_len):
            if each % 2 == 1:
                bg_color = colors.white
            else:
                bg_color = colors.whitesmoke
            table.setStyle(
                TableStyle([("BACKGROUND", (0, each), (-1, each), bg_color)])
            )
        self.add_spacer(0.02)
        self.contents.append(table)
        self.add_spacer(0.02)
        self.contents.append(
            Paragraph('*注："-" 表示无,"+" 表示阳性,"+-" 表示不确定*', style3)
        )
        # self.add_spacer(0.5)
        # style3.fontSize = 12
        # self.contents.append(Paragraph("小结："+sum.tj_summary,style3))

    def add_audiogram_page(self, sum, audiogramdetails, staffs, dept):
        style3 = getSampleStyleSheet()["Normal"]
        # style3.alignment = TA_CENTER
        style3.leading = 10
        style3.fontName = self.font_name
        style3.fontSize = 10
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.underlineOffset = 20

        style3_left = getSampleStyleSheet()["Normal"]
        style3_left.alignment = TA_LEFT
        style3_left.leading = 10
        style3_left.fontName = self.font_name
        style3_left.fontSize = 10
        style3_left.wordWrap = "CJK"  # 设置自动换行
        style3_left.underlineOffset = 20

        style3_right = getSampleStyleSheet()["Normal"]
        style3_right.alignment = TA_RIGHT
        style3_right.leading = 10
        style3_right.fontName = self.font_name
        style3_right.fontSize = 10
        style3_right.wordWrap = "CJK"  # 设置自动换行
        style3_right.underlineOffset = 20

        style3_center = getSampleStyleSheet()["Normal"]
        style3_center.alignment = TA_CENTER
        style3_center.leading = 10
        style3_center.fontName = self.font_name
        style3_center.fontSize = 10
        style3_center.wordWrap = "CJK"  # 设置自动换行
        style3_center.underlineOffset = 20
        bgcolor = self.graybg
        checkdoctor = Paragraph(sum.tj_checkdoctor, style3_right)

        sign = constant.get_staff_sign(staffs, sum.tj_checkdoctor)
        print("sign is:", sign)
        if os.path.exists(sign):
            bgcolor = self.whitebg
            checkdoctor = Paragraph(
                '<img src={} width="70" height="25" valign="middle"/>'.format(sign)
            )

        data = [
            (
                Paragraph(dept.tj_deptname, style3),
                Paragraph("检查者：", style3_right),
                checkdoctor,
            ),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 13),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), bgcolor),  # 设置第一行背景颜色
            ("LINEABOVE", (0, 0), (-1, 0), 0.5, colors.black),
            ("LINEBELOW", (0, -1), (-1, 0), 0.5, colors.black),
            ("BOTTOMPADDING", (0, -1), (-1, -1), 8),
            ("TOPPADDING", (0, -1), (-1, -1), 8),
            ("LEFTPADDING", (0, -1), (-1, -1), 5),
            ("RIGHTPADDING", (0, -1), (-1, -1), 5),
            ("ALIGN", (0, 0), (0, 0), "LEFT"),  #
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
        ]
        table = Table(data, style=style)
        self.contents.append(table)
        # self.add_spacer(0.3)
        audiogramdetails.sort(key=lambda x: x.tj_freq)

        adret = TjAudiogramresult(details=audiogramdetails)
        adret.compute_audiogram_result()
        # print("audiogrm result is:",adret.__dict__)

        # print("current directory:",os.path.abspath(sys.path[0]))

        adchart = AudioChart(audiogramdetails)
        right_char = adchart.drawchart(constant.Ear.Right.value)
        print("draw chart result is:", right_char)
        # if os.path.exists(right_char):
        rightchar = Paragraph(
            '<img src={} width="{}" height="200" valign="top"/>'.format(
                right_char, 270
            ),
        )
        left_char = adchart.drawchart(constant.Ear.Left.value)
        print("draw chart result is:", left_char)
        # if os.path.exists(left_char):
        leftcar = Paragraph(
            '<img src={} width="{}" height="200" valign="top"/>'.format(left_char, 270),
        )

        data = [
            (rightchar, leftcar),
        ]
        style = [
            ("ALIGN", (0, 0), (-1, -1), "CENTER"),
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
        ]
        table = Table(
            data,
            colWidths=[9 * cm, 9 * cm],
            style=style,
            minRowHeights=[2 * cm],
        )

        self.contents.append(table)
        self.add_spacer(6.8)

        # self.contents.append(Paragraph(right_char,style3))

        data2 = [
            (
                Paragraph("频率 (Hz)", style3_center),
                Paragraph("右耳", style3_center),
                Paragraph("", style3_center),
                Paragraph("左耳", style3_center),
                Paragraph("", style3_center),
            ),
            (
                Paragraph("", style3),
                Paragraph("气导 (db)", style3_center),
                Paragraph("骨导 (db)", style3_center),
                Paragraph("气导 (db)", style3_center),
                Paragraph("骨导 (db)", style3_center),
            ),
        ]

        freqs = set()
        for ca in audiogramdetails:
            freqs.add(ca.tj_freq)

        sorted_freqs = sorted(freqs)

        # print("Sorted sets are:",sorted_freqs)

        for freq in sorted_freqs:
            v1 = ""
            v2 = ""
            v3 = ""
            v4 = ""
            r = next(
                (
                    dt
                    for dt in audiogramdetails
                    if dt.tj_freq == freq
                    and dt.tj_ear == constant.Ear.Right.value
                    and dt.tj_adtype == constant.Trans.Air.value
                ),
                None,
            )
            if r is not None:
                v1 = str(r.tj_result) + "(" + str(r.tj_revise) + ")"
            r = next(
                (
                    dt
                    for dt in audiogramdetails
                    if dt.tj_freq == freq
                    and dt.tj_ear == constant.Ear.Right.value
                    and dt.tj_adtype == constant.Trans.Bone.value
                ),
                None,
            )
            if r is not None:
                v2 = str(r.tj_result) + "(" + str(r.tj_revise) + ")"
            r = next(
                (
                    dt
                    for dt in audiogramdetails
                    if dt.tj_freq == freq
                    and dt.tj_ear == constant.Ear.Left.value
                    and dt.tj_adtype == constant.Trans.Bone.value
                ),
                None,
            )
            if r is not None:
                v4 = str(r.tj_result) + "(" + str(r.tj_revise) + ")"
            r = next(
                (
                    dt
                    for dt in audiogramdetails
                    if dt.tj_freq == freq
                    and dt.tj_ear == constant.Ear.Left.value
                    and dt.tj_adtype == constant.Trans.Air.value
                ),
                None,
            )
            if r is not None:
                v3 = str(r.tj_result) + "(" + str(r.tj_revise) + ")"

            data2.append(
                (
                    Paragraph(str(freq), style3_center),
                    Paragraph(v1, style3_center),
                    Paragraph(v2, style3_center),
                    Paragraph(v3, style3_center),
                    Paragraph(v4, style3_center),
                )
            )
        # for ca in audiogramdetails:
        #     data.append((Paragraph(ca.tj_itemid,style3),Paragraph(ca.tj_itemid,style3),Paragraph(ca.tj_itemid,style3_center),Paragraph(ca.tj_itemid,style3_center),Paragraph(ca.tj_itemid,style3_center)))

        style2 = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            # ("BACKGROUND", (0, 0), (-1, 0), "#dddddd"),  # 设置第一行背景颜色
            ("ALIGN", (0, 0), (-1, -1), "CENTER"),  # 全部居中
            ("LINEABOVE", (0, 0), (-1, 0), 0.5, colors.black),
            ("LINEBELOW", (0, 0), (-1, 0), 0.5, colors.black),
            ("LINEBELOW", (0, 1), (-1, 1), 0.5, colors.black),
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            # ('BOTTOMPADDING', (0,0), (-1,-1), 5),
            # ('TOPPADDING', (0,0), (-1,-1), 5),
            ("SPAN", (0, 0), (0, 1)),  # 第一列合并前面2行
            ("SPAN", (1, 0), (2, 0)),  # 第一行合并2-3列
            ("SPAN", (3, 0), (4, 0)),  # 第一行合并1-2列
            # ('LEFTPADDING', (0,0), (-1,-1), 3),
            # (
            #     "GRID",
            #     (0, 0),
            #     (-1, -1),
            #     0.5,
            #     colors.grey,
            # ),  # 设置表格框线为grey色，线宽为0.5
        ]
        repeatedrow = 0
        table2 = Table(
            data2,
            style=style2,
            colWidths=[4.0 * cm, 3.5 * cm, 3.5 * cm, 3.5 * cm, 3.5 * cm],
            repeatRows=repeatedrow,
        )
        # data_len = len(data2)
        # for each in range(data_len):
        #     if each <= 1:
        #         continue
        #     if each % 2 == 0:
        #         bg_color = colors.white
        #     else:
        #         bg_color = colors.whitesmoke
        #     table2.setStyle(TableStyle([('BACKGROUND', (0, each), (-1, each), bg_color)]))

        self.contents.append(table2)
        self.add_spacer(0.2)

        # add result
        data3 = [
            (
                Paragraph("右听阈加权值：", style3_right),
                Paragraph(str(adret.tj_avgyty), style3_left),
                Paragraph("左听阈加权值：", style3_right),
                Paragraph(str(adret.tj_avgzty), style3_left),
                Paragraph("双耳高频听力：", style3_right),
                Paragraph(str(adret.tj_avgsgp), style3_left),
            ),
            (
                Paragraph("右语频听力：", style3_right),
                Paragraph(str(adret.tj_avgyyp), style3_left),
                Paragraph("左语频听力：", style3_right),
                Paragraph(str(adret.tj_avgzyp), style3_left),
                Paragraph("双耳语频听力：", style3_right),
                Paragraph(str(adret.tj_avgsyp), style3_left),
            ),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            # ("BACKGROUND", (0, 0), (-1, 0), "#dddddd"),  # 设置第一行背景颜色
            ("ALIGN", (0, 0), (-1, -1), "CENTER"),  # 全部居中
            ("LINEABOVE", (0, 0), (-1, 0), 0.5, colors.black),
            ("LINEBELOW", (0, 1), (-1, 1), 0.5, colors.black),
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            ("BOTTOMPADDING", (0, 0), (-1, -1), 5),
            ("TOPPADDING", (0, 0), (-1, -1), 5),
        ]
        table = Table(
            data3,
            colWidths=[3.1 * cm, 3 * cm, 3.0 * cm, 3 * cm, 3.1 * cm, 3 * cm],
            style=style,
            # minRowHeights=[1.5 * cm, 1.5 * cm],
        )
        self.contents.append(table)

        self.add_spacer(0.2)
        self.contents.append(Paragraph("*注：气导结果中，括号中的值为校正值*", style3))
        self.add_spacer(0.5)
        style3.fontSize = 12
        style3.leading = 14
        # self.contents.append(
        #     Paragraph("小结：" + sum.tj_summary.replace("\n", "<br/>"), style3)
        # )
        self.add_summary_data(sum.tj_summary)
        self.add_spacer(0.5)

    def add_summary_data(self, summary):
        style3 = getSampleStyleSheet()["Normal"]
        style3.alignment = TA_LEFT
        style3.leading = 12
        style3.fontName = self.font_name
        style3.fontSize = 12
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.underlineOffset = 20
        data = [
            (
                Paragraph("小结:", style3),
                Paragraph(summary.replace("\r\n", "<br/>").replace("\n", "<br/>"), style3),
            )
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 12),  # 第二行到最后一行的字体大小
            # ("BACKGROUND", (0, 0), (-1, 0), "#eeeeee"),  # 设置第一行背景颜色
            # ("ALIGN", (0, 0), (-1, -1), "CENTER"),  # 第一行水平居中
            ("ALIGN", (0, 0), (-1, -1), "LEFT"),  # 第二行到最后一行左右左对齐
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            # ("LINEBELOW", (1, 0), (-1, -1), 1, colors.black),
            # ("BOTTOMPADDING", (1, 0), (-1, -1), 5),
            # ("TEXTCOLOR", (0, 0), (-1, -1), colors.darkslategray),  # 设置表格内文字颜色
        ]
        table = Table(
            data,
            style=style,
            colWidths=[1.6 * cm, 17 * cm],
        )
        self.contents.append(table)

    def add_checkall_page(self, checkallinfo, dicts):
        style_normal = getSampleStyleSheet()["Normal"]
        # style_normal.alignment = TA_CENTER
        style_normal.leading = 10
        style_normal.fontName = self.font_name
        style_normal.fontSize = 12
        style_normal.wordWrap = "CJK"  # 设置自动换行
        style_normal.underlineOffset = 20

        style_left = getSampleStyleSheet()["Normal"]
        style_left.alignment = TA_LEFT
        style_left.leading = 13
        style_left.fontName = self.font_name
        style_left.fontSize = 12
        style_left.wordWrap = "CJK"  # 设置自动换行
        style_left.underlineOffset = 20
        style_left.spaceBefore = 2

        ocuresult = constant.get_dict_name(
            dicts, constant.DictType.DictCheckall.value, checkallinfo.tj_typeid
        )

        if checkallinfo.tj_ocuabnormal == "":
            ocuabnormal = "无"
        else:
            ocuabnormal = checkallinfo.tj_ocuabnormal

        if checkallinfo.tj_othabnormal == "" or checkallinfo.tj_othabnormal == "无":
            othresult = "本次健康检查所检项目未见异常"
        else:
            othresult = checkallinfo.tj_othabnormal
        othsuggestion = checkallinfo.tj_othsuggestion
        data = [
            (Paragraph("职业健康检查结论", style_normal),),
            (
                Paragraph("检查结论：", style_normal),
                Paragraph(ocuresult, style_left),
            ),
            (
                Paragraph("异常指标：", style_normal),
                Paragraph(ocuabnormal.replace("\n", "<br/>"), style_left),
            ),
            (
                Paragraph("处理意见：", style_normal),
                Paragraph(
                    checkallinfo.tj_ocuopinion.replace("\n", "<br/>"), style_left
                ),
            ),
            (Paragraph("其他检查结论", style_normal),),
            (
                Paragraph("异常指标：", style_normal),
                Paragraph(othresult.replace("\n", "<br/>"), style_left),
            ),
            (
                Paragraph("医学建议：", style_normal),
                Paragraph(
                    othsuggestion.replace("\n", "<br/>"),
                    style_left,
                ),
            ),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), self.graybg),  # 设置第一行背景颜色
            ("BACKGROUND", (0, 4), (-1, 4), self.graybg),  # 设置第5行背景颜色
            ("ALIGN", (0, 0), (-1, -1), "CENTER"),  # 全部居中
            ("LINEABOVE", (0, 0), (-1, 0), 0.5, colors.black),
            ("LINEBELOW", (0, 0), (-1, 0), 0.8, colors.black),
            ("LINEABOVE", (0, 4), (-1, 4), 0.5, colors.black),
            ("LINEBELOW", (0, 4), (-1, 4), 0.8, colors.black),
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            ("BOTTOMPADDING", (0, 0), (-1, -1), 5),
            ("TOPPADDING", (0, 0), (-1, -1), 5),
            ("BOTTOMPADDING", (0, 3), (-1, 3), 8),
            ("SPAN", (0, 0), (-1, 0)),
            ("SPAN", (0, 4), (-1, 4)),
        ]
        table = Table(
            data,
            colWidths=[2.6 * cm, 15 * cm],
            style=style,
            minRowHeights=[
                1.1 * cm,
                0.8 * cm,
                0.8 * cm,
                0.8 * cm,
                1.1 * cm,
                0.8 * cm,
                0.8 * cm,
            ],
        )

        self.contents.append(table)
        self.add_spacer(1.0)

    def add_sign_page(self, checkallinfo, dicts):
        style_normal = getSampleStyleSheet()["Normal"]
        # style_normal.alignment = TA_CENTER
        style_normal.leading = 10
        style_normal.fontName = self.font_name
        style_normal.fontSize = 12
        style_normal.wordWrap = "CJK"  # 设置自动换行
        style_normal.underlineOffset = 20

        style_left = getSampleStyleSheet()["Normal"]
        style_left.alignment = TA_LEFT
        style_left.leading = 10
        style_left.fontName = self.font_name
        style_left.fontSize = 12
        style_left.wordWrap = "CJK"  # 设置自动换行
        style_left.underlineOffset = 20
        esign_dict = constant.get_dict(
            dicts, constant.DictType.DictSysparm.value, constant.SysParm.ZJeSign.value
        )
        checkdoctor = ""
        if esign_dict is not None:
            if esign_dict.ss_short == "1":  # 打印签名
                signimg = constant.SIGN_DIR + esign_dict.ss_name
                if os.path.exists(signimg):
                    checkdoctor = (
                        '<img src={} width="{}" height="{}" valign="middle"/>'.format(
                            signimg, 70, 25
                        )
                    )
                else:
                    checkdoctor = ""
            elif esign_dict.ss_short == "2":
                checkdoctor = esign_dict.ss_name
            else:
                checkdoctor = ""
        checkalldate = constant.get_local_chn_date(checkallinfo.tj_checkdate)
        shenhedoctor = ""
        shesign = constant.get_dict(
            dicts, constant.DictType.DictSysparm.value, constant.SysParm.SHYiSheng.value
        )
        # print("shen he :",shesign.ss_short)
        if shesign is not None:
            if shesign.ss_short == "1":
                shsignimg = constant.SIGN_DIR + shesign.ss_name
                if os.path.exists(shsignimg):
                    shenhedoctor = (
                        '<img src={} width="{}" height="{}" valign="middle"/>'.format(
                            shsignimg, 70, 25
                        )
                    )
                else:
                    shenhedoctor = ""
            elif shesign.ss_short == "2":
                shenhedoctor = shesign.ss_name
            else:
                shenhedoctor = ""
        else:
            print("cant find shen he yi sheng")
        orga = constant.get_dict_name(
            dicts,
            constant.DictType.DictCustomer.value,
            constant.CustomerInfo.CustomerName.value,
        )

        stampledict = constant.get_dict(
            dicts, constant.DictType.DictSysparm.value, constant.SysParm.Stample.value
        )
        if stampledict is not None:
            if stampledict.ss_short == "1" or stampledict.ss_short == "3":
                stimg = constant.SIGN_DIR + stampledict.ss_name
                if os.path.exists(stimg):
                    img = Image.open(stimg)
                    ratio = img.width / img.height
                    width = 100 * ratio
                    stample = (
                        '<img src={} width="{}" height="{}" valign="middle"/>'.format(
                            stimg, width, 100
                        )
                    )
                else:
                    stample = ""
            else:
                stample = ""

        data = [
            (
                Paragraph("主检医师：", style_normal),
                Paragraph(checkdoctor),
                Paragraph("日      期：", style_normal),
                Paragraph(checkalldate, style_left),
            ),
            (
                Paragraph("审 核 人：", style_normal),
                Paragraph(shenhedoctor),
                Paragraph("日      期：", style_normal),
                Paragraph(checkalldate, style_left),
            ),
            (
                Paragraph("体检单位：", style_normal),
                Paragraph(orga, style_normal),
                Paragraph(stample),
            ),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 12),  # 第二行到最后一行的字体大小
            # ("BACKGROUND", (0, 0), (-1, 0), "#888888"),  # 设置第一行背景颜色
            # ("BACKGROUND", (0, 4), (-1, 4), "#888888"),  # 设置第5行背景颜色
            ("ALIGN", (0, 0), (-1, -1), "CENTER"),  # 全部居中
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            # ('BOTTOMPADDING', (0,0), (-1,-1), 5),
            # ('TOPPADDING', (0,0), (-1,-1), 5),
            # ('BOTTOMPADDING', (0,3), (-1,3), 8),
            # ("SPAN", (0, 0), (-1, 0)),
            # ("SPAN", (0, 4), (-1, 4)),
        ]
        table = Table(
            data,
            colWidths=[2.6 * cm, 7.4 * cm, 2.3 * cm, 6 * cm],
            style=style,
            minRowHeights=[1.5 * cm, 1.5 * cm, 1.5 * cm],
        )

        self.contents.append(table)
        self.add_spacer(1.0)

    def add_info_page(self, dicts):
        style_normal = getSampleStyleSheet()["Normal"]
        # style_normal.alignment = TA_CENTER
        style_normal.leading = 10
        style_normal.fontName = self.font_name
        style_normal.fontSize = 12
        style_normal.wordWrap = "CJK"  # 设置自动换行
        style_normal.underlineOffset = 20

        style_left = getSampleStyleSheet()["Normal"]
        style_left.alignment = TA_LEFT
        style_left.leading = 10
        style_left.fontName = self.font_name
        style_left.fontSize = 12
        style_left.wordWrap = "CJK"  # 设置自动换行
        style_left.underlineOffset = 20

        address = constant.get_dict_name(
            dicts,
            constant.DictType.DictCustomer.value,
            constant.CustomerInfo.CustomerAddress.value,
        )
        postcode = constant.get_dict_name(
            dicts,
            constant.DictType.DictCustomer.value,
            constant.CustomerInfo.CustomerPostcode.value,
        )
        contactor = constant.get_dict_name(
            dicts,
            constant.DictType.DictCustomer.value,
            constant.CustomerInfo.CustomerContactor.value,
        )
        phone = constant.get_dict_name(
            dicts,
            constant.DictType.DictCustomer.value,
            constant.CustomerInfo.CustomerPhone.value,
        )
        email = constant.get_dict_name(
            dicts,
            constant.DictType.DictCustomer.value,
            constant.CustomerInfo.CustomerEmail.value,
        )
        weburl = constant.get_dict_name(
            dicts,
            constant.DictType.DictCustomer.value,
            constant.CustomerInfo.CustomerUrl.value,
        )

        data = [
            (
                ("地    址："),
                Paragraph(address, style_left),
                ("邮    编："),
                Paragraph(postcode, style_left),
            ),
            (
                ("联 系 人："),
                Paragraph(contactor, style_left),
                ("电    话："),
                Paragraph(phone, style_left),
            ),
            (
                ("邮    箱："),
                Paragraph(email, style_left),
                ("网    址："),
                Paragraph(weburl, style_left),
            ),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 11),  # 第二行到最后一行的字体大小
            # ("BACKGROUND", (0, 0), (-1, 0), "#888888"),  # 设置第一行背景颜色
            # ("BACKGROUND", (0, 4), (-1, 4), "#888888"),  # 设置第5行背景颜色
            ("ALIGN", (0, 0), (-1, -1), "CENTER"),  # 全部居中
            ("LINEABOVE", (0, 0), (-1, 0), 0.5, colors.black),
            ("LINEBELOW", (0, 2), (-1, 2), 0.5, colors.black),
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
        ]
        table = Table(
            data,
            colWidths=[2.6 * cm, 8 * cm, 2.4 * cm, 5 * cm],
            style=style,
            minRowHeights=[1 * cm, 1 * cm, 1 * cm],
        )

        self.contents.append(table)
        info = constant.get_dict_name(
            dicts,
            constant.DictType.DictCustomer.value,
            constant.CustomerInfo.CustomerInfotip.value,
        )
        if info != "":
            self.add_spacer(0.5)
            data = [
                ((""), ("    " + info)),
            ]
            style = [
                ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
                ("FONTSIZE", (0, 0), (-1, -1), 11),  # 第二行到最后一行的字体大小
                ("BOTTOMPADDING", (0, 0), (-1, -1), 5),
                ("TOPPADDING", (0, 0), (-1, -1), 5),
                ("ALIGN", (0, 0), (-1, -1), "LEFT"),  #
                # ('LINEABOVE', (0, 0), (-1,0), 0.5, colors.black),
                # ("LINEBELOW", (0, 2), (-1, 2), 0.5, colors.black),
                # ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            ]
            table = Table(
                data,
                colWidths=[0.2 * cm, 18 * cm],
                style=style,
            )
            self.contents.append(table)

        self.add_spacer(1.1)

    def getImage(path):
        return OffsetImage(plt.imread(path, format="png"), zoom=0.1)

    def on_first_page(self, canvas, doc):
        canvas.saveState()

        textobject = canvas.beginText()
        textobject.setTextOrigin(inch, 2.5 * inch)
        textobject.setFont("Helvetica-Oblique", 14)
        # for line in lyrics:
        # textobject.textLine("caseSensitive")
        textobject.setFillGray(0.4)
        textobject.textLines("")
        canvas.drawText(textobject)

        canvas.restoreState()

    def on_later_pages(self, canvas, doc):
        canvas.saveState()
        canvas.setFont(self.font_name, 13)
        # canvas.drawCentredString(A4[0] // 2, 29 * cm, "体检编号")
        canvas.setFont(self.font_name, 12)
        canvas.drawString(
            doc.leftMargin, 28.3 * cm, "体检编号:" + self.medinfo.tj_testid
        )

        canvas.setLineWidth(0.5)
        canvas.line(doc.leftMargin, 28 * cm, A4[0] - doc.rightMargin, 28 * cm)

        canvas.restoreState()
        # self.contents.append(Spacer(1, 0.5 * cm))

    def add_page_break(self):
        self.contents.append(PageBreak())

    def add_spacer(self, size):
        self.contents.append(Spacer(1, size * cm))

    def build(self):
        self.doc.build(
            self.contents,
            canvasmaker=PersonalNumberedCanvas,
        )


def header_portrait(canvas, doc, rptnumber, ptname):
    if doc.page == 1:
        return
    canvas.saveState()
    # canvas.setFont("SimSun", 13)
    # canvas.drawCentredString(A4[0] // 2, 29 * cm, "职 业 健 康 检 查 报 告 书")
    canvas.setFont("SimSun", 12)
    canvas.drawString(doc.leftMargin, 28.2 * cm, rptnumber)
    canvas.drawString(doc.leftMargin + 8 * cm, 28.2 * cm, ptname)
    canvas.setLineWidth(0.5)
    canvas.line(doc.leftMargin, 28 * cm, A4[0] - doc.rightMargin, 28 * cm)
    canvas.restoreState()


def footer_portrait(canvas, doc, rptnumber, ptname):
    if doc.page == 1:
        return
    canvas.saveState()
    # canvas.setFont("SimSun", 13)
    # canvas.drawCentredString(A4[0] // 2, 29 * cm, "职 业 健 康 检 查 报 告 书")
    canvas.setFont("SimSun", 12)
    canvas.drawString(doc.leftMargin, 0.5 * cm, rptnumber)
    canvas.drawString(doc.leftMargin + 8 * cm, 0.5 * cm, ptname)
    canvas.setLineWidth(0.5)
    canvas.line(doc.leftMargin, 1 * cm, A4[0] - doc.rightMargin, 1 * cm)
    canvas.restoreState()


def header_landscape(canvas, doc, rptnumber, ptname):
    # print("start to draw header:", rptnumber)
    canvas.saveState()
    # canvas.setFont("SimSun", 13)
    # canvas.drawCentredString(A4[1] // 2, 20 * cm, "职 业 健 康 检 查 报 告 书")
    canvas.setFont("SimSun", 12)
    canvas.drawString(doc.leftMargin, 19.5 * cm, rptnumber)
    canvas.drawString(doc.leftMargin + 18 * cm, 19.5 * cm, ptname)
    canvas.setLineWidth(0.5)
    canvas.line(doc.leftMargin, 19.3 * cm, A4[1] - doc.rightMargin, 19.3 * cm)
    canvas.restoreState()
