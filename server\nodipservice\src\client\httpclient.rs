use crate::common::constant::CODE_HTTP_OK;

use super::response::*;
use anyhow::{anyhow, Result};
use mpart_async::client::MultipartRequest;
use reqwest::{header::CONTENT_TYPE, Body, Client, Method};
use std::fmt::Debug;
use tracing::*;
pub struct HttpClient;

impl HttpClient {
  pub async fn send_http_get_request<T: serde::Serialize + Debug, U: for<'a> serde::Deserialize<'a> + Debug>(orginfo: &str, url: &str, dto: &Option<T>) -> Result<U> {
    let mut dto_str = "".to_string();
    if dto.is_some() {
      let ret = serde_json::to_string(&dto);
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      dto_str = ret.unwrap();
    }
    let ret = HttpClient::send_request(Method::GET, orginfo, url, &dto_str).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let result = ret.unwrap();
    // info!("Result is:{}", &result);
    let ret = serde_json::from_str::<ResponseBody<DataBody<U>>>(&result);
    if ret.as_ref().is_err() {
      error!("serde json error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      // return Err(anyhow!("解析返回数据错误"));
    }
    let resp_value = ret.unwrap();
    if resp_value.code != CODE_HTTP_OK {
      return Err(anyhow!("{}", resp_value.message));
    }
    Ok(resp_value.data.data)
  }

  pub async fn send_http_post_request<T: serde::Serialize + Debug, U: for<'a> serde::Deserialize<'a> + Debug>(orginfo: &str, url: &str, dto: &Option<T>) -> Result<U> {
    let mut dto_str = "".to_string();
    if dto.is_some() {
      let ret = serde_json::to_string(&dto);
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      dto_str = ret.unwrap();
    }
    // info!("start to send request");
    let ret = HttpClient::send_request(Method::POST, orginfo, url, &dto_str).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let result = ret.unwrap();

    let ret = serde_json::from_str::<ResponseBody<DataBody<U>>>(&result);
    if ret.as_ref().is_err() {
      error!("serde json error:{}", ret.as_ref().unwrap_err().to_string());
      let newret = serde_json::from_str::<ResponseBody<String>>(&result);
      return Err(anyhow!("{}", newret.unwrap_or_default().message.to_string()));
    }
    let resp_value = ret.unwrap();
    if resp_value.code != CODE_HTTP_OK {
      return Err(anyhow!("{}", resp_value.message));
    }
    Ok(resp_value.data.data)
  }

  pub async fn send_http_put_request<T, U>(orginfo: &str, url: &str, dto: &Option<T>) -> Result<U>
  where
    T: serde::Serialize,
    U: for<'a> serde::Deserialize<'a> + Debug,
  {
    let mut dto_str = "".to_string();
    if dto.is_some() {
      let ret = serde_json::to_string(&dto);
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      dto_str = ret.unwrap();
    }
    let ret = HttpClient::send_request(Method::PUT, orginfo, url, &dto_str).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let result = ret.unwrap();

    let ret = serde_json::from_str::<ResponseBody<DataBody<U>>>(&result);
    if ret.as_ref().is_err() {
      error!("serde json error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let resp_value = ret.unwrap();
    if resp_value.code != CODE_HTTP_OK {
      return Err(anyhow!("{}", resp_value.message));
    }
    Ok(resp_value.data.data)
  }

  pub async fn send_http_delete_request<T, U>(orginfo: &str, url: &str, dto: &Option<T>) -> Result<U>
  where
    T: serde::Serialize,
    U: for<'a> serde::Deserialize<'a> + Debug,
  {
    let mut dto_str = "".to_string();
    if dto.is_some() {
      let ret = serde_json::to_string(&dto);
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      dto_str = ret.unwrap();
    }
    let ret = HttpClient::send_request(Method::DELETE, orginfo, url, &dto_str).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let result = ret.unwrap();
    // info!("result is:{}", &result);

    let ret = serde_json::from_str::<ResponseBody<DataBody<U>>>(&result);
    if ret.as_ref().is_err() {
      error!("serde json error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      // return Err(anyhow!("解析返回数据错误"));
    }
    let resp_value = ret.unwrap();
    if resp_value.code != CODE_HTTP_OK {
      // error!("query error:{:#?}", &resp_value);
      return Err(anyhow!("{}", resp_value.message));
    }
    Ok(resp_value.data.data)
  }

  pub async fn send_post_request(orginfo: &str, url: &str, dto: &str) -> Result<String> {
    HttpClient::send_request(Method::POST, orginfo, url, dto).await
  }

  pub async fn send_request(method: Method, orginfo: &str, url: &str, dto: &str) -> Result<String> {
    if url.is_empty() {
      return Ok("".to_string());
    }
    // let server_uri = format!("{}{}", server, uri);
    // info!("start to build client......");
    let mut reqbuilder = Client::new()
      .request(method, url)
      .timeout(std::time::Duration::from_secs(60))
      .header(CONTENT_TYPE, "application/json; charset=utf-8")
      .header("orginfo", orginfo.to_owned());
    if !dto.is_empty() {
      reqbuilder = reqbuilder.body(dto.to_owned());
    }
    info!("client is builded ......,start to send to server:{}", url);
    let ret = reqbuilder.send().await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = ret.unwrap().text().await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let response_text = ret.unwrap();
    info!("response text is:{:?}", &response_text);
    Ok(response_text)
  }

  pub async fn send_upload_request(filename: &str, testid: &str, orginfo: &str, url: &str) -> Result<String> {
    if url.is_empty() {
      return Ok("".to_string());
    }
    let mut mpart = MultipartRequest::default();
    mpart.add_file(testid.to_string(), filename);

    // let server_uri = format!("{}{}", server, uri);
    // info!("upload server uri is:{}", &server_uri);
    let resp = Client::new()
      .post(url)
      .timeout(std::time::Duration::from_secs(600))
      .header("orginfo", orginfo.to_owned())
      .header(CONTENT_TYPE, format!("multipart/form-data; boundary={}", mpart.get_boundary()))
      .body(Body::wrap_stream(mpart))
      .send()
      .await;
    if resp.is_err() {
      return Err(anyhow!("{}", resp.err().unwrap().to_string()));
    }
    let ret = resp.unwrap().text().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let response_text = ret.unwrap();
    Ok(response_text)
  }
}
