[database]
uri = "mysql://hzone:FKsX3Gde3G4r!@************:5506/nodip_new"

[application]
apphost = "0.0.0.0"
appport = 8002
areaprovince = "33"
uploader = "zj"                       # 上报平台 默认是zj，gj：国家平台
pacsversion = "v2"                    #pacs对接 v1:没有sfyc字段 v2：有sfyc字段
uploaddir = "./upload"                #体检客户端上传的报告保存目录
reportdir = "./reports"               #
photodir = "./photoes"                #体检客户端上传的照片保存目录
filedir = "./files"
signdir = "./sign"
proxyserver = "http://127.0.0.1:8012" # 
# proxyserver = "http://*************:8012"
cdcserver = "http://127.0.0.1:8003"    # http://127.0.0.1:8003
ttl = 30                               #缓存有效时间，单位：分
splitter = "， 、"                       #毒害因素分隔符
autorecvlis = 0                        #自动接收lis数据 0:no 1:yes
autorecvpacs = 0                       #自动接收pacs数据 0:no 1:yes
lisimporttype = 1                      #1 从lis接收 2：从导入接收 
lisego = 0                             #lis是否调用ego 0:不调用 1：调用
pacsego = 0                            #pacs是否调用ego 0:不调用 1：调用
commander = "./report.exe"             #生成pdf报告的命令，
dserver = "http://127.0.0.1:8030"      # http://127.0.0.1:8003
reportserver = "http://127.0.0.1:8000"

[polar]
orgid = 6975302616515485691
password = "214aea47fa576f3a1f8209e18de66b822da12f43"
server = "http://127.0.0.1:8013/api"
# server="https://wechat.ijiusong.com/api"
pubkey = "./key/pub.key"

[cdcupload]
creditcode = "913303214111111"
agentid = "pwc0LtEF4"
appkey = "15fe1c1cff16d1cd70cF"
appsecret = "83215e658d33639928e1c496afb1aa44d76d12fa41662de133f63cfc49d560fb"
issubmit = 1
rpturl = "https://oapi.zyws.net/api/physical/report"
filerul = "https://oapi.zyws.net/api/physical/reportFile"

[external]
exttype = "zjsrmyy"                 #dian,layy,jhts,wyhs,zjsrmyy
serverurl = "http://ehis.21ber.com" #"http://127.0.0.1:8015/apiext/v1/external"
appsn = ""

# exttype = ""   #dian,layy,jhts
# serverurl = ""
# appsn = ""

[audiogram]
dburi = ""
#1：oracle 2：sqlserver 3:mysql
dbtype = 3

[extlis]
[extlis.default]
#数据库类型 1：oracle 2：sqlserver 3:mysql
dbtype = 3
uri = ""
username = "nodip_saas"
password = "saas12345!A"
version = 1              #1:sfyc是整型， 2：sfyc是字符型

[extpacs]
[extpacs.default]
#数据库类型 1：oracle 2：sqlserver 3:mysql
dbtype = 3
# sqlserver uri="server=tcp:***********,1433;Database=lisdb;User=sa;Password=********;encrypt=false;trustServerCertificate=true;";
# mysql uri="mysql://root:123456@localhost:3306/test"
# oracle uri = "//***************/hisrisexchange"
uri = "mysql://hzone:FKsX3Gde3G4r!@************:5506/nodip_new"
username = "hzone"
password = "FKsX3Gde3G4r!"
version = 1                                                     # 1:sfyc是整型， 2：sfyc是字符型
