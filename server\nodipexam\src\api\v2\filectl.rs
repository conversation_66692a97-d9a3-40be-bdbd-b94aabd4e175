use std::sync::Arc;

use crate::api::httpresponse::*;
use crate::{common::filesvc::FileSvc, config::settings::Settings};
use axum::extract::Path;
use axum::response::IntoResponse;
use axum::{extract::multipart::Multipart, Extension, Json};
// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
// use http_body_util::StreamBody;
use hyper::StatusCode;
use nodipservice::dto::KeyDto;
use serde_json::Value;
use tokio::sync::RwLock;
use utility::uidservice::UidgenService;
use tracing::*;

// *************************
// pub async fn upload_file(Extension(config): Extension<Arc<Settings>>, multipart: Multipart) -> Json<Value> {
//   info!("start to upload file to server......");
//   let ret = FileSvc::do_upload("", &config.application.uploaddir, multipart).await;
//   if ret.as_ref().is_err() {
//     error!("upload file error:{}", ret.as_ref().err().unwrap().to_string());
//     return response_json_error(&ret.err().unwrap().to_string());
//   }
//   let res = ResponseBody::new(HttpCode::OK as i32, "OK", "");
//   Json(serde_json::json!(res))
// }

pub async fn upload_photo(Extension(config): Extension<Arc<Settings>>, multipart: Multipart) -> Json<Value> {
  // info!("start to upload file to server......");
  let ret = FileSvc::do_upload(&config.application.photodir, multipart).await;
  if ret.as_ref().is_err() {
    error!("upload file error:{}", ret.as_ref().err().unwrap().to_string());
    return response_json_error(&ret.err().unwrap().to_string());
  }
  response_json_ok(ret.unwrap().as_str())
  // let res = ResponseBody::new(HttpCode::OK as i32, "OK", "");
  // Json(serde_json::json!(res))
}

pub async fn upload_sign(
  Extension(config): Extension<Arc<Settings>>,
  Extension(db): Extension<Arc<DbConnection>>,
  Extension(uid): Extension<Arc<RwLock<UidgenService>>>,
  multipart: Multipart,
) -> Json<Value> {
  info!("start to upload sign to server......");
  let mut idgen = uid.write().await;
  let ret = FileSvc::upload_sign(&config, &db, &mut idgen, multipart).await;
  if ret.as_ref().is_err() {
    error!("upload sign error:{}", ret.as_ref().err().unwrap().to_string());
    return response_json_error(&ret.err().unwrap().to_string());
  }
  info!("upload sign ret:{:#?}", &ret);
  response_json_ok(ret.unwrap().as_str())
}

pub async fn upload_file(Extension(config): Extension<Arc<Settings>>, multipart: Multipart) -> Json<Value> {
  info!("start to upload files to server......");
  let ret = FileSvc::do_upload(&config.application.filedir, multipart).await;
  if ret.as_ref().is_err() {
    error!("upload sign error:{}", ret.as_ref().err().unwrap().to_string());
    return response_json_error(&ret.err().unwrap().to_string());
  }
  response_json_ok(ret.unwrap().as_str())
}

pub async fn download_sign(Extension(config): Extension<Arc<Settings>>, Json(dto): Json<KeyDto>) -> impl IntoResponse {
  // info!("start to download sign from server......");

  let ret = FileSvc::do_download(&format!("{}/{}", config.application.signdir, dto.key)).await;
  if ret.as_ref().is_err() {
    error!("{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }
  Ok(ret.unwrap())
  // ret.unwrap().into()
}

pub async fn download_file(Extension(config): Extension<Arc<Settings>>, Path(path): Path<String>) -> impl IntoResponse {
  // info!("Path is:{:?}", &path);

  let ret = FileSvc::do_download(&format!("{}/{}", config.application.filedir, path)).await;
  if ret.as_ref().is_err() {
    error!("download file error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }

  Ok(ret.unwrap())
}

pub async fn download_photo(Extension(config): Extension<Arc<Settings>>, Json(dto): Json<KeyDto>) -> impl IntoResponse {
  // info!("key is:{:?}", &dto);

  let ret = FileSvc::do_download(&format!("{}/{}", config.application.photodir, dto.key)).await;
  if ret.as_ref().is_err() {
    error!("download file error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }

  Ok(ret.unwrap())
}
