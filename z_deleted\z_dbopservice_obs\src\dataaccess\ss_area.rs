use anyhow::{anyhow, Result};
use rbatis::crud;
// use rbs::value::map::ValueMap;
// use rbs::Value;
use serde::{Deserialize, Serialize};
#[derive(<PERSON><PERSON>, <PERSON><PERSON>ult, Debug, PartialEq, Serialize, Deserialize)]
pub struct SsArea {
  pub id: i64,
  pub area_code: String,
  pub area_name: String,
  pub area_fullname: String,
  pub area_level: i8,
  pub area_pcode: String,
}
crud!(SsArea {}, "ss_area");
rbatis::impl_select!(SsArea{query(code:&str)->Option => "`where area_code = #{code} `"});
rbatis::impl_select!(SsArea{query_by_filter(code:&str) => "`where area_code like #{code+'%'} `"});

impl SsArea {
  pub async fn query_many(rb: &mut rbatis::RBatis, codes: &str) -> Result<Vec<SsArea>> {
    let pre_areas: Vec<&str> = codes.split(",").collect();

    let mut areas: Vec<SsArea> = Vec::new();
    for val in pre_areas.into_iter() {
      let ret = SsArea::query_by_filter(rb, val).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      areas.extend(ret.unwrap().into_iter());
    }
    Ok(areas)
    // let mut logic = ValueMap::new();
    // logic.insert("id > ? and (".into(), Value::I64(0));
    // // logic.insert(k, v)
    // for val in codes.iter() {
    //   logic.insert("and id != ".into(), Value::String("222".to_string()));
    // }
    // Ok(vec![])
    // impled!()
  }
}
