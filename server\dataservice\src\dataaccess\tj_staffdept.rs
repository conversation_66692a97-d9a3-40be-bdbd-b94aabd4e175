use crate::entities::{prelude::*, tj_staffdept};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjStaffdept {
  pub async fn query_many(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<Vec<TjStaffdept>> {
    // let ret = NxJobinfoEntity::find_by_id(id).one(db).await;
    let mut conditions = Condition::all();
    if ids.len() > 0 {
      conditions = conditions.add(tj_staffdept::Column::TjStaffid.is_in(ids.to_owned()));
    }
    let ret = TjStaffdeptEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjStaffdept, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_staffdept::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    Ok(val_id.unwrap())
  }
  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let conditions = Condition::all().add(tj_staffdept::Column::Id.is_in(ids.to_owned()));
    let ret = TjStaffdeptEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn insert_many(info: &Vec<TjStaffdept>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }

    let mut active_values: Vec<tj_staffdept::ActiveModel> = Vec::new();
    for val in info {
      let ret = tj_staffdept::ActiveModel::from_json(json!(val));
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      active_values.push(ret.unwrap());
    }
    let ret = TjStaffdeptEntity::insert_many(active_values).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().last_insert_id)
  }

  pub async fn delete_many(ids: &Vec<i64>, deptids: &Vec<String>, db: &DatabaseConnection) -> Result<()> {
    if ids.len() <= 0 && deptids.len() <= 0 {
      return Err(anyhow!("conditions are empty, not allowed"));
    }
    let mut conditions = Condition::all();
    if !ids.is_empty() {
      conditions = conditions.add(tj_staffdept::Column::Id.is_in(ids.to_owned()));
    }
    if !deptids.is_empty() {
      conditions = conditions.add(tj_staffdept::Column::TjDeptid.is_in(deptids.to_owned()));
    }

    let ret = TjStaffdeptEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(())
  }
}
