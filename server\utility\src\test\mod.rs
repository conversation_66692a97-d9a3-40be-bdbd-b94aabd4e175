#[test]
fn test_func() {
  let dt = 1744784172492;

  let ret = crate::timeutil::format_timestamp(dt);
  println!("formated: {ret}");

  let ret = crate::timeutil::timestamp_add_days(dt, 1);
  println!("formated: {ret}");
  //format_timestamp_date_only_without_sep
  // let ret = crate::timeutil::format_timestamp_date_only_without_sep(dt);
  // println!("formated: {ret}");
  let birth_date = "1990-04-16";
  let age = crate::timeutil::get_age_from_birthdate(birth_date, 1744841308, "%Y-%m-%d");
  println!("age: {age}");
}
