//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_checkiteminfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_testid: String,
  pub tj_itemid: String,
  pub tj_synid: String,
  pub tj_itemname: String,
  pub tj_result: String,
  pub tj_lowvalue: String,
  pub tj_uppervalue: String,
  pub tj_itemrange: String,
  pub tj_itemunit: String,
  pub tj_abnormalflag: i32,
  pub tj_barflag: i32,
  pub tj_abnormalshow: String,
  pub tj_combineflag: i32,
  pub tj_deptorder: i32,
  pub tj_showorder: i32,
  pub tj_combineorder: i32,
  pub tj_deptid: String,
  pub tj_barnum: String,
  pub tj_barcode: String,
  pub tj_checkdate: i64,
  pub tj_checkdoctor: String,
  pub tj_recheckdoctor: String,
  pub tj_recheckdate: i64,
  pub tj_pacs: i8,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
