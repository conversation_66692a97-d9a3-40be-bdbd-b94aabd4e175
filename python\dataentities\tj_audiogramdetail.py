from sqlalchemy import (
    Column,
    Integer,
    String,
)
from dataentities.dbconn import Base


class TjAudiogramdetail(Base):
    # 数据库中存储的表名
    __tablename__ = "tj_audiogramdetail"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    tj_testid = Column(String(64), nullable=False)
    tj_itemid = Column(String(64), nullable=False, unique=False)   
    tj_freq = Column(Integer, nullable=False)
    tj_adtype = Column(Integer, nullable=False)
    tj_ear = Column(Integer, nullable=False)
    tj_result = Column(Integer, nullable=False)
    tj_revise = Column(Integer, nullable=False)
    tj_cover = Column(Integer, nullable=False)