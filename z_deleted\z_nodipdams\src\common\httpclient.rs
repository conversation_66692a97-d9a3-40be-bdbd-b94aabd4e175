use crate::config::settings::Settings;
use anyhow::{anyhow, Result};
use hyper::{header::CONTENT_TYPE, Method};
use reqwest::Client;

pub struct HttpClient {
  client: Client,
  appkey: String,
  appsecret: String,
  server: String,
}

impl HttpClient {
  pub fn new(config: &Settings) -> Self {
    let client = reqwest::Client::builder().danger_accept_invalid_certs(true).build().expect("build client error");
    let appkey = config.application.appkey.to_owned();
    let server = config.application.server.to_owned();
    let appsecret = config.application.appsecret.to_owned();
    Self { client, appkey, appsecret, server }
  }
  pub async fn upload(&self, content: &String, uri: &String) -> Result<()> {
    let server = format!("{}{}", self.server.to_string(), uri);
    let timestamp = utility::timeutil::current_timestamp_millis();
    // info!("current timestamp is:{},readable time is:{}", timestamp, utility::timeutil::format_timestamp(timestamp));
    let sign = self.get_sign(timestamp);
    info!("Sign:{}", &sign);
    let req = self
      .client
      //.post(server)
      .request(Method::POST, server)
      .timeout(std::time::Duration::from_secs(600))
      .header(CONTENT_TYPE, "application/json; charset=utf-8")
      .header("appKey", self.appkey.to_string())
      .header("timeStamp", timestamp.to_string())
      .header("sign", sign)
      .body(content.to_owned());
    info!("request info:{:#?}", &req);
    let resp = req.send().await;
    // info!("Response is:{:#?}", &resp);
    if resp.as_ref().is_err() {
      error!("Response error:{}", resp.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", resp.err().unwrap().to_string()));
    }
    let resp = resp.unwrap().text().await;
    if resp.as_ref().is_err() {
      error!("Response error:{}", resp.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", resp.err().unwrap().to_string()));
    }
    let resp_text = resp.unwrap_or_default();
    info!("Response text is:{}", &resp_text);
    let ret = serde_json::de::from_str::<crate::api::httpresponse::DamsResponseBody>(&resp_text);
    if ret.as_ref().is_err() {
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let respinfo = ret.unwrap();
    if !respinfo.code.eq_ignore_ascii_case(crate::api::httpresponse::CODE_OK) {
      error!("upload error:{}", respinfo.message.to_string());
      return Err(anyhow::anyhow!("{}", &respinfo.message));
    }
    Ok(())
  }

  fn get_sign(&self, timestamp: u128) -> String {
    let signstr = format!("appKey={}&timeStamp={}&appSecret={}", self.appkey, timestamp, self.appsecret);
    info!("Sign string is:{signstr}");
    let sign = utility::encrypt::md5(&signstr).to_ascii_uppercase();
    sign
  }
}
