use std::sync::Arc;

use anyhow::{anyhow, Result};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tiberius::{Client, Config};
use tokio::{net::TcpStream, sync::RwLock};
use tokio_util::compat::{Compat, TokioAsyncWriteCompatExt};
use tracing::*;

// use crate::config::settings::Extlis;
// "server=tcp:localhost,1433;IntegratedSecurity=true;TrustServerCertificate=true".to_owned()
#[derive(Default, Debug)]
pub struct RecvExternalLis {
  liskey: String,
  dbtype: i32,
  // username: String,
  // password: String,
  // uri: String,
  // oracle_conn: Option<oracle::Connection>,
  oracle_conn: Option<oracle::pool::Pool>,
  mssql_conn: Option<Arc<RwLock<tiberius::Client<Compat<TcpStream>>>>>,
  mysql_conn: Option<sqlx::MySqlPool>,
}

impl RecvExternalLis {
  pub async fn new(liskey: &str, username: &str, password: &str, uri: &str, dbtype: i32) -> Self {
    let mut externallis = RecvExternalLis {
      liskey: liskey.to_string(),
      dbtype,
      // username: username.to_string(),
      // password: password.to_string(),
      // uri: uri.to_string(),
      ..Default::default()
    };
    if liskey.is_empty() {
      return externallis;
    }
    match dbtype {
      x if x == crate::common::constant::DatabaseType::MsSql as i32 => {
        info!("start to create external lis ms sqlserver connection");
        let config = Config::from_ado_string(uri).expect("ado_string error");
        let tcp = TcpStream::connect(config.get_addr()).await.expect("connect error");
        tcp.set_nodelay(true).expect("set no delay error");
        let client = Client::connect(config, tcp.compat_write()).await.expect("connection error");
        externallis.mssql_conn = Some(Arc::new(RwLock::new(client)));
        info!("external lis ms sqlserver connection created successfully");
      }
      x if x == crate::common::constant::DatabaseType::Oracle as i32 => {
        info!("start to create external lis oracle connection");

        // let oracle_conn = oracle::Connection::connect(username, password, uri).expect("init oracle connection error");
        // externallis.oracle_conn = Some(oracle_conn);
        let pool = oracle::pool::PoolBuilder::new(username, password, uri)
          // .min_connections(3)
          // .timeout(std::time::Duration::from_secs(5))
          // .expect("create time out error")
          // .max_lifetime_connection(std::time::Duration::from_secs(5))
          // .expect("create max lifetime error")
          .build()
          .expect("create pool error:");
        externallis.oracle_conn = Some(pool);
        info!("external lis oracle connection created successfully");
      }
      x if x == crate::common::constant::DatabaseType::MySql as i32 => {
        info!("start to create external lis mysql connection");
        let conn = sqlx::MySqlPool::connect(uri).await.expect("connect mysql error");
        externallis.mysql_conn = Some(conn);
        info!("external lis mysql connection created successfully");
      }
      _ => {
        //
        // info!("NOT supported database");
      }
    };
    // info!("current extlis is:{:?}", &externallis);
    // let sqlstr = "select tjbh, brxm, xmxh, xmmc, xmdw, xmjg, gdbj, ckdz, ckgz, ckfw, jyys, bgrq, bgys,sfyc from v_tj_lisresult where tjbh = {}";
    externallis
  }
  pub fn get_liskey(&self) -> String {
    self.liskey.to_string()
  }
  pub async fn query_from_external_lis(&self, tjbh: &str, db: &DbConnection) -> Result<Vec<VTjLisresult>> {
    let mut result: Vec<VTjLisresult> = vec![];
    if self.dbtype == crate::common::constant::DatabaseType::MsSql as i32 {
      if self.mssql_conn.is_none() {
        return Err(anyhow!("mssql 无链接？？？？？？"));
      }

      let mssql_conn = self.mssql_conn.as_ref().unwrap(); //.to_owned();
      let mut mssql_conn_wr = mssql_conn.write().await;
      // info!("start to query lis result from ms sqlserver with conn:{:?}", &mssql_conn_wr);
      let ret = super::recv::recvmssql::RecvMssql::query_from_external_labresult(&mut mssql_conn_wr, tjbh).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      result = ret.unwrap();
    };
    if self.dbtype == crate::common::constant::DatabaseType::Oracle as i32 {
      if self.oracle_conn.is_none() {
        return Err(anyhow!("Oracle 无链接？？？？？？"));
      }
      //get from oracle
      let ret = self.oracle_conn.as_ref().unwrap().get();

      //如果连接已经断开
      if ret.as_ref().is_err() {
        error!("当前连接已经断开，需要重连......");
        // self.oracle_conn.unwrap().b
        // let oracle_conn = oracle::Connection::connect(self.username, self.password, self.uri).expect("init oracle connection error");
        // self.oracle_conn = Some(oracle_conn);
      }
      let oracle_conn = ret.unwrap();
      let ret = super::recv::recvoracle::RecvOracle::query_from_external_labresult(&oracle_conn, tjbh).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      result = ret.unwrap();
    }
    if self.dbtype == crate::common::constant::DatabaseType::MySql as i32 {
      if self.mysql_conn.is_none() {
        return Err(anyhow!("mysql not connected"));
      }
      let mysql_conn = self.mysql_conn.as_ref().unwrap();
      let ret = super::recv::recvmysql::RecvMysql::query_from_external_labresult(mysql_conn, tjbh).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      result = ret.unwrap();
      //get from mysql
    }
    if result.len() <= 0 {
      // let _ = VTjLisresult::delete_many(&tjbh, &db.get_connection()).await;
      return Err(anyhow!("lis系统无结果数据......"));
    }
    let xmxhs: Vec<String> = result.iter().map(|v| v.xmxh.to_string()).collect();
    let ret = VTjLisresult::delete_many_by_xmxh(&vec![tjbh.to_string()], &xmxhs, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }
    let ret = VTjLisresult::save_many(&result, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }
    Ok(result)
  }
  pub async fn query_lis_xmxx(&self) -> Result<Vec<VLisXmxx>> {
    // info!("extlis info:{:?}", &self);
    let mut result: Vec<VLisXmxx> = vec![];
    if self.dbtype == crate::common::constant::DatabaseType::MsSql as i32 {
      if self.mssql_conn.is_none() {
        return Err(anyhow!("mssql 无链接？？？？？？"));
      }

      let mssql_conn = self.mssql_conn.as_ref().unwrap().to_owned();
      let mut mssql_conn_wr = mssql_conn.write().await;
      let ret = super::recv::recvmssql::RecvMssql::query_lis_xmxx(&mut mssql_conn_wr).await;
      if ret.as_ref().is_err() {
        error!("query from mssql error:{:?}", &ret);
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      result = ret.unwrap();
    };
    if self.dbtype == crate::common::constant::DatabaseType::Oracle as i32 {
      if self.oracle_conn.is_none() {
        return Err(anyhow!("Oracle 无链接？？？？？？"));
      }
      //get from oracle
      let ret = self.oracle_conn.as_ref().unwrap().get();
      if ret.as_ref().is_err() {
        error!("get oracle connection error:{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("get oracle connection error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let oracle_conn = ret.unwrap();
      // let oracle_conn = self.oracle_conn.as_ref().unwrap();
      let ret = super::recv::recvoracle::RecvOracle::query_lis_xmxx(&oracle_conn).await;
      if ret.as_ref().is_err() {
        error!("query from oracle error:{:?}", &ret);
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      result = ret.unwrap();
    }
    if self.dbtype == crate::common::constant::DatabaseType::MySql as i32 {
      if self.mysql_conn.is_none() {
        return Err(anyhow!("mysql not connected"));
      }
      let mysql_conn = self.mysql_conn.as_ref().unwrap();
      let ret = super::recv::recvmysql::RecvMysql::query_lis_xmxx(mysql_conn).await;
      if ret.as_ref().is_err() {
        error!("query from mysql error:{:?}", &ret);
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      result = ret.unwrap();
    }
    Ok(result)
  }

  pub async fn release(&self) {
    if self.oracle_conn.is_some() {
      // let _ = self.oracle_conn.as_ref().unwrap().close();
      let _ = self.oracle_conn.as_ref().unwrap().close(&oracle::pool::CloseMode::Default);
    }
    if self.mssql_conn.is_some() {
      // let client = self.mssql_conn.as_ref().unwrap().to_owned();
      // let _ = client.clone().read().await.close().await;
    }
    if let Some(mysql_conn) = self.mysql_conn.as_ref() {
      let _ = mysql_conn.close().await;
    }
  }
}
