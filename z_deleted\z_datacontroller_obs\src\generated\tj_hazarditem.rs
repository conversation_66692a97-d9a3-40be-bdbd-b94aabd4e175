//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_hazarditem")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_hid: i32,
    pub tj_itemid: String,
    pub tj_testtype: i32,
    pub tj_oflag: i8,
    pub tj_showorder: i32,
    pub tj_memo: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::tj_hazardinfo::Entity",
        from = "Column::TjHid",
        to = "super::tj_hazardinfo::Column::Id",
        on_update = "Restrict",
        on_delete = "Restrict"
    )]
    TjHazardinfo,
    #[sea_orm(
        belongs_to = "super::tj_iteminfo::Entity",
        from = "Column::TjItemid",
        to = "super::tj_iteminfo::Column::TjItemid",
        on_update = "Restrict",
        on_delete = "Restrict"
    )]
    TjIteminfo,
}

impl Related<super::tj_hazardinfo::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjHazardinfo.def()
    }
}

impl Related<super::tj_iteminfo::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjIteminfo.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
