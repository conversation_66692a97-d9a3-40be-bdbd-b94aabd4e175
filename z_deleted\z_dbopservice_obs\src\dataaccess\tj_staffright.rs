use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, De<PERSON>ult, PartialEq, Serialize, Deserialize)]
pub struct TjStaffright {
  pub id: i64,
  pub tj_staffid: i32,
  pub tj_rname: String,
  pub tj_right: i32,
  pub tj_operator: i32,
  pub tj_moddate: i64,
  pub tj_menuid: i32,
}
crud!(Tj<PERSON><PERSON><PERSON>right {}, "tj_staffright");
rbatis::impl_select!(TjStaffright{query_many(staffid:&i64) => "`where tj_staffid = #{staffid} `"});
rbatis::impl_delete!(TjStaffright{delete(staffids:&[&str]) => "`where tj_staffid in ${staffids.sql()} `"});
