import os
from datetime import datetime
from PIL import Image
from reportlab.platypus import (
    BaseDocTemplate,
    Spacer,
    PageBreak,
    NextPageTemplate,
    PageTemplate,
    Frame,
)
from reportlab.platypus import Paragraph, Table, HRFlowable
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape, portrait
from reportlab.lib.units import cm, inch
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.lib.styles import getSampleStyleSheet
from corpreport.numberedcanvas import NumberedCanvas
from functools import partial
import constant

from dataentities.dbconn import session
from dataentities.tj_staffadmin import TjStaffadmin
from dataentities.tj_checkallnew import TjCheckallnew
from dataentities.tj_medexaminfo import TjMedexaminfo
from dataentities.tj_patient import TjPatient
from dataentities.ss_dictionary import SsDictionary

def generate_pdf_corp_recheckreport(
    rptnum,
    corpname,
    recheckdate,
    dptname,
    testids,
    notice,
    outdir,
):
    """
    Generate a PDF recheck notice for corporate health examination.
    
    Args:
        rptinfo: Report information
        checkallinfos: List of check-all information
        medinfos: List of medical examination information
        patients: List of patient information
        corpinfo: Corporate information
        dicts: Dictionary information
        customer: Customer information
        outdir: Output directory
        areaname: Area name
    
    Returns:
        str: Path to the generated PDF file
    """
    # Generate output filename
    output = "复查通知书-{}.pdf".format(
       rptnum, 
    )
    
    # Create report instance
    recheck = recheck_report(rptnum, corpname, recheckdate, dptname, testids, notice, outdir, output)
    
    # Add content to the report
    recheck.add_front_page()
    # recheck.add_page_break()
    # recheck.add_recheck_notice(corpinfo, dicts)
    # recheck.add_page_break()
    # recheck.add_recheck_list(checkallinfos, medinfos, patients, dicts)
    # recheck.add_page_break()
    # recheck.add_sign_page(dicts, customer)
    
    # Build the PDF
    recheck.build()
    
    return os.path.join(outdir, output)

class recheck_report:
    def __init__(self, rptnum, corpname, recheckdate, dptname, testids, notice, outdir, output):
        # self.rptinfo = rptinfo
        self.font_normal = "SimSun"
        self.font_hei = "SimHei"
        self.corpname = corpname
        self.recheckdate = recheckdate
        self.dptname = dptname
        self.testids = testids
        self.notice = notice
        self.contents = []
        
        stylesheet = getSampleStyleSheet()
        self.title_style = stylesheet["Title"]
        self.normal_style = stylesheet["Normal"]
        self.body_style = stylesheet["BodyText"]
        
        # Configure document
        self.doc = BaseDocTemplate(
            os.path.join(outdir, output),
            pagesize=A4,
            leftMargin=1.2 * cm,
            rightMargin=1.2 * cm,
            topMargin=2.0 * cm,
            bottomMargin=1.2 * cm,
        )
        
        # Configure frames
        self.portrait_frame = Frame(
            self.doc.leftMargin,
            self.doc.bottomMargin,
            self.doc.width,
            self.doc.height,
            id="portrait_frame",
        )
        
        # Add page templates
        self.doc.addPageTemplates([
            PageTemplate(
                id="portrait",
                frames=self.portrait_frame,
                onPage=partial(header_portrait, rptnumber="编号:{}".format(rptnum)),
                pagesize=portrait(A4),
            ),
        ])

    def add_front_page(self):
        """Add the front page with corporate information."""
        style = getSampleStyleSheet()["Heading1"]
        style.fontName = self.font_normal
        style.fontSize = 24
        style.alignment = TA_CENTER
        
        normalstyle = getSampleStyleSheet()["Normal"]
        normalstyle.fontName = self.font_normal
        normalstyle.fontSize = 12
        normalstyle.leading = 20 #行间距
        normalstyle.wordWrap = "CJK"  # 设置自动换行
        normalstyle.strikeWidth = 10
        # normalstyle.leftIndent = 0.5 * cm
        # normalstyle.underlineWidth = 1
        # normalstyle.underlineColor = colors.black
        # normalstyle.underlineOffset = -2  # Adjust underline position
        # normalstyle.underlineGap = 1  # Add small gap between text and underline
        # normalstyle.underline = True  # Enable underline
        
        # Title
        self.contents.append(Paragraph("复查通知书", style))
        self.contents.append(Spacer(1, 1 * cm))
        
        # corpname
        self.contents.append(Paragraph("<u> {} :</u>".format(self.corpname), normalstyle))
        # # Corporate information

        # notice
        self.contents.append(Spacer(1, 0.2 * cm))
        self.contents.append(Paragraph("&nbsp;&nbsp;&nbsp;&nbsp;我机构发现你单位部分劳动者体检异常（具体情况请看以下异常情况列表），需要进一步检查以明确结论。请安排以下劳动者于<u>{}</u>日前来我院{}进行复查。".format(self.recheckdate, self.dptname), normalstyle))
        self.contents.append(Paragraph("&nbsp;&nbsp;&nbsp;&nbsp;特此通知。", normalstyle))
        #details
        
        self.contents.append(Spacer(1, 0.5 * cm))
        normalstyle.fontSize = 13
        self.contents.append(Paragraph("&nbsp;&nbsp;&nbsp;&nbsp;一、异常情况列表", normalstyle))
        # add table
        style3 = getSampleStyleSheet()["Normal"]
        style3.alignment = TA_CENTER
        style3.leading = 10
        style3.fontName = self.font_normal
        style3.fontSize = 10
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.underlineOffset = 20

        style3_left = getSampleStyleSheet()["Normal"]
        style3_left.alignment = TA_LEFT
        style3_left.leading = 10
        style3_left.fontName = self.font_normal
        style3_left.fontSize = 10
        style3_left.wordWrap = "CJK"  # 设置自动换行
        style3_left.underlineOffset = 20
        data = [
            (
                Paragraph("体检编号", style3),
                Paragraph("姓名", style3),
                Paragraph("证件号码", style3),
                Paragraph("工种", style3),
                Paragraph("体检日期", style3),
                Paragraph("异常结果", style3),
                Paragraph("处理意见", style3),
            ),
        ]
        # get data from db
        medinfos = session.query(TjMedexaminfo).filter(TjMedexaminfo.tj_testid.in_(self.testids)).all()
        #sort by tj_testid
        medinfos.sort(key=lambda x: x.tj_testid)
        # collect pids from medinfos 
        pids = [med.tj_pid for med in medinfos]
        # print(pids)
        # get patient info from db
        patients = session.query(TjPatient).filter(TjPatient.tj_pid.in_(pids)).all()
        # get tjcheckallnew from db by testids
        tjcheckallnews = session.query(TjCheckallnew).filter(TjCheckallnew.tj_testid.in_(self.testids)).all()
        # add data
        for med in medinfos:
            #get tjpatient by pid
            patient = next((p for p in patients if p.tj_pid == med.tj_pid), None)
            # print(patient)
            #get tjcheckallnew by testid
            checkallnew = next((c for c in tjcheckallnews if c.tj_testid == med.tj_testid), None)
            # print(checkallnew)
            if checkallnew is None or patient is None:
                continue
            data.append(
                (
                    Paragraph(med.tj_testid, style3),
                    Paragraph(patient.tj_pname, style3),
                    Paragraph(patient.tj_pidcard, style3),
                    Paragraph(med.tj_worktype, style3),
                    Paragraph(datetime.fromtimestamp(med.tj_testdate).strftime("%Y-%m-%d"), style3),
                    Paragraph(checkallnew.tj_ocuabnormal, style3_left),
                    Paragraph(checkallnew.tj_ocusuggestion, style3_left),
                )
            )
        
        table = Table(data, colWidths=[2*cm, 1.8*cm, 2.2*cm, 2*cm, 2.2*cm, 4.6*cm, 4.6*cm],
                      repeatRows=1,minRowHeights=[0.8 * cm],)
        table.setStyle([
            ("FONTNAME", (0, 0), (-1, -1), self.font_normal),  # 字体
            # ("NOSPLIT", (0, 0), (10, 1)),
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), "#eeeeee"),  # 设置第一行背景颜色
            ("ALIGN", (0, 1), (-1, -1), "LEFT"),  # 第二行到最后一行左右左对齐
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            (
                "GRID",
                (0, 0),
                (-1, -1),
                0.5,
                colors.grey,
            ),  # 设置表格框线为grey色，线宽为0.5
        ])
        
        self.contents.append(table)

        #add notice
        self.contents.append(Spacer(1, 0.8 * cm))
        self.contents.append(Paragraph("&nbsp;&nbsp;&nbsp;&nbsp;二、复查时注意事项:", normalstyle))
        self.contents.append(Spacer(1, 0.2 * cm))
        self.contents.append(Paragraph("{}".format(self.notice.replace("\n", "<br/>")), normalstyle))

        #add sign
        self.contents.append(Spacer(1, 1.0 * cm))
        rightstyle = getSampleStyleSheet()["Normal"]
        rightstyle.fontSize = 15
        rightstyle.fontName = self.font_normal
        rightstyle.alignment = TA_CENTER
        rightstyle.leading = 20

        # query dict from database by pid:1 typeid:10
        hospital_name = ""
        dicts = session.query(SsDictionary).filter(SsDictionary.ss_pid == 1, SsDictionary.ss_typeid == 10).first()
        if dicts is not None:
            hospital_name = dicts.ss_name

        # Create a table for signature section
        sign_data = [
            [Paragraph("&nbsp;",rightstyle), Paragraph("{}".format(hospital_name), rightstyle)],
            [Paragraph("&nbsp;",rightstyle),Paragraph("{}".format("(盖章)"), rightstyle)],
            [Paragraph("&nbsp;",rightstyle),Paragraph("{}".format(datetime.now().strftime("%Y年%m月%d日")), rightstyle)]
        ]
        sign_table = Table(sign_data, colWidths=[10*cm,8*cm])
        sign_table.setStyle([
            ("ALIGN", (1, 0), (1, -1), "CENTER"),
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
            # ("LEFTPADDING", (0, 0), (-1, -1), 0),
            # ("RIGHTPADDING", (0, 0), (-1, -1), 0),
            # ("TOPPADDING", (0, 0), (-1, -1), 0),
            # ("BOTTOMPADDING", (0, 0), (-1, -1), 0),
        ])
        self.contents.append(sign_table)

        #add receiver sign
        self.contents.append(Spacer(1, 0.8 * cm))
        smalltype = getSampleStyleSheet()["Normal"]
        smalltype.fontSize = 10
        smalltype.alignment = TA_LEFT
        smalltype.leading = 30
        smalltype.fontName = self.font_normal
        self.contents.append(Paragraph("{}".format("签收人（签名）：" + "&nbsp" * 50 + "年" + "&nbsp;" * 4 + "月" + "&nbsp;" * 4 + "日"), smalltype))
        self.contents.append(Paragraph("一式二份，用人单位一份，医疗卫生机构存档一份。", smalltype))

    def add_page_break(self):
        """Add a page break."""
        self.contents.append(PageBreak())

    def build(self):
        """Build the PDF document."""
        self.doc.build(self.contents, canvasmaker=NumberedCanvas)

def header_portrait(canvas, doc, rptnumber):
    """Add header to portrait pages."""
    canvas.saveState()
    canvas.setFont("SimSun", 10)
    canvas.drawRightString(doc.pagesize[0] - doc.rightMargin, doc.pagesize[1] - doc.topMargin + 10, rptnumber)
    canvas.restoreState()
