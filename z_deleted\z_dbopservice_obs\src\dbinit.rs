use rbatis::{rbd<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct DbConnection {
  dbconn: RBati<PERSON>,
}

impl DbConnection {
  pub async fn new(uri: &str, _dbtype: i32) -> Self {
    // enable log crate to show sql logs
    // fast_log::init(fast_log::Config::new().console()).expect("rbatis init fail");
    let rb = RBatis::new();
    // ------------choose database driver------------
    // rb.init(rbdc_mysql::driver::MysqlDriver {}, "mysql://root:123456@localhost:3306/test").unwrap();
    // rb.init(rbdc_pg::driver::PgDriver {}, "postgres://postgres:123456@localhost:5432/postgres").unwrap();
    // rb.init(rbdc_mssql::driver::Ms<PERSON><PERSON><PERSON><PERSON><PERSON> {}, "mssql://SA:TestPass!123456@localhost:1433/test").unwrap();
    rb.init(rbdc_mysql::driver::MysqlDriver {}, uri).unwrap();

    DbConnection { dbconn: rb }
  }
  pub fn get_connection_ref(&self) -> &RBatis {
    &self.dbconn
  }
  pub fn get_connection(&self) -> RBatis {
    self.dbconn.clone()
  }
  // pub fn get_connection_clone(&self) -> RBatis {
  //   self.dbconn.clone()
  // }
  pub async fn execute_sql(&self, sqlstr: &str) -> Result<rbdc::db::ExecResult, Error> {
    let ret = self.dbconn.exec(&sqlstr, vec![]).await;
    info!("execute sql ret:{:?}, sql:{}", ret, sqlstr);
    ret
  }
}
