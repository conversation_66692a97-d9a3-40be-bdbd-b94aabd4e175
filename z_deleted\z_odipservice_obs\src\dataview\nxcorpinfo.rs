use datacontroller::entities::tj_corpinfo::TjCorpinfo;
use serde::{Deserialize, Serialize};
use utility::serde::{deserialize_string_to_i64, serialize_i64_to_string};

use crate::constant::{CorpStatus, YesOrNo};
#[derive(Debug, Serialize, Deserialize)]
pub struct NxCorpinfo {
  pub id: i64,
  #[serde(deserialize_with = "deserialize_string_to_i64")]
  #[serde(serialize_with = "serialize_i64_to_string")]
  pub cmp_id: i64,
  pub cmp_name: String,
  pub cmp_code: String,
  pub economics_type: i64,
  pub industry_type: i64,
  pub scale_type: i64,
  pub head_name: String,
  pub contacts_name: String,
  pub contacts_phone: String,
  pub cmp_address: String,
  pub cmp_mail: String,
  pub area_code: String,
  pub zip_code: String,
  pub cmp_fax: String,
  pub cmp_total: i32,
  pub cmp_totalf: i32,
  pub cmp_operationer: i32,
  pub cmp_operationerf: i32,
  pub cmp_hazarders: i32,
  pub cmp_hazardersf: i32,
  pub cmp_status: i32, //-1:删除 0：未同步 1：已同步
  pub p_orgid: i64,
  pub create_date: i64,
  pub creator: String,
  pub modify_date: i64,
  pub modifier: String,
}

pub fn convert_to_tj_corpinfo(info: &NxCorpinfo) -> TjCorpinfo {
  let corpinfo = TjCorpinfo {
    id: 0,
    tj_corpid: info.cmp_id.to_string(),
    tj_corpname: info.cmp_name.to_owned(),
    tj_contactor: info.contacts_name.to_owned(),
    tj_principle: info.head_name.to_owned(),
    tj_phone: info.contacts_phone.to_owned(),
    tj_fax: info.cmp_fax.to_owned(),
    tj_areacode: info.area_code.to_owned(),
    tj_address: info.cmp_address.to_owned(),
    tj_postcode: info.zip_code.to_owned(),
    tj_industry2: info.industry_type.to_string(),
    tj_economic2: info.economics_type.to_string(),
    tj_pyjm: "".to_string(),
    tj_zdym: "".to_string(),
    tj_operator: 0,
    tj_adddate: 0,
    tj_testtype: 0,
    tj_password: "".to_string(),
    tj_mail: info.cmp_mail.to_owned(),
    tj_memo: "".to_string(),
    tj_orgcode: info.cmp_code.to_owned(),
    tj_gbcode: "".to_string(),
    tj_ecotypeh: info.economics_type as i32,
    tj_ecotype: info.economics_type as i32,
    tj_industryh: info.industry_type as i32,
    tj_industry: info.industry_type as i32,
    tj_secondcode: "".to_string(),
    tj_secondname: "".to_string(),
    tj_corpscale: info.scale_type as i32,
    tj_total: info.cmp_total,
    tj_totalf: info.cmp_totalf,
    tj_operationer: info.cmp_operationer,
    tj_operationerf: info.cmp_operationerf,
    tj_hazarders: info.cmp_hazarders,
    tj_hazardersf: info.cmp_hazardersf,
    tj_syncflag: YesOrNo::No as i32,
    tj_status: CorpStatus::OK as i32,
    tj_monitortype: "01".to_string(),
    p_cmpid: info.cmp_id,
  };
  corpinfo
}
