use lopdf::{
  content::{Content, Operation},
  Dictionary, Document, Object, Stream,
};
use std::collections::BTreeMap;

fn main() -> Result<(), lopdf::Error> {
  // Create a new document
  let mut doc = Document::with_version("1.5");

  // Define table parameters
  let col_widths = [100.0, 100.0, 100.0, 100.0];
  let page_height = 842.0;
  let margin_top = 50.0;
  let margin_bottom = 50.0;

  // Define table data (expanded to demonstrate multi-page)
  // Use String instead of &str to avoid borrowing issues
  let mut data: Vec<String> = Vec::new();

  // Headers
  data.push("Header 1".to_string());
  data.push("Header 2".to_string());
  data.push("Header 3".to_string());
  data.push("Header 4".to_string());

  // Add some normal rows
  for i in 1..20 {
    data.push(format!("Row {}, Col 1", i));
    data.push(format!("Row {}, Col 2", i));
    data.push(format!("Row {}, Col 3", i));
    data.push(format!("Row {}, Col 4", i));
  }

  // Add a row with normal text wrapping
  data.push("Row 20, Col 1".to_string());
  data.push("Row 20, Col 2 text that will wrap to a few lines to demonstrate normal text wrapping behavior in the table".to_string());
  data.push("Row 20, Col 3".to_string());
  data.push("Row 20, Col 4".to_string());

  // Add an extremely tall row that exceeds page height to test our handling
  data.push("Tall Row".to_string());
  // Create a very long text that will produce many lines and exceed page height
  let very_long_text = "This is an extremely long text that will create a very tall row. ".repeat(100);
  data.push(very_long_text);
  data.push("This cell is normal".to_string());
  data.push("This cell is also normal".to_string());

  // Add more normal rows after the tall row
  for i in 21..30 {
    data.push(format!("Row {}, Col 1", i));
    data.push(format!("Row {}, Col 2", i));
    data.push(format!("Row {}, Col 3", i));
    data.push(format!("Row {}, Col 4", i));
  }

  // Convert Vec<String> to Vec<&str> for the function call
  let data_refs: Vec<&str> = data.iter().map(|s| s.as_str()).collect();

  // Create multi-page table
  let page_ids = create_multi_page_table(&mut doc, &data_refs, &col_widths, page_height, margin_top, margin_bottom)?;

  // Set up document catalog
  let pages_id = doc.get_object(page_ids[0].as_reference().unwrap()).unwrap().as_dict().unwrap().get(b"Parent").unwrap().clone();

  let catalog_id = doc.add_object(Dictionary::from_iter(vec![("Type", "Catalog".into()), ("Pages", pages_id)]));

  doc.trailer.set("Root", catalog_id);

  // Save the document
  doc.save("multi_page_table.pdf")?;

  Ok(())
}

// Function to draw a line
fn draw_line(x1: f32, y1: f32, x2: f32, y2: f32) -> Vec<Operation> {
  vec![
    Operation::new("m", vec![x1.into(), y1.into()]), // Move to start point
    Operation::new("l", vec![x2.into(), y2.into()]), // Line to end point
    Operation::new("S", vec![]),                     // Stroke the path
  ]
}

// Function to set text position and write text
fn draw_text(x: f32, y: f32, text: &str) -> Vec<Operation> {
  vec![
    Operation::new("BT", vec![]),                             // Begin text
    Operation::new("Tf", vec!["F1".into(), 10.into()]),       // Set font and size
    Operation::new("Td", vec![x.into(), y.into()]),           // Set text position
    Operation::new("Tj", vec![Object::string_literal(text)]), // Show text
    Operation::new("ET", vec![]),                             // End text
  ]
}

// Function to wrap text within a given width
fn wrap_text(text: &str, max_width: f32, font_size: f32) -> Vec<String> {
  let avg_char_width = font_size * 0.5; // Approximate average character width for Helvetica
  let max_chars = (max_width / avg_char_width) as usize;
  let mut lines = Vec::new();
  let mut current_line = String::new();
  let words = text.split_whitespace();

  for word in words {
    if current_line.len() + word.len() + 1 <= max_chars {
      if !current_line.is_empty() {
        current_line.push(' ');
      }
      current_line.push_str(word);
    } else {
      if !current_line.is_empty() {
        lines.push(current_line);
        current_line = String::new();
      }
      if word.len() > max_chars {
        // Handle very long words by splitting them
        let mut chars = word.chars().collect::<Vec<char>>();
        while !chars.is_empty() {
          let take = max_chars.min(chars.len());
          let chunk: String = chars.drain(..take).collect();
          lines.push(chunk);
        }
      } else {
        current_line.push_str(word);
      }
    }
  }

  if !current_line.is_empty() {
    lines.push(current_line);
  }

  lines
}

fn create_table(x: f32, y: f32, cols: usize, rows: usize, col_widths: &[f32], data: &[&str]) -> Vec<Operation> {
  let mut operations = Vec::new();
  let font_size = 10.0;
  let line_height = font_size * 1.2; // Line height for wrapped text
  let min_row_height = line_height + 10.0; // Minimum row height for padding
  let cell_padding = 5.0; // Padding inside cells

  // Calculate row heights based on wrapped text
  let mut row_heights = Vec::new();

  for row in 0..rows {
    let mut max_lines = 1; // Minimum 1 line per cell
    for col in 0..cols {
      let cell_index = row * cols + col;
      if cell_index < data.len() {
        let cell_width = col_widths[col] - (cell_padding * 2.0); // Subtract padding on both sides
        let lines = wrap_text(data[cell_index], cell_width, font_size);
        max_lines = max_lines.max(lines.len());
      }
    }
    // Calculate row height: max_lines * line_height + padding
    let row_height = (max_lines as f32 * line_height + (cell_padding * 2.0)).max(min_row_height);
    row_heights.push(row_height);
  }

  // Calculate total width and height
  let total_width: f32 = col_widths.iter().sum();
  let total_height: f32 = row_heights.iter().sum();

  // Draw table border
  operations.extend(draw_line(x, y, x + total_width, y)); // Top horizontal line
  operations.extend(draw_line(x, y - total_height, x + total_width, y - total_height)); // Bottom horizontal line
  operations.extend(draw_line(x, y, x, y - total_height)); // Left vertical line
  operations.extend(draw_line(x + total_width, y, x + total_width, y - total_height)); // Right vertical line

  // Draw horizontal grid lines
  let mut current_y = y;
  for i in 1..rows {
    current_y -= row_heights[i - 1];
    operations.extend(draw_line(x, current_y, x + total_width, current_y));
  }

  // Draw vertical grid lines
  let mut current_x = x;
  for i in 1..cols {
    current_x += col_widths[i - 1];
    operations.extend(draw_line(current_x, y, current_x, y - total_height));
  }

  // Add text to cells
  // Start at the top of the table
  let mut current_y = y;

  // Process each row
  for row in 0..rows {
    let row_height = row_heights[row];
    let mut current_x = x;

    // For each column in the row
    for col in 0..cols {
      let cell_index = row * cols + col;
      if cell_index < data.len() {
        let cell_width = col_widths[col] - (cell_padding * 2.0);
        let lines = wrap_text(data[cell_index], cell_width, font_size);

        // Calculate text height
        let text_height = lines.len() as f32 * line_height;

        // Calculate vertical position to center text in cell
        let vertical_padding = (row_height - text_height) / 2.0;

        // Position text properly within the cell
        // Start at the top of the cell minus padding and font size
        let mut text_y = current_y - vertical_padding - font_size;

        // Add each line of text
        for line in lines {
          let text_x = current_x + cell_padding;
          operations.extend(draw_text(text_x, text_y, &line));
          text_y -= line_height;
        }
      }

      // Move to the next column
      current_x += col_widths[col];
    }

    // Move to the next row
    current_y -= row_height;
  }

  operations
}

fn create_multi_page_table(doc: &mut Document, data: &[&str], col_widths: &[f32], page_height: f32, margin_top: f32, margin_bottom: f32) -> Result<Vec<Object>, lopdf::Error> {
  let mut page_ids = Vec::new();
  let font_size = 10.0;
  let line_height = font_size * 1.2; // Line height for wrapped text
  let min_row_height = line_height + 10.0; // Minimum row height for padding
  let table_x = 50.0;
  let cols = col_widths.len();

  // Extract headers (first row)
  let headers: Vec<&str> = data.iter().take(cols).copied().collect();

  // Calculate header row height
  let header_row_height = {
    let mut max_lines = 1;
    for (i, &header) in headers.iter().enumerate() {
      let cell_width = col_widths[i] - 10.0;
      let lines = wrap_text(header, cell_width, font_size);
      max_lines = max_lines.max(lines.len());
    }
    (max_lines as f32 * line_height + 10.0).max(min_row_height)
  };

  // Skip headers in data for content rows
  let content_data = &data[cols..];

  // Calculate the maximum height available for content on a page
  let max_content_height = page_height - margin_top - margin_bottom - header_row_height;

  // Process content rows and create pages
  let mut data_index = 0;
  let content_total_cells = content_data.len();

  // Pre-process all cells to get their wrapped text
  let mut cell_lines: Vec<Vec<String>> = Vec::with_capacity(content_total_cells);
  for &cell_text in content_data.iter() {
    let col_index = (cell_lines.len() % cols) as usize;
    let cell_width = col_widths[col_index] - 10.0; // Subtract padding
    let lines = wrap_text(cell_text, cell_width, font_size);
    cell_lines.push(lines);
  }

  // Track which lines of each cell have been processed
  let mut cell_line_indices: Vec<usize> = vec![0; content_total_cells];

  while data_index < content_total_cells {
    // Create a new page
    let page_id = doc.add_object(Dictionary::from_iter(vec![
      ("Type", "Page".into()),
      ("MediaBox", vec![0.into(), 0.into(), 595.into(), page_height.into()].into()),
    ]));

    // Set up document catalog and font resources
    let font_dict = Dictionary::from_iter(vec![(
      "F1",
      Dictionary::from_iter(vec![("Type", "Font".into()), ("Subtype", "Type1".into()), ("BaseFont", "Helvetica".into())]).into(),
    )]);

    let resources = Dictionary::from_iter(vec![("Font", font_dict.into())]);

    doc.get_dictionary_mut(page_id)?.set::<&str, Dictionary>("Resources", resources.into());

    // Create table data for this page, starting with headers
    let mut table_data_for_page = Vec::with_capacity(cols * 10); // Initial capacity
    table_data_for_page.extend_from_slice(&headers);

    // Track current height used on this page
    let mut current_height = 0.0;
    let mut rows_on_page = 0;
    let mut cells_added = 0;
    let mut row_in_progress = false;
    let mut current_row_start = data_index;

    // Add rows until we run out of space or data
    while data_index + cells_added < content_total_cells {
      // Start a new row if we're not continuing a row
      if !row_in_progress {
        current_row_start = data_index + cells_added;
      }

      // Check if we have enough cells left for a complete row
      let remaining_cells = content_total_cells - current_row_start;
      let cells_for_row = cols.min(remaining_cells);

      // Prepare for row height calculation
      let mut row_cell_heights = Vec::with_capacity(cols);
      let mut row_completed = true;
      let mut cell_content_strings = Vec::with_capacity(cols);

      // Calculate how many lines we can fit for each cell in this row
      for i in 0..cells_for_row {
        let cell_index = current_row_start + i;
        let cell_lines_remaining = cell_lines[cell_index].len() - cell_line_indices[cell_index];

        if cell_lines_remaining > 0 {
          // Determine how many lines we can fit on this page
          let max_lines_per_cell = ((max_content_height - current_height) / line_height).floor() as usize;
          let lines_to_take = max_lines_per_cell.min(cell_lines_remaining);

          if lines_to_take > 0 {
            // Create a cell with just the lines that fit
            let start_line = cell_line_indices[cell_index];
            let end_line = start_line + lines_to_take;
            let cell_content = cell_lines[cell_index][start_line..end_line].join("\n");
            cell_content_strings.push(cell_content);

            // Calculate height for this partial cell
            let cell_height = (lines_to_take as f32 * line_height + 10.0).max(min_row_height);
            row_cell_heights.push(cell_height);
          } else {
            // No space left, add empty cell
            cell_content_strings.push(String::new());
            row_cell_heights.push(min_row_height);
            row_completed = false;
          }
        } else {
          // Cell is already fully processed
          cell_content_strings.push(String::new());
          row_cell_heights.push(min_row_height);
        }
      }

      // Add empty cells to complete the row if needed
      for _ in cells_for_row..cols {
        cell_content_strings.push(String::new());
        row_cell_heights.push(min_row_height);
      }

      // Calculate row height (maximum of all cell heights)
      let row_height = row_cell_heights.iter().copied().fold(min_row_height, f32::max);

      // Check if row fits
      if current_height + row_height <= max_content_height || rows_on_page == 0 {
        // Add the row data to the table
        for (i, cell_content) in cell_content_strings.iter().enumerate() {
          // Create a static string that will live for the duration of the function
          let content_str = String::from(cell_content);
          let content_ref = Box::leak(content_str.into_boxed_str());
          table_data_for_page.push(content_ref);

          // Update line indices for cells that were added
          if i < cells_for_row {
            let cell_index = current_row_start + i;
            let max_lines_per_cell = ((max_content_height - current_height) / line_height).floor() as usize;
            let cell_lines_remaining = cell_lines[cell_index].len() - cell_line_indices[cell_index];
            let lines_to_take = max_lines_per_cell.min(cell_lines_remaining);

            cell_line_indices[cell_index] += lines_to_take;

            // Check if this cell is completed
            if cell_line_indices[cell_index] < cell_lines[cell_index].len() {
              row_completed = false;
            }
          }
        }

        current_height += row_height;
        rows_on_page += 1;

        if row_completed {
          // Move to the next row
          cells_added += cells_for_row;
          row_in_progress = false;
        } else {
          // This row will continue on the next page
          row_in_progress = true;
          break; // Move to next page
        }
      } else {
        // Row doesn't fit and we already have rows on this page
        break;
      }
    }

    // Create table operations for this page
    let table_y = page_height - margin_top;

    // Create separate operations for headers and content
    let header_data = &table_data_for_page[0..cols];
    let content_data = &table_data_for_page[cols..];

    // Create header table at the top of the page
    let header_operations = create_table(
      table_x,
      table_y,
      cols,
      1, // Just one row for headers
      col_widths,
      header_data,
    );

    // Create content table below the header
    let content_operations = if content_data.len() > 0 {
      create_table(table_x, table_y - header_row_height, cols, rows_on_page, col_widths, content_data)
    } else {
      vec![] // Empty vector if no content
    };

    // Combine header and content operations
    let mut table_operations = Vec::new();
    table_operations.extend(header_operations);
    table_operations.extend(content_operations);

    // Create content with just the table operations
    let content = Content { operations: table_operations };

    // Add content to the page
    let content_id = doc.add_object(Stream::new(Dictionary::new(), content.encode()?));
    doc.get_dictionary_mut(page_id)?.set("Contents", content_id);

    // Add the page to the document
    page_ids.push(page_id);

    // Update data_index for the next page
    if !row_in_progress {
      data_index += cells_added;
    }

    // If we couldn't add any content to this page, force moving forward
    if rows_on_page == 0 {
      // Force at least one line of one cell to be processed
      for i in 0..cols.min(content_total_cells - data_index) {
        let cell_index = data_index + i;
        if cell_line_indices[cell_index] < cell_lines[cell_index].len() {
          cell_line_indices[cell_index] += 1;
          break;
        }
      }

      // Check if we need to move to the next row
      let row_completed = (0..cols.min(content_total_cells - data_index)).all(|i| {
        let cell_index = data_index + i;
        cell_line_indices[cell_index] >= cell_lines[cell_index].len()
      });

      if row_completed {
        data_index += cols.min(content_total_cells - data_index);
      }
    }
  }

  // No page numbers to update

  // Set up page tree
  let pages_id = doc.add_object(Dictionary::from_iter(vec![
    ("Type", "Pages".into()),
    ("Kids", page_ids.iter().map(|&id| id.into()).collect::<Vec<Object>>().into()),
    ("Count", (page_ids.len() as i64).into()),
  ]));

  for page_id in &page_ids {
    doc.get_dictionary_mut(*page_id)?.set("Parent", pages_id);
  }

  Ok(page_ids.into_iter().map(|id| Object::Reference(id)).collect())
}
