//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Default, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "v_external_pacs")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub testid: String,
  pub testername: String,
  pub tid: String,
  pub birthdate: String,
  pub idcard: String,
  pub psex: i32,
  pub age: i32,
  pub itemid2: String,
  pub itemid: String,
  pub itemname: String,
  pub pacs: String,
  pub deptid: String,
  pub deptname: String,
  pub requesterid: String,
  pub requestername: String,
  pub requestdate: String,
  pub diag: String,
  pub price: String,
  pub phone: String,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
