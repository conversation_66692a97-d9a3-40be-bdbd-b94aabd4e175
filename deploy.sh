#!/bin/bash

# Report Server Deployment Script for Linux/Mac
# This script automates the deployment process

set -e  # Exit on any error

echo "========================================"
echo "Report Server Deployment Script"
echo "========================================"

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_DIR="$SCRIPT_DIR/python"
BUILD_DIR="$SCRIPT_DIR/build"
DIST_DIR="$SCRIPT_DIR/dist"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.7+ and try again"
    exit 1
fi

echo "Python found: $(python3 --version)"

# Change to python directory
cd "$PYTHON_DIR"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Check if PyInstaller is available
if ! pip show pyinstaller &> /dev/null; then
    echo "Installing PyInstaller..."
    pip install pyinstaller
fi

# Build executable
echo "Building standalone executable..."
python build.py --executable

# Create deployment package
echo "Creating deployment package..."
python build.py --package tar

echo ""
echo "========================================"
echo "Deployment completed successfully!"
echo "========================================"
echo ""
echo "Files created:"
echo "- Executable: $DIST_DIR/reportserver"
echo "- Package: $DIST_DIR/reportserver_package.tar.gz"
echo ""
echo "To run the server:"
echo "1. Extract reportserver_package.tar.gz"
echo "2. Run ./start_server.sh"
echo "3. Access API at http://localhost:8080/api"
echo ""

# Deactivate virtual environment
deactivate
