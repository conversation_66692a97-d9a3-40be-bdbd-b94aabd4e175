use crate::entities::{prelude::*, ss_identity};
use anyhow::{anyhow, Result};
use chrono::Datelike;
use chrono::{DateTime, Local};
use tracing::*;
use sea_orm::{ActiveModelTrait, ColumnTrait, DatabaseConnection, DbBackend, EntityTrait, QueryFilter, QueryTrait, TransactionTrait};
use serde_json::json;
use utility::timeutil;

impl SsIdentity {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<SsIdentity>> {
    if code.is_empty() {
      return Err(anyhow!("empty code, not allowed"));
    }

    let ret = db.begin().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("Start txn error:{}", ret.as_ref().err().unwrap().to_string()));
    }
    let txn = ret.unwrap();
    let query = SsIdentityEntity::find().filter(ss_identity::Column::IdName.eq(code));
    info!("query string:{:?}", &query.build(DbBackend::MySql).to_string());
    let ret = query.one(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let ret_data = ret.unwrap();
    if ret_data.is_none() {
      return Err(anyhow!("cant find ss_identity by {}", code));
    }
    let mut idty = ret_data.unwrap();
    idty.id_cvalue = idty.id_cvalue + idty.id_incvalue as i64;
    if code.eq_ignore_ascii_case("reportnum")
      || code.eq_ignore_ascii_case("nreportnum")
      || code.eq_ignore_ascii_case("olreport")
      || code.eq_ignore_ascii_case("olnotice")
      || code.eq_ignore_ascii_case("ofreport")
      || code.eq_ignore_ascii_case("recheck")
      || code.eq_ignore_ascii_case("transnum")
      || code.eq_ignore_ascii_case("radio")
    {
      //check years
      let local: DateTime<Local> = Local::now();
      let year = local.year();
      if year != idty.id_ovalue {
        idty.id_cvalue = 1;
        idty.id_ovalue = year;
      }
    }

    let current_year = timeutil::get_current_year_month_day().0;
    // if code.eq_ignore_ascii_case("barcode") && (idty.id_ovalue == current_year || idty.id_ovalue == current_year - 1) {
    if code.eq_ignore_ascii_case("barcode") && (idty.id_ovalue - current_year).abs() == 1 {
      //txsy 年份变化
      if current_year != idty.id_ovalue {
        idty.id_ovalue = current_year;
        idty.id_cvalue = 8000000;
      }
    }

    // info!("new identity is:{:?}", &idty);
    let ret = ss_identity::ActiveModel::from_json(json!(idty.clone()));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let new_val = ret.unwrap();
    // let mut new_val: ss_identity::ActiveModel = idty.clone().into();
    // new_val.id = Set(idty.id);
    // info!("new active model:{:#?}", &new_val);
    // let query = SsIdentityEntity::update(new_val).filter(ss_identity::Column::Id.eq(idty.id));
    // info!("query string:{:?}", &query.build(DbBackend::MySql).to_string());
    // let ret = query.exec(db).await;

    let ret = new_val.save(&txn).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let ret = txn.commit().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("Commit txn error:{}", ret.as_ref().err().unwrap().to_string()));
    }
    info!("index value:{:#?}", &idty);
    Ok(Some(idty))
  }

  pub async fn query_many(code: &str, db: &DatabaseConnection) -> Result<Vec<SsIdentity>> {
    // let ret = NxJobinfoEntity::find_by_id(id).one(db).await;
    let ret = SsIdentityEntity::find().filter(ss_identity::Column::IdName.starts_with(code)).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
