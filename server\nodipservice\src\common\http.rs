// use std::result;

use serde::{Deserialize, Serialize};
// use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct ResponseBody<T> {
  pub code: i32,
  pub message: String,
  pub data: T,
}

impl<T> ResponseBody<T> {
  pub fn new(code: i32, message: &str, data: T) -> ResponseBody<T> {
    ResponseBody {
      code: code,
      message: message.to_string(),
      data,
    }
  }
  // pub fn response_error(error: &str, data: T) -> ResponseBody<T> {
  //   let databody = DataBody::new(1, data);
  //   ResponseBody::new(HttpCode::Error as i32, &error, databody)
  // }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DataBody<T> {
  pub total: u64,
  pub data: T,
}
impl<T> DataBody<T> {
  pub fn new(total: u64, data: T) -> DataBody<T> {
    DataBody { total: total, data: data }
  }
}
