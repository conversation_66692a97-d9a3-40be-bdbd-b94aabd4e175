//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "gj_cdc_occudisease")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub cdc_occdis_code: String,
    pub cdc_occdis_name: String,
    pub cdc_occdis_pcode: String,
    pub cdc_remark: String,
    pub tj_disid: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
