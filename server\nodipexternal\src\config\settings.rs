use config::{Config, ConfigError, File};
use serde::Deserialize;

#[derive(Debu<PERSON>, <PERSON><PERSON>, Deserialize)]
pub struct Database {
  pub uri: String,
}

#[derive(Debug, Clone, Deserialize)]
pub struct Application {
  pub apphost: String,
  pub appport: i32,
}

#[derive(Debug, Clone, Deserialize)]
pub struct Server {
  pub host: String,
  pub port: i32,
}

#[derive(Debug, <PERSON>lone, Deserialize)]
#[allow(unused)]
pub struct External {
  pub exttype: String,
  pub serverurl: String,
  pub appsn: String,
}

#[derive(Debug, <PERSON>lone, Deserialize)]
pub struct Settings {
  pub database: Database,
  pub application: Application,
  pub server: Server,
  pub external: External,
}

impl Settings {
  pub fn init() -> Result<Self, ConfigError> {
    let s = Config::builder()
      // Start off by merging in the "default" configuration file
      .add_source(File::with_name("config/nodipexternal.toml"))
      // // Add in the current environment file
      // // Default to 'development' env
      // // Note that this file is _optional_
      // .add_source(File::with_name(&format!("examples/hierarchical-env/config/{}", run_mode)).required(false))
      // // Add in a local configuration file
      // // This file shouldn't be checked in to git
      // .add_source(File::with_name("examples/hierarchical-env/config/local").required(false))
      // // Add in settings from the environment (with a prefix of APP)
      // // Eg.. `APP_DEBUG=1 ./target/app` would set the `debug` key
      // .add_source(Environment::with_prefix("app"))
      // // You may also programmatically change settings
      // .set_override("database.url", "postgres://")?
      .build()
      .expect("build config file error");

    s.try_deserialize()
  }
}
