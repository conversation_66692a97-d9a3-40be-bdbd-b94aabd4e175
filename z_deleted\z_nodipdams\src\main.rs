use axum::{body::Body, extract::Request};
use base64::Engine;
use chrono::prelude::*;
use dataservice::{dbinit::DbConnection, entities::prelude::*};
// use axum::extract::State;
// use dbopservice::dbinit::DbConnection;
// use dataservice::dbinit::DbConnection;
use hyper::body::Incoming;
use hyper_util::{
  client::legacy::connect::HttpConnector,
  rt::{TokioExecutor, TokioIo},
};

use service::damsservice::DamsService;
// use strum::IntoEnumIterator;
use tower::Service;

use std::{sync::Arc, time::Duration};
// use nodipservice::prelude::DictService;
use crate::{common::constant::DatabaseType, config::settings::Settings};

use tokio::{
  signal,
  sync::{watch, RwLock},
};
use utility::{loggings, uidservice::UidgenService};

mod api;
mod auth;
mod common;
mod config;
mod nxmiddleware;
mod service;

#[cfg(test)]
mod test;

type Client = hyper_util::client::legacy::Client<HttpConnector, Body>;

#[tokio::main]
async fn main() {
  loggings::log_init("config/dams.yaml");

  let sys_config = Settings::init("config/dams.toml").expect("init configuration error");
  info!("Configuration file:{:?}", &sys_config);
  init_startup(&sys_config);
  let sys_config_clone = sys_config.clone();

  // let client = Client::new();
  let client: Client = hyper_util::client::legacy::Client::<(), ()>::builder(TokioExecutor::new()).build(HttpConnector::new());

  let httpaddr = format!("{}:{}", sys_config.application.apphost, sys_config.application.appport);
  // let httpaddr: SocketAddr = http_url.as_str().parse().expect("parse url error");
  // info!("start to create database connection......");
  let dbconn = DbConnection::new(&sys_config.database.uri.as_str(), DatabaseType::MySql as i32).await;
  let syscache = nodipservice::common::syscache::SysCache::new(&"33".to_string(), &dbconn).await;
  nodipservice::SYSCACHE.set(syscache).expect("set global value error");
  let mut uidgen = UidgenService::new(1, 1);
  let mut uidgen_clone = uidgen.clone();
  // let ret = crate::service::damsservice::DamsService::upload_medexam_report(&"10016609".to_string(), &sys_config, &mut uidgen, &dbconn).await;

  let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel::<i32>();
  //create database clone
  let dbconn_clone = dbconn.clone();
  let mut dosync = 0;
  let args: Vec<String> = std::env::args().collect();
  info!("参数列表:{:?}", &args);
  if args.len() > 1 {
    dosync = args[1].parse::<i32>().unwrap_or_default();
  }
  if dosync == 1 {
    let _ret = DamsService::auto_upload_dams(&sys_config, &mut uidgen, &dbconn).await;
  }

  info!("start to create routes......");
  let app = api::routers::create_route(Arc::new(dbconn), Arc::new(sys_config), Arc::new(RwLock::new(uidgen)), Arc::new(tx), client);

  let version = env!("CARGO_PKG_VERSION");
  info!(
    "{} server started on {}, version:{}, {}",
    env!("CARGO_PKG_NAME"),
    &httpaddr,
    &version,
    env!("CARGO_PKG_DESCRIPTION")
  );

  let mut time_interval = tokio::time::interval(Duration::from_secs(3600));
  tokio::spawn(async move {
    time_interval.tick().await;
    loop {
      tokio::select! {
        ct = rx.recv() =>{
            if let Some(_cachetype) = ct{
              info!("缓存数据需要更新了:{:?}",&ct);
              // SYSCACHE.get().unwrap().update_caches(cachetype,&dbconn_clone).await;
          }
        }
        _=time_interval.tick()=>{
            //触发定时任务，可以清除本地数据库中一些不用的临时数据，比如tj_labresult表
          let local: DateTime<Local> = Local::now();
            if local.hour() > 1 && local.hour() <= 2 {
              info!("1-2点，开始数据清理......");
              // AkaSvc::clear_data(&dbconn_clone).await;
              // HazardSvc::clean_hazardinfos(&dbconn_clone).await;
              // for ct in nodipservice::common::constant::CacheType::iter(){
              //   SYSCACHE.get().unwrap().update_caches(ct as i32, &dbconn_clone).await;
              // }
            }
            if (local.hour() > 14 && local.hour() <= 17) || (local.hour() > 1 && local.hour() <= 2) {
              let _ret = DamsService::auto_upload_dams(&sys_config_clone,&mut uidgen_clone, &dbconn_clone).await;
            //   if sys_config_clone.application.autorecvlis == YesOrNo::Yes as i32{
            //   info!("2-3点，开始自动接收lis数据......");
            //   // let _ = RecvLabdata::auto_recv_labresult(&sys_config_clone,  YesOrNo::Yes as i64, &dbconn_clone).await;
            // }
              // if sys_config_clone.application.autorecvpacs == YesOrNo::Yes as i32{
              // info!("2-3点，开始自动接收pacs数据......");
              // let _ = RecvPacsData::auto_recv_pacsresult(&sys_config_clone,  YesOrNo::Yes as i64, &dbconn_clone).await;
            // }
          }
        }
      }
    }
  });
  // let listener = tokio::net::TcpListener::bind(&httpaddr).await.expect("bind error");
  // axum::serve(listener, app.into_make_service_with_connect_info::<SocketAddr>()).await.expect("start server error");

  let listener = tokio::net::TcpListener::bind(&httpaddr).await.expect("bind error");

  // Create a watch channel to track tasks that are handling connections and wait for them to complete.
  let (close_tx, close_rx) = watch::channel(());

  // axum::serve(listener, app.into_make_service_with_connect_info::<SocketAddr>()).await.expect("start server error");
  loop {
    let (socket, remote_addr) = tokio::select! {
        // Either accept a new connection...
        result = listener.accept() => {
            result.unwrap()
        }
        // ...or wait to receive a shutdown signal and stop the accept loop.
        _ = shutdown_signal() => {
            info!("signal received, not accepting new connections");
            break;
        }
    };

    info!("connection {remote_addr} accepted");
    // We don't need to call `poll_ready` because `Router` is always ready.
    let tower_service = app.clone();
    // Clone the watch receiver and move it into the task.
    let close_rx = close_rx.clone();
    // Spawn a task to handle the connection. That way we can serve multiple connections
    // concurrently.
    tokio::spawn(async move {
      // Hyper has its own `AsyncRead` and `AsyncWrite` traits and doesn't use tokio.
      // `TokioIo` converts between them.
      let socket = TokioIo::new(socket);

      // Hyper also has its own `Service` trait and doesn't use tower. We can use
      // `hyper::service::service_fn` to create a hyper `Service` that calls our app through
      // `tower::Service::call`.
      let hyper_service = hyper::service::service_fn(move |mut request: Request<Incoming>| {
        // We have to clone `tower_service` because hyper's `Service` uses `&self` whereas
        // tower's `Service` requires `&mut self`.
        // request.headers_mut().insert(HOST, remote_addr);
        request.extensions_mut().insert(remote_addr);
        // We don't need to call `poll_ready` since `Router` is always ready.
        tower_service.clone().call(request)
      });

      // `hyper_util::server::conn::auto::Builder` supports both http1 and http2 but doesn't
      // support graceful so we have to use hyper directly and unfortunately pick between
      // http1 and http2.
      let conn = hyper::server::conn::http1::Builder::new()
        .serve_connection(socket, hyper_service)
        // `with_upgrades` is required for websockets.
        .with_upgrades();

      // `graceful_shutdown` requires a pinned connection.
      let mut conn = std::pin::pin!(conn);

      loop {
        tokio::select! {
            // Poll the connection. This completes when the client has closed the
            // connection, graceful shutdown has completed, or we encounter a TCP error.
            result = conn.as_mut() => {
                if let Err(err) = result {
                    info!("failed to serve connection: {err:#}");
                }
                break;
            }
            // Start graceful shutdown when we receive a shutdown signal.
            //
            // We use a loop to continue polling the connection to allow requests to finish
            // after starting graceful shutdown. Our `Router` has `TimeoutLayer` so
            // requests will finish after at most 10 seconds.
            _ = shutdown_signal() => {
                info!("signal received, starting graceful shutdown");
                conn.as_mut().graceful_shutdown();
            }
        }
      }

      info!("connection {remote_addr} closed");

      // Drop the watch receiver to signal to `main` that this task is done.
      drop(close_rx);
    });
  }
  // We only care about the watch receivers that were moved into the tasks so close the residual
  // receiver.
  drop(close_rx);

  // Close the listener to stop accepting new connections.
  drop(listener);

  // Wait for all tasks to complete.
  info!("waiting for {} tasks to finish", close_tx.receiver_count());
  close_tx.closed().await;
  info!("server exited successfully");

  //  let socket = hyper_util::rt::TokioIo::new(socket);
  // let conn = hyper::server::conn::http1::Builder::new().serve_connection(socket, app)

  // axum::Server::bind(&httpaddr)
  //   // .serve(app.into_make_service())
  //   .serve(app.into_make_service_with_connect_info::<SocketAddr>())
  //   .with_graceful_shutdown(shutdown_signal())
  //   .await
  //   .expect("start server error");
}

fn init_startup(config: &Settings) {
  //create directory???
  // fs::create_dir_all(&config.application.uploaddir).expect("create uploaddir directory error");
  // fs::create_dir_all(&config.application.reportdir).expect("create reportdir directory error");
  // fs::create_dir_all(&config.application.photodir).expect("create photodir directory error");
  // fs::create_dir_all(&config.application.signdir).expect("create signdir directory error");
  // fs::create_dir_all(&config.application.filedir).expect("create filedir directory error");
}

async fn shutdown_signal() {
  let ctrl_c = async {
    signal::ctrl_c().await.expect("failed to install Ctrl+C handler");
  };
  #[cfg(unix)]
  let terminate = async {
    signal::unix::signal(signal::unix::SignalKind::terminate())
      .expect("failed to install signal handler")
      .recv()
      .await;
  };
  #[cfg(not(unix))]
  let terminate = std::future::pending::<()>();
  tokio::select! {
      _ = ctrl_c => {},
      _ = terminate => {},
  }
  // info!("signal received, starting graceful shutdown");
}
