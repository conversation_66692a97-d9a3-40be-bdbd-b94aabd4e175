use pinyin::To<PERSON>inyin;

pub fn get_first_letter(s: &str) -> String {
    let mut ret = String::from("");
    for pinyin in s.to_pinyin() {
        if let Some(pinyin2) = pinyin {
            ret.push_str(pinyin2.first_letter().to_string().to_uppercase().as_str());
            // print!("{}", pinyin2.first_letter().to_string().to_uppercase());
        }
    }
    // println!("first letter: {}", ret);

    ret
}

pub fn get_pinyin_multi(s: &str) -> String {
    let mut ret = String::from("");
    for pinyin in s.to_pinyin() {
        if let Some(pinyin2) = pinyin {
            ret.push_str(pinyin2.with_tone_num().to_string().to_uppercase().as_str());
            // print!("{}", pinyin2.first_letter().to_string().to_uppercase());
        }
    }
    // println!("first letter: {}", ret);

    ret
}
