//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "gj_cdc_checkitem")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub cdc_itemcode: String,
    pub cdc_item_name: String,
    pub cdc_item_level: i8,
    pub cdc_item_pcode: String,
    pub cdc_is_final: i8,
    pub tj_itemid: String,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
