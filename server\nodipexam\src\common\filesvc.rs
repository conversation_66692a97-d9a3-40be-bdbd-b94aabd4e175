// use std::{fs::File, io::Write, sync::Arc};

use std::{ffi::OsStr, path::Path};

use anyhow::{Result, anyhow};
use axum::{body::Bytes, extract::Multipart};
// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
// use futures::{Stream, TryStreamExt};
use headers::{HeaderMap, HeaderValue};
// use http_body_util::StreamBody;
use hyper::header;
use nodipservice::medexam::patientsvc::PatientSvc;
use std::io::Read;
use tokio::{fs::File, io::AsyncWriteExt};
use tracing::*;
use utility::uidservice::UidgenService;
// use tokio_util::io::ReaderStream;

use crate::config::settings::Settings;
pub struct FileSvc;

impl FileSvc {
  pub async fn upload_sign(config: &Settings, db: &DbConnection, uid: &mut UidgenService, mut multipart: Multipart) -> Result<String> {
    let mut new_filename = "".to_string();
    while let Some(field) = multipart.next_field().await.unwrap() {
      let file_name = field.file_name();
      let field_name = field.name();
      info!("field name:{:?}", &field_name);
      if file_name.is_none() {
        return Err(anyhow!("file name is empty"));
      }
      let path = Path::new(file_name.unwrap());

      // let filename = path.file_stem().and_then(OsStr::to_str).unwrap_or_default().to_string();
      let mut testid = path.file_stem().and_then(OsStr::to_str).unwrap_or_default().to_string();
      let filename = uid.get_uid();
      let extension = path.extension().and_then(OsStr::to_str).map_or("ext".to_string(), |f| f.to_string());
      new_filename = format!("{}.{}", filename, extension);
      info!("new file name is:{}", &new_filename);
      // let mut testid = filename.clone();
      if field_name.is_some() {
        //this should be testid
        let newtestid = field_name.unwrap();
        if !newtestid.is_empty() {
          testid = newtestid.to_owned();
        }
      }

      //更新签名信息
      let ret = PatientSvc::query_patient_by_testid(&testid, &db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let ptret = ret.unwrap();
      if ptret.is_some() {
        let mut ptinfo = ptret.unwrap();
        // new_filename = format!("{}.{}", &ptinfo.tj_pid, extension);
        ptinfo.tj_sign = new_filename.clone();
        let ret = PatientSvc::update_patient(&ptinfo, &db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
      }

      let ret = field.bytes().await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      }
      let data = ret.unwrap();
      let ret = FileSvc::save_file(&config.application.signdir, &new_filename, &data).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }

      // let savefile = format!("{}/{}", &config.application.signdir, &new_filename);
      // let ret = File::create(savefile).await;
      // if ret.as_ref().is_err() {
      //   return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      // }
      // let mut f = ret.unwrap();
      // let ret = f.write_all(&data).await;
      // if ret.as_ref().is_err() {
      //   return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      // }
      // let ret = f.sync_all().await;
      // if ret.as_ref().is_err() {
      //   return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      // }
    }

    Ok(new_filename)
  }

  // pub async fn download_sign(filename: &str) -> Result<(HeaderMap, StreamBody<ReaderStream<File>>)> {
  //   let file = match tokio::fs::File::open(&filename).await {
  //     Ok(file) => file,
  //     Err(err) => {
  //       error!("{} error: {:?}", &filename, &err);
  //       return Err(anyhow!("{} not found", filename));
  //     }
  //   };
  //   // convert the `AsyncRead` into a `Stream`
  //   let stream = ReaderStream::new(file);
  //   let mime_type = mime_guess::from_path(filename).first_or_text_plain();
  //   info!("mime type:{:?}", &mime_type);
  //   // convert the `Stream` into an `axum::body::HttpBody`
  //   let body = StreamBody::new(stream);
  //   let mut headers = HeaderMap::new();
  //   // headers.insert("Content-Type", "image/jpeg".parse().unwrap());
  //   headers.insert(header::CONTENT_TYPE, HeaderValue::from_str(mime_type.as_ref()).unwrap());
  //   let file_onely_name = filename.split('/').last();
  //   info!("file only name is:{:?}", &file_onely_name);
  //   let mut file_only_name = "";
  //   if file_onely_name.is_some() {
  //     file_only_name = file_onely_name.unwrap();
  //   }
  //   headers.insert(header::CONTENT_DISPOSITION, format!(r#"attachment; filename="{}""#, file_only_name).parse().unwrap());
  //   Ok((headers, body))
  // }

  pub async fn do_upload(directory: &str, mut multipart: Multipart) -> Result<String> {
    let mut filename = "".to_string();
    while let Some(field) = multipart.next_field().await.unwrap() {
      // let fname = field.name();
      let file_name = field.file_name();

      info!("file_name:{:?}", &file_name);

      if file_name.is_none() {
        return Err(anyhow!("file name is empty"));
      }

      filename = file_name.unwrap().to_string();

      let ret = field.bytes().await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      }
      let data = ret.unwrap();
      // let new_filename = format!("{}.{}", newname, extension);
      let ret = FileSvc::save_file(&directory, &filename, &data).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      }
    }

    Ok(filename)
  }

  // pub async fn do_download(filename: &str) -> Result<(HeaderMap, StreamBody<ReaderStream<File>>)> {
  pub async fn do_download(filename: &str) -> Result<(HeaderMap, Vec<u8>)> {
    // let file = match tokio::fs::File::open(&filename).await {
    //   Ok(file) => file,
    //   Err(err) => {
    //     error!("{} not found with error:{}", &filename, err.to_string());
    //     return Err(anyhow!("{} not found", filename));
    //   } // Err(err) => return response_json_error("can't find file specified"),
    // };

    // // convert the `AsyncRead` into a `Stream`
    // let stream = ReaderStream::new(file);
    // // convert the `Stream` into an `axum::body::HttpBody`
    // let body = StreamBody::new(stream);

    // let path = Path::new(filename);
    let mut file = match std::fs::File::open(filename) {
      Ok(f) => f,
      Err(err) => {
        error!("{} not found with error:{}", &filename, err.to_string());
        return Err(anyhow!("{} not found", filename));
      }
    };
    let mut body = vec![];
    file.read_to_end(&mut body)?;

    let mime_type = mime_guess::from_path(filename).first_or_text_plain();
    info!("mime type:{:?}", &mime_type);

    let mut headers = HeaderMap::new();
    // headers.insert("Content-Type", "image/jpeg".parse().unwrap());
    headers.insert(header::CONTENT_TYPE, HeaderValue::from_str(mime_type.as_ref()).unwrap());
    let file_onely_name = filename.split('/').last();
    // info!("file only name is:{:?}", &file_onely_name);
    let mut file_only_name = "";
    if file_onely_name.is_some() {
      file_only_name = file_onely_name.unwrap();
    }
    info!("file name is:{:?}", &file_only_name);
    headers.insert(header::CONTENT_DISPOSITION, format!("attachment; filename=\"{}\"", file_only_name).parse().unwrap());
    // headers.insert(header::CONTENT_DISPOSITION, "attachment;filename=\"report.pdf\"".parse().unwrap());

    Ok((headers, body))
  }

  pub async fn save_file(directory: &str, filename: &str, data: &Bytes) -> Result<String> {
    let new_filename = format!("{}/{}", directory, filename);
    info!("new file name is:{}", &new_filename);
    let ret = File::create(&new_filename).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
    }
    let mut f = ret.unwrap();
    let ret = f.write_all(&data).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
    }
    let ret = f.sync_all().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
    }
    Ok(new_filename)
  }
}
