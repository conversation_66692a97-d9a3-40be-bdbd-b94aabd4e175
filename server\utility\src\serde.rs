use serde::{ser::SerializeSeq, Deserialize, Deserializer, Serializer};

use crate::timeutil;
pub fn deserialize_string_to_i64<'de, D>(deserializer: D) -> Result<i64, D::Error>
where
  D: Deserializer<'de>,
{
  #[derive(Deserialize)]
  #[serde(untagged)]
  enum StringOrInt {
    String(String),
    Int(i64),
  }

  match StringOrInt::deserialize(deserializer)? {
    StringOrInt::String(s) => Ok(s.parse::<i64>().unwrap_or_default()),
    StringOrInt::Int(i) => Ok(i),
  }
}

pub fn serialize_i64_to_string<S>(x: &i64, s: S) -> Result<S::Ok, S::Error>
where
  S: Serializer,
{
  s.serialize_str(x.to_string().as_str())
}

pub fn serialize_timestamp_to_string<S>(x: &i64, s: S) -> Result<S::Ok, S::Error>
where
  S: Serializer,
{
  // s.serialize_str(x.to_string().as_str())
  // let ret = "";
  if *x == 0 {
    return s.serialize_str("");
  }

  s.serialize_str(timeutil::format_timestamp(*x).as_str())
}

pub fn deserialize_string_to_timestamp<'de, D>(deserializer: D) -> Result<i64, D::Error>
where
  D: Deserializer<'de>,
{
  let ret = Deserialize::deserialize(deserializer);
  if ret.as_ref().is_err() {
    return Ok(timeutil::current_timestamp());
  }
  let s = ret.unwrap();
  Ok(timeutil::convert_datetime_to_timestamp(s, "YYYY-MM-DD HH:MM:SS"))
}

pub fn serialize_i64_to_string_seq<S>(x: &Vec<i64>, s: S) -> Result<S::Ok, S::Error>
where
  S: Serializer,
{
  // s.serialize_str(x.to_string().as_str())
  let mut seq = s.serialize_seq(Some(x.len()))?;
  for ele in x {
    seq.serialize_element(&ele.to_string())?;
  }
  seq.end()
}

pub fn deserialize_string_to_i64_seq<'de, D>(deserializer: D) -> Result<Vec<i64>, D::Error>
where
  D: Deserializer<'de>,
{
  let ret: Result<Vec<String>, <D as Deserializer>::Error> = Deserialize::deserialize(deserializer);
  // let ret = Deserialize::deserialize(deserializer);
  if ret.as_ref().is_err() {
    return Err(ret.err().unwrap());
  }
  let s = ret.unwrap();
  let vec_i64: Vec<i64> = s.iter().map(|x| x.parse::<i64>().unwrap_or_default()).collect();
  // Ok(timeutil::convert_datetime_to_timestamp(s))

  Ok(vec_i64)
}

pub fn serialize_string_to_i64_seq<S>(x: &Vec<String>, s: S) -> Result<S::Ok, S::Error>
where
  S: Serializer,
{
  // s.serialize_str(x.to_string().as_str())
  let mut seq = s.serialize_seq(Some(x.len()))?;
  for ele in x {
    seq.serialize_element(&ele.parse::<i64>().unwrap_or_default())?;
  }
  seq.end()
}

pub fn deserialize_i64_to_string_seq<'de, D>(deserializer: D) -> Result<Vec<String>, D::Error>
where
  D: Deserializer<'de>,
{
  let ret: Result<Vec<i64>, <D as Deserializer>::Error> = Deserialize::deserialize(deserializer);
  // let ret = Deserialize::deserialize(deserializer);
  if ret.as_ref().is_err() {
    return Err(ret.err().unwrap());
  }
  let s = ret.unwrap();
  let vec_ret: Vec<String> = s.iter().map(|x| x.to_string()).collect();
  // Ok(timeutil::convert_datetime_to_timestamp(s))

  Ok(vec_ret)
}
