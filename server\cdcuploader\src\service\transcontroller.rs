use crate::{
  app::{self, CardlistqueryDto, DataType, Refusedcard},
  client::transclient::TransClient,
  config::settings::Settings,
};
use anyhow::{anyhow, Result};
// use log::*;
use tracing::*;
// use dbopservice::{dataaccess::tj_medexaminfo::TjMedexaminfo, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
// use nodipservice::common::syscache::SysCache;
use utility::timeutil::current_timestamp;
// use odipservice::prelude::{DictService, MedinfoService};
// use std::fs;
// use std::io::prelude::*;
use std::sync::Arc;
// use zip::write::FileOptions;

use super::{
  nbservice::{self, transdata::NbService},
  wzservice,
  zjservice::{self, transdata::ZjService},
  zwxservice::transdata::ZwxService,
};
use crate::app::ParmDto;
#[derive(Debug)]
pub struct TransController {
  // cardid: Vec<String>, //上报数据的ID
  dbconn: Arc<DbConnection>,
  setting: Arc<Settings>,
  // dto: ParmDto,
}

impl TransController {
  pub fn new(dbconn: &Arc<DbConnection>, setting: &Arc<Settings>) -> Self {
    Self {
      // cardid: parm.tid.to_owned(),
      dbconn: dbconn.clone(),
      setting: setting.clone(),
      // cache: cache.clone(),
      // datatype,
      // dto: dto.clone(),
      // dictdata,
      // optype,
      // force,
    }
  }

  #[tracing::instrument(name = "do_upload", skip_all)]
  pub async fn do_upload(&self, dto: &ParmDto) -> Result<String> {
    //prepare data

    //prepare dictionary
    // let mut dicts_data = DictData::new();
    // // info!("start to init dict data");
    // let ret = dicts_data.init(&self.dbconn).await;
    // if ret.as_ref().is_err() {
    //   return Err(anyhow!("init dicts error:{:?}", ret.as_ref().err().unwrap().to_string()));
    // }

    let server_url;
    let upload_content: String;
    // let upload_ret: Result<String>;
    if self.setting.system.platform == app::Platform::Shanxi.to_string() || self.setting.system.platform == app::Platform::Zhongweixin.to_string() {
      //zwx
      // info!("start to upload zwx ");
      let ret = self.generate_zwx_upload(dto).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{:?}", ret.as_ref().err().unwrap().to_string()));
      }
      upload_content = ret.unwrap();
      if dto.datatype == DataType::CORPINFO as i32 {
        server_url = self.setting.organization.server_url2.as_str();
      } else {
        server_url = self.setting.organization.server_url.as_str();
      }
    } else if self.setting.system.platform == app::Platform::Zhejiang.to_string() || self.setting.system.platform == app::Platform::Wenzhou.to_string() {
      //zhejiang
      server_url = self.setting.organization.server_url.as_str();
      let ret = self.generate_zj_upload(dto).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{:?}", ret.as_ref().err().unwrap().to_string()));
      }
      upload_content = ret.unwrap();
    } else if self.setting.system.platform == app::Platform::Ningbo.to_string() {
      server_url = self.setting.organization.server_url.as_str();
      let ret = self.generate_nb_upload(dto).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{:?}", ret.as_ref().err().unwrap().to_string()));
      }
      upload_content = ret.unwrap();
    } else {
      error!("error platform");
      return Err(anyhow::anyhow!("platform:{} not supported", &self.setting.system.platform));
    }

    let final_upload_content: String;
    if self.setting.system.platform == app::Platform::Zhejiang.to_string() {
      final_upload_content = zjservice::common::get_body_wrapper(&upload_content);
    } else if self.setting.system.platform == app::Platform::Ningbo.to_string() {
      final_upload_content = nbservice::common::get_body_wrapper_nb(&upload_content);
    } else if self.setting.system.platform == app::Platform::Wenzhou.to_string() {
      let server_url2 = self.setting.organization.server_url2.as_str();
      if !server_url2.is_empty() {
        //如果是温州，需要先上传到省平台，然后再上传到本地平台
        info!("温州平台，先上传到省平台");
        let zj_content = zjservice::common::get_body_wrapper(&upload_content);
        let ret = TransClient::upload(&zj_content, &self.setting.organization.server_url2, &self.setting).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("省平台:{:?}", ret.as_ref().err().unwrap().to_string()));
        }
        info!("温州平台同步上报省平台返回成功");
      }
      final_upload_content = wzservice::common::get_body_wrapper_wz(&upload_content);
    } else {
      final_upload_content = upload_content;
    }

    let ret = TransClient::upload(&final_upload_content, server_url, &self.setting).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{:?}", ret.as_ref().err().unwrap().to_string()));
    }

    //update database
    if dto.datatype == DataType::HEALTHY as i32 {
      let testids: Vec<String> = dto.tid.iter().map(|v| v.to_string()).collect();
      let mut upstatus = 1;
      let mut checkstatus = nodipservice::common::constant::ExamStatus::CDCUploaded as i32;
      if dto.optype == app::OperationType::DELETE as i32 {
        upstatus = 0;
        checkstatus = nodipservice::common::constant::ExamStatus::Reported as i32;
      }
      info!("update medexam status, upload status:{upstatus}, checkstatus:{checkstatus}");
      let ret = TjMedexaminfo::update_medinfo_upload_status(upstatus, current_timestamp(), checkstatus, &testids, &self.dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("update upload status error"));
      }
    }
    // info!("{:?} uploaded successfully", self.dto.tid);
    let ret_str = ret.unwrap();
    // info!("上报返回数据:{ret_str}");
    Ok(ret_str)
  }

  #[tracing::instrument(name = "do_query_refused_profiles", skip_all)]
  pub async fn do_query_refused_profiles(&self, dto: &CardlistqueryDto) -> Result<Vec<Refusedcard>> {
    let server_url;
    let upload_content: String;
    if self.setting.system.platform == app::Platform::Zhejiang.to_string() || self.setting.system.platform == app::Platform::Wenzhou.to_string() {
      //zhejiang
      server_url = self.setting.organization.server_url.as_str();
      let ret = self.generate_zj_query(dto).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{:?}", ret.as_ref().err().unwrap().to_string()));
      }
      upload_content = ret.unwrap();
    } else if self.setting.system.platform == app::Platform::Ningbo.to_string() {
      server_url = self.setting.organization.server_url.as_str();
      let ret = self.generate_nb_query(dto).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{:?}", ret.as_ref().err().unwrap().to_string()));
      }
      upload_content = ret.unwrap();
    } else {
      error!("error platform");
      return Err(anyhow::anyhow!("platform:{} not supported", &self.setting.system.platform));
    }

    let final_upload_content: String;
    if self.setting.system.platform == app::Platform::Zhejiang.to_string() {
      final_upload_content = zjservice::common::get_body_wrapper(&upload_content);
    } else if self.setting.system.platform == app::Platform::Ningbo.to_string() {
      final_upload_content = nbservice::common::get_body_wrapper_nb(&upload_content);
    }
    // else if self.setting.system.platform == app::Platform::Wenzhou.to_string() {
    //   let server_url2 = self.setting.organization.server_url2.as_str();
    //   if !server_url2.is_empty() {
    //     //如果是温州，需要先上传到省平台，然后再上传到本地平台
    //     info!("温州平台，先上传到省平台");
    //     let zj_content = zjservice::common::get_body_wrapper(&upload_content);
    //     let ret = TransClient::upload(&zj_content, &self.setting.organization.server_url2, &self.setting).await;
    //     if ret.as_ref().is_err() {
    //       return Err(anyhow!("省平台:{:?}", ret.as_ref().err().unwrap().to_string()));
    //     }
    //     info!("温州平台同步上报省平台返回成功");
    //   }
    //   final_upload_content = wzservice::common::get_body_wrapper_wz(&upload_content);
    // }
    else {
      final_upload_content = upload_content;
    }
    info!("final_upload_content is: {:?}", &final_upload_content);
    let ret = TransClient::query(&final_upload_content, server_url, &self.setting).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{:?}", ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  async fn generate_zwx_upload(&self, dto: &ParmDto) -> Result<String> {
    // let upload_content = ZwxService::trans_corpinfo(info)
    let ret = ZwxService::trans_data(dto, &self.setting, &self.dbconn).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  async fn generate_zj_upload(&self, dto: &ParmDto) -> Result<String> {
    let ret = ZjService::trans_data(dto, &self.setting, &self.dbconn).await;
    // info!("FINAL:{:?}", &ret);
    ret
    // Ok("".to_string())
  }

  async fn generate_nb_upload(&self, dto: &ParmDto) -> Result<String> {
    let ret = NbService::trans_data(dto, &self.setting, &self.dbconn).await;
    // info!("FINAL:{:?}", &ret);
    ret
    // Ok("".to_string())
  }

  async fn generate_zj_query(&self, dto: &CardlistqueryDto) -> Result<String> {
    let query_parm = zjservice::datastructs::Cardlistqueryparam {
      pagenum: dto.pagenum.to_string(),
      pagesize: dto.pagesize.to_string(),
      writedatebegin: dto.writedatebegin.to_string(),
      writedateend: dto.writedateend.to_string(),
      checktimebegin: dto.checktimebegin.to_string(),
      checktimeend: dto.checktimeend.to_string(),
    };
    let ret = ZjService::trans_refused_profiles(&query_parm, &self.setting).await;
    // info!("FINAL:{:?}", &ret);
    ret
    // Ok("".to_string())
  }

  async fn generate_nb_query(&self, dto: &CardlistqueryDto) -> Result<String> {
    let query_parm = zjservice::datastructs::Cardlistqueryparam {
      pagenum: dto.pagenum.to_string(),
      pagesize: dto.pagesize.to_string(),
      writedatebegin: dto.writedatebegin.to_string(),
      writedateend: dto.writedateend.to_string(),
      checktimebegin: dto.checktimebegin.to_string(),
      checktimeend: dto.checktimeend.to_string(),
    };
    let ret = ZjService::trans_refused_profiles(&query_parm, &self.setting).await;
    // info!("FINAL:{:?}", &ret);
    ret
  }
}
