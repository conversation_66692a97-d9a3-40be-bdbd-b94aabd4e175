use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_diseasehistory")]
#[derive(<PERSON><PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct TjDiseasehistory {
    pub id: i64,
    pub tj_testid: String,
    pub tj_disname: String,
    pub tj_date: i32,
    pub tj_orgname: String,
    pub tj_curve: String,
    pub tj_isrecover: i32,
    pub tj_finalcode: String,
    pub tj_isoccu: i32,
}
