use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use crate::dto::MultiKeysDto;
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;

pub struct CorpSvc;

impl CorpSvc {
  pub async fn get_corpinfo(id: i64, db: &DbConnection) -> Result<TjCorpinfo> {
    if id <= 0 {
      return Err(anyhow!("id <=0,not allowed"));
    }
    let ret = crate::SYSCACHE.get().unwrap().get_corpinfos(&vec![id], &"".to_string(), &vec![], &db).await;
    if ret.len() <= 0 {
      return Ok(TjCorpinfo { ..Default::default() });
    }
    Ok(ret[0].to_owned())
  }

  pub async fn query_corpinfos(dto: &MultiKeysDto, db: &DbConnection) -> Result<Vec<TjCorpinfo>> {
    let mut corpname = "".to_string();
    if dto.keys_str.len() > 0 {
      corpname = dto.keys_str[0].to_owned();
    }
    info!("query corpinfos dto:{:?}", &dto);
    // let ret = TjCorpinfo::query_many(&mut db.get_connection(), &dto.keys_int, &corpname, &vec![]).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
    let ret = crate::SYSCACHE.get().unwrap().get_corpinfos(&dto.keys_int, &corpname, &vec![], &db).await;
    Ok(ret)
  }

  pub async fn save_corpinfo(dto: &TjCorpinfo, db: &DbConnection) -> Result<TjCorpinfo> {
    let mut corpinfo = dto.to_owned();
    if corpinfo.tj_status != -1 {
      corpinfo.tj_status = 1;
    }
    let ret = crate::SYSCACHE.get().unwrap().get_corpinfos(&vec![corpinfo.id], &"".to_string(), &vec![], &db).await;
    if ret.len() >= 1 {
      info!("企业信息:{:?}", &ret);
      corpinfo.p_cmpid = ret[0].p_cmpid;
    }
    let ret = TjCorpinfo::save(&corpinfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    corpinfo.id = ret.unwrap();
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(crate::common::constant::CacheType::Corpinfo as i32, &dbclone).await;
    });
    Ok(corpinfo)
  }

  pub async fn delete_corpinfo(dto: &MultiKeysDto, db: &DbConnection) -> Result<u64> {
    let ret = TjCorpinfo::delete(&dto.keys_int, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(crate::common::constant::CacheType::Corpinfo as i32, &dbclone).await;
    });
    Ok(1 as u64)
  }
}
