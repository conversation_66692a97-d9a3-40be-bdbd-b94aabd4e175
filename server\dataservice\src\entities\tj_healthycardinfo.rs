use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_healthycardinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_testid: String,
  pub tj_pid: String,
  pub tj_pname: String,
  pub tj_pidcard: String,
  pub tj_meddate: i64,
  pub tj_expdate: i64,
  pub tj_opdate: i64,
  pub tj_operator: i32,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
