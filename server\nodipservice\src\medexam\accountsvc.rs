use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;

pub struct AccountSvc;

impl AccountSvc {
  pub async fn user_login(staffno: &String, password: &String, db: &DbConnection) -> Result<TjStaffadmin> {
    //
    let ret = TjStaffadmin::query(&staffno, &db.get_connection()).await;
    info!("query by staffno result:{:?}", &ret);
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let staff = ret.unwrap();
    if staff.is_none() {
      return Err(anyhow!("账号或者密码错误，请检查账号跟密码"));
    }
    let staff = staff.unwrap();
    // info!("start to encrypt password:{password}");
    let encrypt_passwrod = utility::encrypt::encrypt_nodip(&password);
    if encrypt_passwrod.is_err() {
      return Err(anyhow!("{}", encrypt_passwrod.as_ref().unwrap_err().to_string()));
    }
    let encrypt_password = encrypt_passwrod.unwrap();
    // info!("cbc encrypted password is:{encrypt_password}");

    if encrypt_password != staff.tj_password {
      return Err(anyhow!("账号或者密码错误，请检查账号跟密码"));
    }

    // let token = Claims::generate_token(&staff);

    Ok(staff)
  }
}
