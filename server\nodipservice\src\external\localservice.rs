use std::collections::HashSet;

use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use itertools::Itertools;
use tracing::*;
use utility::{timeutil, uidservice::UidgenService};

use crate::{common::constant, dto::ExternalDTO, SYSCACHE};

pub struct LocalExternal;

impl LocalExternal {
  pub async fn do_upload(dto: &ExternalDTO, medinfo: &TjMedexaminfo, uid: &mut UidgenService, exist_infos: &mut Vec<ExtCheckiteminfo>, db: &DbConnection) -> Result<Vec<ExtCheckiteminfo>> {
    if dto.testid.is_empty() {
      return Err(anyhow!("testid is empty,can't do local upload"));
    }

    //删除
    if dto.exttype == crate::common::constant::ExtOpType::DEL as i32 {
      let ret = ExtCheckiteminfo::delete_many(dto.testid.as_str(), &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      return Ok(vec![]);
    }

    info!("开始体检号为:{} 的外部对接(ext_checkiteminfo)", &dto.testid);
    // let ret = TjMedexaminfo::query(&dto.testid, &db.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let mret = ret.unwrap();
    // if mret.is_none() {
    //   return Err(anyhow!("can't find medinfo by testid:{}", &dto.testid));
    // }
    // let mut medinfo = mret.unwrap();
    let mut medinfo = medinfo.to_owned();

    //处于预约状态的，不需要同步
    if medinfo.tj_checkstatus == crate::common::constant::ExamStatus::Appoint as i32 {
      //如果是预约状态，清除一下对接得数据表???
      // let _ = ExtCheckiteminfo::delete_many(&medinfo.tj_testid, &db.get_connection()).await;
      return Ok(vec![]);
    }

    if !dto.additional.is_empty() && !dto.additional.eq_ignore_ascii_case(&medinfo.tj_additional) {
      medinfo.tj_additional = dto.additional.to_string();
      let ret = TjMedexaminfo::save(&medinfo, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mret = ret.unwrap();
    if mret.is_none() {
      return Err(anyhow!("can't find patient by pid:{}", &medinfo.tj_pid));
    }
    let ptinfo = mret.unwrap();

    let ret = TjCheckiteminfo::query(&medinfo.tj_testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ciinfos = ret.unwrap();

    let ret = TjIteminfo::query_combines(&db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let iteminfos = ret.unwrap();

    let mut new_infos: Vec<ExtCheckiteminfo> = Vec::new();
    // info!("开始处理项目信息......");
    for val in ciinfos.into_iter() {
      if val.tj_combineflag == constant::YesOrNo::No as i32 {
        continue; //明细项目不处理
      }
      let dept = SYSCACHE.get().unwrap().get_department(&val.tj_deptid, db).await;
      // let item = SYSCACHE.get().unwrap().get_iteminfo(&val.tj_itemid, db).await;

      let recorder: i64 = medinfo.tj_recorder.parse().unwrap_or_default();
      let staff = SYSCACHE.get().unwrap().get_staff(recorder, db).await;

      let item_info = iteminfos.iter().find(|&f| f.tj_itemid.eq_ignore_ascii_case(&val.tj_itemid));
      let mut itemid = val.tj_itemid.to_string(); //item.tj_lisnum.clone();
      let mut sample = "".to_string();
      let mut price = "0".to_string();

      if item_info.is_some() {
        let iteminfo = item_info.unwrap();
        sample = iteminfo.sample_code.to_owned(); //SYSCACHE.get().unwrap().get_sampletype(&iteminfo.sample_code, db).await;
        itemid = iteminfo.tj_lisnum.to_owned();
        price = iteminfo.tj_itemprice.to_string();
      }
      let csex = match ptinfo.tj_psex {
        1 => "男",
        2 => "女",
        _ => "",
      };
      let extinfo: ExtCheckiteminfo = ExtCheckiteminfo {
        id: 0,
        testid: medinfo.tj_testid.to_owned(),
        tid: val.tj_barcode.to_owned(),
        testername: ptinfo.tj_pname.to_owned(),
        idcard: ptinfo.tj_pidcard.to_owned(),
        psex: ptinfo.tj_psex,
        csex: csex.to_string(),
        birthdate: ptinfo.tj_pbirthday.to_owned(),
        phone: ptinfo.tj_pphone.to_owned(),
        age: medinfo.tj_age,
        itemname: val.tj_itemname.to_owned(),
        itemid,
        itemid2: val.tj_itemid.to_owned(),
        deptid: val.tj_deptid.to_owned(),
        deptname: dept.tj_deptname.to_owned(),
        depttype: dept.tj_depttype,
        requesterid: medinfo.tj_recorder.to_owned(),
        requestername: staff.tj_staffname.to_owned(),
        requestdate: medinfo.tj_recorddate,
        requestdate2: timeutil::format_timestamp(medinfo.tj_recorddate),
        paytype: medinfo.tj_paymethod,
        packagename: medinfo.tj_packagename.to_owned(),
        zdym: dept.tj_zdym.to_owned(),
        sampletype: sample,
        corpnum: medinfo.tj_corpnum,
        uid: uid.get_uid(),
        syncstatus: 0,
        lis: 0,
        pacs: 0,
        extsn: "".to_string(),
        price,
      };
      new_infos.push(extinfo);
    }

    info!("项目整理结束,开始写入ext_checkiteminfo表...");
    let to_del_infos: Vec<ExtCheckiteminfo> = exist_infos
      .iter()
      // .filter(|p| new_infos.iter().find(|f| f.itemid2.eq_ignore_ascii_case(&p.itemid2)).is_none())
      .filter(|p| new_infos.iter().find(|f| f.tid.eq_ignore_ascii_case(&p.tid)).is_none() || new_infos.iter().find(|f| f.itemid2.eq_ignore_ascii_case(&p.itemid2)).is_none())
      .map(|v| v.to_owned())
      .collect();

    if to_del_infos.len() > 0 {
      //开始删除
      let ids: Vec<i64> = to_del_infos.iter().map(|v| v.id).collect();
      let ret = ExtCheckiteminfo::delete_many_by_ids(&ids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("ext_checkiteminfo delete many error:{}", ret.as_ref().unwrap_err().to_string());
        // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }

    let to_add_iteminfos: Vec<ExtCheckiteminfo> = new_infos
      .iter()
      .filter(|p| exist_infos.iter().find(|f| f.itemid2.eq_ignore_ascii_case(&p.itemid2)).is_none() || exist_infos.iter().find(|f| f.tid.eq_ignore_ascii_case(&p.tid)).is_none())
      .map(|v| v.to_owned())
      .collect();

    info!("本地对接，需要新增项目：{:?}", &to_add_iteminfos);
    info!("本地对接，需要删除项目: {:?}", &to_del_infos);

    if to_add_iteminfos.len() > 0 {
      let ret: std::result::Result<i64, anyhow::Error> = ExtCheckiteminfo::save_many(&to_add_iteminfos, &db.get_connection()).await;
      for i in 1..10 {
        if ret.as_ref().is_err() {
          error!("ext_checkiteminfo insert many error,第{i}次错误{}", ret.as_ref().unwrap_err().to_string());
          // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
          tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
          continue;
        } else {
          info!("第{i}次保存体检号:{}到ext_checkiteminfo成功", &medinfo.tj_testid);
          break;
        }
      }
    }

    if to_add_iteminfos.len() == 0 && to_del_infos.len() == 0 {
      info!("项目不需要增加，也不需要删除，那就是修改体检信息......,exist infos:{}", exist_infos.len());
      if exist_infos.len() > 0 {
        for val in exist_infos.iter_mut() {
          let new_info = new_infos
            .iter()
            .find(|&f| f.testid.eq_ignore_ascii_case(&val.testid) && f.itemid2.eq_ignore_ascii_case(&val.itemid2));
          if new_info.is_none() {
            info!("can't find exist infos by itemid2:{}", &val.itemid2);
            continue;
          }
          let new_info = new_info.unwrap();
          val.age = new_info.age;
          val.birthdate = new_info.birthdate.to_string();
          val.corpnum = new_info.corpnum;
          val.csex = new_info.csex.to_string();
          val.idcard = new_info.idcard.to_string();
          val.packagename = new_info.packagename.to_string();
          val.paytype = new_info.paytype;
          val.phone = new_info.phone.to_string();
          val.psex = new_info.psex;
          val.testername = new_info.testername.to_string();
          val.deptid = new_info.deptid.to_string();
          val.deptname = new_info.deptname.to_string();
          val.itemid = new_info.itemid.to_string();
        }
        let ret = ExtCheckiteminfo::save_many(exist_infos, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
    }

    Ok(new_infos)
  }

  pub async fn import_lab_data(labresults: &Vec<TjLabresult>, db: &DbConnection) -> Result<()> {
    // info!("detail results:{:#?}", &labresults);
    // let struments: Vec<String> = dto
    //   .iter()
    //   .filter(|f| !f.tj_testgroup.is_empty())
    //   .map(|v| v.tj_testgroup.to_string())
    //   .collect::<HashSet<_>>()
    //   .into_iter()
    //   .collect();
    // let itemrefinfos: Vec<TjItemrefinfo>; // = vec![];
    let ret = TjItemrefinfo::query_many(&vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }
    let itemrefinfos = ret.unwrap();
    let mut labresults = labresults.to_owned();
    for labret in labresults.iter_mut() {
      if labret.tj_testgroup.is_empty() {
        //find from iteminfos
        let iteminfos = crate::SYSCACHE.get().unwrap().get_iteminfo_by_lisnum(&labret.tj_analyte).await;
        labret.tj_itemid = iteminfos.into_iter().map(|v| v.tj_itemid).collect::<Vec<String>>().join(",");
      } else {
        //从仪器信息里查找
        let itemids: Vec<String> = itemrefinfos
          .iter()
          .filter(|&f| f.tj_strucode.eq_ignore_ascii_case(&labret.tj_analyte) && f.tj_struid.eq_ignore_ascii_case(&labret.tj_testgroup))
          .map(|v| v.tj_itemid.to_string())
          .collect();
        if itemids.len() <= 0 {
          continue;
        }
        labret.tj_itemid = itemids.join(",");
      }
    }

    let testids: Vec<String> = labresults
      .iter()
      .filter(|f| !f.tj_clinicid.is_empty())
      .map(|v| v.tj_clinicid.to_string())
      .collect::<HashSet<_>>()
      .into_iter()
      .collect();

    let analytes: Vec<String> = labresults
      .iter()
      .filter(|f| !f.tj_analyte.is_empty())
      .map(|v| v.tj_analyte.to_string())
      .collect::<HashSet<_>>()
      .into_iter()
      .collect();

    let ret = TjLabresult::delete_by_analytes(&testids, &analytes, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }
    // info!("start to insert lab results......");
    let labrets: Vec<Vec<TjLabresult>> = labresults.into_iter().chunks(1000).into_iter().map(|c| c.collect()).collect();
    for val in labrets.into_iter() {
      let ret = TjLabresult::save_many(&val, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
    }
    // info!("lab results imported......");
    Ok(())
  }
}
