//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_itemresultinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_itemid: String,
  pub tj_itemresult: String,
  pub tj_suminfo: String,
  pub tj_sumflag: i32,
  pub tj_showorder: i32,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
