//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_diseases")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_discode: String,
  pub tj_disname: String,
  pub tj_disnum: String,
  pub tj_deptid: i32,
  pub tj_type: String,
  pub tj_content: String,
  pub tj_occudiseaseflag: i32,
  pub tj_startage: i32,
  pub tj_endage: i32,
  pub tj_sex: i32,
  pub tj_career: String,
  pub tj_marriage: i32,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_typeid: i32,
  pub tj_opinion: String,
  pub tj_status: i32, //1:有效 0:无效
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
  #[sea_orm(has_many = "super::tj_autodiagcondition::Entity")]
  TjAutodiagcondition,
}

impl Related<super::tj_autodiagcondition::Entity> for Entity {
  fn to() -> RelationDef {
    Relation::TjAutodiagcondition.def()
  }
}

impl ActiveModelBehavior for ActiveModel {}
