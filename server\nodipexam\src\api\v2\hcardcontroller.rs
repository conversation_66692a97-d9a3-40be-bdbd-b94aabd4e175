use crate::api::httpresponse::{response_json_value_error, response_json_value_ok};
use axum::{Extension, Json};
// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
use nodipservice::{dto::*, medexam::healthycardsvc::HealthycardSvc};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

pub async fn query_hcardinfos(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyDto>) -> Json<Value> {
  let ret = HealthycardSvc::query_latest_hcard_by_idcard(&dto.key, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  info!("健康证信息:{:#?}", &results);
  response_json_value_ok(1, results)
}
