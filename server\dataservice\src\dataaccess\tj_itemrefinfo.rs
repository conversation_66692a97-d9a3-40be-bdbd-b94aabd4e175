use crate::entities::{prelude::*, tj_itemrefinfo};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjItemrefinfo {
  pub async fn query_many(struids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjItemrefinfo>> {
    let mut conditions = Condition::all();
    if struids.len() > 0 {
      conditions = conditions.add(tj_itemrefinfo::Column::TjStruid.is_in(struids.to_owned()));
    }
    let ret = TjItemrefinfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjItemrefinfo, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_itemrefinfo::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }
  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let conditions = Condition::all().add(tj_itemrefinfo::Column::Id.is_in(ids.to_owned()));
    let ret = TjItemrefinfoEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
