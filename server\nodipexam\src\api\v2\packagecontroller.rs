use crate::{api::httpresponse::{response_json_value_error, response_json_value_ok}, auth::auth::Claims};
use axum::{Extension, Json};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{basic::packagesvc::PackageSvc, dto::*};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

pub async fn save_packageinfo(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<PackageDto>) -> Json<Value> {
  info!("save packageinfos, dto:{:?}", &dto);
  let ret = PackageSvc::save_packageinfos(&dto.pkginfo, &dto.details, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(format!("错误：{}", ret.as_ref().unwrap_err().to_string()).as_str(), TjPackageinfo { ..Default::default() });
  }

  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
//
pub async fn delete_packageinfo(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeysDto>) -> Json<Value> {
  if dto.keys_int.len() <= 0 {
    return response_json_value_error("empty ids, not allowed", 0);
  }
  let ret = PackageSvc::delete_packageinfos(&dto.keys_int, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }

  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
pub async fn query_packageinfos(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  let ret = PackageSvc::query_packageinfos(&db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjPackageinfo>::new());
  }

  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn query_package_details(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeysDto>) -> Json<Value> {
  let ret = PackageSvc::query_package_details(&dto.keys_int, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjPackagedetail>::new());
  }

  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn remove_package_details(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeysDto>) -> Json<Value> {
  if dto.keys_int.len() <= 0 {
    return response_json_value_error("empty ids, not alloed", "");
  }
  let ret = PackageSvc::delete_package_details(&dto.keys_int, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(0, ret.unwrap())
}
