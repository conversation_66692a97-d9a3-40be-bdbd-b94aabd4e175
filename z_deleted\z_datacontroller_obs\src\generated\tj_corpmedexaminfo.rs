//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_corpmedexaminfo")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_corpnum: i32,
    pub tj_corpname: String,
    pub tj_pyjm: String,
    pub tj_testtype: i32,
    pub tj_status: i32,
    pub tj_testers: i32,
    pub tj_checked: i32,
    pub tj_reportnum: String,
    pub tj_staffid: i32,
    pub tj_aptnum: i64,
    pub tj_notifystate: i8,
    pub tj_testdate: i64,
    pub tj_recdate: i64,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::tj_medexamtasks::Entity")]
    TjMedexamtasks,
}

impl Related<super::tj_medexamtasks::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjMedexamtasks.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
