1. 同步结算费用信息时，如果添加或者减少项目，如何处理。总费用（金额）是否汇总所有项目的金额？
2. 订单信息中的费别(ChargeType)怎么填写？
3. 体检时，申请科室名称和代码用什么
4. 开单院区是否必填，如何处理？
5. 检查申请单，比如开腹部 B 超，肝胆脾胰双肾，需要怎么传
6. 检验申请订单，检验主题，检验目的的代码和名称怎么填写？
7. 有没有获取小项目(比如白细胞，红细胞)的接口？
8. 获取收费项目是全部大项目嘛？比如血常规之类的。
9. 麻烦发几个调用的数据，我们参考一下。比如从体检系统到 his 系统，同步基本信息，收费信息，检查信息和检验信息的报文样子，以及返回的报文。

# 新问题

1. 有没有获取小项目(比如白细胞，红细胞)的接口？
2. pacs 中的是否阳性， 什么数值表示阳性？？？
3. 检查结果，如何区分 DR，CT，超声？
4. lis 的结果正常标志，如何判定？什么表示异常，什么表示正常？
5. lis 如果是异常，偏高，偏低，之类的怎么展示？
6. 如果仅仅添加或者减少项目时，如何处理？

## ---

1:没有 你们直接找 LIS 系统要 报告项目
2:不是数值阳性，阳性可能是代表发现异常问题
3:申请单是你们开的，你们自己知道，
4：N-正常 L-低 H 高 至于值域 得看 LIS 回传结果
5:如何展示，问你们对应科室老师，
6:目前是总价，直接加项目的时候，走加钱那个接口，然后收费确认回传你的订单号通知你们收费，退费用你们的订单号退钱，原则是全退在收，把之前的订单推掉，然后生成一笔新的收费订单，这个我需要去确认下。


### Sample1
<soapeny:Envelope xmlns:soapenv="htp://schemas.xmlsoap.org /soap/envelope/" xmlns:tem="http: /tempur.org" >
<soapenv:Header/>
<soapenv:Body>
<tem:Sample1>
<!--Optiona:-->
<tem:method>?</tem:method> (method:ExamApplyInfo;LabApplyInfo)
<!--Optional:-->
<tem:StrJson>?</tem:StrJson>
</tem:Sample1>
</soapenv:Body>
</soapenv:Envelope>

### sample 1 response

<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http:/www.w3.org/2001/XMLSchema-instance" xmlns:s= "http:/www,w3.org/2001/XMLSchema">
<SOAP-ENV:Body>
<Sample1Response xmlns="http://tempuri.org">
<Sample1Result>{"resultCode":"1","descMessage":"没有该方法"}</Sample1Result></Sample1Response>
</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<SOAP-ENV:Envelope xmIns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org2001/XMSchema-instance" xmlns:s="http://www.w3.org/2001/XMLSchema">
<SOAP-ENV:Body>
<Sample1Response xmIns="http://tempuri.org">
<Sample1Result>
    {"resultCode:"1","descMessage":""appNamel": "Master ", "errorCode!"; "ORA-01400\""fulErrorCodel"; "Master ORA-01400\". "
    mesagel": "could not execute batch; SOL I INO DOCTOR ORDERS (BABY NO, BILING ATTR. CHARGE,CHARGE STATUS.CUNIC CATE COSTS DOCTOR. DOCTOR ID. DRUG BI"
    }
</Sample1Result>
</Sample1Response>
</SOAP-ENV:Body>
</SOAP-ENV:Envelope>


### Sample2
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org">
<soapenv:Header/>
<soapenv:Body>
    <tem:Sample2>
    <!--0ptional:-->
    <tem:pInput>?</tem:pInput>
    </tem:Sample2>
</soapenv:Body>
</soapenv:Envelope>

<soapenv:Envelope xmlns:soapenv="http:/schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http:/tempur.org">
<soapenv:Header/>
<soapenv:Body>
<tem:Sample1>
<!--Optional:-->
<tem:method>?</tem:method>
<!--Optional-->
<tem:Strson>?</tem:StrJson>
</tem:Sample1>
</soapenv:Body>
</soapenv:Envelope>

### Sample2 Response
<?xml version="1.0" encoding="UTF-8" ?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV='http://schemas.xmlsoap.org/soap/envelope/' xmlns:xsi= 'http://www.w3.org/2001/XMLSchema-instance' xmlns:s='http://www.w3.org/2001/XMLSchema'>
 <SOAP-ENV:Body>
    <Sample2Response xmlns="http://tempuri.org">
        <Sample2Result> {"resultCode": "0", "descMessage": "", "result":{"visitNo":"20250422114105","patientId":"92856461"}}</Sample2Result> 
    </Sample2Response>
 </SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<SOAP-ENV:Envelope xmlns:SOAP-ENV='http://schemas.xmlsoap.org/soap/envelope/' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xmlns:s='http://www.w3.org/2001/XMLSchema'>
  <SOAP-ENV:Body>
    <Sample2Response xmlns="http://tempuri.org">
        <Sample2Result>{'resultCode":"0" "descMessage ":"" "result ":null}
        </Sample2Result>
    </Sample2Response>
  </SOAP-ENV:Body>
</SOAP-ENV:Envelope>