use crate::entities::{
  prelude::{TjHealthycardinfo, TjHealthycardinfoEntity},
  tj_healthycardinfo,
};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, QueryOrder, Set};
use serde_json::json;

impl TjHealthycardinfo {
  pub async fn query(code: i64, db: &DatabaseConnection) -> Result<Option<TjHealthycardinfo>> {
    let ret = TjHealthycardinfoEntity::find().filter(tj_healthycardinfo::Column::Id.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_latest(idcard: &str, db: &DatabaseConnection) -> Result<Option<TjHealthycardinfo>> {
    let ret = TjHealthycardinfoEntity::find()
      // .filter(tj_healthycardinfo::Column::Id.max())
      .filter(tj_healthycardinfo::Column::TjPidcard.eq(idcard))
      .order_by_desc(tj_healthycardinfo::Column::Id)
      .one(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(idcard: &str, testid: &str, db: &DatabaseConnection) -> Result<Vec<TjHealthycardinfo>> {
    let mut conditions = Condition::all();
    if !idcard.is_empty() {
      conditions = conditions.add(tj_healthycardinfo::Column::TjPidcard.eq(idcard))
    }
    if !testid.is_empty() {
      conditions = conditions.add(tj_healthycardinfo::Column::TjTestid.eq(testid))
    }

    let ret = TjHealthycardinfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjHealthycardinfo, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_healthycardinfo::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn delete(testid: &str, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjHealthycardinfoEntity::delete_many()
      .filter(Condition::all().add(tj_healthycardinfo::Column::TjTestid.eq(testid)))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
}
