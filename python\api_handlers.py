# coding=utf-8

import os
import time
import logging
import threading
from datetime import datetime
from typing import Optional, List, Dict, Any
from pathlib import Path

from api_models import (
    ReportGenerationRequest,
    ReportGenerationResponse,
    ReportStatusResponse,
    ReportFileInfo
)
from report import main as generate_report
from dataentities.tj_corpoccureport import TjCorpoccureport

logger = logging.getLogger(__name__)


class ReportHandler:
    """Handler for report generation and management operations"""

    def __init__(self, config):
        self.config = config
        self.report_status_cache = {}  # Simple in-memory cache for report status
        self.lock = threading.Lock()

    def get_db_session(self):
        """Get the global database session"""
        try:
            # Import here to avoid circular imports
            from reportserver import get_db_session
            return get_db_session()
        except ImportError:
            # Fallback to the original session if reportserver is not available
            from dataentities.dbconn import session
            return session

    def generate_report(self, request: ReportGenerationRequest) -> ReportGenerationResponse:
        """Generate a report based on the request parameters"""
        start_time = time.time()

        try:
            # Update status to processing
            with self.lock:
                self.report_status_cache[request.report_id] = {
                    "status": "processing",
                    "message": "Report generation in progress",
                    "created_at": datetime.now().isoformat(),
                    "output_file": None,
                    "error_details": None
                }

            # Determine output directory
            output_dir = request.output_directory or self.config.output_directory

            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)

            # Call the main report generation function
            logger.info(f"Generating report {request.report_id} with format {request.report_format}")

            result = generate_report(
                rptid=request.report_id,
                reporttype=request.report_format,
                pagestyle=request.page_style,
                splitinrow=request.split_in_row,
                outdir=output_dir
            )

            generation_time = time.time() - start_time

            # Check if report was generated successfully
            if result is None:
                # The generate_report function doesn't return the file path for all cases
                # We need to determine the output file based on the report info
                output_file = self._get_expected_output_file(request.report_id, request.report_format, output_dir)
            else:
                output_file = result

            if output_file and os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                filename = os.path.basename(output_file)

                # Update status to completed
                with self.lock:
                    self.report_status_cache[request.report_id] = {
                        "status": "completed",
                        "message": "Report generated successfully",
                        "created_at": self.report_status_cache[request.report_id]["created_at"],
                        "completed_at": datetime.now().isoformat(),
                        "output_file": filename,
                        "error_details": None
                    }

                return ReportGenerationResponse(
                    success=True,
                    message="Report generated successfully",
                    report_id=request.report_id,
                    output_file=filename,
                    file_size=file_size,
                    generation_time=generation_time
                )
            else:
                # Update status to failed
                with self.lock:
                    self.report_status_cache[request.report_id] = {
                        "status": "failed",
                        "message": "Report generation failed - output file not found",
                        "created_at": self.report_status_cache[request.report_id]["created_at"],
                        "completed_at": datetime.now().isoformat(),
                        "output_file": None,
                        "error_details": "Output file was not created"
                    }

                return ReportGenerationResponse(
                    success=False,
                    message="Report generation failed - output file not found",
                    report_id=request.report_id,
                    generation_time=generation_time
                )

        except Exception as e:
            generation_time = time.time() - start_time
            error_msg = str(e)

            # Update status to failed
            with self.lock:
                self.report_status_cache[request.report_id] = {
                    "status": "failed",
                    "message": f"Report generation failed: {error_msg}",
                    "created_at": self.report_status_cache.get(request.report_id, {}).get("created_at", datetime.now().isoformat()),
                    "completed_at": datetime.now().isoformat(),
                    "output_file": None,
                    "error_details": error_msg
                }

            logger.error(f"Error generating report {request.report_id}: {error_msg}", exc_info=True)

            return ReportGenerationResponse(
                success=False,
                message=f"Report generation failed: {error_msg}",
                report_id=request.report_id,
                generation_time=generation_time
            )

    def get_report_status(self, report_id: int) -> Dict[str, Any]:
        """Get the status of a report generation request"""
        with self.lock:
            status_info = self.report_status_cache.get(report_id)

        if status_info:
            return ReportStatusResponse(
                report_id=report_id,
                status=status_info["status"],
                message=status_info["message"],
                output_file=status_info.get("output_file"),
                created_at=status_info.get("created_at"),
                completed_at=status_info.get("completed_at"),
                error_details=status_info.get("error_details")
            ).__dict__
        else:
            # Check if report exists in database
            try:
                db_session = self.get_db_session()
                rptinfo = db_session.query(TjCorpoccureport).filter(TjCorpoccureport.id == report_id).first()
                if rptinfo:
                    return ReportStatusResponse(
                        report_id=report_id,
                        status="not_found",
                        message="Report exists in database but no generation request found",
                        output_file=None,
                        created_at=None,
                        completed_at=None,
                        error_details=None
                    ).__dict__
                else:
                    return ReportStatusResponse(
                        report_id=report_id,
                        status="not_found",
                        message="Report not found in database",
                        output_file=None,
                        created_at=None,
                        completed_at=None,
                        error_details=None
                    ).__dict__
            except Exception as e:
                logger.error(f"Error checking report status in database: {str(e)}")
                return ReportStatusResponse(
                    report_id=report_id,
                    status="error",
                    message="Error checking report status",
                    output_file=None,
                    created_at=None,
                    completed_at=None,
                    error_details=str(e)
                ).__dict__

    def get_report_file_path(self, filename: str) -> Optional[str]:
        """Get the full path to a report file"""
        file_path = os.path.join(self.config.output_directory, filename)
        logger.info(f"Checking file path: {file_path}")
        if os.path.exists(file_path):
            return file_path
        return None

    def list_available_reports(self) -> List[Dict[str, Any]]:
        """List all available report files"""
        reports = []

        try:
            if not os.path.exists(self.config.output_directory):
                return reports

            for filename in os.listdir(self.config.output_directory):
                file_path = os.path.join(self.config.output_directory, filename)

                if os.path.isfile(file_path) and filename.lower().endswith(('.pdf', '.docx')):
                    stat = os.stat(file_path)

                    # Determine report type and format from filename
                    report_type = None
                    report_format = None

                    if '职业' in filename:
                        report_type = "职业健康报告"
                    elif '普通' in filename:
                        report_type = "普通体检报告"
                    elif '放射' in filename:
                        report_type = "放射报告"

                    if filename.lower().endswith('.pdf'):
                        report_format = "PDF"
                    elif filename.lower().endswith('.docx'):
                        report_format = "DOCX"

                    report_info = ReportFileInfo(
                        filename=filename,
                        file_path=file_path,
                        file_size=stat.st_size,
                        created_at=datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        report_type=report_type,
                        report_format=report_format
                    )

                    reports.append(report_info.__dict__)

            # Sort by creation time (newest first)
            reports.sort(key=lambda x: x['created_at'], reverse=True)

        except Exception as e:
            logger.error(f"Error listing reports: {str(e)}")
            raise

        return reports

    def _get_expected_output_file(self, report_id: int, report_format: int, output_dir: str) -> Optional[str]:
        """Get the expected output file path based on report info"""
        try:
            # Query the database to get report information
            db_session = self.get_db_session()
            rptinfo = db_session.query(TjCorpoccureport).filter(TjCorpoccureport.id == report_id).first()
            if not rptinfo:
                return None

            # Determine file extension
            ext = "docx" if report_format == 1 else "pdf"

            # Determine report type name
            if rptinfo.tj_reporttype == 0:
                type_name = "职业"
            elif rptinfo.tj_reporttype == 1:
                type_name = "普通"
            elif rptinfo.tj_reporttype == 2:
                type_name = "放射"
            else:
                type_name = "未知"

            # Construct expected filename (this should match the pattern used in report.py)
            expected_filename = f"{rptinfo.tj_corpname}-{type_name}-在岗期间-{rptinfo.tj_reportnumint}.{ext}"
            expected_path = os.path.join(output_dir, expected_filename)

            if os.path.exists(expected_path):
                return expected_path

            # Try alternative patterns if the first one doesn't exist
            patterns = [
                f"{rptinfo.tj_corpname}-{type_name}-{rptinfo.tj_reportnumint}.{ext}",
                f"{rptinfo.tj_corpname}-{rptinfo.tj_reportnumint}.{ext}",
            ]

            for pattern in patterns:
                path = os.path.join(output_dir, pattern)
                if os.path.exists(path):
                    return path

            return None

        except Exception as e:
            logger.error(f"Error getting expected output file: {str(e)}")
            return None
