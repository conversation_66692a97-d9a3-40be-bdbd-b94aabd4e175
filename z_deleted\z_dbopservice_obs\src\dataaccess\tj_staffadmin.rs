use anyhow::{anyhow, Result};
use rbatis::rbatis_codegen::IntoSql;
use rbatis::{crud, py_sql};
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjStaffadmin {
  pub id: i64,
  pub tj_staffno: String,
  pub tj_staffname: String,
  pub tj_sex: i32,
  pub tj_deptid: i32,
  pub tj_groupid: i32,
  pub tj_password: String,
  pub tj_role: i32,
  pub tj_checkallflag: i32,
  pub tj_title: String,
  pub tj_status: i32,
  pub tj_operator: String,
  pub tj_moddate: i64,
  pub tj_memo: String,
  pub tj_isadmin: i32,
  pub login_session: String,
  pub tj_esign: String,
}
crud!(TjStaffadmin {}, "tj_staffadmin");
rbatis::impl_select!(TjStaffadmin{query(staffid:&str) -> Option => "`where tj_staffno = #{staffid} `"});
rbatis::impl_select!(TjStaffadmin{query_by_id(id:i64) -> Option => "`where id = #{id} `"});
rbatis::impl_select!(TjStaffadmin{query_many(staffnos:&[&str], ids:&[i64]) => 
  "`where id >= 0 and tj_status >= 0`
  if !staffnos.is_empty():
    ` and tj_staffno in ${staffnos.sql()} `
  if !ids.is_empty():
    ` and id in ${ids.sql()} `
  "});
impl TjStaffadmin {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjStaffadmin) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjStaffadmin::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjStaffadmin::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
  #[py_sql("update tj_staffadmin set tj_status = -1 where id = #{id} ")]
  pub async fn delete(rb: &mut rbatis::RBatis, id: &i64) -> rbatis::Result<()> {
    impled!()
  }
  #[py_sql("update tj_staffadmin set tj_groupid = 0 where tj_groupid = #{gid} ")]
  pub async fn delete_group(rb: &mut rbatis::RBatis, gid: &i64) -> rbatis::Result<()> {
    impled!()
  }
}
