//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_audiogramdetail")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_testid: String,
  pub tj_itemid: String,
  pub tj_freq: Option<i32>,
  pub tj_adtype: i32,
  pub tj_ear: Option<i32>,
  pub tj_result: Option<i32>,
  pub tj_revise: Option<i32>,
  pub tj_cover: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
