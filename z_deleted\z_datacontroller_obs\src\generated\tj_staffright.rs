//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_staffright")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_staffid: i32,
    pub tj_rname: String,
    pub tj_right: i32,
    pub tj_operator: i32,
    pub tj_moddate: i64,
    pub tj_menuid: i32,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
