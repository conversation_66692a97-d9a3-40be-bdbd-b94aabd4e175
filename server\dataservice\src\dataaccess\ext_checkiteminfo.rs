use crate::entities::{
  ext_checkiteminfo,
  prelude::{ExtCheckiteminfo, ExtCheckiteminfoEntity},
};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set, TransactionTrait};
use serde_json::json;

impl ExtCheckiteminfo {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<ExtCheckiteminfo>> {
    let ret = ExtCheckiteminfoEntity::find().filter(ext_checkiteminfo::Column::Testid.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many_by_dto(testid: &String, deptids: &Vec<String>, depttype: i32, db: &DatabaseConnection) -> Result<Vec<ExtCheckiteminfo>> {
    let mut conditions = Condition::all();

    if !testid.is_empty() {
      conditions = conditions.add(ext_checkiteminfo::Column::Testid.eq(testid));
    }

    if deptids.len() > 0 {
      conditions = conditions.add(ext_checkiteminfo::Column::Deptid.is_in(deptids.to_owned()));
    }

    if depttype > 0 {
      conditions = conditions.add(ext_checkiteminfo::Column::Depttype.eq(depttype));
    }

    let ret = ExtCheckiteminfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_by_extsn(extsn: &String, db: &DatabaseConnection) -> Result<Option<ExtCheckiteminfo>> {
    let ret = ExtCheckiteminfoEntity::find().filter(ext_checkiteminfo::Column::Extsn.eq(extsn)).one(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(testid: &String, depttype: i32, db: &DatabaseConnection) -> Result<Vec<ExtCheckiteminfo>> {
    let mut conditions = Condition::all();
    if !testid.is_empty() {
      conditions = conditions.add(ext_checkiteminfo::Column::Testid.eq(testid.to_owned()));
    }

    if depttype > 0 {
      conditions = conditions.add(ext_checkiteminfo::Column::Depttype.eq(depttype));
    }
    let ret = ExtCheckiteminfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn insert_many(info: &Vec<ExtCheckiteminfo>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data to insert checkitems, not allowed"));
    }
    let mut active_values: Vec<ext_checkiteminfo::ActiveModel> = Vec::new();
    for val in info {
      let ret = ext_checkiteminfo::ActiveModel::from_json(json!(val));
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      active_values.push(ret.unwrap());
    }
    let ret = ExtCheckiteminfoEntity::insert_many(active_values).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().last_insert_id)
  }

  pub async fn save_many(infos: &Vec<ExtCheckiteminfo>, db: &DatabaseConnection) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len();
    let (insert_vals, update_vals): (Vec<ExtCheckiteminfo>, Vec<ExtCheckiteminfo>) = infos.to_owned().into_iter().partition(|f| f.id == 0);
    let txn = db.begin().await.expect("start transaction error");
    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<ext_checkiteminfo::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = ext_checkiteminfo::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = ExtCheckiteminfoEntity::insert_many(active_insert_values).exec(&txn).await;
      if ret.as_ref().is_err() {
        let _ = txn.rollback();
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      for val in update_vals {
        let mut new_val = ext_checkiteminfo::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(&txn).await;
        if ret.as_ref().is_err() {
          let _ = txn.rollback();
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    let ret = txn.commit().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(total as i64)
  }

  pub async fn delete_many_by_ids(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids empty, not allowed"));
    }
    let conditions = Condition::all().add(ext_checkiteminfo::Column::Id.is_in(ids.to_owned()));

    let ret = ExtCheckiteminfoEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn delete_many(testid: &str, db: &DatabaseConnection) -> Result<u64> {
    if testid.is_empty() {
      return Err(anyhow!("testid empty, not allowed"));
    }
    let conditions = Condition::all().add(ext_checkiteminfo::Column::Testid.eq(testid));

    let ret = ExtCheckiteminfoEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
  pub async fn clear(date: i64, db: &DatabaseConnection) -> Result<u64> {
    let ret = ExtCheckiteminfoEntity::delete_many()
      .filter(Condition::all().add(ext_checkiteminfo::Column::Requestdate.lt(date)))
      .exec(db)
      .await;

    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
