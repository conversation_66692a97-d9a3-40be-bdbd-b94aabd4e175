use hl7_mllp_codec::MllpCodec;
use tokio::net::TcpStream;
use tokio_util::codec::Framed;
use log::*;

// #[derive(Debug)]
pub struct Hl7Client {
  uri: String,
  pub transport: Framed<TcpStream, MllpCodec>,
}

impl Hl7Client {
  pub async fn init(uri: &str) -> Self {
    let server_uri = uri.to_string();
    let handle = tokio::spawn(async move {
      loop {
        if let Ok(client) = TcpStream::connect(&server_uri).await {
          info!("服务端{}连接成功......", &server_uri);
          return client;
        } else {
          info!("服务端连接失败，等10秒后再次链接");
          tokio::time::sleep(std::time::Duration::from_secs(10)).await;
        }
      }
    });
    let stream = handle.await.unwrap();
    let client = Hl7Client {
      uri: uri.to_string(),
      transport: Framed::new(stream, MllpCodec::new()),
    };
    client
  }

  pub async fn retry_connect(&mut self) {
    let uri = self.uri.to_string();
    let handle = tokio::spawn(async move {
      loop {
        if let Ok(client) = TcpStream::connect(&uri).await {
          info!("服务端{}连接成功......", &uri);
          return client;
        } else {
          info!("服务端连接失败，等10秒后再次链接");
          tokio::time::sleep(std::time::Duration::from_secs(10)).await;
        }
      }
    });

    let stream = handle.await.unwrap();
    self.transport = Framed::new(stream, MllpCodec::new());

    // Ok(())
  }
}
