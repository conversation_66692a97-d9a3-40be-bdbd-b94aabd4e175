use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_checkallnew")]
#[derive(<PERSON><PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct TjCheckallnew {
    pub id: i64,
    pub tj_testid: String,
    pub tj_typeid: i32,
    pub tj_discode: String,
    pub tj_itemcode: String,
    pub tj_hazardcode: String,
    pub tj_diseasename: String,
    pub tj_ocuabnormal: String,
    pub tj_othabnormal: String,
    pub tj_ocuconclusion: String,
    pub tj_othconclusion: String,
    pub tj_ocusuggestion: String,
    pub tj_othsuggestion: String,
    pub tj_ocuopinion: String,
    pub tj_othopinion: String,
    pub tj_staffid: i64,
    pub tj_checkdate: i64,
    pub tj_castatus: i32,
}
