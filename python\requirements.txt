# Core dependencies for report generation (existing)
reportlab>=4.4.1
sqlalchemy>=2.0.41
python-docx>=1.1.2  # Note: imports as 'docx'
# tomli>=2.2.1
pymysql>=1.1.1

# New dependencies for REST API server
flask>=3.1.1
flask-cors>=6.0.0
werkzeug>=3.1.3

# Optional: For better performance and production deployment
# gunicorn>=23.0.0
# waitress>=3.0.2

# Development and testing (optional)
# pytest>=8.3.5
# pytest-flask>=1.3.0
# requests>=2.32.3
