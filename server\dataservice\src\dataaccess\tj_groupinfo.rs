use crate::entities::{
  prelude::{TjGroupinfo, TjGroupinfoEntity},
  tj_groupinfo,
};
use anyhow::{anyhow, Result};
use sea_orm::{sea_query::Expr, ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjGroupinfo {
  pub async fn query(code: i64, db: &DatabaseConnection) -> Result<Option<TjGroupinfo>> {
    let ret = TjGroupinfoEntity::find().filter(tj_groupinfo::Column::Id.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(gids: &Vec<i64>, db: &DatabaseConnection) -> Result<Vec<TjGroupinfo>> {
    let mut conditions = Condition::all().add(tj_groupinfo::Column::TjStatus.gte(0));
    if gids.len() > 0 {
      conditions = conditions.add(tj_groupinfo::Column::Id.is_in(gids.to_owned()));
    }
    let ret = TjGroupinfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save(info: &TjGroupinfo, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_groupinfo::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjGroupinfoEntity::update_many()
      .col_expr(tj_groupinfo::Column::TjStatus, Expr::value(-1))
      .filter(Condition::all().add(tj_groupinfo::Column::Id.is_in(ids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
}
