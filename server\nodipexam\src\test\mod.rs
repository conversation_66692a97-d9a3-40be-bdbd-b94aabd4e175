use nodipservice::external::yibukeji::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, YibuResponse};
use tracing::*;
// use utility::loggings;

#[tokio::test]
pub async fn test_yibu_upload() {
  // let sys_config = Settings::init("./config/nodipexam.toml").expect("init configuration error");
  // let log_cfg = "./config/nodipexam.yaml";
  // loggings::log_init(log_cfg);
  // log4rs::init_file(cfg, Default::default()).unwrap();

  // let testid = "10018601";
  // let ret = crate::external do_external_upload
  // let result = "{\"code\":0,\"message\":\"success\",\"result\":{\"id\":23,\"classify\":8,\"number\":\"10018601\",\"patientSn\":\"1051575100887150592\",\"patientName\":\"sa\",\"initials\":\"sa\",\"gender\":1,\"birthday\":null,\"cardType\":\"01\",\"cardNumber\":\"522428198911104635\",\"age\":34000000,\"cost\":1,\"deptSn\":\"1004\",\"deptName\":\"体检中心\",\"majorSn\":null,\"majorName\":null,\"state\":1,\"time\":1730881507025,\"backTime\":null,\"personSn\":\"763176591273627648\",\"personName\":\"叶勇光\",\"billDeptSn\":\"1004\",\"billDeptName\":\"体检中心\",\"settlement\":1,\"examType\":1,\"enterpriseName\":null,\"addrSn\":null,\"addr\":null,\"jobSn\":null,\"job\":null,\"phone\":null,\"settleTime\":0}}";

  let result = r#"{"code":0,"message":"success","result":{"id":23,"classify":8,"number":"10018601","patientSn":"1051575100887150592","patientName":"sa","initials":"sa","gender":1,"birthday":null,"cardType":"01","cardNumber":"522428198911104635","age":34000000,"cost":1,"deptSn":"1004","deptName":"体检中心","majorSn":null,"majorName":null,"state":1,"time":1730881507025,"backTime":null,"personSn":"763176591273627648","personName":"叶勇光","billDeptSn":"1004","billDeptName":"体检中心","settlement":1,"examType":1,"enterpriseName":null,"addrSn":null,"addr":null,"jobSn":null,"job":null,"phone":null,"settleTime":0}}"#;

  // let ret = serde_json::from_str::<nodipservice::external::yibukeji::YibuResponse<nodipservice::external::yibukeji::YibuProfile>>(&result);
  let ret = serde_json::from_str::<YibuResponse<YibuProfile>>(&result);
  println!("serde result:{:?}", &ret);
}

#[tokio::test]
pub async fn test_func() {
  // let cfg = "./config/nodipexam.yaml";
  // loggings::log_init(log_cfg);
  // log4rs::init_file(cfg, Default::default()).unwrap();

  let plain = "Admin123".to_string();
  let encret = utility::encrypt::encrypt_nodip(&plain);
  info!("Result ========== is:{:?}", &encret);

  let encstr = "Bu+5MW12VKoJF7JtJbOGYw==".to_string();

  let ret = utility::encrypt::aesgcm_decrypt(&encstr);
  info!("Result ********** is:{:?}", &ret);

  // let sys_config = Settings::init("C:\\Users\\<USER>\\rainerix\\joesong\\nodipservers\\config\\nodipexam.toml").expect("init configuration error");

  // let dbconn = DbConnection::new(&sys_config.database.uri.as_str(), DatabaseType::MySql as i32).await;

  // // let syscache = SysCache::new(&sys_config.application.areaprovince, &dbconn).await;
  // // nodipservice::SYSCACHE.set(syscache).expect("set global value error");
  // // let _ = RecvPacsData::auto_recv_pacsresult(sys_config.extpacs.get("default").unwrap(), YesOrNo::Yes as i64, &dbconn).await;

  // let local_now = Local::now();
  // if let Some(checked_add) = local_now.checked_add_signed(Duration::days(-100)) {
  //   let one_year_before = checked_add.timestamp();
  //   info!("time stamp is:{}", &one_year_before);
  //   let one_year_before_time = utility::timeutil::format_timestamp(one_year_before);
  //   info!("one year befroe time is :{one_year_before_time}");
  // }
}
