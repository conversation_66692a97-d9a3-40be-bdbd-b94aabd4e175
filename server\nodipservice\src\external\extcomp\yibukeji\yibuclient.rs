use super::yib<PERSON>jidto::*;
use anyhow::{anyhow, Result};
use mpart_async::client::MultipartRequest;
use reqwest::{header::CONTENT_TYPE, Body, Client, Method};
use std::fmt::Debug;
use tracing::*;
pub struct YibuClient;

impl YibuClient {
  pub async fn send_http_get_request<T: serde::Serialize + Debug, U: for<'a> serde::Deserialize<'a> + Debug>(url: &str, dto: &Option<T>) -> Result<U> {
    let mut dto_str = "".to_string();
    if dto.is_some() {
      let ret = serde_json::to_string(&dto);
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      dto_str = ret.unwrap();
    }
    let ret = YibuClient::send_request(Method::GET, url, &dto_str).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let result = ret.unwrap();
    // info!("Result is:{}", &result);
    let ret = serde_json::from_str::<YibuResponse<U>>(&result);
    if ret.as_ref().is_err() {
      error!("serde json error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      // return Err(anyhow!("解析返回数据错误"));
    }
    let resp_value = ret.unwrap();
    if resp_value.code != YIBU_CODE_OK && resp_value.code != 200 {
      return Err(anyhow!("{}", resp_value.message));
    }
    let result = resp_value.result;
    if result.is_none() {
      return Err(anyhow!("{}", resp_value.message));
    }
    Ok(result.unwrap())
  }

  pub async fn send_http_post_request<T: serde::Serialize + Debug, U: for<'a> serde::Deserialize<'a> + Debug>(url: &str, dto: &Option<T>) -> Result<U> {
    let mut dto_str = "".to_string();
    if dto.is_some() {
      let ret = serde_json::to_string(&dto);
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      dto_str = ret.unwrap();
    }
    // info!("start to send request");
    let ret = YibuClient::send_request(Method::POST, url, &dto_str).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let result = ret.unwrap();
    // info!("result string is:{}", &result);

    let ret = serde_json::from_str::<YibuResponse<U>>(&result);
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      // let newret = serde_json::from_str::<YibuResponse<String>>(&result);
      // return Err(anyhow!("{}", newret.unwrap_or_default().message.to_string()));
    }
    let resp_value = ret.unwrap();
    // info!("response value of post: {:?}", resp_value);
    if resp_value.code != YIBU_CODE_OK && resp_value.code != 200 {
      return Err(anyhow!("{}", resp_value.message));
    }
    let result = resp_value.result;
    if result.is_none() {
      return Err(anyhow!("{}", resp_value.message));
    }
    Ok(result.unwrap())
  }

  pub async fn send_request(method: Method, url: &str, dto: &str) -> Result<String> {
    if url.is_empty() {
      return Ok("".to_string());
    }
    // let server_uri = format!("{}{}", server, uri);
    // info!("start to build client......");
    let mut reqbuilder = Client::new()
      .request(method, url)
      .timeout(std::time::Duration::from_secs(60))
      .header(CONTENT_TYPE, "application/json; charset=utf-8")
      // .header("orginfo", orginfo.to_owned())
      ;
    info!("client is builded ......,start to send to server:{} body:{}", url, dto);
    if !dto.is_empty() {
      reqbuilder = reqbuilder.body(dto.to_owned());
    }
    let ret = reqbuilder.send().await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = ret.unwrap().text().await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let response_text = ret.unwrap();
    info!("response text is:{:?}", &response_text);
    Ok(response_text)
  }

  pub async fn send_upload_request(filename: &str, testid: &str, orginfo: &str, url: &str) -> Result<String> {
    if url.is_empty() {
      return Ok("".to_string());
    }
    let mut mpart = MultipartRequest::default();
    mpart.add_file(testid.to_string(), filename);

    // let server_uri = format!("{}{}", server, uri);
    // info!("upload server uri is:{}", &server_uri);
    let resp = Client::new()
      .post(url)
      .timeout(std::time::Duration::from_secs(600))
      .header("orginfo", orginfo.to_owned())
      .header(CONTENT_TYPE, format!("multipart/form-data; boundary={}", mpart.get_boundary()))
      .body(Body::wrap_stream(mpart))
      .send()
      .await;
    if resp.is_err() {
      return Err(anyhow!("{}", resp.err().unwrap().to_string()));
    }
    let ret = resp.unwrap().text().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let response_text = ret.unwrap();
    Ok(response_text)
  }
}
