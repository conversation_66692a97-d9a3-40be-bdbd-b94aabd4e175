mod config;
mod nxmiddleware;
// mod utility;
mod api;
mod common;

use crate::{config::settings::Settings, nxmiddleware::printmiddleware::print_request_response};
use axum::{
  async_trait,
  extract::Extension,
  // body::{self, BoxBody, Bytes, Full},
  // extract::FromRequest,
  http::{Request, StatusCode},
  middleware::{self, Next},
  response::{IntoResponse, Response},
  routing::post,
  Router,
};
use datacontroller::datasetup::DbConnection;
use odipservice::{prelude::DictService, service::uidservice::UidgenService};
use std::sync::Arc;
use tokio::{signal, sync::RwLock};
use tower::ServiceBuilder;
// use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use std::fs;
use utility::loggings;
#[tokio::main]
async fn main() {
  let log_cfg = "config/nodipsync.yaml";
  loggings::log_init(log_cfg);
  let sys_config = Settings::init().expect("init configuration error");
  info!("Configuration file:{:?}", &sys_config);

  let http_url = format!("{}:{}", sys_config.application.apphost, sys_config.application.appport);
  let httpaddr = http_url.as_str().parse().unwrap();
  let dbconn = DbConnection::new(&sys_config.database.uri.as_str()).await;
  let dictsvc = DictService::init("", &dbconn).await;
  let uidservice = Arc::new(RwLock::new(UidgenService::new(sys_config.application.machineid, sys_config.application.nodeid)));

  fs::create_dir_all(&sys_config.system.uploaddir).expect("create upload direcotry error");
  fs::create_dir_all(&sys_config.system.reportdir).expect("create report direcotry error");
  fs::create_dir_all(&sys_config.system.photodir).expect("create photo direcotry error");
  fs::create_dir_all(&sys_config.system.filedir).expect("create photo direcotry error");

  let app = api::routers::create_route().layer(
    ServiceBuilder::new()
      .layer(Extension(Arc::new(dbconn)))
      .layer(Extension(Arc::new(sys_config)))
      .layer(Extension(uidservice))
      .layer(Extension(Arc::new(dictsvc))),
  );

  let app = app.layer(middleware::from_fn(print_request_response));

  let version = env!("CARGO_PKG_VERSION");
  info!("server started on {}, version:{}", &http_url, &version);

  axum::Server::bind(&httpaddr)
    .serve(app.into_make_service())
    .with_graceful_shutdown(shutdown_signal())
    .await
    .unwrap();
}

async fn shutdown_signal() {
  let ctrl_c = async {
    signal::ctrl_c().await.expect("failed to install Ctrl+C handler");
  };

  #[cfg(unix)]
  let terminate = async {
    signal::unix::signal(signal::unix::SignalKind::terminate())
      .expect("failed to install signal handler")
      .recv()
      .await;
  };

  #[cfg(not(unix))]
  let terminate = std::future::pending::<()>();

  tokio::select! {
      _ = ctrl_c => {},
      _ = terminate => {},
  }

  info!("signal received, starting graceful shutdown");
}
