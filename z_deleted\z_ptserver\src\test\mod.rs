// use chrono::{Duration, Local};
// use dbopservice::dbinit::DbConnection;

// use utility::loggings;

// use crate::{common::constant::DatabaseType, config::settings::Settings};

#[tokio::test]
pub async fn test_func() {
  // let log_cfg = "C:\\Users\\<USER>\\rainerix\\joesong\\nodipservers\\config\\ptserver.yaml";
  // loggings::log_init(log_cfg);

  // let sys_config = Settings::init("C:\\Users\\<USER>\\rainerix\\joesong\\nodipservers\\config\\nodipexam.toml").expect("init configuration error");

  // let dbconn = DbConnection::new(&sys_config.database.uri.as_str(), DatabaseType::MySql as i32).await;

  // // let syscache = SysCache::new(&sys_config.application.areaprovince, &dbconn).await;
  // // nodipservice::SYSCACHE.set(syscache).expect("set global value error");
  // // let _ = RecvPacsData::auto_recv_pacsresult(sys_config.extpacs.get("default").unwrap(), YesOrNo::Yes as i64, &dbconn).await;

  // let local_now = Local::now();
  // if let Some(checked_add) = local_now.checked_add_signed(Duration::days(-100)) {
  //   let one_year_before = checked_add.timestamp();
  //   info!("time stamp is:{}", &one_year_before);
  //   let one_year_before_time = utility::timeutil::format_timestamp(one_year_before);
  //   info!("one year befroe time is :{one_year_before_time}");
  // }
}
