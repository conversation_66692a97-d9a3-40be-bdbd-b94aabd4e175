from sqlalchemy import (
    Column,
    Integer,
    String,
)
from dataentities.dbconn import Base


class TjCorpoccureport(Base):
    # 数据库中存储的表名
    __tablename__ = "tj_corpoccureport"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    tj_corpid = Column(String(256), nullable=False)
    tj_corpname = Column(String(256), nullable=False)
    tj_wtcorpname = Column(String(256), nullable=False, unique=True)
    tj_starttime = Column(Integer, nullable=False)
    tj_endtime = Column(Integer, nullable=False)
    tj_testyear = Column(Integer, nullable=False)
    tj_testtype = Column(Integer, nullable=False)
    tj_typename = Column(String(128), nullable=False)
    tj_poisions = Column(String(1024), nullable=False)
    tj_testdate = Column(String(256), nullable=False)
    tj_testaddress = Column(String(256), nullable=False)
    tj_peoplenum = Column(Integer, nullable=False)
    tj_apeoplenum = Column(Integer, nullable=False)
    tj_testitems = Column(String(256), nullable=False)
    tj_evalaw = Column(String(1024), nullable=False)
    tj_testlaw = Column(String(1024), nullable=False)
    tj_result = Column(String(1024), nullable=False)
    tj_createdate = Column(Integer, nullable=False)
    tj_moddate = Column(Integer, nullable=False)
    tj_creator = Column(String(256), nullable=False)
    tj_modifier = Column(String(256), nullable=False)
    tj_reportnum = Column(String(256), nullable=False)
    tj_status = Column(Integer, nullable=False)
    tj_peid = Column(Integer, nullable=False)
    tj_pages = Column(Integer, nullable=False)
    tj_isrecheck = Column(Integer, nullable=False)
    tj_orptid = Column(Integer, nullable=False)
    tj_reportnumint = Column(String(256), nullable=False)
    tj_pyjm = Column(Integer, nullable=False)
    tj_reporttype = Column(Integer, nullable=False)
    tj_syncflag = Column(Integer, nullable=False)
    tj_memo = Column(String(256), nullable=False)
