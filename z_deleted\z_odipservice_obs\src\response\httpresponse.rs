use serde::{Deserialize, Serialize};
// use serde_json::Value;
#[derive(<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Debug)]
pub enum HttpCode {
  OK = 200,
  Error = 201,
}

pub const CODE_HTTP_OK: i32 = 200;
pub const CODE_HTTP_ERROR: i32 = 201;

#[derive(Debug, Serialize, Deserialize)]
pub struct ResponseBody<T> {
  pub code: i32,
  pub message: String,
  pub data: T,
}

impl<T> ResponseBody<T> {
  pub fn new(code: i32, message: &str, data: T) -> ResponseBody<T> {
    ResponseBody {
      code: code,
      message: message.to_string(),
      data,
    }
  }
}
#[derive(Debug, Serialize, Deserialize)]
pub struct DataBody<T> {
  pub total: usize,
  pub data: T,
}
impl<T> DataBody<T> {
  pub fn new(total: usize, data: T) -> DataBody<T> {
    DataBody { total: total, data: data }
  }
}
