use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_patient")]
#[derive(<PERSON><PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct TjPatient {
    pub id: i64,
    pub tj_pid: String,
    pub tj_pname: String,
    pub tj_psex: i32,
    pub tj_pmarriage: i32,
    pub tj_ptestnum: i32,
    pub tj_paddress: String,
    pub tj_pphone: String,
    pub tj_pemail: String,
    pub tj_pbirthday: String,
    pub tj_nation: String,
    pub tj_pidcard: String,
    pub tj_pcareer: String,
    pub tj_pmobile: String,
    pub tj_photo: String,
    pub tj_cryptflag: i32,
    pub tj_popdate: i64,
    pub tj_staffid: i64,
    pub tj_pmemo: String,
    pub tj_syncflag: i32,
    pub p_wkid: i64,
}
