#[allow(unused)]
use crate::rusthl7::{Field, Hl7ParseError, Message, Separators};
use anyhow::Result;
use std::fmt::Display;

//ch04
// 1 4 SI O 00237 Set ID - OBR
// 2 22 EI C 00216 Placer Order Number
// 3 22 EI C 00217 Filler Order Number
// 4 250 CE R 00238 Universal Service Identifier
// 5 2 ID B 00239 Priority - OBR
// 6 26 TS B 00240 Requested Date/Time
// 7 26 TS C 00241 Observation Date/Time #
// 8 26 TS O 00242 Observation End Date/Time #
// 9 20 CQ O 00243 Collection Volume *
// 10 250 XCN O Y 00244 Collector Identifier *
// 11 1 ID O 0065 00245 Specimen Action Code *
// 12 250 CE O 00246 Danger Code
// 13 300 ST O 00247 Relevant Clinical Information
// 14 26 TS C 00248 Specimen Received Date/Time *
// 15 300 CM O 0070/00249 Specimen Source
// 16 250 XCN O Y 00226 Ordering Provider
// 17 250 XTN O Y/2 00250 Order Callback Phone Number
// 18 60 ST O 00251 Placer Field 1
// 19 60 ST O 00252 Placer Field 2
// 20 60 ST O 00253 Filler Field 1 +
// 21 60 ST O 00254 Filler Field 2 +
// 22 26 TS C 00255 Results Rpt/Status Chng - Date/Time +
// 23 40 CM O 00256 Charge to Practice +
// 24 10 ID O 0074 00257 Diagnostic Serv Sect ID
// 25 1 ID C 0123 00258 Result Status +
// 26 400 CM O 00259 Parent Result +
// 27 200 TQ O Y 00221 Quantity/Timing
// 28 250 XCN O Y/5 00260 Result Copies To
// 29 200 CM O 00222 Parent
// 30 20 ID O 0124 00262 Transportation Mode
// 31 250 CE O Y 00263 Reason for Study
// 32 200 CM O 00264 Principal Result Interpreter +
// 33 200 CM O Y 00265 Assistant Result Interpreter +
// 34 200 CM O Y 00266 Technician +
// 35 200 CM O Y 00267 Transcriptionist +
// 36 26 TS O 00268 Scheduled Date/Time +
// 37 4 NM O 01028 Number of Sample Containers *
// 38 250 CE O Y 01029 Transport Logistics of Collected Sample *
// 39 250 CE O Y 01030 Collector’s Comment *
// 40 250 CE O 01031 Transport Arrangement Responsibility
// 41 30 ID O 0224 01032 Transport Arranged
// 42 1 ID O 0225 01033 Escort Required
// 43 250 CE O Y 01034 Planned Patient Transport Comment
// 44 250 CE O 0088 00393 Procedure Code
// 45 250 CE O Y 0340 01316 Procedure Code Modifier
// 46 250 CE O Y 0411 01474 Placer Supplemental Service Information
// 47 250 CE O Y 0411 01475 Filler Supplemental Service Information
#[derive(Debug, PartialEq)]
pub struct ObrSegment<'a> {
  pub source: &'a str,
  //this initial layout largely stolen from the _other_ hl7 crate: https://github.com/njaremko/hl7
  pub msg_encoding_characters: Separators,
  pub obr_1_set_id: Option<Field<'a>>,
  pub obr_2_place_order_number: Option<Field<'a>>,
  pub obr_3_filler_order_number: Option<Field<'a>>,
  pub obr_4_universal_service_identifier: Field<'a>,
  pub obr_5_priority: Option<Field<'a>>,
  pub obr_6_request_date_time: Option<Field<'a>>,
  pub obr_7_observation_datetime: Option<Field<'a>>,
  pub obr_8_observation_end_datetime: Option<Field<'a>>,
  pub obr_9_collection_volumn: Option<Field<'a>>,
  pub obr_10_collector_identifier: Option<Field<'a>>,
  pub obr_11_specimen_action_code: Option<Field<'a>>,
  pub obr_12_danger_code: Option<Field<'a>>,
  pub obr_13_relevant_clinical_info: Option<Field<'a>>,
  pub obr_14_specimen_received_datetime: Option<Field<'a>>,
  pub obr_15_specimen_source: Option<Field<'a>>,
  pub obr_16_ordering_provider: Option<Field<'a>>,
  pub obr_17_order_callback_phonenumber: Option<Field<'a>>,
  pub obr_18_palce_field_1: Option<Field<'a>>,
  pub obr_19_place_field_2: Option<Field<'a>>,
  pub obr_20_filler_field_1p: Option<Field<'a>>,
  pub obr_21_filler_field_2p: Option<Field<'a>>,
  pub obr_22_statue_datetime: Option<Field<'a>>,
  pub obr_23_charge_to_practice: Option<Field<'a>>,
  pub obr_24_diagnostic_serv_set_id: Option<Field<'a>>,
  pub obr_25_result_status: Option<Field<'a>>,
  pub obr_26_parent_result: Option<Field<'a>>,
  pub obr_27_quantity: Option<Field<'a>>,
  pub obr_28_result_copies_to: Option<Field<'a>>,
  pub obr_29: Option<Field<'a>>,
  pub obr_30: Option<Field<'a>>,
  pub obr_31: Option<Field<'a>>,
  pub obr_32: Option<Field<'a>>,
  pub obr_33: Option<Field<'a>>,
  pub obr_34: Option<Field<'a>>,
  pub obr_35: Option<Field<'a>>,
  pub obr_36: Option<Field<'a>>,
  pub obr_37: Option<Field<'a>>,
  pub obr_38: Option<Field<'a>>,
  pub obr_39: Option<Field<'a>>,
  pub obr_40: Option<Field<'a>>,
  pub obr_41: Option<Field<'a>>,
  pub obr_42: Option<Field<'a>>,
  pub obr_43: Option<Field<'a>>,
  pub obr_44: Option<Field<'a>>,
  pub obr_45: Option<Field<'a>>,
  pub obr_46: Option<Field<'a>>,
  pub obr_47: Option<Field<'a>>,
}

impl<'a> ObrSegment<'a> {
  pub fn parse<S: Into<&'a str>>(input: S, delims: &Separators) -> Result<ObrSegment<'a>, Hl7ParseError> {
    let input = input.into();

    let mut fields = input.split(delims.field);

    assert!(fields.next().unwrap() == "OBR");

    let seg = ObrSegment {
      source: input,
      msg_encoding_characters: delims.to_owned(),
      obr_1_set_id: Field::parse_optional(fields.next(), delims)?,
      obr_2_place_order_number: Field::parse_optional(fields.next(), delims)?,
      obr_3_filler_order_number: Field::parse_optional(fields.next(), delims)?,
      obr_4_universal_service_identifier: Field::parse_mandatory(fields.next(), delims)?,
      obr_5_priority: Field::parse_optional(fields.next(), delims)?,
      obr_6_request_date_time: Field::parse_optional(fields.next(), delims)?,
      obr_7_observation_datetime: Field::parse_optional(fields.next(), delims)?,
      obr_8_observation_end_datetime: Field::parse_optional(fields.next(), delims)?,
      obr_9_collection_volumn: Field::parse_optional(fields.next(), delims)?,
      obr_10_collector_identifier: Field::parse_optional(fields.next(), delims)?,
      obr_11_specimen_action_code: Field::parse_optional(fields.next(), delims)?,
      obr_12_danger_code: Field::parse_optional(fields.next(), delims)?,
      obr_13_relevant_clinical_info: Field::parse_optional(fields.next(), delims)?,
      obr_14_specimen_received_datetime: Field::parse_optional(fields.next(), delims)?,
      obr_15_specimen_source: Field::parse_optional(fields.next(), delims)?,
      obr_16_ordering_provider: Field::parse_optional(fields.next(), delims)?,
      obr_17_order_callback_phonenumber: Field::parse_optional(fields.next(), delims)?,
      obr_18_palce_field_1: Field::parse_optional(fields.next(), delims)?,
      obr_19_place_field_2: Field::parse_optional(fields.next(), delims)?,
      obr_20_filler_field_1p: Field::parse_optional(fields.next(), delims)?,
      obr_21_filler_field_2p: Field::parse_optional(fields.next(), delims)?,
      obr_22_statue_datetime: Field::parse_optional(fields.next(), delims)?,
      obr_23_charge_to_practice: Field::parse_optional(fields.next(), delims)?,
      obr_24_diagnostic_serv_set_id: Field::parse_optional(fields.next(), delims)?,
      obr_25_result_status: Field::parse_optional(fields.next(), delims)?,
      obr_26_parent_result: Field::parse_optional(fields.next(), delims)?,
      obr_27_quantity: Field::parse_optional(fields.next(), delims)?,
      obr_28_result_copies_to: Field::parse_optional(fields.next(), delims)?,
      obr_29: Field::parse_optional(fields.next(), delims)?,
      obr_30: Field::parse_optional(fields.next(), delims)?,
      obr_31: Field::parse_optional(fields.next(), delims)?,
      obr_32: Field::parse_optional(fields.next(), delims)?,
      obr_33: Field::parse_optional(fields.next(), delims)?,
      obr_34: Field::parse_optional(fields.next(), delims)?,
      obr_35: Field::parse_optional(fields.next(), delims)?,
      obr_36: Field::parse_optional(fields.next(), delims)?,
      obr_37: Field::parse_optional(fields.next(), delims)?,
      obr_38: Field::parse_optional(fields.next(), delims)?,
      obr_39: Field::parse_optional(fields.next(), delims)?,
      obr_40: Field::parse_optional(fields.next(), delims)?,
      obr_41: Field::parse_optional(fields.next(), delims)?,
      obr_42: Field::parse_optional(fields.next(), delims)?,
      obr_43: Field::parse_optional(fields.next(), delims)?,
      obr_44: Field::parse_optional(fields.next(), delims)?,
      obr_45: Field::parse_optional(fields.next(), delims)?,
      obr_46: Field::parse_optional(fields.next(), delims)?,
      obr_47: Field::parse_optional(fields.next(), delims)?,
    };

    Ok(seg)
  }
}
/// Common formatter trait implementation for the strongly-typed segment
impl<'a> Display for ObrSegment<'a> {
  /// Required for to_string() and other formatter consumers
  fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
    write!(f, "{}", self.source)
  }
}
/// Common clone trait implementation for the strongly-typed segment
impl<'a> Clone for ObrSegment<'a> {
  /// Creates a new Message object using _the same source_ slice as the original.
  fn clone(&self) -> Self {
    let delims = self.msg_encoding_characters;
    ObrSegment::parse(self.source, &delims).unwrap()
  }
}
/// Extracts header element for external use
pub fn _obr<'a>(msg: &Message<'a>) -> Result<ObrSegment<'a>, Hl7ParseError> {
  let seg = msg.segments_by_identifier("OBR").unwrap()[0];
  let segment = ObrSegment::parse(seg.source, &msg.get_separators()).expect("Failed to parse Pv1 segment");
  Ok(segment)
}
