use std::collections::HashSet;
use log::*;

use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
// use futures::SinkExt;
use nodipservice::{
  common::constant::{DeptType, ExtOpType},
  dto::ExtTypeDTO,
};
use serde::{Deserialize, Serialize};

use crate::rusthl7::Message;

use super::messages::MessageHelper;

// use crate::HL7CLIENT;
pub struct ExternalService;

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct Row {
  pub ret: String,
  pub msg: String,
  pub patid: i64,
  pub blh: i64,
}

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct HisResult {
  #[serde(rename(deserialize = "Status"))]
  pub status: String,
  #[serde(rename(deserialize = "MSG"))]
  pub msg: String,
}

#[derive(<PERSON><PERSON>, Debug, <PERSON><PERSON>ult, PartialEq, Serialize, Deserialize)]
pub struct Rows {
  #[serde(rename(deserialize = "Row"))]
  pub row: Row,
}

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct His {
  #[serde(rename(deserialize = "Rows"))]
  pub rows: Rows,
  #[serde(rename(deserialize = "Result"))]
  pub result: HisResult,
}

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct HisRet {
  #[serde(rename(deserialize = "HIS"))]
  pub his: His,
}

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct DftMessage {
  pub pid: String,
  pub adtmsgid: String,
  pub dftmsg: String,
}

impl ExternalService {
  pub async fn external_extend(
    dto: &ExtTypeDTO,
    db: &DbConnection,
    tx: &futures::channel::mpsc::UnboundedSender<String>,
    txp03: &futures::channel::mpsc::UnboundedSender<DftMessage>,
  ) -> Result<String> {
    let ret = TjMedexaminfo::query(&dto.testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", &ret.as_ref().unwrap_err().to_string()));
    }
    let medret = ret.unwrap();
    if medret.is_none() {
      return Err(anyhow!("Can't find medinfo by testid:{}", &dto.testid));
    }
    let medinfo = medret.unwrap();

    let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", &ret.as_ref().unwrap_err().to_string()));
    }
    let medret = ret.unwrap();
    if medret.is_none() {
      return Err(anyhow!("Can't find patient by testid:{}", &dto.testid));
    }
    let ptinfo = medret.unwrap();
    let ret = TjCorpinfo::query(medinfo.tj_corpnum, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", &ret.as_ref().unwrap_err().to_string()));
    }
    let oret = ret.unwrap();
    if oret.is_none() {
      return Err(anyhow!("Can't find patient by testid:{}", &dto.testid));
    }
    let corpinfo = oret.unwrap();

    let staffid: i64 = medinfo.tj_recorder.parse().unwrap_or_default();
    let ret = TjStaffadmin::query_by_id(staffid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", &ret.as_ref().unwrap_err().to_string()));
    }
    let oret = ret.unwrap();
    if oret.is_none() {
      return Err(anyhow!("Can't find patient by testid:{}", &dto.testid));
    }
    let doctor = oret.unwrap();

    // let mut amount = "0.00".to_string();
    let mut pkginfo = TjPackageinfo { ..Default::default() };
    if medinfo.tj_packageid > 0 {
      let ret = TjPackageinfo::query(medinfo.tj_packageid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", &ret.as_ref().unwrap_err().to_string()));
      }
      let oret = ret.unwrap();
      if oret.is_some() {
        pkginfo = oret.unwrap();
      }
    }
    let mut adt_uuid = "".to_string();
    //登记时，发送健康档案报文，收费报文，体检项目报文,一个成功以后才能发下一个？
    if dto.exttype == ExtOpType::ADD as i32 {
      let (adt_a04_msg, uuid) = MessageHelper::generate_adt_a04_message(&ptinfo, &medinfo, &corpinfo, &doctor);
      adt_uuid = uuid;
      let ret = tx.unbounded_send(adt_a04_msg.to_owned());
      if ret.as_ref().is_err() {
        error!("tx send error:{}", ret.unwrap_err().to_string());
      }
    }

    //增加，新建收费，删除，取消收费
    if dto.exttype == ExtOpType::ADD as i32 || dto.exttype == ExtOpType::DEL as i32 {
      //dft
      let dft_p03_msg = MessageHelper::generate_dft_p03_message(dto.exttype, &ptinfo, &medinfo, &corpinfo, &doctor, &pkginfo, 0);

      if !ptinfo.tj_patid.is_empty() {
        let ret = tx.unbounded_send(dft_p03_msg.to_owned());
        if ret.as_ref().is_err() {
          error!("tx send error:{}", ret.unwrap_err().to_string());
        }
      } else {
        //没有hispatid，需要等患者登记成功后再次发送
        let dftmsg: DftMessage = DftMessage {
          pid: ptinfo.tj_pid.to_string(),
          adtmsgid: adt_uuid,
          dftmsg: dft_p03_msg,
        };
        let ret = txp03.unbounded_send(dftmsg.to_owned());
        if ret.as_ref().is_err() {
          error!("tx send error:{}", ret.unwrap_err().to_string());
        }
      }
    }

    let mut newinfos: Vec<ExtCheckiteminfo> = Vec::new();
    let mut existinfos: Vec<ExtCheckiteminfo> = Vec::new();
    if dto.newinfos.is_some() {
      newinfos = dto.newinfos.as_ref().unwrap().to_owned();
    }
    if dto.existinfos.is_some() {
      existinfos = dto.existinfos.as_ref().unwrap().to_owned();
    }
    info!("new items count:{}, exist items count:{}", newinfos.len(), existinfos.len());

    let to_add_items: Vec<ExtCheckiteminfo> = newinfos
      .iter()
      .filter(|f| existinfos.iter().find(|v| v.itemid2.eq_ignore_ascii_case(&f.itemid2)).is_none() && f.depttype == DeptType::Lab as i32)
      .map(|v| v.to_owned())
      .collect();

    let to_del_items: Vec<ExtCheckiteminfo> = existinfos
      .iter()
      .filter(|p| newinfos.iter().find(|f| f.itemid2.eq_ignore_ascii_case(&p.itemid2)).is_none() && p.depttype == DeptType::Lab as i32)
      .map(|m| m.clone())
      .collect();

    info!("to_add_items count:{}, to_del_items count:{}", to_add_items.len(), to_del_items.len());

    let mut itemids: HashSet<String> = newinfos.iter().map(|v| v.itemid2.clone()).collect();
    itemids.extend(existinfos.iter().map(|f| f.itemid2.clone())); //.collect());
    info!("all itemids:{:?}", &itemids);
    let mut iteminfos: Vec<TjIteminfo> = Vec::new();
    if to_add_items.len() > 0 || to_del_items.len() > 0 {
      let itemids: Vec<String> = itemids.iter().map(|v| v.to_string()).collect();
      let ret = TjIteminfo::query_many(&itemids, -1, 1, -1, &vec![], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      iteminfos = ret.unwrap();
    }

    if to_add_items.len() > 0 {
      for val in to_add_items.into_iter() {
        //过滤非lab实验室的项目
        if val.depttype != nodipservice::common::constant::DeptType::Lab as i32 {
          continue;
        }
        let ret = iteminfos.iter().find(|p| p.tj_itemid.eq_ignore_ascii_case(&val.itemid2));
        if ret.is_none() {
          error!("cant find iteminfo by itemid:{}", &val.itemid2);
        }
        let iteminfo = ret.unwrap();
        let orm_o01_msg = MessageHelper::generate_orm_o01_message(nodipservice::common::constant::ExtOpType::ADD as i32, &ptinfo, &medinfo, &corpinfo, &doctor, &iteminfo);
        // info!("testid:{},ORM^O01 message:\r{}", &dto.testid, &orm_o01_msg);
        let ret = tx.unbounded_send(orm_o01_msg.to_owned());
        if ret.as_ref().is_err() {
          error!("tx send error:{}", ret.unwrap_err().to_string());
        }
      }
    }

    if to_del_items.len() > 0 {
      for val in to_del_items.into_iter() {
        //过滤非lab实验室的项目
        if val.depttype != nodipservice::common::constant::DeptType::Lab as i32 {
          continue;
        }
        let ret = iteminfos.iter().find(|p| p.tj_itemid.eq_ignore_ascii_case(&val.itemid2));
        if ret.is_none() {
          error!("cant find iteminfo by itemid:{}", &val.itemid2);
        }
        let iteminfo = ret.unwrap();
        let orm_o01_msg = MessageHelper::generate_orm_o01_message(nodipservice::common::constant::ExtOpType::DEL as i32, &ptinfo, &medinfo, &corpinfo, &doctor, &iteminfo);
        // info!("testid:{},ORM^O01 message:\r{}", &dto.testid, &orm_o01_msg);
        let ret = tx.unbounded_send(orm_o01_msg.to_owned());
        if ret.as_ref().is_err() {
          error!("tx send error:{}", ret.unwrap_err().to_string());
        }
      }
    }

    // let sample_hl7 =
    // "MSH|^~\\&|EPIC|EPICADT|SMS|SMSADT|199912271408|CHARRIS|ADT^A04|1817457|D|2.5|\rPID||0493575^^^2^ID 1|454721||DOE^JOHN^^^^|DOE^JOHN^^^^|19480203|M||B|254 MYSTREET AVE^^MYTOWN^OH^44123^USA||(216)123-4567|||M|NON|400003403~1129086|\rNK1||ROE^MARIE^^^^|SPO||(216)123-4567||EC|||||||||||||||||||||||||||\rPV1||O|168 ~219~C~PMA^^^^^^^^^||||277^ALLEN MYLASTNAME^BONNIE^^^^|||||||||| ||2688684|||||||||||||||||||||||||199912271408||||||002376853";

    Ok("OK".to_string())
  }

  pub fn extract_ack_a01(message: &Message) -> Result<Row> {
    // info!("message is:{:?}", &message);
    if message.query("MSH.F8") != "ACK^A01" {
      return Err(anyhow!("not valid ack^a01 message"));
    }

    if message.query("MSA.F1") != "AA" {
      return Err(anyhow!("调用失败，不处理"));
    }

    let msgid = message.query("MSA.F2");
    info!("msgid is:{}", &msgid);
    let msgret = message.query("MSA.F3");
    info!("message ret is:{:?}", &msgret);
    if let Ok(hismsg) = serde_json::from_str::<HisRet>(msgret) {
      // info!("message structure is:{:?}", &hismsg);
      let mut row = hismsg.his.rows.row;
      row.msg = msgid.to_string();
      return Ok(row);
    }
    return Err(anyhow!("deserde message error"));
  }
}
