use crate::entities::prelude::*;
use anyhow::{anyhow, Result};
use sea_orm::{DatabaseConnection, EntityTrait};

impl TjSampletype {
  pub async fn query_many(db: &DatabaseConnection) -> Result<Vec<TjSampletype>> {

    let ret = TjSampletypeEntity::find().all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
