use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok},
  auth::auth::Claims,
};
use axum::{Extension, Json};
use tracing::*;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{dto::*, medexam::audiogramsvc::AudiogramSvc};
use serde_json::Value;
use std::sync::Arc;
pub async fn query_audiograms(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyDto>) -> Json<Value> {
  info!("start to query audiogram details by dto:{:?}", &dto);
  let testid = dto.key.to_owned();
  if testid.is_empty() {
    return response_json_value_error("testid is empty, not allowed", AudiogramResponse { ..Default::default() });
  }
  let ret = AudiogramSvc::query_audiogram_details(&testid, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), AudiogramResponse { ..Default::default() });
  }

  let results = ret.unwrap();
  let resp = AudiogramResponse {
    testid: testid.to_owned(),
    addetails: results.0,
    adresult: results.1,
  };
  response_json_value_ok(1, resp)
}

pub async fn save_audiogramdetails(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(details): Json<Vec<TjAudiogramdetail>>) -> Json<Value> {
  info!("save audiogram details dto:{:?}", &details);
  if details.len() <= 0 {
    return response_json_value_error("details are empty, not allowed", AudiogramResponse { ..Default::default() });
  }
  let testid = details.get(0).unwrap().tj_testid.to_string();
  let ret = AudiogramSvc::save_audiogram_details(&details, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), AudiogramResponse { ..Default::default() });
  }

  let results = ret.unwrap();
  let resp = AudiogramResponse {
    testid: testid.to_owned(),
    addetails: results.0,
    adresult: results.1,
  };
  response_json_value_ok(1, resp)
}

pub async fn delete_audiogramdetails(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyDto>) -> Json<Value> {
  info!("start to delete audiogram details by dto:{:?}", &dto);
  let testid = dto.key.to_owned();
  if testid.is_empty() {
    return response_json_value_error("empty testid, not allowed", "");
  }
  let ret = AudiogramSvc::delete_audiogram_details(&testid, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), "");
  }

  //   let results = ret.unwrap();
  response_json_value_ok(1, "")
}
//auto_summary_audiograms
pub async fn auto_summary_audiograms(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyDto>) -> Json<Value> {
  info!("start to summary audiogram details by dto:{:?}", &dto);
  let testid = dto.key.to_owned();
  if testid.is_empty() {
    return response_json_value_error("empty testid, not allowed", TjTestsummary { ..Default::default() });
  }
  let ret = AudiogramSvc::auto_summary_audiogram(&testid, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), TjTestsummary { ..Default::default() });
  }

  response_json_value_ok(1, ret.unwrap())
}

pub async fn query_audiogram_summaries(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  let ret = AudiogramSvc::query_audiogram_summaries(&db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjAudiogramsummary>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_audiogram_summary(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjAudiogramsummary>) -> Json<Value> {
  let ret = AudiogramSvc::save_audiogram_summary(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1, ret.unwrap())
}

pub async fn delete_audiogram_summary(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjAudiogramsummary>) -> Json<Value> {
  let ret = AudiogramSvc::delete_audiogram_summary(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1, ret.unwrap())
}
