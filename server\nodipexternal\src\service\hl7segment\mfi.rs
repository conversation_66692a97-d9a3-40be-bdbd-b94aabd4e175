#[allow(unused)]
use crate::rusthl7::{Field, Hl7ParseError, Message, Separators};
use anyhow::Result;
use std::fmt::Display;

//CH08 page 9
#[derive(Debug, PartialEq)]
pub struct MfiSegment<'a> {
  pub source: &'a str,
  pub msg_encoding_characters: Separators,
  pub msi_1_masterfile_identifier: Field<'a>,
  pub msi_2_masterfile_application_identifier: Option<Field<'a>>,
  pub msi_3_filelevel_event_code: Field<'a>,
  pub msi_4_entered_datetime: Option<Field<'a>>,
  pub msi_5_effective_datetime: Option<Field<'a>>,
  pub msi_6_response_level_code: Option<Field<'a>>,
}

impl<'a> MfiSegment<'a> {
  pub fn parse<S: Into<&'a str>>(input: S, delims: &Separators) -> Result<MfiSegment<'a>, Hl7ParseError> {
    let input = input.into();

    let mut fields = input.split(delims.field);

    assert!(fields.next().unwrap() == "MFI");

    let msh = MfiSegment {
      source: input,
      msg_encoding_characters: delims.to_owned(),
      msi_1_masterfile_identifier: Field::parse_mandatory(fields.next(), delims)?,
      msi_2_masterfile_application_identifier: Field::parse_optional(fields.next(), delims)?,
      msi_3_filelevel_event_code: Field::parse_mandatory(fields.next(), delims)?,
      msi_4_entered_datetime: Field::parse_optional(fields.next(), delims)?,
      msi_5_effective_datetime: Field::parse_optional(fields.next(), delims)?,
      msi_6_response_level_code: Field::parse_optional(fields.next(), delims)?,
    };

    Ok(msh)
  }
}
/// Common formatter trait implementation for the strongly-typed segment
impl<'a> Display for MfiSegment<'a> {
  /// Required for to_string() and other formatter consumers
  fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
    write!(f, "{}", self.source)
  }
}
/// Common clone trait implementation for the strongly-typed segment
impl<'a> Clone for MfiSegment<'a> {
  /// Creates a new Message object using _the same source_ slice as the original.
  fn clone(&self) -> Self {
    let delims = self.msg_encoding_characters;
    MfiSegment::parse(self.source, &delims).unwrap()
  }
}

/// Extracts header element for external use
pub fn _mfi<'a>(msg: &Message<'a>) -> Result<MfiSegment<'a>, Hl7ParseError> {
  let seg = msg.segments_by_identifier("MFI").unwrap()[0];
  let segment = MfiSegment::parse(seg.source, &msg.get_separators()).expect("Failed to parse MFI segment");
  Ok(segment)
}
