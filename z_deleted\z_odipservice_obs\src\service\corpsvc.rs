use anyhow::{anyhow, Result};
use datacontroller::{datasetup::DbConnection, entities::tj_corpinfo::TjCorpinfo};
pub struct CorpSvc;

impl CorpSvc {
  pub async fn check_corpinfos(corpinfos: &Vec<TjCorpinfo>, db: &DbConnection) -> Result<()> {
    let mut corpinfos = corpinfos.to_owned();
    // let corp_orgcodes: Vec<String> = corpinfos.iter().map(|x| x.tj_orgcode.clone()).collect();
    let ids: Vec<i64> = Vec::new();
    let ret = TjCorpinfo::query_many(&ids, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let exist_corpinfos = ret.unwrap();
    // let mut update_corpinfos: Vec<TjCorpinfo> = Vec::new();
    // let mut insert_corpinfos: Vec<TjCorpinfo> = Vec::new();
    for val in corpinfos.iter_mut() {
      let isexist = exist_corpinfos.iter().find(|x| x.tj_orgcode.eq_ignore_ascii_case(&val.tj_orgcode));
      if isexist.is_none() {
        // insert_corpinfos.push(val.clone());
        val.id = 0;
      } else {
        val.id = isexist.unwrap().id;
        // update_corpinfos.push(val.clone());
      }
    }
    let ret = TjCorpinfo::save_many(&corpinfos, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    Ok(())
  }
}
