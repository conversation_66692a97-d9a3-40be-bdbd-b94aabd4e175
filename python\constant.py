#coding=utf-8
from enum import Enum
from datetime import datetime

AUDIOGRAM_DEPTID = "0109"
SIGN_DIR = "./sign/"

def get_sex(sex):
    if sex == Sex.Femal.value:
        return "女"
    elif sex == Sex.Male.value:
        return "男"
    else:
        return "未知"
    
def get_dict(dicts, typeid, pid):
    dict_info = next((
        dict for dict in dicts if dict.ss_typeid == typeid and dict.ss_pid == pid
    ),None)

    return dict_info

def get_dict_name(dicts, typeid, pid):
    dict_info = next(
        dict for dict in dicts if dict.ss_typeid == typeid and dict.ss_pid == pid
    )
    if dict_info is None:
        return ""
    else:
        return dict_info.ss_name

def get_staff_by_name(staffs, name):
    info = next((
        dict for dict in staffs if dict.tj_staffname == name),None
    )
    return info


def get_staff_sign(staffs, name):
    info = next((
        dict for dict in staffs if dict.tj_staffname == name),None
    )
    # print("staff info is:",info.tj_staffname)
    sign = ""
    if info is not None and info.tj_esign != "":
        sign = SIGN_DIR+info.tj_esign
    return sign


def get_local_date(timestamp):
    dt_object = datetime.fromtimestamp(timestamp)
    return dt_object.strftime("%Y-%m-%d")

def get_local_chn_date(timestamp):
    dt_object = datetime.fromtimestamp(timestamp)
    return dt_object.strftime("%Y-%m-%d")

class ReportFormat(Enum):
    Docx = 1
    Pdf = 2


class YesOrNo(Enum):
    No = 0
    Yes = 1

class Trans(Enum):
    Air = 0
    Bone = 1

class Ear(Enum):
    Right = 0
    Left = 1


class PageStyle(Enum):
    Portrait = 0
    Landscape = 1


class Sex(Enum):
    Unknown = 0
    Male = 1
    Femal = 2
    All = 3


# 医院个性化内容 AS:安舒= 0XSLJ:萧山邻家= 0 XSHF:萧山航发
class Customer(str, Enum):
    ASYY = "AS"
    XSLJ = "XSLJ"
    XSHF = "XSHF"
    WZDAMS = "DIAN"


class DictType(Enum):
    DictAll = 0
    DictSex = 1
    DictMarriage = 2
    DictCategory = 3
    DictSource = 4
    DictTesttype = 5
    DictItemtype = 6
    DictCheckall = 8  # 总检结果
    DictCustomer = 10
    DictCPMEStatus = 11
    DictStatus = 12
    DictYesORNo = 13
    DicttAskType = 14
    DictGuiMo = 17
    DictGuide = 18
    DictSysparm = 19
    DictDisRet = 20  # 疾病转归代码


class TestType(Enum):
    All = 0
    PT = 1
    TZ = 2
    SG = 3
    ZG = 4
    LG = 5
    YJ = 6
    JKZ = 7


class ExamStatus(Enum):
    Noprogress = 0  # 体检状态0
    Appoint = 1  # 预约 1
    Register = 2  # 登记 2
    Examining = 3  # 正在体检 3
    Examined = 4  # 体检结束 4
    Allchecked = 5  # 已总检 5
    Reported = 6  # 已报告 6
    CDCUploaded = 7  # CDC已上报 7
    Syncd = 8  # 已同步 8
    Printed = 9  # 已打印 9
    MaxStatus = 100  # 最大值


class CheckResultType(Enum):
    Normal = 0  # 正常
    Recheck = 1  # 复查
    Forbidden = 2  # 禁忌
    Oculike = 3  # 疑似
    Other = 4  # 其他疾病或异常
    Addional = 5  # 需补检

class DepartType(Enum):
    Check = 1  # 
    Lab = 2  # 
    Func = 3  # 


class CustomerInfo(Enum):
    CustomerAll = 0
    CustomerName = 1
    CustomerPhone = 2
    CustomerConsultPhone = 3
    CustomerUrl = 4
    CustomerAddress = 5
    CustomerBus = 6
    CustomerBead = 7
    CustomerReportNum = 8
    CustomerContactor = 9
    CustomerEmail = 10
    CustomerPostcode = 11
    CustomerShowLog = 12
    CustomerLogo = 13
    CustomerFax = 14
    CustomerDJH = 15
    CustomerTJYJ = 16
    CustomerNreportnum = 17
    CustomerInfotip = 18
    CustomerDptname = 19
    CustomerRecheck = 20
    CustomerReportRadio = 21
    CustomerRadioEval = 22
    CustomerMax = 100


class SysParm(Enum):
    SysInfo = 0
    PacsResut = 1
    PacsItem = 2
    LisMethod = 3
    PacsMethod = 4
    TestidPrecode = 5
    SSO = 6
    LisCodeZh = 7
    FromPacs = 8  # /* 8 pacs对接 0：否 1：是*/
    PacsNorm = 9  # /* 9 pacs小结正常值，用逗号分割 */
    ShenHeYS = 10  # /*10 个人报告里是否需要审核医师 0：不需要 1：需要*/
    SHYiSheng = 11  # /*11 审核医生的电子签名，为空则没有*/
    ZJeSign = 12  # /*12 个人报告主检医生的电子签名 为空则没有*/
    ReportIntro = 13  # /* 13 企业报告说明书是否增加额外内容 0：不启用 1：启用*/
    NormalRet = 14  # /* 14 普通体检的默认正常结果*/
    OcuRet = 15  # /* 15 职业健康体检正常的默认结果*/
    CustomizeRpt = 16  # /* 16 是否支持个性化项目选择 0：不支持 1：支持*/
    ExternalUpload = 17  # /* 17 是否外部对接 0：否 1：是*/
    ShowPaymethod = 18  # /* 18 是否显示支付方式 0：否 1：是*/
    ReportUpload = 19  # /* 19 是否启用报告上报 0：否 1：是*/
    ExternalName = 20  # /*20 对接得上手*/
    PZeSign = 21  # /*21 批准医生的电子签名 为空则没有*/
    ZYGR = 22  # /*22 个人职业报告*/
    PTGR = 23  # /*23 个人普通报告*/
    ZYHZ = 24  # /*24 职业汇总表*/
    PTHZ = 25  # /*25 普通汇总表*/
    PTesign = 26  # /*26 普通体检的主检医生签名*/
    Platform = 27  # /*27 是否开启平台服务*/
    Customer = 28  # /*28 医院个性化内容 AS:安舒= 0XSLJ:萧山邻家= 0 XSHF:萧山航发*/
    BCRule = 29  # /*29 条码规则 1：桐乡三院 其他：默认*/
    BCPrecode = 30  # /*30 条码前置 ss_short:前缀 ss_name:总长度*/
    PacsCode = 31  # /*31 pacs代码 1 拆分， 2；不拆分*/
    UpdateTestdate = 32  # /*32*/
    Stample = 33  # /*33 */
    NormalStample = 34  # /*34 普通报告电子章*/
    Symptom = 35  # /*35 报告里是否合并症状 0：不合并 1：合并*/
    EZJeSign = 36  # /*36 企业总表的主检医师电子签名，为空或者ss_short为0，无，1：有 2：打印名字*/
    Advanced = 37  # /*37 显示高级版本*/
    Photo = 38  # /*38 报告是否打印照片*/
    Audio = 39  # /*39 电测听异常是否默认为职业异常 0：否 1：是*/
    ESHYiSheng = 40  # /*40 企业总表审核医生的电子签名，为空则没有 为空或者ss_short为0，无，1：有 2：打印名字*/
    PacsUseDept = 41  # /*41 影像类用科室结论汇总异常 0：否 1：是*/
    ZBRSign = 46  # /*46 制表人电子签名 0：否 1：是*/
