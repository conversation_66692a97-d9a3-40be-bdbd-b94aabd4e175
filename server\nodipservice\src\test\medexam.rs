use crate::{
  common::syscache::SysCache,
  dto::AutoSummaryDto,
  medexam::{datasumsvc::DatasumSvc, summarysvc::SummarySvc},
};
// use dbopservice::{
//   dataaccess::prelude::*,
//   dbinit::{self},
// };
use dataservice::{
  dbinit::{self, DbConnection},
  entities::prelude::*,
};
use utility::timeutil;
// use anyhow::{Ok, Result};
#[tokio::test]
async fn clean_hdlaw() {}

#[tokio::test]
async fn test_export() {
  log4rs::init_file("C:\\Users\\<USER>\\rainerix\\joesong\\nodipservers\\config\\nodipexam.yaml", Default::default()).unwrap();
  let dburl = "mysql://joedev:JoeSong0406!@*************:8306/nodip_new";
  let db = dbinit::DbConnection::new(&dburl, 1).await;

  info!("start to init cache......");
  let ret = crate::common::syscache::SysCache::new(&"33".to_string(), &db).await;

  // info!("sleep 6 secs");
  // tokio::time::sleep(tokio::time::Duration::from_secs(6)).await;
  // let dict = ret.get_dict(5, 3, &db).await;
  // info!("dict data:{:?}", &dict);

  let testids = vec!["10017579".to_string(), "10004635".to_string()];
  let syncids: Vec<String> = vec![];
  // let syncids = vec!["0302".to_string(), "0702".to_string()];

  let ret = DatasumSvc::export_medexam_with_details(&testids, &syncids, -1, "C:\\Users\\<USER>\\rainerix\\joesong\\nodipservers\\reports", &db).await;
  // info!("result is:{}", ret.unwrap().to_string());
}

#[derive(Default, Debug, Clone)]
pub struct DemoTest {
  pub i1: i32,
  pub i2: Vec<i32>,
}

#[tokio::test]
async fn do_test() {
  //以下这3个是测试的基本条件，需要初始化
  let log_cfg = "C:\\Users\\<USER>\\rainerix\\joesong\\nodipservers\\config\\nodipexam.yaml";
  let uri = "mysql://joedev:JoeSong0406!@*************:8306/nodip_new";
  log4rs::init_file(log_cfg, Default::default()).unwrap();

  let dbconn = DbConnection::new(&uri, 3).await;

  let syscache = SysCache::new(&uri.to_string(), &dbconn).await;
  crate::SYSCACHE.set(syscache).expect("set global value error");

  let demo_test = vec![DemoTest { i1: 1, i2: vec![1, 2, 3] }, DemoTest { i1: 2, i2: vec![4, 5, 6] }];
  let demo_id: Vec<i32> = demo_test.iter().map(|v| v.i2.clone()).collect::<Vec<Vec<i32>>>().into_iter().flatten().collect();
  println!("Demo id is:{:#?}", &demo_id);
  let stdate = 1710729600;
  let enddate = 1714092389;
  let ret = crate::medexam::statsvc::StatSvc::stat_dept_item(stdate, enddate, "", &dbconn).await;
  println!("result is:{:?}", &ret);
}
// #[tokio::test]
// async fn test_quickregister() {
//   log4rs::init_file("D:\\joesong\\nodip_server\\config\\log.yaml", Default::default()).unwrap();
//   let dburl = "mysql://nodip_saas:saas12345!A@*************:41787/nodip_jxxc";
//   let db = dbinit::DbConnection::new(&dburl, 1).await;
//   let syscache = SysCache::new(1, &"32,33".to_string(), &db).await;

//   let mut medinfo = TjMedexaminfo { ..Default::default() };
//   medinfo.tj_testtype = 1;
//   medinfo.tj_paymethod = 2;
//   medinfo.tj_packagename = "荣盛噪声".to_string();
//   medinfo.tj_testdate = timeutil::current_timestamp();

//   let mut ptinfo = TjPatient { ..Default::default() };
//   ptinfo.tj_pname = "测试1".to_string();
//   ptinfo.tj_pmobile = "aaaa".to_string();
//   ptinfo.tj_pbirthday = "1988-01-23".to_string();
//   let mut pkginfo = TjPackageinfo { ..Default::default() };
//   pkginfo.id = 236;

//   // let ret = MedexamSvc::medexam_quick_register(&ptinfo, &medinfo, &pkginfo, &syscache, &db).await;
//   // info!("quick register result:{:?}", &ret);
// }

// #[tokio::test]
// async fn test_autosummary() {
//   log4rs::init_file("D:\\joesong\\nodip_server\\config\\log.yaml", Default::default()).unwrap();
//   let current_dir = env!("CARGO_MANIFEST_DIR");
//   println!("current director:{}", &current_dir);

//   // let summaries: Vec<TjTestsummary> = vec![TjTestsummary {
//   //   id: 0,
//   //   tj_testid: "10000029".to_string(),
//   //   tj_deptid: "0013".to_string(),
//   //   tj_summary: "todo!()".to_string(),
//   //   tj_suggestion: "admin".to_string(),
//   //   tj_isfinished: 0,
//   //   tj_doctorid: "123".to_string(),
//   //   tj_doctor: "123".to_string(),
//   //   tj_date: 12345,
//   //   tj_forceend: 0,
//   //   tj_checkdoctor: "123".to_string(),
//   //   tj_checkdate: 12345,
//   // }];
//   let summary = TjTestsummary {
//     id: 0,
//     tj_testid: "10000029".to_string(),
//     tj_deptid: "0013".to_string(),
//     tj_summary: "todo!()".to_string(),
//     tj_suggestion: "admin".to_string(),
//     tj_isfinished: 0,
//     tj_doctorid: "123".to_string(),
//     tj_doctor: "123".to_string(),
//     tj_date: 12345,
//     tj_forceend: 0,
//     tj_checkdoctor: "123".to_string(),
//     tj_checkdate: 12345,
//   };
//   let medinfo: TjMedexaminfo = TjMedexaminfo {
//     tj_testid: "**********".to_string(),
//     ..Default::default()
//   };
//   // TjMedexaminfo {
//   //   tj_testid: "10000033".to_string(),
//   //   ..Default::default()
//   // },

//   let pthazards: Vec<TjPatienthazards> = Vec::new();

//   let dto: AutoSummaryDto = AutoSummaryDto {
//     medinfo,
//     summary,
//     ciinfos: vec![],
//     pthazards,
//     ignore: 1,
//     saveci: 1,
//     pacssum: "".to_string(),
//   };
//   let dburl = "mysql://nodip_saas:saas12345!A@*************:41787/nodip_jxxc";
//   let db = dbinit::DbConnection::new(&dburl, 1).await;

//   let syscache = SysCache::new(1, &"".to_string(), &db).await;

//   let result = SummarySvc::auto_summary(&dto, &syscache, &db).await; //.expect("summary error");
//   info!("summary result:{:#?}", &result);
//   assert_eq!(1, 1)
// }
