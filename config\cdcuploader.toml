[database]
uri = "mysql://hzone:FKsX3Gde3G4r!@************:5506/nodip_new"

[application]
apphost = "0.0.0.0"
appport = 8003

[system]
platform = "zj"         #zj, nb, wz,sx,zwx
upload_mode = 0         # 0直接上报  1存文件
file_path = "./cdcfile"
area_code = "33030100"
area_name = "浙江省杭州市"
# use_https = 0 #0 不用https， 1：使用https
# other_hdcode="110004,129999,130099,140099,150099,160099" # zwx
# other_items = "" #zwx
# dr_deptid = "11061" # 胸片得项目代码 zwx
dr_deptid = "11061,11247,11493,11494,20005"         # 胸片得项目代码 zj
other_hdcode = "11999,12999,13999,14999,15999"      # zj
other_items = "11347,13008,10747,13006,13013,13014" #zj
area = "33"                                         # 区域多个区域用逗号隔开,比如33,61
rangeval = 0                                        #0：用本系统的限值 1：用lis系统的限制
multiitem = 0                                       #是否开启一对多（体检系统一个项目对应平台多个项目）0:关闭 1:开启
additems = ["11317"]

[organization]
key_file = "./key/pri.key"
org_code = "330114999"
password = "hzasyyyxgs330114999#zyb"
org_name = "杭州安舒医院有限公司"
# key_file="./key/pri_j.key"
# org_code="330402019"
# password="nhcnjdsqzx330402019#zyb"
# org_name="嘉兴市南湖区城南街道社区卫生服务中心"

# org_code="3601110001"
# password="d929d165df0046cdac0868e0d88e009a"
# org_name="南昌中医药大学第二附属医院"

# server_url="http://47.111.103.89:18889/ws_data/ws/TJ?wsdl"

#江西测试地址
# server_url = "http://10.16.130.36:9002/BhkDataService/zwbhkService"
# server_url2= "http://10.16.130.36:9002/BhkDataService/crptUpService"

#浙江省测试地址
server_url = "http://120.26.209.208:8003/ws_data/ws/TJ?wsdl"
server_url2 = ""

#vpn
# http://10.10.3.30:8001/ws_data/ws/TJ?wsdl

#正式环境：http://10.10.3.30:8001/ws_data/ws/TJ?WSDL
#测试环境：http://10.10.3.30:8002/ws_data/ws/TJ?WSDL
