@echo off
REM Report Server Deployment Script for Windows
REM This script automates the deployment process

echo ========================================
echo Report Server Deployment Script
echo ========================================

set PYTHON_DIR=%~dp0python
set BUILD_DIR=%~dp0build
set DIST_DIR=%~dp0dist

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

echo Python found: 
python --version

REM Change to python directory
cd /d "%PYTHON_DIR%"

REM Install dependencies
echo.
echo Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

REM Check if PyInstaller is available
pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo Installing PyInstaller...
    pip install pyinstaller
)

REM Build executable
echo.
echo Building standalone executable...
python build.py --executable
if errorlevel 1 (
    echo Error: Failed to build executable
    pause
    exit /b 1
)

REM Create deployment package
echo.
echo Creating deployment package...
python build.py --package zip
if errorlevel 1 (
    echo Error: Failed to create package
    pause
    exit /b 1
)

echo.
echo ========================================
echo Deployment completed successfully!
echo ========================================
echo.
echo Files created:
echo - Executable: %DIST_DIR%\reportserver.exe
echo - Package: %DIST_DIR%\reportserver_package.zip
echo.
echo To run the server:
echo 1. Extract reportserver_package.zip
echo 2. Run start_server.bat
echo 3. Access API at http://localhost:8080/api
echo.
pause
