use crate::dto::*;
use anyhow::{anyhow, Result};
use tracing::*;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
pub struct HealthyinfoSvc;

impl HealthyinfoSvc {
  pub async fn query_healthyinfos(testid: &str, db: &DbConnection) -> Result<QuestionnairDto> {
    if testid.is_empty() {
      return Err(anyhow!("testid is empty"));
    }
    let ret = TjMedexaminfo::query(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let medret = ret.unwrap();
    if medret.is_none() {
      return Err(anyhow!("Can not find medinfo by {testid}"));
    }
    let medinfo = medret.unwrap();
    let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = ret.unwrap();
    let mut patient = TjPatient { ..Default::default() };
    if !ret.is_none() {
      patient = ret.unwrap();
    }

    let mut retdto: QuestionnairDto = QuestionnairDto {
      medinfo,
      patient,
      ..Default::default()
    };

    let ret = TjHealthyinfo::query_many(&retdto.medinfo.tj_pid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let healthyinfo = ret.unwrap();
    if healthyinfo.len() <= 0 {
      return Ok(retdto);
    }
    let healthyinfo = healthyinfo.iter().max_by_key(|f| f.id).map_or(
      TjHealthyinfo {
        tj_testid: testid.to_owned(),
        ..Default::default()
      },
      |v| v.to_owned(),
    );
    info!("final query testid is:{}", &healthyinfo.tj_testid);
    let ret = TjOccupationhistory::query_many(&healthyinfo.tj_testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let occupations = ret.unwrap();
    let ret = TjDiseasehistory::query_many(&healthyinfo.tj_testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let diseases = ret.unwrap();
    let ret = TjMarriagehistory::query_many(&healthyinfo.tj_testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let marriages = ret.unwrap();
    retdto.healthyinfo = healthyinfo;
    retdto.diseases = diseases;
    retdto.marriages = marriages;
    retdto.occupations = occupations;
    // retdto.medinfo = medinfo;

    Ok(retdto)
  }

  pub async fn save_healthyinfos(dto: &QuestionnairDto, db: &DbConnection) -> Result<()> {
    // Ok(QuestionnairDto { ..Default::default() })
    // info!("healthyinfo dto:{:?}", &dto);
    let healthyinfo = dto.healthyinfo.to_owned();
    let mut occupations = dto.occupations.to_owned();
    let mut diseases = dto.diseases.to_owned();
    let mut marriages = dto.marriages.to_owned();
    // if healthyinfo.id <= 0 {
    let testids: Vec<String> = vec![healthyinfo.tj_testid.clone()];
    let ret = TjHealthyinfo::delete(&testids, healthyinfo.id, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    // }
    let ret = TjHealthyinfo::save(&healthyinfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    //delete
    let ret = TjOccupationhistory::delete(&vec![healthyinfo.tj_testid.to_string()], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = TjDiseasehistory::delete(&vec![healthyinfo.tj_testid.to_string()], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = TjMarriagehistory::delete(&vec![healthyinfo.tj_testid.to_string()], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    //insert
    if occupations.len() > 0 {
      occupations.iter_mut().for_each(|f| f.id = 0);
      let ret = TjOccupationhistory::save_many(&occupations, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if diseases.len() > 0 {
      diseases.iter_mut().for_each(|f| f.id = 0);
      let ret = TjDiseasehistory::save_many(&diseases, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if marriages.len() > 0 {
      marriages.iter_mut().for_each(|f| f.id = 0);
      let ret = TjMarriagehistory::save_many(&marriages, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    Ok(())
  }
}
