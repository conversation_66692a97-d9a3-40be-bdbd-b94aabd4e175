use super::segment::SegmentHelper;
use crate::common::constant;
// use anyhow::{anyhow, Result};
// use dbopservice::dataaccess::prelude::*;
use dataservice::entities::prelude::*;
use utility::timeutil;

pub struct MessageHelper;

impl MessageHelper {
  // MSH|^~\&|HIS_OutPatient|WinningSoft|TECH|WinningSoft|20230605082006|pv1|ADT^A04|AUZMMTG6-LF37-388K-MRYR-DUC56SJZOQ3S|P|2.4|||||CHN
  // EVN|A04|20230605082006
  // PID|1|**********^^^&PATID|**********^^^&PATID~^^^&BLH~**********^^^&BRKH~341204199404102617^^^&SFZH~^^^&YEXH||钱令||19940410000000|M|||安徽省阜阳市颍泉区苏屯乡尚小庄行政村钱小�?0号||^^^^^^18226921153|||M^已婚||||||17||||||40^中国
  // PV1|1|P|3022^体检中心||||650095^谢屹|||||||||||0|**********|101~自费||||||||||||||||||||||||20230605082006||||||**********
  pub fn generate_adt_a04_message(ptinfo: &TjPatient, medinfo: &TjMedexaminfo, corpinfo: &TjCorpinfo, doctor: &TjStaffadmin) -> (String, String) {
    let current_datetime = timeutil::current_datetime();
    let (msh, uuid) = SegmentHelper::get_msh_segment(&current_datetime, &constant::MessageCode::ADT.to_string(), &constant::EventCode::A04.to_string());
    let evn = SegmentHelper::get_evn_segment(&constant::EventCode::A04.to_string(), &current_datetime);

    let mut porc = 0;
    let mut corpname = "";
    if corpinfo.id > 2 {
      corpname = corpinfo.tj_corpname.as_str();
      porc = 1;
    }

    let pid = SegmentHelper::get_pid_segment(
      &ptinfo.tj_pid,
      &medinfo.tj_testid,
      &ptinfo.tj_pidcard,
      &ptinfo.tj_pname,
      &ptinfo.tj_pbirthday,
      &ptinfo.tj_pphone,
      ptinfo.tj_psex,
      &ptinfo.tj_paddress,
      corpname,
    );
    let pv1 = SegmentHelper::get_pv1_segment(&ptinfo.tj_pid, &doctor.tj_staffno, &doctor.tj_staffname, porc, &current_datetime);
    (format!("{msh}{evn}{pid}{pv1}"), uuid)
  }
  // MSH|^~\&|HIS_OutPatient|WinningSoft|TECH|WinningSoft|20230605082007||DFT^P03|BJJFE3U9-RZFN-D70Z-WPPG-QHPYMMAJQ4HC|P|2.4|||||CHN
  // EVN|P03|20230605082007
  // PID|1||11311737^^^&HISPATID|||||M||||||||S
  // PV1|1|P|||||||||||||||||**********|~||||||||||||||||||||||||||||||**********
  // ORC|NW|061104760|||0|||||^^^^^^^0||9209^^|||||||^^^^^^^^^P~^^^^^^^^^D
  // OBR||||11050000100_01^体检费^^0|0|20230605082007|||1|||||||||3022|体检中心|001|体检中心||&251^&&&1||||||||||||||||||||||^^^zk061104760|WCT^^^2
  // FT1|1|Sw100001||20230605082007||0|11050000100_01^体检费|0|收费项目|1|251|251|3022^健康管理中心||||0|0||9209^
  pub fn generate_dft_p03_message(
    optype: i32,
    ptinfo: &TjPatient,
    medinfo: &TjMedexaminfo,
    corpinfo: &TjCorpinfo,
    doctor: &TjStaffadmin,
    pkginfo: &TjPackageinfo,
    extitemtype: i32,
  ) -> String {
    let current_datetime = timeutil::current_datetime();
    let (msh, _uuid) = SegmentHelper::get_msh_segment(&current_datetime, &constant::MessageCode::DFT.to_string(), &constant::EventCode::P03.to_string());
    let evn = SegmentHelper::get_evn_segment(&constant::EventCode::P03.to_string(), &current_datetime);
    let mut porc = 0;
    // let mut corpname = "";
    if corpinfo.id > 2 {
      // corpname = corpinfo.tj_corpname.as_str();
      porc = 1;
    }
    let pid = SegmentHelper::get_dft_pid_segment(&ptinfo.tj_patid.to_string());
    let pv1 = SegmentHelper::get_pv1_segment(&ptinfo.tj_pid, &doctor.tj_staffno, &doctor.tj_staffname, porc, &current_datetime);

    let orc = SegmentHelper::get_orc_segment(&medinfo.tj_testid, &current_datetime, optype, &doctor.tj_staffno, &doctor.tj_staffname);
    let obr = SegmentHelper::get_obr_segment("11050000100_01", "体检费", &pkginfo.tj_price, &current_datetime, &medinfo.tj_testid, "体检中心", extitemtype);
    let ft1 = SegmentHelper::get_ft1_segment(&medinfo.tj_testid, &pkginfo.tj_price, &current_datetime, &doctor.tj_staffno);

    format!("{msh}{evn}{pid}{pv1}{orc}{obr}{ft1}")
  }

  // MSH|^~\&|HIS_OutPatient|WinningSoft|LIS|WinningSoft|20230605082007||ORM^O01|X9IQODPN-64RH-MIBF-ZWZK-NKNEK6YNXCYP|P|2.4|||||CHN
  // PID|1|**********^^^&PATID|**********^^^&PATID~**********^^^&BLH~**********^^^&BRKH~^^^&SFZH~341204199404102617^^^&YEXH||钱令||19940410000000|M|||安徽省阜阳市颍泉区苏屯乡尚小庄行政村钱小庄60号||^^^^^^18226921153|||M^已婚||||||17^||||||40^中国
  // PV1|1|P|||||||||||0|||||0|**********|101~体检费||||||||||||||||||||||||||||||**********
  // ORC|NW|061104756|||0|||||^^^^^^^0||9209^^|||20230605082007||||^^^^^^^^^P~^^^^^^^^^D
  // OBR||||2073^体检尿常规^^0|0|20230605082007|||1|||||||||3022|体检中心||检验科||&19^&&&1|||||||||||||20230605082007|||体检尿常规^^0^^申请单体检尿常规^0||||||^^^zk061104756|WCT^^^2
  pub fn generate_orm_o01_message(optype: i32, ptinfo: &TjPatient, medinfo: &TjMedexaminfo, corpinfo: &TjCorpinfo, doctor: &TjStaffadmin, iteminfo: &TjIteminfo) -> String {
    let current_datetime = timeutil::current_datetime();
    let (msh, _uuid) = SegmentHelper::get_msh_segment(&current_datetime, &constant::MessageCode::ORM.to_string(), &constant::EventCode::O01.to_string());
    // let evn = SegmentHelper::get_evn_segment(&constant::EventCode::O01.to_string(), &current_datetime);
    let mut porc = 0;
    let mut corpname = "";
    if corpinfo.id > 2 {
      corpname = corpinfo.tj_corpname.as_str();
      porc = 1;
    }

    let pid = SegmentHelper::get_pid_segment(
      &ptinfo.tj_pid,
      &medinfo.tj_testid,
      &ptinfo.tj_pidcard,
      &ptinfo.tj_pname,
      &ptinfo.tj_pbirthday,
      &ptinfo.tj_pphone,
      ptinfo.tj_psex,
      &ptinfo.tj_paddress,
      corpname,
    );
    let pv1 = SegmentHelper::get_pv1_segment(&ptinfo.tj_pid, &doctor.tj_staffno, &doctor.tj_staffname, porc, &current_datetime);

    let orc = SegmentHelper::get_orc_segment(&medinfo.tj_testid, &current_datetime, optype, &doctor.tj_staffno, &doctor.tj_staffname);
    let mut lisnum = iteminfo.tj_lisnum.to_string();
    if lisnum.is_empty() {
      lisnum = iteminfo.tj_itemid.to_string();
    }
    let obr = SegmentHelper::get_obr_segment(
      &lisnum,
      &iteminfo.tj_itemname,
      &iteminfo.tj_itemprice.to_string(),
      &current_datetime,
      &medinfo.tj_testid,
      "检验科",
      iteminfo.tj_extitemtype,
    );
    format!("{msh}{pid}{pv1}{orc}{obr}")
  }

  pub fn generate_orr_message(msgid: &str) -> String {
    // let msh = format!("MSH|^~\\&|HIS|HISSystem|EXAM|EXAMSystem|{20230605082007}||ACK^A01|{4f7c224a-087c-4c3f-9f59-6f6f9f59d90b}|P|2.4|||||CHN\r");
    let current_datetime = timeutil::current_datetime();
    let (msh, _uuid) = SegmentHelper::get_msh_segment(&current_datetime, &constant::MessageCode::ORR.to_string(), &constant::EventCode::O02.to_string());
    let msa = format!("MSA|AA|{msgid}\r");
    format!("{msh}{msa}")
  }
  pub fn generate_oru_message(msgid: &str) -> String {
    let current_datetime = timeutil::current_datetime();
    let (msh, _uuid) = SegmentHelper::get_msh_segment(&current_datetime, &constant::MessageCode::ACK.to_string(), &constant::EventCode::R01.to_string());
    let msa = format!("MSA|AA|{msgid}\r");
    format!("{msh}{msa}")
  }
}
