use crate::entities::{prelude::*, tj_iteminfo};
use anyhow::{anyhow, Result};
use sea_orm::{sea_query::Expr, ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjIteminfo {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<TjIteminfo>> {
    let ret = TjIteminfoEntity::find().filter(tj_iteminfo::Column::TjItemid.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }
  pub async fn query_combines(db: &DatabaseConnection) -> Result<Vec<TjIteminfo>> {
    let ret = TjIteminfoEntity::find().filter(tj_iteminfo::Column::TjCombineflag.eq(1)).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  //query_all_with_lisnum
  pub async fn query_all_with_lisnum(db: &DatabaseConnection) -> Result<Vec<TjIteminfo>> {
    let ret = TjIteminfoEntity::find().filter(tj_iteminfo::Column::TjLisnum.ne("")).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(itemids: &Vec<String>, okflag: i32, combineflag: i32, itemtype: i32, lisnum: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjIteminfo>> {
    let mut conditions = Condition::all();

    if okflag > -1 {
      conditions = conditions.add(tj_iteminfo::Column::TjOkflag.gte(0));
    }

    if combineflag > -1 {
      conditions = conditions.add(tj_iteminfo::Column::TjCombineflag.eq(combineflag));
    }

    if itemtype > 0 {
      conditions = conditions.add(tj_iteminfo::Column::TjItemtype.eq(itemtype));
    }

    if itemids.len() > 0 {
      conditions = conditions.add(tj_iteminfo::Column::TjItemid.is_in(itemids.to_owned()));
    }
    if lisnum.len() > 0 {
      conditions = conditions.add(tj_iteminfo::Column::TjLisnum.is_in(lisnum.to_owned()));
    }

    let ret = TjIteminfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_many(infos: &Vec<TjIteminfo>, db: &DatabaseConnection) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len();
    let (insert_vals, update_vals): (Vec<TjIteminfo>, Vec<TjIteminfo>) = infos.to_owned().into_iter().partition(|f| f.id == 0);
    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_iteminfo::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_iteminfo::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjIteminfoEntity::insert_many(active_insert_values).exec(db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      for val in update_vals {
        let mut new_val = tj_iteminfo::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    Ok(total as i64)
  }

  pub async fn save(info: &TjIteminfo, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_iteminfo::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }
  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let ret = TjIteminfoEntity::update_many()
      .col_expr(tj_iteminfo::Column::TjOkflag, Expr::value(-1))
      .filter(Condition::all().add(tj_iteminfo::Column::Id.is_in(ids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
}
