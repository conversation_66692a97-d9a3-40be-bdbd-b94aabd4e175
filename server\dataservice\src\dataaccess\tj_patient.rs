use crate::entities::{prelude::*, tj_medexaminfo, tj_patient};
use anyhow::{anyhow, Result};
use sea_orm::{
  sea_query::Query, ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, ConnectionTrait, DatabaseBackend, DatabaseConnection, EntityTrait, QueryFilter, Set, Statement,
};
use serde_json::json;
// use tracing::log;

impl TjPatient {
  pub fn new(pid: &str) -> Self {
    TjPatient {
      tj_pid: pid.to_string(),
      ..Default::default()
    }
  }

  pub async fn query(pid: &str, db: &DatabaseConnection) -> Result<Option<TjPatient>> {
    if pid.is_empty() {
      return Err(anyhow!("pid is empty,not allowed"));
    }
    let ret = TjPatientEntity::find().filter(tj_patient::Column::TjPid.eq(pid)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_by_testid(testid: &str, db: &DatabaseConnection) -> Result<Option<TjPatient>> {
    if testid.is_empty() {
      return Err(anyhow!("testid is empty, not allowed"));
    }
    let query = TjPatientEntity::find().filter(
      Condition::all().add(
        tj_patient::Column::TjPid.in_subquery(
          Query::select()
            .column(tj_medexaminfo::Column::TjPid)
            // .expr(tj_medexaminfo::Column::TjTestid.eq(testid.to_owned()))
            .and_where(tj_medexaminfo::Column::TjTestid.eq(testid.to_owned()))
            .from(tj_medexaminfo::Entity)
            .to_owned(),
        ),
      ),
    );
    // let sqlstr = query.build(DbBackend::MySql).to_string();
    // info!("sql string:{}", &sqlstr);
    let ret = query.one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_by_idcard(code: &str, db: &DatabaseConnection) -> Result<Option<TjPatient>> {
    if code.is_empty() {
      return Err(anyhow!("idcard is empty, not allowed"));
    }
    let ret = TjPatientEntity::find().filter(tj_patient::Column::TjPidcard.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(pids: &Vec<String>, name: &str, idcards: &Vec<String>, testids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjPatient>> {
    let mut conditions = Condition::all();
    if pids.len() > 0 {
      conditions = conditions.add(tj_patient::Column::TjPid.is_in(pids.to_owned()));
    }
    if !name.is_empty() {
      conditions = conditions.add(tj_patient::Column::TjPname.contains(name));
    }
    if idcards.len() > 0 {
      conditions = conditions.add(tj_patient::Column::TjPidcard.is_in(idcards.to_owned()));
    }

    if testids.len() > 0 {
      conditions = conditions.add(
        tj_patient::Column::TjPid.in_subquery(
          Query::select()
            .column(tj_medexaminfo::Column::TjPid)
            // .expr(tj_medexaminfo::Column::TjTestid.is_in(testids.to_owned()))
            .and_where(tj_medexaminfo::Column::TjTestid.is_in(testids.to_owned()))
            .from(tj_medexaminfo::Entity)
            .to_owned(),
        ),
      );
    }
    let query = TjPatientEntity::find().filter(conditions);
    // info!("patient query string:{:?}", &query.build(DbBackend::MySql).to_string());
    let ret = query.all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjPatient, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_patient::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn update_patid_by_pid(patid: &str, pid: &str, db: &DatabaseConnection) -> Result<u64> {
    let sqlstr = format!("update tj_patient set tj_patid = {patid} where tj_pid = {pid}");
    let ret = db.execute(Statement::from_string(DatabaseBackend::MySql, sqlstr.to_owned())).await;
    // info!("execute sql ret:{:?}, sql:{}", ret, sqlstr);
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap().rows_affected())
  }

  // pub async fn delete(testids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
  //   let ret = TjMedexaminfoEntity::update_many()
  //     .col_expr(tj_medexaminfo::Column::TjCheckstatus, Expr::value(-1))
  //     .filter(Condition::all().add(tj_medexaminfo::Column::TjTestid.is_in(testids.to_owned())))
  //     .exec(db)
  //     .await;
  //   if ret.as_ref().is_err() {
  //     return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
  //   }
  //   let update_ret = ret.unwrap();
  //   Ok(update_ret.rows_affected)
  // }
}
