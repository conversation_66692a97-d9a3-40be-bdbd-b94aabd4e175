//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Co<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, Debug, DeriveEntity)]
pub struct Entity;

impl EntityName for Entity {
  fn table_name(&self) -> &str {
    "s_cdc_hazardfactor_zwx"
  }
}

#[derive(<PERSON>lone, Debug, PartialEq, DeriveModel, DeriveActiveModel, Eq, Serialize, Deserialize)]
pub struct Model {
  pub id: i32,
  pub cdc_code: String,
  pub cdc_hazardname: String,
  pub hazard_type: i32,
  pub tj_hazardid: i32,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveColumn)]
pub enum Column {
  Id,
  CdcCode,
  CdcHazardname,
  HazardType,
  TjHazardid,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, <PERSON>umI<PERSON>, DerivePrimaryKey)]
pub enum PrimaryKey {
  Id,
}

impl PrimaryKeyTrait for PrimaryKey {
  type ValueType = i32;
  fn auto_increment() -> bool {
    true
  }
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter)]
pub enum Relation {}

impl ColumnTrait for Column {
  type EntityName = Entity;
  fn def(&self) -> ColumnDef {
    match self {
      Self::Id => ColumnType::Integer.def(),
      Self::CdcCode => ColumnType::String(Some(10u32)).def(),
      Self::CdcHazardname => ColumnType::String(Some(100u32)).def(),
      Self::HazardType => ColumnType::Integer.def(),
      Self::TjHazardid => ColumnType::Integer.def(),
    }
  }
}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
