use crate::entities::{prelude::*, ss_monitor};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter};

impl SsMonitor {
  pub async fn query(id: i64, db: &DatabaseConnection) -> Result<Option<SsMonitor>> {
    if id <= 0 {
      return Err(anyhow!("id<=0, not allowed"));
    }
    let ret = SsMonitorEntity::find().filter(ss_monitor::Column::Id.eq(id)).one(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(/*codes: &Vec<String>,*/ scode: &str, db: &DatabaseConnection) -> Result<Vec<SsMonitor>> {
    let mut condition = Condition::all();
    // if codes.len() > 0 {
    //   condition = condition.add(ss_monitor::Column::SsCode.is_in(codes.to_owned()));
    // }
    if !scode.is_empty() {
      condition = condition.add(ss_monitor::Column::SsJobcode.eq(scode));
    }
    let ret = SsMonitorEntity::find().filter(condition).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
