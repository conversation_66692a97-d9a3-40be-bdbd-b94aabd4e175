//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "sys_hazard_checkitem")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub check_item_name: String,
  pub check_item_description: String,
  pub local_tj_itemid: String,
  pub result_type: Option<i8>,
  pub result_unit_name_value: Option<String>,
  pub result_refer_min_value: Option<String>,
  pub result_refer_max_value: Option<String>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
