use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(C<PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjAudiogramsummary {
  pub id: i64,
  pub tj_adsummary: String,
  pub tj_adsuggestion: String,
  pub tj_isabnormal: i32,
}
crud!(TjAudiogramsummary {}, "tj_audiogramsummary");
rbatis::impl_delete!(TjAudiogramsummary{delete(ids:&[i64]) => "`where id in ${ids.sql()} `"});

impl TjAudiogramsummary {
  pub async fn query_many(rb: &mut rbatis::RBatis) -> Result<Vec<TjAudiogramsummary>> {
    // let mut rb = db.get_connection_clone();
    let ret = TjAudiogramsummary::select_all(rb).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjAudiogramsummary) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    if info.id <= 0 {
      let ret = TjAudiogramsummary::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      return Ok(ret.unwrap().last_insert_id.as_i64().unwrap_or_default());
    } else {
      let ret = TjAudiogramsummary::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      return Ok(ret.unwrap().last_insert_id.as_i64().unwrap_or_default());
    }
  }
}
