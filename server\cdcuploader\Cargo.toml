[package]
name = "cdcuploader"
version = "0.101.0"
edition = "2021"
description = "上报系统,build time: 2025-07-03"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { path = "../utility" }
# dbopservice = { path = "../dbopservice" }
dataservice = { path = "../dataservice" }
nodipservice = { path = "../nodipservice" }

serde = { workspace = true }      # { version = "1", features = ["derive"] }
serde_json = { workspace = true } #"1"
tokio = { workspace = true }      #{ version = "1", features = ["full"] }
chrono = { workspace = true }     #"0.4"

#tokio-rs web framework
axum = { workspace = true }       #{ version = "0.8.1", features = ["multipart", "tokio", "original-uri"] }
axum-extra = { workspace = true } #{ version = "0.9", features = ["typed-header"] }

tower = { workspace = true }                      #{ version = "0.5.1", features = ["util", "filter"] }
hyper = { workspace = true, features = ["full"] }
hyper-util = { workspace = true }                 #{ version = "0.1", features = ["client-legacy"] }
tower-http = { workspace = true }                 #{ version = "0.6.0", features = ["trace", "fs"] }
headers = { workspace = true }                    #"0.4"
http-body-util = { workspace = true }             #"0.1"
futures = { workspace = true }                    #"0.3"

anyhow = { workspace = true }    #"1.0"
thiserror = { workspace = true } #"2.0.3"
dashmap = { workspace = true }   #"6"

# log = { workspace = true }    #"0.4"
# log4rs = { workspace = true }

tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = [
    "env-filter",
    "fmt",
    "time",
] }
tracing-error = { workspace = true }
tracing-appender = { workspace = true }
file-rotate = { workspace = true }


config = { workspace = true } #"0.15.6"


quick-xml = { workspace = true }                              #{ version = "0.37.0", features = ["serialize"] }
num_enum = { workspace = true }                               #"0.7"
yaserde = { workspace = true, features = ["yaserde_derive"] }
#{ version = "0.12.0", features = ["yaserde_derive"] }
# yaserde_derive = "0.12.0"
md-5 = { workspace = true } #"0.10"
# reqwest = { version = "0.11.10", features = ["json","blocking", "multipart"] }
reqwest = { workspace = true } #{ version = "0.12.3", default-features = false, features = [
#     "json",
#     "stream",
#     "multipart",
#     "rustls-tls",
# ] }

mpart-async = { workspace = true } # "0.7"
base64 = { workspace = true }      #"0.22"
encoding_rs = { workspace = true } #"0.8"

# strum = "0.24"
strum = { workspace = true }        #{ version = "0.26", features = ["derive"] }
strum_macros = { workspace = true } #"0.26"

# [target.'cfg(not(target_os = "windows"))'.dependencies]
zip = { workspace = true } #"2.1.2"

html-escape = { workspace = true }

# [target.'cfg(target_os = "windows")'.dependencies]
# zip = "1.1.4"

# [profile.release]
# lto = true
# codegen-units = 1
# panic = "abort"
# strip = true
