//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Default, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_checkallnew")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  #[sea_orm(unique)]
  pub tj_testid: String,
  pub tj_typeid: i32,
  pub tj_discode: String,
  pub tj_itemcode: String,
  pub tj_hazardcode: String,
  pub tj_diseasename: String,
  pub tj_drret: i32,
  #[sea_orm(column_type = "custom(\"LONGTEXT\")")]
  pub tj_ocuabnormal: String,
  #[sea_orm(column_type = "custom(\"LONGTEXT\")")]
  pub tj_othabnormal: String,
  #[sea_orm(column_type = "custom(\"LONGTEXT\")")]
  pub tj_ocuconclusion: String,
  #[sea_orm(column_type = "custom(\"LONGTEXT\")")]
  pub tj_othconclusion: String,
  #[sea_orm(column_type = "custom(\"LONGTEXT\")")]
  pub tj_ocusuggestion: String,
  #[sea_orm(column_type = "custom(\"LONGTEXT\")")]
  pub tj_othsuggestion: String,
  #[sea_orm(column_type = "custom(\"LONGTEXT\")")]
  pub tj_ocuopinion: String,
  #[sea_orm(column_type = "custom(\"LONGTEXT\")")]
  pub tj_othopinion: String,
  pub tj_staffid: i64,
  pub tj_checkdate: i64,
  pub tj_castatus: i32,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
