//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_corpmedexaminfo")]
pub struct Model {
  #[sea_orm(column_name = "ID", primary_key)]
  pub id: i32,
  pub tj_corpnum: Option<i32>,
  pub tj_corpname: Option<String>,
  pub tj_pyjm: Option<String>,
  pub tj_testtype: i32,
  pub tj_status: i32,
  pub tj_testers: i32,
  pub tj_checked: i32,
  pub tj_reportnum: Option<String>,
  pub tj_staffid: i32,
  pub tj_aptnum: Option<i64>,
  pub tj_notifystate: Option<i8>,
  pub tj_testdate: i64,
  pub tj_recdate: i64,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, <PERSON><PERSON><PERSON><PERSON>, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
