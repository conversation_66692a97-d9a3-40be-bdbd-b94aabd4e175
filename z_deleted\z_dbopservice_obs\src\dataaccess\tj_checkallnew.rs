use anyhow::{anyhow, Result};
use rbatis::rbatis_codegen::IntoSql;
use rbatis::{crud, py_sql};
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Default, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjCheckallnew {
  pub id: i64,
  pub tj_testid: String,
  pub tj_typeid: i32,
  pub tj_discode: String,
  pub tj_itemcode: String,
  pub tj_hazardcode: String,
  pub tj_diseasename: String,
  pub tj_ocuabnormal: String,
  pub tj_othabnormal: String,
  pub tj_ocuconclusion: String,
  pub tj_othconclusion: String,
  pub tj_ocusuggestion: String,
  pub tj_othsuggestion: String,
  pub tj_ocuopinion: String,
  pub tj_othopinion: String,
  pub tj_staffid: i64,
  pub tj_checkdate: i64,
  pub tj_castatus: i32,
}
crud!(TjCheckallnew {}, "tj_checkallnew");
rbatis::impl_select!(TjCheckallnew{query(testid:&str) ->Option => "`where tj_testid = #{testid} limit 1 `"});
rbatis::impl_select!(TjCheckallnew{query_many(testids:&[&str]) => "`where tj_testid in ${testids.sql()} `"});

impl TjCheckallnew {
  pub fn new(testid: &str) -> Self {
    TjCheckallnew {
      tj_testid: testid.to_string(),
      ..Default::default()
    }
  }

  #[py_sql(
    "`select * from tj_checkallnew where id > 0 `
                  if dtstart > 0:
                    ` and tj_checkdate > #{dtstart} `
                  if dtend > 0:
                    ` and tj_checkdate < #{dtend} `
                  if !testids.is_empty():
                    ` and tj_testid in ${testids.sql()} `         
                  if !typeids.is_empty():
                    ` and tj_typeid in ${typeids.sql()} `              
                  if castatus > 0:
                    ` and tj_castatus = #{castatus} `
                  "
  )]
  pub async fn query_many_by_dto(rb: &mut rbatis::RBatis, dtstart: i64, dtend: i64, testids: &[&str], typeids: &[i32], castatus: i32) -> Result<Vec<TjCheckallnew>, rbatis::Error> {
    impled!()
  }

  pub async fn save(rb: &mut rbatis::RBatis, info: &TjCheckallnew) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjCheckallnew::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjCheckallnew::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }

  // #[sql("update tj_checkallnew where testid = ? and depttype = ?")]
  pub async fn delete(rb: &mut rbatis::RBatis, info: &TjCheckallnew) -> Result<i64> {
    let mut info_new = TjCheckallnew { ..Default::default() };
    info_new.id = info.id;
    info_new.tj_testid = info.tj_testid.to_string();
    // info_new.tj_typeid = 0;
    // let mut rb = db.get_connection_clone();
    let ret = TjCheckallnew::update_by_column(rb, &info_new, "id").await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(info.id)
  }
}
