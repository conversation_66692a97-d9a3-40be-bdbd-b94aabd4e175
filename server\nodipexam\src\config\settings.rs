use std::collections::{BTreeMap, HashMap};

use config::{Config, ConfigError, File};
use serde::{Deserialize, Serialize};

use crate::config::srmyy::Zjsrmyy;

#[derive(Debug, <PERSON><PERSON>ult, <PERSON><PERSON>, Deserialize)]
pub struct Database {
  pub uri: String,
}

#[derive(Debu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Deserialize)]
pub struct Application {
  pub apphost: String,
  pub appport: i32,
  pub areaprovince: String,
  // pub uploader: String,
  pub pacsversion: String,
  pub uploaddir: String,
  pub reportdir: String,
  pub photodir: String,
  pub signdir: String,
  pub filedir: String,
  pub proxyserver: String,
  pub cdcserver: String,
  // pub ttl: u64,
  pub splitter: String,
  pub autorecvlis: i32,
  pub autorecvpacs: i32,
  pub lisimporttype: i32,
  pub lisego: i32,
  pub pacsego: i32,
  pub commander: String,
  pub dserver: String,
  pub reportserver: String,
}

#[derive(Debug, De<PERSON><PERSON>, <PERSON><PERSON>, Deserialize)]
pub struct Polar {
  pub orgid: i64,
  // pub password: String,
  pub server: String,
  // pub pubkey: String,
}

#[derive(Debug, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Cdcupload {
  pub creditcode: String,
  // #[serde(rename = "agentId")]
  pub agentid: String,
  // #[serde(rename = "appKey")]
  pub appkey: String,
  // #[serde(rename = "appSecret")]
  pub appsecret: String,
  // #[serde(rename = "isSubmit")]
  pub issubmit: i32,
  pub rpturl: String,
  pub filerul: String,
}

#[derive(Debug, Default, Clone, Deserialize)]
pub struct External {
  pub exttype: String,
  pub serverurl: String,
  // pub appsn: String,
}

#[derive(Debug, Default, Clone, Deserialize)]
pub struct Audiogram {
  pub dburi: String,
  // pub dbtype: i32,
}

#[derive(Debug, Default, Clone, Deserialize)]
pub struct Extlis {
  pub dbtype: i32,
  pub uri: String,
  pub username: String,
  pub password: String,
  // pub version: i32, //sfcy字段 1:表示用整型 2：表示字符型
}

#[derive(Debug, Default, Clone, Deserialize)]
pub struct Extpacs {
  pub dbtype: i32,
  pub uri: String,
  pub username: String,
  pub password: String,
  pub version: i32, //sfcy字段 1:表示用整型 2：表示字符型
}

#[derive(Debug, Default, Clone, Deserialize)]
pub struct Settings {
  pub database: Database,
  pub application: Application,
  pub polar: Polar,
  // pub cdcupload: Cdcupload,
  pub external: External,
  pub audiogram: Audiogram,
  pub extlis: HashMap<String, Extlis>,
  pub extpacs: HashMap<String, Extpacs>,
  pub hazardstats: Option<BTreeMap<i32, nodipservice::medexam::statsvc::HazardStats>>,
  pub zjsrmyy: Option<Zjsrmyy>,
}

impl Settings {
  pub fn init(cfgfile: &str) -> Result<Self, ConfigError> {
    let s = Config::builder()
      // Start off by merging in the "default" configuration file
      .add_source(File::with_name(cfgfile))
      .add_source(File::with_name("config/stats.toml").required(false))
      .add_source(File::with_name("config/*yy.toml").required(false))
      // // Add in the current environment file
      // // Default to 'development' env
      // // Note that this file is _optional_
      // .add_source(File::with_name(&format!("examples/hierarchical-env/config/{}", run_mode)).required(false))
      // // Add in a local configuration file
      // // This file shouldn't be checked in to git
      // .add_source(File::with_name("examples/hierarchical-env/config/local").required(false))
      // // Add in settings from the environment (with a prefix of APP)
      // // Eg.. `APP_DEBUG=1 ./target/app` would set the `debug` key
      // .add_source(Environment::with_prefix("app"))
      // // You may also programmatically change settings
      // .set_override("database.url", "postgres://")?
      .build()
      .expect("build config file error");

    s.try_deserialize()
  }
}
