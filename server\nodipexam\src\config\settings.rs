use std::collections::{<PERSON>reeMap, HashMap};

use config::{Config, ConfigError, File};
use glob::glob;
use serde::{Deserialize, Serialize};

use crate::config::zjsrmyy::Zjsrmyy;

#[derive(Debug, De<PERSON>ult, <PERSON>lone, Deserialize)]
pub struct Database {
  pub uri: String,
}

#[derive(Debug, De<PERSON>ult, <PERSON><PERSON>, Deserialize)]
pub struct Application {
  pub apphost: String,
  pub appport: i32,
  pub areaprovince: String,
  // pub uploader: String,
  pub pacsversion: String,
  pub uploaddir: String,
  pub reportdir: String,
  pub photodir: String,
  pub signdir: String,
  pub filedir: String,
  pub proxyserver: String,
  pub cdcserver: String,
  // pub ttl: u64,
  pub splitter: String,
  pub autorecvlis: i32,
  pub autorecvpacs: i32,
  pub lisimporttype: i32,
  pub lisego: i32,
  pub pacsego: i32,
  pub commander: String,
  pub dserver: String,
  pub reportserver: String,
}

#[derive(Debug, De<PERSON>ult, <PERSON>lone, Deserialize)]
pub struct Polar {
  pub orgid: i64,
  // pub password: String,
  pub server: String,
  // pub pubkey: String,
}

#[derive(Debug, Default, Clone, Serialize, Deserialize)]
pub struct Cdcupload {
  pub creditcode: String,
  // #[serde(rename = "agentId")]
  pub agentid: String,
  // #[serde(rename = "appKey")]
  pub appkey: String,
  // #[serde(rename = "appSecret")]
  pub appsecret: String,
  // #[serde(rename = "isSubmit")]
  pub issubmit: i32,
  pub rpturl: String,
  pub filerul: String,
}

#[derive(Debug, Default, Clone, Deserialize)]
pub struct External {
  pub exttype: String,
  pub serverurl: String,
  // pub appsn: String,
}

#[derive(Debug, Default, Clone, Deserialize)]
pub struct Audiogram {
  pub dburi: String,
  // pub dbtype: i32,
}

#[derive(Debug, Default, Clone, Deserialize)]
pub struct Extlis {
  pub dbtype: i32,
  pub uri: String,
  pub username: String,
  pub password: String,
  // pub version: i32, //sfcy字段 1:表示用整型 2：表示字符型
}

#[derive(Debug, Default, Clone, Deserialize)]
pub struct Extpacs {
  pub dbtype: i32,
  pub uri: String,
  pub username: String,
  pub password: String,
  pub version: i32, //sfcy字段 1:表示用整型 2：表示字符型
}

#[derive(Debug, Default, Clone, Deserialize)]
pub struct Settings {
  pub database: Database,
  pub application: Application,
  pub polar: Polar,
  // pub cdcupload: Cdcupload,
  pub external: External,
  pub audiogram: Audiogram,
  pub extlis: HashMap<String, Extlis>,
  pub extpacs: HashMap<String, Extpacs>,
  pub hazardstats: Option<BTreeMap<i32, nodipservice::medexam::statsvc::HazardStats>>,
  pub zjsrmyy: Option<Zjsrmyy>,
}

impl Settings {
  pub fn init(cfgfile: &str) -> Result<Self, ConfigError> {
    let mut builder = Config::builder()
      // Start off by merging in the "default" configuration file
      .add_source(File::with_name(cfgfile))
      .add_source(File::with_name("config/stats.toml").required(false));

    // 使用 glob 模式匹配 config/*yy.toml 文件
    // 尝试多个可能的路径（支持不同的工作目录）
    let patterns = ["config/*yy.toml"];

    for pattern in &patterns {
      if let Ok(entries) = glob(pattern) {
        for entry in entries {
          match entry {
            Ok(path) => {
              if let Some(path_str) = path.to_str() {
                // 移除 .toml 扩展名，因为 config crate 会自动添加
                let path_without_ext = path_str.strip_suffix(".toml").unwrap_or(path_str);
                builder = builder.add_source(File::with_name(path_without_ext).required(false));
              }
            }
            Err(_) => {
              // 忽略单个文件的错误，继续处理其他文件
            }
          }
        }
      }
    }

    let s = builder.build().expect("build config file error");
    s.try_deserialize()
  }
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_zjsrmyy_direct_loading() {
    // 直接测试加载 zjsrmyy.toml 文件
    let config = Config::builder()
      .add_source(File::with_name("../../config/zjsrmyy").required(true))
      .build()
      .expect("Failed to build config");

    // 现在配置文件有 zjsrmyy 顶级节点，所以需要提取该节点
    #[derive(serde::Deserialize)]
    struct ZjsrmyyConfig {
      zjsrmyy: Zjsrmyy,
    }

    let config_wrapper: ZjsrmyyConfig = config.try_deserialize().expect("Failed to deserialize zjsrmyy config");
    let zjsrmyy = config_wrapper.zjsrmyy;

    assert!(!zjsrmyy.deptcode.is_empty());
    assert!(!zjsrmyy.deptname.is_empty());
    assert!(!zjsrmyy.executorinfo.is_empty());
  }

  #[test]
  fn test_config_loading() {
    // 从项目根目录运行测试
    let settings = Settings::init("../../config/nodipexam.toml").expect("Failed to load config");

    // 测试 zjsrmyy 是否正确加载
    if let Some(zjsrmyy) = &settings.zjsrmyy {
      assert!(!zjsrmyy.deptcode.is_empty(), "deptcode should not be empty");
      assert!(!zjsrmyy.deptname.is_empty(), "deptname should not be empty");
      assert!(!zjsrmyy.executorinfo.is_empty(), "executorinfo should not be empty");

      // 测试具体的值
      assert_eq!(zjsrmyy.deptcode, "ddd");
      assert_eq!(zjsrmyy.deptname, "ddd");

      // 测试 executorinfo 中的数据
      if let Some(exam_info) = zjsrmyy.executorinfo.get("exam") {
        assert_eq!(exam_info.doctorid, "exam");
        assert_eq!(exam_info.doctorname, "exam");
      }

      if let Some(lis_info) = zjsrmyy.executorinfo.get("lis") {
        assert_eq!(lis_info.doctorid, "lis");
        assert_eq!(lis_info.doctorname, "lisname");
      }
    } else {
      panic!("zjsrmyy should not be None");
    }
  }
}
