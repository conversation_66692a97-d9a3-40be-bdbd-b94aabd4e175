use anyhow::{anyhow, Result};
use chrono::{DateTime, Local, TimeZone, Utc};
// use log::*;
use std::time::{Duration, UNIX_EPOCH};
use tracing::*;

pub fn get_year_and_month(cdate: i64, txt: &str) -> Result<(i32, i32, String)> {
  info!("计算时间:{}", &txt);
  if txt.is_empty() {
    return Err(anyhow!("没有时间信息，请输入"));
  }
  let mut time_result: (i32, i32, String) = (0, 0, "".to_string());

  let mut year_str = "";
  let mut month_str = "";

  let work_ages: Vec<&str> = txt.split('年').collect();
  info!("split result:{:?}", &work_ages);

  if work_ages.len() >= 2 {
    //有年
    year_str = work_ages.get(0).unwrap().to_owned();
    let months_info = work_ages.get(1).unwrap().to_owned();
    if let Some(result) = months_info.find("个月") {
      if let Some(inner) = months_info.get(..result) {
        month_str = inner;
      }
    } else if let Some(result) = months_info.find("月") {
      if let Some(inner) = months_info.get(..result) {
        month_str = inner;
      }
    }
  } else if work_ages.len() == 1 {
    //没有找到年
    let months_info = work_ages.get(0).unwrap().to_owned();

    if let Some(result) = months_info.find("年") {
      if let Some(inner) = months_info.get(..result) {
        year_str = inner;
      }
    } else if let Some(result) = months_info.find("个月") {
      if let Some(inner) = months_info.get(..result) {
        month_str = inner;
      }
    } else if let Some(result) = months_info.find("月") {
      if let Some(inner) = months_info.get(..result) {
        month_str = inner;
      }
    } else {
      year_str = months_info;
    }
  } else {
    return Err(anyhow!("incorrect date string"));
  }

  // info!("year str:{}, month str:{}", year_str, month_str);

  // let naive_now = NaiveDateTime::from_timestamp_opt(cdate, 0).unwrap_or(chrono::offset::Local::now().naive_local());
  let naive_now = DateTime::from_timestamp(cdate, 0).unwrap_or(Utc::now()).naive_utc();
  let local_now: DateTime<Local> = Local.from_utc_datetime(&naive_now);

  let mut days: i64 = 0;
  if !year_str.is_empty() {
    let ret = year_str.parse::<f64>();
    if ret.as_ref().is_err() {
      return Err(anyhow!("incorrect date string"));
    }
    let year_info = ret.unwrap();
    days = days + (year_info * 365 as f64).round() as i64;
  }

  if !month_str.is_empty() {
    let ret = month_str.parse::<f64>();
    if ret.as_ref().is_err() {
      return Err(anyhow!("incorrect date string"));
    }
    let month_info = ret.unwrap();
    days = days + (month_info * 30 as f64).round() as i64;
  }

  // info!("总天数:{}", days);

  let new_date = local_now - chrono::Duration::days(days);
  let mut years = days / 365;
  let mut monthes = ((days % 365) as f64 / 30 as f64).round() as i64;
  if monthes >= 12 {
    monthes = 0;
    years += 1;
  }
  time_result.0 = years as i32;
  time_result.1 = monthes as i32;
  time_result.2 = new_date.format("%Y-%m-%d").to_string();

  info!("time result:{:?}", &time_result);
  Ok(time_result)
}

pub fn timestamp_to_local_date(t: i64) -> String {
  if t <= 0 {
    return "".to_string();
  }
  // let timestamp: i64 = 1578585600;
  let ts_type = utility::check_timestamp(t);
  if ts_type == "seconds" {
    let d = UNIX_EPOCH + Duration::from_secs(t as u64);
    // Create DateTime from SystemTime
    let datetime = DateTime::<Local>::from(d);
    datetime.format("%Y-%m-%d").to_string()
  } else {
    let d = UNIX_EPOCH + Duration::from_millis(t as u64);
    // Create DateTime from SystemTime
    let datetime = DateTime::<Local>::from(d);
    datetime.format("%Y-%m-%d").to_string()
  }
}

pub fn timestamp_to_local_date_without_seperator(t: i64) -> String {
  // info!("timestamp_to_local_date_without_seperator, value:{t}");
  if t <= 0 {
    return "".to_string();
  }
  // let timestamp: i64 = 1578585600;
  let ts_type = utility::check_timestamp(t);
  // info!("timestamp_to_local_date_without_seperator, ts_type:{ts_type}");
  if ts_type == "seconds" {
    let d = UNIX_EPOCH + Duration::from_secs(t as u64);
    // Create DateTime from SystemTime
    let datetime = DateTime::<Local>::from(d);
    // info!("{}", datetime.format("%Y%m%d %H:%M:%S").to_string());
    datetime.format("%Y%m%d").to_string()
  } else {
    let d = UNIX_EPOCH + Duration::from_millis(t as u64);
    // Create DateTime from SystemTime
    let datetime = DateTime::<Local>::from(d);
    datetime.format("%Y%m%d").to_string()
  }
}

// pub fn convert_date_to_without_seperator()
