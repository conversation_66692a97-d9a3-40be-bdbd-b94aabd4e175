use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_corpoccureport")]
#[derive(Clone, Debug, Default, Serialize, Deserialize)]
pub struct TjCorpoccureport {
    pub id: i64,
    pub tj_corpid: String,
    pub tj_corpname: String,
    pub tj_wtcorpname: String,
    pub tj_starttime: i64,
    pub tj_endtime: i64,
    pub tj_testyear: i32,
    pub tj_testtype: i32,
    pub tj_typename: String,
    pub tj_poisions: String,
    pub tj_testdate: String,
    pub tj_testaddress: String,
    pub tj_peoplenum: i32,
    pub tj_apeoplenum: i32,
    pub tj_testitems: String,
    pub tj_evalaw: String,
    pub tj_testlaw: String,
    pub tj_result: String,
    pub tj_createdate: i64,
    pub tj_moddate: i64,
    pub tj_creator: String,
    pub tj_modifier: String,
    pub tj_reportnum: String,
    pub tj_status: i32,
    pub tj_peid: i32,
    pub tj_pages: i32,
    pub tj_isrecheck: i32,
    pub tj_reportnumint: String,
    pub tj_pyjm: String,
    pub tj_reporttype: i32,
    pub tj_syncflag: i32,
    pub tj_memo: String,
}
