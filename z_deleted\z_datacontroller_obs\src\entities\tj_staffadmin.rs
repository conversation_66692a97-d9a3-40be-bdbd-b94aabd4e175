use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_staffadmin")]
#[derive(<PERSON><PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct TjStaffadmin {
    pub id: i64,
    pub tj_staffno: String,
    pub tj_staffname: String,
    pub tj_sex: i32,
    pub tj_deptid: i32,
    pub tj_groupid: i32,
    pub tj_password: String,
    pub tj_role: i32,
    pub tj_checkallflag: i32,
    pub tj_title: String,
    pub tj_status: i32,
    pub tj_operator: String,
    pub tj_moddate: i64,
    pub tj_memo: String,
    pub tj_isadmin: u8,
    pub login_session: String,
    pub tj_esign: String,
}
