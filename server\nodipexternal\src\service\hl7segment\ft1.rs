#[allow(unused)]
use crate::rusthl7::{Field, Hl7ParseError, Message, Separators};
use anyhow::Result;
use std::fmt::Display;
//ch6
// 1 4 SI O 00355 Set ID - FT1
// 2 12 ST O 00356 Transaction ID
// 3 10 ST O 00357 Transaction Batch ID
// 4 26 TS R 00358 Transaction Date
// 5 26 TS O 00359 Transaction Posting Date
// 6 8 IS R 0017 00360 Transaction Type
// 7 250 CE R 0132 00361 Transaction Code
// 8 40 ST B 00362 Transaction Description
// 9 40 ST B 00363 Transaction Description - Alt
// 10 6 NM O 00364 Transaction Quantity
// 11 12 CP O 00365 Transaction Amount - Extended
// 12 12 CP O 00366 Transaction Amount - Unit
// 13 250 CE O 0049 00367 Department Code
// SEQ LEN DT OPT RP/# TBL# ITEM# ELEMENT NAME
// 14 250 CE O 0072 00368 Insurance Plan ID
// 15 12 CP O 00369 Insurance Amount
// 16 80 PL O 00133 Assigned Patient Location
// 17 1 IS O 0024 00370 Fee Schedule
// 18 2 IS O 0018 00148 Patient Type
// 19 250 CE O Y 0051 00371 Diagnosis Code - FT1
// 20 250 XCN O Y 0084 00372 Performed By Code
// 21 250 XCN O Y 00373 Ordered By Code
// 22 12 CP O 00374 Unit Cost
// 23 22 EI O 00217 Filler Order Number
// 24 250 XCN O Y 00765 Entered By Code
// 25 250 CE O 0088 00393 Procedure Code
// 26 250 CE O Y 0340 01316 Procedure Code Modifier
#[derive(Debug, PartialEq)]
pub struct Ft1Segment<'a> {
  pub source: &'a str,
  //this initial layout largely stolen from the _other_ hl7 crate: https://github.com/njaremko/hl7
  pub msg_encoding_characters: Separators,
  pub ft1_1: Option<Field<'a>>,
  pub ft1_2: Option<Field<'a>>,
  pub ft1_3: Option<Field<'a>>,
  pub ft1_4: Field<'a>,
  pub ft1_5: Option<Field<'a>>,
  pub ft1_6: Field<'a>,
  pub ft1_7: Option<Field<'a>>,
  pub ft1_8: Option<Field<'a>>,
  pub ft1_9: Option<Field<'a>>,
  pub ft1_10: Option<Field<'a>>,
  pub ft1_11: Option<Field<'a>>,
  pub ft1_12: Option<Field<'a>>,
  pub ft1_13: Option<Field<'a>>,
  pub ft1_14: Option<Field<'a>>,
  pub ft1_15: Option<Field<'a>>,
  pub ft1_16: Option<Field<'a>>,
  pub ft1_17: Option<Field<'a>>,
  pub ft1_18: Option<Field<'a>>,
  pub ft1_19: Option<Field<'a>>,
  pub ft1_20: Option<Field<'a>>,
  pub ft1_21: Option<Field<'a>>,
  pub ft1_22: Option<Field<'a>>,
  pub ft1_23: Option<Field<'a>>,
  pub ft1_24: Option<Field<'a>>,
  pub ft1_25: Option<Field<'a>>,
  pub ft1_26: Option<Field<'a>>,
  // pub orc_27_fillers_expected_availability_date_time: Option<Field<'a>>,
  // pub orc_28_confidentiality_code: Option<Field<'a>>,
  // pub orc_29_order_type: Option<Field<'a>>,
  // pub orc_30_enterer_authorization_mode: Option<Field<'a>>,
  // pub orc_31_parent_universal_service_identifier: Option<Field<'a>>,
  // pub orc_32_advanced_beneficiary_notice_date: Option<Field<'a>>,
  // pub orc_33_alternate_placer_order_number: Option<Field<'a>>,
  // pub orc_34_order_workflow_profile: Option<Field<'a>>,
}

impl<'a> Ft1Segment<'a> {
  pub fn parse<S: Into<&'a str>>(input: S, delims: &Separators) -> Result<Ft1Segment<'a>, Hl7ParseError> {
    let input = input.into();

    let mut fields = input.split(delims.field);

    assert!(fields.next().unwrap() == "FT1");

    let seg = Ft1Segment {
      source: input,
      msg_encoding_characters: delims.to_owned(),
      ft1_1: Field::parse_optional(fields.next(), delims)?,
      ft1_2: Field::parse_optional(fields.next(), delims)?,
      ft1_3: Field::parse_optional(fields.next(), delims)?,
      ft1_4: Field::parse_mandatory(fields.next(), delims)?,
      ft1_5: Field::parse_optional(fields.next(), delims)?,
      ft1_6: Field::parse_mandatory(fields.next(), delims)?,
      ft1_7: Field::parse_optional(fields.next(), delims)?,
      ft1_8: Field::parse_optional(fields.next(), delims)?,
      ft1_9: Field::parse_optional(fields.next(), delims)?,
      ft1_10: Field::parse_optional(fields.next(), delims)?,
      ft1_11: Field::parse_optional(fields.next(), delims)?,
      ft1_12: Field::parse_optional(fields.next(), delims)?,
      ft1_13: Field::parse_optional(fields.next(), delims)?,
      ft1_14: Field::parse_optional(fields.next(), delims)?,
      ft1_15: Field::parse_optional(fields.next(), delims)?,
      ft1_16: Field::parse_optional(fields.next(), delims)?,
      ft1_17: Field::parse_optional(fields.next(), delims)?,
      ft1_18: Field::parse_optional(fields.next(), delims)?,
      ft1_19: Field::parse_optional(fields.next(), delims)?,
      ft1_20: Field::parse_optional(fields.next(), delims)?,
      ft1_21: Field::parse_optional(fields.next(), delims)?,
      ft1_22: Field::parse_optional(fields.next(), delims)?,
      ft1_23: Field::parse_optional(fields.next(), delims)?,
      ft1_24: Field::parse_optional(fields.next(), delims)?,
      ft1_25: Field::parse_optional(fields.next(), delims)?,
      ft1_26: Field::parse_optional(fields.next(), delims)?,
    };

    Ok(seg)
  }
}
/// Common formatter trait implementation for the strongly-typed segment
impl<'a> Display for Ft1Segment<'a> {
  /// Required for to_string() and other formatter consumers
  fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
    write!(f, "{}", self.source)
  }
}
/// Common clone trait implementation for the strongly-typed segment
impl<'a> Clone for Ft1Segment<'a> {
  /// Creates a new Message object using _the same source_ slice as the original.
  fn clone(&self) -> Self {
    let delims = self.msg_encoding_characters;
    Ft1Segment::parse(self.source, &delims).unwrap()
  }
}
/// Extracts header element for external use
pub fn _ft1<'a>(msg: &Message<'a>) -> Result<Ft1Segment<'a>, Hl7ParseError> {
  let seg = msg.segments_by_identifier("FT1").unwrap()[0];
  let segment = Ft1Segment::parse(seg.source, &msg.get_separators()).expect("Failed to parse FT1 segment");
  Ok(segment)
}
