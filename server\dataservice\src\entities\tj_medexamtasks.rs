//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_medexamtasks")]
pub struct Model {
  #[sea_orm(column_name = "ID", primary_key)]
  pub id: i32,
  pub tj_mdid: i32,
  pub tj_staffid: Option<i32>,
  pub tj_tasktype: i32,
  pub tj_deptid: Option<i32>,
  pub tj_taskdate: Option<DateTime>,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
