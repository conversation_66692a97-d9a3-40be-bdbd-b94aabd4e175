//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_autodiagcondition")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_disid: i32,
    pub tj_hazardfactor: i32,
    pub tj_metype: i32,
    pub tj_itemid: String,
    pub tj_operator: String,
    pub tj_refvalue: String,
    pub tj_conditionsymbol: String,
    pub tj_order: i32,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::tj_diseases::Entity",
        from = "Column::TjDisid",
        to = "super::tj_diseases::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    TjDiseases,
}

impl Related<super::tj_diseases::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjDiseases.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
