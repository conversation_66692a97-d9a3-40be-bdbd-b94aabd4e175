use anyhow::{anyhow, Result};
use rbatis::rbdc::datetime::DateTime;
use rbatis::sql::IntoSql;
use rbatis::utils::time_util;
use rbatis::{crud, impl_delete, impl_select, impl_select_page, impl_update, make_table, RBatis};
use serde_json::json;
use utility::timeutil;

use crate::config::settings::Extpacs;

#[derive(Clone, Default, Debug, serde::Serialize, serde::Deserialize)]
pub struct HisJCRW {
  pub lsh: u64,         //流水号
  pub jch: String,      //检查号，如超声号、X线号
  pub djxh: i32,        //登记序号：1-住院、2-门诊、3-急诊、4-体检
  pub bah: String,      //病案号
  pub bqmc: String,     //病区名称
  pub bqdm: String,     //病区代码
  pub brch: String,     //病人床号
  pub jzkh: String,     //就诊卡号
  pub brxm: String,     //病人姓名
  pub brxb: i32,        //病人性别
  pub brnl: i32,        //病人年龄
  pub csrq: String,     //病人出生日期 datetime
  pub jtzz: String,     //家庭住址
  pub lxdh: String,     //联系电话
  pub ylxh: i32,        //医疗序号，即检查项目代码
  pub ylmc: String,     //医疗名称，即检查项目名称
  pub sjysgh: String,   //申检医生工号，即开单医生工号
  pub sjysxm: String,   //申检医生姓名
  pub sjksdm: String,   //申检科室代码
  pub sjksmc: String,   //申检科室名称
  pub sjsj: String,     //申检时间 datetime
  pub lczd: String,     //临床诊断
  pub djysgh: String,   //登记医生工号
  pub djysxm: String,   //登记医生姓名
  pub djrq: String,     //登记日期 datetime
  pub jclxdm: String,   //检查类型代码，即HisJCLX.jclxdm字段
  pub jcbwdm: String,   //检查部位代码，即HisJCBW.jcbwdm字段
  pub jcfsdm: String,   //检查方式代码，即HisJCFS.jcfsdm字段
  pub jcsbdm: String,   //检查设备代码，即HisJCSB.jcsbdm字段
  pub jcfy: f32,        //检查费用 numeric
  pub yyh: String,      //预约号
  pub yyrq: String,     //预约日期  datetime
  pub jcysgh: String,   //检查医生工号
  pub jcysxm: String,   //检查医生姓名
  pub jcrq: String,     //检查日期 datetime
  pub bgysgh: String,   //报告医生工号
  pub bgysxm: String,   //报告医生姓名
  pub bgrq: String,     //报告填写日期 datetime
  pub shysgh: String,   //审核医生工号
  pub shysxm: String,   //审核医生姓名
  pub shrq: String,     //审核日期 datetime
  pub dyysgh: String,   //打印医生工号
  pub dyysxm: String,   //打印医生姓名
  pub dyrq: String,     //打印日期 datetime
  pub jcjf: String,     //检查机房
  pub jcsb: String,     //检查设备
  pub see: String,      //检查所见
  pub jcjg: String,     //检查结果
  pub status: String,   //检查任务状态：预约、登记、检查、报告、审核
  pub studyuid: String, //检查全球唯一号：Study Instance UID
  pub sqdh: String,     //电子申请单号
  pub brbh: String,     //病人编号：根据用户配置的策略，由接口自动生成。
  pub jcdh: String,     //检查单号：根据用户配置的策略，由接口自动生成。
  pub sfzh: String,     //身份证号
  pub yqmc: String,     //院区名称
  pub zymb: String,     //转院目标
  pub lcxx: String,     //临床信息LCXX格式=提交日期^^临床信心^^病史摘要^^体检信息^^检查结果^^特殊说明^^|开单项目
  pub brtz: i32,        //病人体重
  pub jpfy: f32,        //胶片费用
  pub nlxx: String, //NLXX字段，支持由HIS系统输入年龄的详细信息。年龄信息数据规则为三位数字（不足三位补零）加年龄单位，年龄单位支持Y（年）、M（月）、W（周）、D（日）、H（小时）。举例说明，HIS系统填入020Y，表示20岁；填入030M，表示30个月；填入007W，表示7周；填入120D，表示120天；填入300H，表示300小时。注：如该字段为空，检查年龄计算采用原有计算方式。
  pub jcpd: String, //检查排队号
}

// impl_select!(HisJCRW{select_all_by_id(table_name:&str,id:&str) => "`where id = #{id}`"}); //custom table name
crud!(HisJCRW {}, "HisJCRW");
impl_select!(HisJCRW{select_all_by_id(testid:&str,lcxx:&str) => "`where bah = #{testid} and lcxx = #{lcxx}`"});
impl_update!(HisJCRW{update_by_name(name:&str) => "`where id = '2'`"});
impl_delete!(HisJCRW {delete_by_name(name:&str) => "`where name= '2'`"});
impl_select_page!(HisJCRW{select_page_by_name(name:&str) =>"
     if name != null && name != '':
       `where name != #{name}`
     if name == '':
       `where name != ''`"});

#[derive(Clone, Default, Debug, serde::Serialize, serde::Deserialize)]
pub struct HisRWZL {
  pub rwzllsh: u64,     //任务指令流水号
  pub jcrwlsh: u64,     //检查任务流水号：指定操作HisJCRW的记录，对应HisJCRW.lsh字段
  pub rwzllx: i32,      //任务类型：1–登录、5–修改、 10–取消、 15–添加部位、100–检查、105–报告、110–审核、115–修改、1000-转院
  pub rwzlsj: String,   //指令生成时间
  pub rwzlczhm: String, //任务指令操作目标：仅在处理转院指令时需填写，填入转院病人的病案号
  pub rwzlczmb: String, //任务指令操作目标：仅在处理转院指令时需填写，填入转院病人转至的院区
}
crud!(HisRWZL {}, "HisRWZL");
// impl_select!(HisRWZL{select_by_jcrwlsh(jcrwlsh:u64) => "`where jcrwlsh = #{jcrwlsh}`"});

#[derive(Clone, Default, Debug, serde::Serialize, serde::Deserialize)]
pub struct HisJCLX {
  pub jclxdm: String, //检查类型代码
  pub jclxmc: String, //检查类型名称
  pub jchqz: String,  //生成检查号时使用的前缀
  pub srm1: String,   //简明输入1
  pub srm2: String,   //简明输入2
  pub sybz: String,   //使用标志（0 – 正常，1 – 取消）
}
crud!(HisJCLX {}, "HisJCLX");

#[derive(Clone, Default, Debug, serde::Serialize, serde::Deserialize)]
pub struct HisJCSB {
  pub jcsbdm: String,   //检查设备代码
  pub jcsblxdm: String, //检查设备类型代码，对应HisJCLX表中的jclxdm字段
  pub jcsbmcz: String,  //检查设备名称
  pub jcsbyw: String,   //检查设备英文名称
  pub jcsbjf: String,   //检查设备所在机房
  pub jcsbwz: String,   //检查设备所在位置
}
crud!(HisJCSB {}, "HisJCSB");

#[derive(Clone, Default, Debug, serde::Serialize, serde::Deserialize)]
pub struct HisJCBW {
  pub jcbwdm: String, //检查部位代码
  pub jcbwmc: String, //检查部位名称
  pub jcbwyw: String, //检查部位英文名称
  pub srm1: String,   //简明输入1
  pub srm2: String,   //简明输入2
  pub yyfw: String,   //应用范围
}
crud!(HisJCBW {}, "HisJCBW");

#[derive(Clone, Default, Debug, serde::Serialize, serde::Deserialize)]
pub struct HisJCXMDM {
  pub ylxh: i64,      //医疗序号
  pub ylmc: String,   //检查项目名称
  pub ksdm: String,   //科室代码
  pub jclxdm: String, //检查类型代码，对应HisJCLX表中的jclxdm字段
  pub jcbwdm: String, //检查部位代码，对应HisJCBW表中的jcbwdm字段
  pub jcfsdm: String, //检查方式代码
}
crud!(HisJCXMDM {}, "HisJCXMDM");

#[derive(Debug)]
pub struct LaidaPacs {
  conn: RBatis,
}

impl LaidaPacs {
  pub async fn new(default_pacs: &Extpacs) -> Result<Self> {
    // rb.init(rbdc_mysql::driver::MysqlDriver {}, "mysql://root:123456@localhost:3306/test").unwrap();
    // rb.init(rbdc_pg::driver::PgDriver {}, "postgres://postgres:123456@localhost:5432/postgres").unwrap();
    //
    let conn = RBatis::new();
    match default_pacs.dbtype {
      1 => {
        //oracle
        info!("connection string:{}", &default_pacs.uri);
        let ret = conn.link(rbdc_oracle::driver::OracleDriver {}, &default_pacs.uri).await; //.expect("conn error"); //.unwrap_or_default();
        if ret.as_ref().is_err() {
          error!("init connection:{}, error:{}", &default_pacs.uri, ret.as_ref().unwrap_err().to_string());
        }
      }
      2 => {
        //mssql
        //rb.init(rbdc_mssql::driver::MssqlDriver {}, "mssql://SA:TestPass!123456@localhost:1433/test").unwrap();
        //************************************************************************************************;
        info!("connection string:{}", &default_pacs.uri);
        let ret = conn.link(rbdc_mssql::driver::MssqlDriver {}, &default_pacs.uri).await; //.unwrap_or_default();
        if ret.as_ref().is_err() {
          error!("init connection:{}, error:{}", &default_pacs.uri, ret.as_ref().unwrap_err().to_string());
        }
      }
      3 => {
        //mysql
        info!("connection string:{}", &default_pacs.uri);
        let ret = conn.link(rbdc_mysql::driver::MysqlDriver {}, &default_pacs.uri).await; //.unwrap_or_default();
        if ret.as_ref().is_err() {
          error!("init connection:{}, error:{}", &default_pacs.uri, ret.as_ref().unwrap_err().to_string());
        }
      }
      _ => {
        error!("unsupported database type:{} ", &default_pacs.dbtype);
        return Err(anyhow!("unsupported database type:{} ", &default_pacs.dbtype));
      }
    }
    info!("database connection initiated:{:?}", &conn);
    Ok(LaidaPacs { conn })
  }

  pub async fn insert(&mut self, jcrw: &HisJCRW) -> Result<u64> {
    // if jcrws.len() <= 0 {
    //   return Ok(());
    // }
    // for jcrw in jcrws {
    let ret = HisJCRW::insert(&mut self.conn, &jcrw).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let exec_id = ret.unwrap().last_insert_id.as_u64().unwrap_or_default();
    let rwzl = HisRWZL {
      rwzllsh: 0,
      jcrwlsh: exec_id,
      rwzllx: 1,
      rwzlsj: timeutil::format_timestamp(timeutil::current_timestamp()),
      rwzlczhm: "".to_string(),
      rwzlczmb: "".to_string(),
    };
    let ret = HisRWZL::insert(&mut self.conn, &rwzl).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    // }
    Ok(exec_id)
  }
  pub async fn delete(&mut self, testid: &str, lcxx: &str) -> Result<i64> {
    let ret = HisJCRW::select_all_by_id(&mut self.conn, testid, lcxx).await;
    if ret.as_ref().is_err() {
      error!("init database error:{:?}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("init database error:{:?}", ret.as_ref().unwrap_err().to_string()));
    }
    let result_datas = ret.unwrap();
    for val in result_datas.into_iter() {
      let rwzl = HisRWZL {
        rwzllsh: 0,
        jcrwlsh: val.lsh,
        rwzllx: 10,
        rwzlsj: timeutil::format_timestamp(timeutil::current_timestamp()),
        rwzlczhm: "".to_string(),
        rwzlczmb: "".to_string(),
      };
      let ret = HisRWZL::insert(&mut self.conn, &rwzl).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    Ok(0)
  }
}
