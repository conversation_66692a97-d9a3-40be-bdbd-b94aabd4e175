//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_corpoccureport_fc")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_old_testid: String,
  pub tj_report_id: i64,
  pub tj_old_rptnum: String,
  pub tj_new_testid: String,
  pub cdatetime: i64,
  pub tj_new_rptnum: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
