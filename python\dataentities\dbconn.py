
# import datetime
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import scoped_session
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base

# import tomllib as tomli
import tomli

# from pathlib import Path

# 基础类
Base = declarative_base()


def create_mysql_engine():
    # dburi = ""
    print("start to create_mysql_engine")

    # try:
    #     # Try to use the robust config reader
    #     from config_reader import load_database_config
    #     dburi = load_database_config()
    #     if dburi is None:
    #         raise ValueError("Database URI not found in configuration")
    # except ImportError:
    #     # Fallback to direct tomli loading
    #     # import tomli
    #     # Try different possible paths for the config file
    #     config_paths = [
    #         "./config/nodipexam.toml",
    #         # "../config/nodipexam.toml",
    #         # "../../config/nodipexam.toml"
    #     ]
    #     config = None
    #     for config_path in config_paths:
    #         try:
    #             with open(config_path, "rb") as f:
    #                 config = tomli.load(f)
    #                 break
    #         except FileNotFoundError:
    #             continue
    #     if config is None:
    #         raise FileNotFoundError("Could not find nodipexam.toml config file in any of the expected locations")
    #     dburi = config["database"]["uri"]

    with open("./config/nodipexam.toml", "rb") as f:
        config = tomli.load(f)
    dburi = config["database"]["uri"]
    dburi = dburi.replace("mysql:", "mysql+pymysql:")
    print("database uri is: ", dburi)
    if dburi == "":
        return None
    # 创建引擎
    engine = create_engine(
        dburi,
        # "mysql+pymysql://tom@127.0.0.1:3306/db1?charset=utf8mb4", # 无密码时
        # 超过链接池大小外最多创建的链接
        max_overflow=0,
        # 链接池大小
        pool_size=5,
        # 链接池中没有可用链接则最多等待的秒数，超过该秒数后报错
        pool_timeout=300,
        # 多久之后对链接池中的链接进行一次回收
        # pool_recycle=-1,
        # 查看原生语句（未格式化）
        echo=False,
        connect_args={"read_timeout": 300}
    )

    return engine

print("making mysql session in dbconn.....")
# 绑定引擎
Session = sessionmaker(bind=create_mysql_engine())
# 创建数据库链接池，直接使用session即可为当前线程拿出一个链接对象conn
# 内部会采用threading.local进行隔离
session = scoped_session(Session)
