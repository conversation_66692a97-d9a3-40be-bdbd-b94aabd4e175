use serde::{Deserialize, Serialize};

//调用服务更新检查申请状态
#[derive(Debug, Serialize, Deserialize)]
pub struct ExamStatus {
  #[serde(rename = "patientID")]
  patient_id: String, // 姓名，长度20，不可为空
  #[serde(rename = "visitNO")]
  visit_no: String, // 就诊号，长度20，不可为空
  #[serde(rename = "patientClass")]
  patient_class: String, // 病人类型，长度2，不可为空
  #[serde(rename = "orderID")]
  order_id: String, // 医嘱号，长度20，不可为空
  #[serde(rename = "examNO")]
  exam_no: Option<String>, // 检查单号，长度20，可为空
  #[serde(rename = "examStatus")]
  exam_status: String, // 检查状态，长度2，不可为空
  #[serde(rename = "operatorID")]
  operator_id: String, // 操作者ID，长度20，不可为空
  #[serde(rename = "operatorName")]
  operator_name: String, // 操作者姓名，长度20，不可为空
  #[serde(rename = "operationTime")]
  operation_time: String, // 操作时间，长度20，不可为空
}

//2.4.4.2.	调用服务写检查报告
#[derive(Debug, Serialize, Deserialize)]
pub struct PacsResultRecord {
  #[serde(rename = "patientID")]
  patient_id: String, // 姓名，长度20，不可为空
  #[serde(rename = "visitNO")]
  visit_no: String, // 就诊号，长度20，不可为空
  #[serde(rename = "patientClass")]
  patient_class: String, // 病人类型，长度2，不可为空
  #[serde(rename = "orderID")]
  order_id: String, // 医嘱号，长度20，不可为空
  #[serde(rename = "examNO")]
  exam_no: String, // 检查单号，长度20，不可为空（此处传空）
  #[serde(rename = "examStatus")]
  exam_status: String, // 检查申请状态，长度2，不可为空
  #[serde(rename = "reportDoctorName")]
  report_doctor_name: String, // 报告医生姓名，长度20，不可为空
  #[serde(rename = "reportTime")]
  report_time: String, // 报告时间，长度20，不可为空
  #[serde(rename = "verifierDoctorName")]
  verifier_doctor_name: String, // 审核报告医生姓名，长度20，不可为空
  #[serde(rename = "verifierTime")]
  verifier_time: String, // 审核报告时间，长度20，不可为空
  #[serde(rename = "operatorID")]
  operator_id: String, // 操作者ID，长度20，不可为空
  #[serde(rename = "operatorName")]
  operator_name: String, // 操作者姓名，长度20，不可为空
  #[serde(rename = "operationTime")]
  operation_time: String, // 操作时间，长度20，不可为空
  #[serde(rename = "pacsLocallId")]
  pacs_local_id: Option<String>, // 检查系统检查单号，可为空
  #[serde(rename = "reports")]
  reports: Vec<Report>, // 报告列表
  #[serde(rename = "dicoms")]
  dicoms: Vec<Dicom>, // DICOM列表
  #[serde(rename = "images")]
  images: Vec<Image>, // 图像列表
}

// 报告子结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct Report {
  #[serde(rename = "examPara")]
  exam_para: String, // 检查参数，长度1000，不可为空
  #[serde(rename = "description")]
  description: String, // 检查所见，长度1000，不可为空
  #[serde(rename = "impression")]
  impression: String, // 印象，长度2000，不可为空
  #[serde(rename = "recommendation")]
  recommendation: String, // 建议，长度100，不可为空
  #[serde(rename = "isAbnormal")]
  is_abnormal: String, // 是否阳性，长度1，不可为空
  #[serde(rename = "device")]
  device: Option<String>, // 使用仪器，长度20，可为空
  #[serde(rename = "useImage")]
  use_image: Option<String>, // 报告中图象编号，长度20，可为空
  #[serde(rename = "memo")]
  memo: Option<String>, // 备注，长度40，可为空
  #[serde(rename = "reportID")]
  report_id: String, // 医技科室生成的报告ID，长度36，不可为空
  #[serde(rename = "criticalFlag")]
  critical_flag: String, // 危急值标志，长度1，不可为空
  #[serde(rename = "infectionFlag")]
  infection_flag: String, // 传染病标识，长度1，不可为空
}

// DICOM子结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct Dicom {
  #[serde(rename = "acceNo")]
  acce_no: Option<String>, // 影像号，长度20，可为空
  #[serde(rename = "examNo")]
  exam_no: Option<String>, // 申请单号，长度20，可为空
  #[serde(rename = "reportId")]
  report_id: Option<String>, // 报告号，长度20，可为空
}

// 图像子结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct Image {
  #[serde(rename = "reportId")]
  report_id: Option<String>, // 报告号，长度20，可为空
  #[serde(rename = "orderId")]
  order_id: Option<String>, // 医嘱号，长度20，可为空
  #[serde(rename = "url")]
  url: Option<String>, // 图像路径，长度30，可为空
}

//2.4.4.3.	调用服务更新检验申请状态

#[derive(Debug, Serialize, Deserialize)]
pub struct LabStatusRecord {
  #[serde(rename = "patientID")]
  patient_id: String, // 姓名，长度20，不可为空
  #[serde(rename = "visitNO")]
  visit_no: String, // 就诊号，长度20，不可为空
  #[serde(rename = "patientClass")]
  patient_class: String, // 病人类型，长度2，不可为空
  #[serde(rename = "orderID")]
  order_id: String, // 医嘱号，长度20，不可为空
  #[serde(rename = "applyNO")]
  apply_no: String, // 检验单号，长度20，不可为空（此处传空）
  #[serde(rename = "labStatus")]
  lab_status: String, // 检验状态，长度2，不可为空
  #[serde(rename = "operatorID")]
  operator_id: String, // 操作者ID，长度20，不可为空
  #[serde(rename = "operatorName")]
  operator_name: String, // 操作者姓名，长度20，不可为空
  #[serde(rename = "operationTime")]
  operation_time: String, // 操作时间，长度20，不可为空
  #[serde(rename = "barCode")]
  bar_code: Option<String>, // 条码号，可为空
}

//2.4.4.4.	调用服务更新检验报告
#[derive(Debug, Serialize, Deserialize)]
pub struct LabResultRecord {
  #[serde(rename = "patientID")]
  patient_id: String, // 姓名，长度20，不可为空
  #[serde(rename = "visitNO")]
  visit_no: String, // 就诊号，长度20，不可为空
  #[serde(rename = "patientClass")]
  patient_class: String, // 病人类型，长度2，不可为空
  #[serde(rename = "orderID")]
  order_id: String, // 医嘱号，长度20，不可为空
  #[serde(rename = "applyNO")]
  apply_no: String, // 检验单号，长度20，不可为空（此处传空）
  #[serde(rename = "labStatus")]
  lab_status: String, // 检验申请状态，长度2，不可为空
  #[serde(rename = "spcmSampleDateTime")]
  spcm_sample_date_time: Option<String>, // 标本采集时间，长度20，可为空
  #[serde(rename = "spmRecvedDateTime")]
  spm_recved_date_time: Option<String>, // 标本接收时间，长度20，可为空
  #[serde(rename = "executeDoctorID")]
  execute_doctor_id: Option<String>, // 执行者ID，长度20，可为空
  #[serde(rename = "executeDoctorName")]
  execute_doctor_name: Option<String>, // 执行者姓名，长度20，可为空
  #[serde(rename = "executeDate")]
  execute_date: Option<String>, // 执行时间，长度20，可为空
  #[serde(rename = "reportDoctorID")]
  report_doctor_id: Option<String>, // 报告者ID，长度20，可为空
  #[serde(rename = "reportDoctorName")]
  report_doctor_name: Option<String>, // 报告医生姓名，长度20，可为空
  #[serde(rename = "reportTime")]
  report_time: Option<String>, // 报告时间，长度20，可为空
  #[serde(rename = "verifierDoctorID")]
  verifier_doctor_id: Option<String>, // 审核者ID，长度20，可为空
  #[serde(rename = "verifierDoctorName")]
  verifier_doctor_name: Option<String>, // 审核报告医生姓名，长度20，可为空
  #[serde(rename = "verifierTime")]
  verifier_time: Option<String>, // 审核报告时间，长度20，可为空
  #[serde(rename = "operatorID")]
  operator_id: Option<String>, // 操作者ID，长度20，可为空
  #[serde(rename = "operatorName")]
  operator_name: Option<String>, // 操作者姓名，长度20，可为空
  #[serde(rename = "operationTime")]
  operation_time: Option<String>, // 操作时间，长度20，可为空
  #[serde(rename = "applyPurpose")]
  apply_purpose: Option<String>, // 检验目的(报告名称)，长度50，可为空
  #[serde(rename = "note")]
  note: Option<String>, // 备注，长度500，可为空
  #[serde(rename = "advice")]
  advice: Option<String>, // 解释信息(临床提示)，长度500，可为空
  #[serde(rename = "sampleDetail")]
  sample_detail: Option<String>, // 样本性状，长度100，可为空
  #[serde(rename = "reportUrl")]
  report_url: Option<String>, // Pdf路径，长度100，可为空
  #[serde(rename = "images")]
  images: Vec<String>, // 图像路径，可循环，可为空
  #[serde(rename = "results")]
  results: Vec<LisResult>, // 检验结果列表
}

// 检验结果子结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct LisResult {
  #[serde(rename = "itemNO")]
  item_no: String, // 项目序号，长度20，不可为空
  #[serde(rename = "itemCode")]
  item_code: String, // 检验报告项目代码，长度20，不可为空
  #[serde(rename = "itemName")]
  item_name: String, // 检验报告项目名称，长度20，不可为空
  #[serde(rename = "result")]
  result: String, // 检验结果值，长度20，不可为空
  #[serde(rename = "units")]
  units: String, // 检验结果单位，长度20，不可为空
  #[serde(rename = "upperAndlowerLimits")]
  upper_and_lower_limits: String, // 结果参考值，长度20，不可为空
  #[serde(rename = "abnormalIndicator")]
  abnormal_indicator: String, // 结果正常标志，长度2，不可为空
  #[serde(rename = "resultDateTime")]
  result_date_time: String, // 检验日期及时间，长度20，不可为空
  #[serde(rename = "instrument")]
  instrument: Option<String>, // 检验仪器或编号，长度20，可为空
  #[serde(rename = "crisisValue")]
  crisis_value: String, // 危机值，长度2，不可为空
}
