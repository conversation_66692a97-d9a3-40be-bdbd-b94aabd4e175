use anyhow::{anyhow, Result};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;

#[derive(Default, Debug)]
pub struct MessageService;

impl MessageService {
  pub async fn save_message(testid: &str, msgtype: &str, msgcontent: &str, db: &DbConnection) -> Result<()> {
    let msg = TjMessages {
      id: 0,
      tj_testid: testid.to_string(),
      tj_msgtype: msgtype.to_string(),
      tj_msgcontent: msgcontent.to_string(),
      tj_datetime: utility::timeutil::current_timestamp(),
    };

    let ret = TjMessages::insert_many(&vec![msg], &db.get_connection()).await;

    if ret.as_ref().is_err() {
      //   return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      error!("保存报文错误:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(())
  }
}
