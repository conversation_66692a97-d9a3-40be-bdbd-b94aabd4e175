[package]
name = "nodipservice"
version = "0.80.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
dataservice = { path = "../dataservice" }
# dbopservice = { path = "../dbopservice" }
utility = { path = "../utility" }

anyhow = { workspace = true }   #"1"
tokio = { workspace = true }    #{ version = "1", features = ["full"] }
moka = { workspace = true }     #{ version = "0.12", features = ["future"] }
num_enum = { workspace = true } #"0.7"
# You can also use the "derive" feature, and import the macros directly from "strum"
strum = { workspace = true }        #{ version = "0.26", features = ["derive"] }
strum_macros = { workspace = true } #"0.26"
# strum = "^0.24"
# strum_macros = "^0.24"
evalexpr = { workspace = true } #{ version = "11.3.1" }
# log = { workspace = true }       #"0.4"
# log4rs = { workspace = true }    #"1.2"
itertools = { workspace = true }
once_cell = { workspace = true } #"1"

tracing = { workspace = true }

rust_decimal = { workspace = true }
serde = { workspace = true }        #{ version = "1", features = ["derive"] }
serde_json = { workspace = true }   #"1"
reqwest = { workspace = true }      #
# { version = "0.12", default-features = false, features = [
#     "json",
#     "stream",
#     "multipart",
#     "rustls-tls",
# ] }
mpart-async = { workspace = true } #"0.7"

rust_xlsxwriter = { workspace = true }
chrono = { workspace = true }          #"0.4"
# decimal = "2"
quick-xml = { workspace = true }
yaserde = { workspace = true }

docx-rs = { workspace = true } #"0.4"

tokio-util = { workspace = true }

indexmap = { workspace = true }

oracle = { workspace = true }       #"0.6"
futures-util = { workspace = true } #{ version = "0.3.31" }
sqlx = { workspace = true }         #{ version = "0.8.2", features = ["runtime-tokio", "mysql"] }
calamine = { workspace = true }     #"0.18.0"
uuid.workspace = true
#非windows平台，用rustls
[target.'cfg(not(target_os = "windows"))'.dependencies]
tiberius = { version = "0.12", default-features = false, features = [
    "rust_decimal",
    "rustls",
    "tds73",
    "chrono",
] }

uuid = { workspace = true }
#Windows平台，用native-tls
[target.'cfg(target_os = "windows")'.dependencies]
tiberius = { version = "0.12", features = ["rust_decimal", "tds73", "chrono"] }
