//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_staffright")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_staffid: i32,
  pub tj_rname: String,
  pub tj_right: i32,
  pub tj_operator: i32,
  pub tj_moddate: i64,
  pub tj_menuid: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
