
use anyhow::{anyhow, Result};
use rbatis::crud;
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;

#[derive(C<PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjPackagedetail {
  pub id: i64,
  pub tj_pnum: i64,
  pub tj_itemid: String,
  pub tj_deptid: String,
  pub tj_showorder: i32,
}
crud!(TjPackagedetail {}, "tj_packagedetail");
rbatis::impl_select!(TjPackagedetail{query_many(ids:&[i64]) => 
  "`where id > 0 `
  if !ids.is_empty():
    ` and tj_pnum in ${ids.sql()} `"});
rbatis::impl_delete!(TjPackagedetail{delete_by_pnum(pid:i64) => "`where tj_pnum = #{pid} `"});
rbatis::impl_delete!(TjPackagedetail{delete(ids:&[i64]) => "`where id in ${ids.sql()} `"});

impl TjPackagedetail {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjPackagedetail) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjPackagedetail::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjPackagedetail::insert( rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<TjPackagedetail> ) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<TjPackagedetail>, Vec<TjPackagedetail>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = TjPackagedetail::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = TjPackagedetail::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }
}
