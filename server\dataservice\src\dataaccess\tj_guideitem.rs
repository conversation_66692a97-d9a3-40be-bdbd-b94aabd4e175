use crate::entities::{prelude::*, tj_guideitem};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set, TransactionTrait};
use serde_json::json;

impl TjGuideitem {
  // pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<TjGuideitem>> {
  //   // let ret = NxJobinfoEntity::find_by_id(id).one(db).await;
  //   let ret = TjGuideitemEntity::find().filter(tj_guideitem::Column::AreaCode.eq(code)).one(db).await;
  //   if ret.as_ref().is_err() {
  //     // error!("query error:{}", ret.err().unwrap().to_string());
  //     return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
  //   }
  //   Ok(ret.unwrap())
  // }

  pub async fn query_many(code: &Vec<i64>, db: &DatabaseConnection) -> Result<Vec<TjGuideitem>> {
    let mut conditions = Condition::all();
    if code.len() > 0 {
      conditions = conditions.add(tj_guideitem::Column::TjGuideid.is_in(code.to_owned()));
    }
    let ret = TjGuideitemEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_many(infos: &Vec<TjGuideitem>, db: &DatabaseConnection) -> Result<i64> {
    if infos.len() <= 0 {
      // return Err(anyhow!("empty data"));
      return Ok(0);
    }
    let total = infos.len();
    let (insert_vals, update_vals): (Vec<TjGuideitem>, Vec<TjGuideitem>) = infos.to_owned().into_iter().partition(|f| f.id == 0);
    let txn = db.begin().await.expect("start transaction error");
    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_guideitem::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_guideitem::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjGuideitemEntity::insert_many(active_insert_values).exec(&txn).await;
      if ret.as_ref().is_err() {
        let _ = txn.rollback();
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_packagedetail::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_guideitem::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(&txn).await;
        if ret.as_ref().is_err() {
          let _ = txn.rollback();
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    let ret = txn.commit().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(total as i64)
  }
  pub async fn delete_by_itemid(itemid: &String, db: &DatabaseConnection) -> Result<u64> {
    if itemid.is_empty() {
      return Err(anyhow!("itemid is empty, not allowed"));
    }
    let conditions = Condition::all().add(tj_guideitem::Column::TjItemid.eq(itemid));
    let ret = TjGuideitemEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
  pub async fn delete(guideids: &Vec<i64>, itemids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjGuideitemEntity::delete_many()
      .filter(
        Condition::all()
          .add(tj_guideitem::Column::TjGuideid.is_in(guideids.to_owned()))
          .add(tj_guideitem::Column::TjItemid.is_in(itemids.to_owned())),
      )
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
