//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Default, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_patienthazards")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_testid: String,
  pub tj_hid: i64,
  pub tj_poisionage: String,
  pub tj_typeid: i32,
  pub tj_diseases: String,
  pub tj_recheckitems: String,
  pub tj_olcorpname: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
