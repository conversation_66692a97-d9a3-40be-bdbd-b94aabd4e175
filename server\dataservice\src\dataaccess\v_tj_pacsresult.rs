use crate::entities::{prelude::*, v_tj_pacsresult};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl VTjPacsresult {
  pub async fn query_many(code: &str, db: &DatabaseConnection) -> Result<Vec<VTjPacsresult>> {
    // let ret = NxJobinfoEntity::find_by_id(id).one(db).await;
    let ret = VTjPacsresultEntity::find().filter(v_tj_pacsresult::Column::Tjbh.eq(code)).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_many(info: &Vec<VTjPacsresult>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = info.len();
    let (insert_vals, update_vals): (Vec<VTjPacsresult>, Vec<VTjPacsresult>) = info.to_owned().into_iter().partition(|f| f.id == 0);

    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<v_tj_pacsresult::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = v_tj_pacsresult::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = VTjPacsresultEntity::insert_many(active_insert_values).exec(db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = v_tj_pacsresult::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    Ok(total as i64)
  }

  pub async fn delete_many(testid: &str, db: &DatabaseConnection) -> Result<u64> {
    if testid.is_empty() {
      return Err(anyhow!("testid empty, not allowed"));
    }
    let conditions = Condition::all().add(v_tj_pacsresult::Column::Tjbh.eq(testid));

    let ret = VTjPacsresultEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn delete_many_by_jclx(testids: &Vec<String>, jclx: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    if testids.len() <= 0 || jclx.len() <= 0 {
      return Err(anyhow!("testid empty, not allowed"));
    }
    let mut conditions = Condition::all().add(v_tj_pacsresult::Column::Tjbh.is_in(testids.to_owned()));

    if jclx.len() > 0 {
      conditions = conditions.add(v_tj_pacsresult::Column::Jclx.is_in(jclx.to_owned()))
    }

    let ret = VTjPacsresultEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn clear(date: &str, db: &DatabaseConnection) -> Result<u64> {
    if date.is_empty() {
      return Err(anyhow!("date empty, not allowed"));
    }
    let conditions = Condition::all().add(v_tj_pacsresult::Column::Bgrq.lt(date));

    let ret = VTjPacsresultEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
