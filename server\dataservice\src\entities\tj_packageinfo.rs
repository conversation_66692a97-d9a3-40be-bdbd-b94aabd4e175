//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, Default, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_packageinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_pnum: String,
  pub tj_pname: String,
  pub tj_price: String,
  pub tj_type: i32,
  pub tj_memo: String,
  pub tj_showorder: i32,
  pub tj_fitrange: i32,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_flag: i32,
  pub tj_operator: i32,
  pub tj_moddate: i64,

  pub tj_originalprice: String,
  pub tj_discount: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
