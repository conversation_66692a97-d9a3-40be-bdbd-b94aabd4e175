//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "s_cdc_sptinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub cdc_sptid: Option<i32>,
  pub cdc_sptname: Option<String>,
  pub tj_disid: Option<i32>,
  pub is_cate: i32,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
