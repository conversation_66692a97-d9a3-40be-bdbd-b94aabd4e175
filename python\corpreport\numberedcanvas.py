import os
from reportlab.pdfgen import canvas
from reportlab.lib.units import cm

# from reportlab.platypus import Image
# from reportlab.lib.pagesizes import A4


class NumberedCanvas(canvas.Canvas):
    def __init__(self, *args, **kwargs):
        canvas.Canvas.__init__(self, *args, **kwargs)
        self._saved_page_states = []

    def showPage(self):
        self._saved_page_states.append(dict(self.__dict__))
        self._startPage()

    def save(self):
        """add page info to each page (page x of y)"""
        num_pages = len(self._saved_page_states)
        for i, state in enumerate(self._saved_page_states):
            self.__dict__.update(state)
            if i != 0:
                self.draw_page_number(num_pages)
            if i == 1:
                qrcode_file = "./images/qrcode.png"
                if os.path.exists(qrcode_file):
                    self.drawImage(
                        qrcode_file,
                        13 * cm,
                        3 * cm,
                        width=3.5 * cm,
                        height=3.5 * cm,
                    )
            canvas.Canvas.showPage(self)
        canvas.Canvas.save(self)

    def draw_page_number(self, page_count):
        self.setFont("SimSun", 12)
        self.drawRightString(
            self._pagesize[0] - 2 * cm,
            self._pagesize[1] - 2 * cm,
            "第%d页，共%d页 " % (self._pageNumber-1, page_count-1),
        )
