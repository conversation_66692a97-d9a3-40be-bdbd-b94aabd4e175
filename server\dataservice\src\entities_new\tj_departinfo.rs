//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_departinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  #[sea_orm(unique)]
  pub tj_deptid: String,
  pub tj_deptname: String,
  pub tj_showorder: i32,
  pub tj_depttype: i32,
  pub tj_deptinfo: String,
  pub tj_pricinple: i32,
  pub tj_deptaddr: String,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_flag: i32,
  pub tj_operator: i32,
  pub tj_moddate: i64,
  pub tj_diagtype: i32,
  pub tj_reportmode: i32,
  pub tj_reportorder: i32,
  pub tj_sex: i32,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
