use anyhow::{anyhow, Result};
// use dbopservice::{
//   dataaccess::{tj_audiogramdetail::TjAudiogramdetail, tj_audiogramresult::TjAudiogramresult, tj_medexaminfo::TjMedexaminfo, tj_patient::TjPatient, tj_testsummary::TjTestsummary},
//   dbinit::DbConnection,
// };
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;
// use hyper::Method;
use crate::{
  client::httpclient::HttpClient,
  common::constant,
  dto::{ExtTypeDTO, ExternalDTO},
  medexam::audiogramsvc::AudiogramSvc,
};
use serde::{Deserialize, Serialize};

use crate::common::http::DataBody;

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct DianResponseBody<T> {
  pub code: String,
  // #[serde(rename = "message")]
  pub msg: String,
  pub body: T,
}
#[derive(Debug, Serialize, Deserialize)]
pub struct DianDataBody<T> {
  pub count: u64,
  pub list: T,
}
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct OrderInfo {
  pub order_no: String,
  pub order_name: String,
}

pub struct DianService;

impl DianService {
  pub async fn do_upload(dto: &ExternalDTO, server: &String, audiodburi: &String, db: &DbConnection) -> Result<()> {
    let testid = dto.testid.to_string();
    // let server = config.application.proxyserver.to_string();
    let mut error_msg = "".to_string();
    if !server.is_empty() {
      // return Ok(());
      // }
      let uri = format!("{}/api/external/upload", server);

      let dto = ExtTypeDTO {
        testid: dto.testid.to_owned(),
        exttype: dto.exttype,
        newinfos: None,
        existinfos: None,
      };

      // let ret: Result<String> = HttpClient::send_http_post_request("", &uri, &Some(dto)).await;
      let dto_str = serde_json::to_string(&dto).unwrap_or_default();
      let ret: Result<String> = HttpClient::send_post_request("", &uri, &dto_str).await;
      // let ret: Result<String> = HttpClient::send_http_post_request("", &uri, &Some(dto)).await;
      if ret.as_ref().is_err() {
        error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
        // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        error_msg = format!("{}{}", error_msg, ret.as_ref().unwrap_err().to_string());
      }
      let retstr = ret.unwrap_or_default();
      info!("dian response :{:?}", &retstr);
      let result = serde_json::from_str::<crate::common::http::ResponseBody<DataBody<String>>>(&retstr);
      //.unwrap_or_default();
      info!("local response :{:?}", &result);
      if result.is_err() {
        error!("external upload error:{}", result.as_ref().unwrap_err().to_string());
        // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        error_msg = format!("{}{}", error_msg, result.as_ref().unwrap_err().to_string());
      }
      let result = result.unwrap();
      if result.code != 200 {
        error!("external upload error:{}", &result.message);
        error_msg = format!("sync error: {}{}", error_msg, &result.message);
      }
    }
    //同步电测听
    if !audiodburi.is_empty() {
      let ret = TjTestsummary::query_one(&testid, crate::common::constant::AUDIOGRAM_DEPTID, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
        // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        error_msg = format!("{}{}", error_msg, ret.as_ref().unwrap_err().to_string());
      }
      if let Some(summary) = ret.unwrap() {
        //有电测听数据
        if summary.tj_isfinished == constant::YesOrNo::No as i32 {
          // info!("开始电测听对接......");
          let ret = TjMedexaminfo::query(&testid, &db.get_connection()).await;
          if ret.as_ref().is_err() {
            error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
            // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
            error_msg = format!("{}{}", error_msg, ret.as_ref().unwrap_err().to_string());
          }
          if let Some(medinfo) = ret.unwrap() {
            let pid = medinfo.tj_pid.to_string();
            let ret = TjPatient::query(&pid, &db.get_connection()).await;
            if ret.as_ref().is_err() {
              error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
              // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
              error_msg = format!("{}{}", error_msg, ret.as_ref().unwrap_err().to_string());
            }
            if let Some(ptinfo) = ret.unwrap() {
              //开始保存到电测听的数据表
              // info!("找到体检信息跟人员信息，开始")
              let ret = super::maili::MailiService::send_testinfo(&medinfo, &ptinfo, &audiodburi).await;
              if ret.as_ref().is_err() {
                // return Err(anyhow!("{}", ret.unwrap_err().to_string()));
                error_msg = format!("{}{}", error_msg, ret.as_ref().unwrap_err().to_string());
              }
            } else {
              error!("不能根据pid：{pid}找到体检人员信息");
              error_msg = format!("{}{}", error_msg, "不能根据pid找到体检人员信息");
            }
          } else {
            // error!("不能根据体检号：{testid}找到体检信息");
            error_msg = format!("{}{}", error_msg, "不能根据体检号找到体检信息");
          }
        }
      }
    }
    if error_msg.is_empty() {
      return Ok(());
    } else {
      return Err(anyhow!(error_msg));
    }
    // Ok(())
  }

  // //下载电测听结果数据
  pub async fn receive_audiogram(testid: &String, dburi: &String, db: &DbConnection) -> Result<(Vec<TjAudiogramdetail>, TjAudiogramresult)> {
    if dburi.is_empty() {
      return Err(anyhow!("没有配置电测听的连接信息，无法获取数据"));
    }
    // rb.link(MssqlDriver{},"************************************************={TestPass!123456};Database=test").await.unwrap();
    // let mut rb = RBatis::new();
    // let ret = match config.audiogram.dbtype {
    //   x if x == crate::common::constant::DatabaseType::MySql as i32 => rb.link(rbdc_mysql::driver::MysqlDriver {}, &config.audiogram.dburi).await,
    //   x if x == crate::common::constant::DatabaseType::MsSql as i32 => rb.link(MssqlDriver {}, &config.audiogram.dburi).await,
    //   _ => rb.link(rbdc_mysql::driver::MysqlDriver {}, &config.audiogram.dburi).await,
    // };
    // if ret.as_ref().is_err() {
    //   error!("连接电测听数据库错误:{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }

    let ret = super::maili::MailiService::query_result_by_testid(testid, dburi, db).await;
    if ret.as_ref().is_err() {
      error!("获取电测听数据错误:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let aud_result = ret.unwrap();
    let sumamry = aud_result.1;
    //update summary
    let ret = TjTestsummary::save(&sumamry, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("获取电测听数据错误:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let audio_details = aud_result.0;
    let audio_result = AudiogramSvc::compute_audiogram_result(&testid, &audio_details);
    Ok((audio_details, audio_result))
  }

  pub async fn get_orders(serverurl: &String) -> Result<Vec<OrderInfo>> {
    let order_uri = format!("{}/v2/occupy/order/list", serverurl);
    // let ret: Result<String> = HttpClient::send_http_get_request("", &order_uri, &None::<String>).await;
    info!("start to get orders from:{:?}", &order_uri);
    let ret = HttpClient::send_request(reqwest::Method::GET, "", &order_uri, "").await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let result = ret.unwrap();
    info!("get order list is:{}", &result);
    let ret = serde_json::from_str::<DianResponseBody<DianDataBody<Vec<OrderInfo>>>>(&result);
    if ret.as_ref().is_err() {
      error!("serde json error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      // return Err(anyhow!("解析返回数据错误"));
    }
    let resp_value = ret.unwrap();
    if resp_value.code != "000000" {
      return Err(anyhow!("{}", resp_value.msg));
    }
    Ok(resp_value.body.list)

    // info!("get order response:{:?}", &ret);
    // if ret.as_ref().is_err() {
    //   error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // ret
  }
}
