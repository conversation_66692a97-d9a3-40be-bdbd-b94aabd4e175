use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok, DataBody, ResponseBody},
  auth::auth::Claims,
  common::constant::{self},
  config::settings::Settings,
};
use axum::{extract::Path, Extension, Json};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{
  dto::*,
  external::externalservice::ExtService,
  medexam::{healthyinfosvc::HealthyinfoSvc, medexamsvc::MedexamSvc},
};
use serde_json::Value;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::*;
use utility::uidservice::UidgenService;

pub async fn query_medexaminfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): J<PERSON><MedQueryDto>) -> Json<Value> {
  info!("start to query medinfos by dto:{:?}", &dto);

  let ret = MedexamSvc::query_medexaminfos_by_dto(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<MedexamResultDetail>::new());
  }

  let results = ret.unwrap();
  let databody = DataBody::new(results.len() as u64, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn query_medexaminfos_by_testids(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeysDto>) -> Json<Value> {
  info!("start to query medinfos by dto:{:?}", &dto);

  let ret = MedexamSvc::query_medinfos_by_testids(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjMedexaminfo>::new());
  }

  let results = ret.unwrap();
  let databody = DataBody::new(results.len() as u64, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn query_medexaminfo(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Path(testid): Path<String>) -> Json<Value> {
  info!("start to query medinfos by testid:{:?}", &testid);

  let ret = MedexamSvc::query_by_testid(&testid, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), MedexamResultDetail { ..Default::default() });
  }

  let result = ret.unwrap();

  response_json_value_ok(1, result)
  // let databody = DataBody::new(results.len() as u64, results);
  // Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn save_medexaminfo(
  _claims: Claims,
  Extension(db): Extension<Arc<DbConnection>>,
  Extension(config): Extension<Arc<Settings>>,
  Extension(uid): Extension<Arc<RwLock<UidgenService>>>,
  Json(dto): Json<QuickRegisterDto>,
) -> Json<Value> {
  info!("开始更新或者保存体检信息......,dto:{dto:?}");
  // let hdinfos: Vec<TjHazardinfo> = vec![];
  let ret = MedexamSvc::save_medexaminfo(&dto.ptinfo, &dto.medinfo, &dto.hdinfos, &db).await;
  // if ret.as_ref().is_err() {
  //   return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  // }
  // let ret = PatientSvc::update_patient(&dto.ptinfo, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }

  let mut results = ret.unwrap();
  //外部对接
  let dto = ExternalDTO {
    testid: results.medinfo.tj_testid.to_string(),
    exttype: nodipservice::common::constant::ExtOpType::EDIT as i32,
    additional: results.medinfo.tj_additional.to_string(),
  };
  let mut idgen = uid.write().await;
  let ret = ExtService::do_external_upload(
    &db,
    &config.external.exttype,
    // &config.application.proxyserver,
    &config.external.serverurl,
    &config.audiogram.dburi,
    &mut idgen,
    0,
    &dto,
  )
  .await; //
  if ret.as_ref().is_err() {
    // return response_json_value_error(format!("external upload error:{}", ret.as_ref().unwrap_err().to_string()).as_str(), results);
    results.medinfo.tj_additional = format!("对接错误:{}", ret.as_ref().unwrap_err().to_string());
  }

  response_json_value_ok(1, results)
}

pub async fn delete_medexaminfo(
  _claims: Claims,
  Extension(db): Extension<Arc<DbConnection>>,
  config: Extension<Arc<Settings>>,
  uid: Extension<Arc<RwLock<UidgenService>>>,
  Json(dto): Json<KeysDto>,
) -> Json<Value> {
  info!("delete medexaminfo dto is:{:?}", &dto);
  if dto.keys.len() <= 0 {
    return response_json_value_error("empty ids, not allowed", 0);
  }
  let ret = MedexamSvc::delete_medexaminfos(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  //外部对接
  for testid in dto.keys.iter() {
    let dto = ExternalDTO {
      testid: testid.to_string(),
      exttype: nodipservice::common::constant::ExtOpType::DEL as i32,
      additional: "".to_string(),
    };
    let mut idgen = uid.write().await;
    let ret = ExtService::do_external_upload(
      &db,
      &config.external.exttype,
      // &config.application.proxyserver,
      &config.external.serverurl,
      &config.audiogram.dburi,
      &mut idgen,
      0,
      &dto,
    )
    .await; //
    if ret.as_ref().is_err() {
      // return response_json_value_error(format!("external upload error:{}", ret.as_ref().unwrap_err().to_string()).as_str(), 0);
      error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
    }
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn quick_register(
  _claims: Claims,
  Extension(db): Extension<Arc<DbConnection>>,
  Extension(settings): Extension<Arc<Settings>>,
  Extension(uid): Extension<Arc<RwLock<UidgenService>>>,
  Json(dto): Json<QuickRegisterDto>,
) -> Json<Value> {
  info!("quick register dto:{:?}", &dto);

  let ret = MedexamSvc::medexam_quick_register(&dto.ptinfo, &dto.medinfo, &dto.pkginfo, &dto.hdinfos, &settings.application.splitter, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), RegisterResponseDto { ..Default::default() });
  }

  let mut results = ret.unwrap();

  //外部对接
  let dto = ExternalDTO {
    testid: results.medinfo.tj_testid.to_string(),
    exttype: nodipservice::common::constant::ExtOpType::ADD as i32,
    additional: results.medinfo.tj_additional.to_string(),
  };
  let mut idgen = uid.write().await;
  let ret = ExtService::do_external_upload(
    &db,
    &settings.external.exttype,
    // &settings.application.proxyserver,
    &settings.external.serverurl,
    &settings.audiogram.dburi,
    &mut idgen,
    0,
    &dto,
  )
  .await; //
  if ret.as_ref().is_err() {
    // return response_json_value_error(format!("external upload error:{}", ret.as_ref().unwrap_err().to_string()).as_str(), results);
    results.medinfo.tj_additional = format!("对接错误:{}", ret.as_ref().unwrap_err().to_string());
  }
  let databody = DataBody::new(1, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn medexam_register(
  _claims: Claims,
  Extension(db): Extension<Arc<DbConnection>>,
  config: Extension<Arc<Settings>>,
  uid: Extension<Arc<RwLock<UidgenService>>>,
  Json(dto): Json<MedexamRegisterDto>,
) -> Json<Value> {
  info!("medexam register dto:{:?}", &dto);

  let ret = MedexamSvc::medexam_register(&dto.ptinfo, &dto.medinfo, &dto.iteminfos, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), RegisterResponseDto { ..Default::default() });
  }

  let mut results = ret.unwrap();

  //外部对接
  let dto = ExternalDTO {
    testid: results.medinfo.tj_testid.to_string(),
    exttype: nodipservice::common::constant::ExtOpType::ADD as i32,
    additional: results.medinfo.tj_additional.to_string(),
  };
  let mut idgen = uid.write().await;
  let ret = ExtService::do_external_upload(
    &db,
    &config.external.exttype,
    // &config.application.proxyserver,
    &config.external.serverurl,
    &config.audiogram.dburi,
    &mut idgen,
    0,
    &dto,
  )
  .await; //
  if ret.as_ref().is_err() {
    // return response_json_value_error(format!("external upload error:{}", ret.as_ref().unwrap_err().to_string()).as_str(), results);
    results.medinfo.tj_additional = format!("对接错误:{}", ret.as_ref().unwrap_err().to_string());
  }

  let databody = DataBody::new(1, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn recheck_register(
  _claims: Claims,
  Extension(db): Extension<Arc<DbConnection>>,
  config: Extension<Arc<Settings>>,
  uid: Extension<Arc<RwLock<UidgenService>>>,
  Json(dto): Json<MKeyDto>,
) -> Json<Value> {
  // info!("recheck register dto:{:?}", &dto);

  let ret = MedexamSvc::medexam_recheck_register(&dto.key_str, dto.key_int, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    info!("{}", ret.as_ref().unwrap_err().to_string());
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), RegisterResponseDto { ..Default::default() });
  }

  let mut results = ret.unwrap();

  //外部对接
  let dto = ExternalDTO {
    testid: results.medinfo.tj_testid.to_string(),
    exttype: nodipservice::common::constant::ExtOpType::ADD as i32,
    additional: results.medinfo.tj_additional.to_string(),
  };
  let mut idgen = uid.write().await;
  let ret = ExtService::do_external_upload(
    &db,
    &config.external.exttype,
    // &config.application.proxyserver,
    &config.external.serverurl,
    &config.audiogram.dburi,
    &mut idgen,
    0,
    &dto,
  )
  .await; //
  if ret.as_ref().is_err() {
    // return response_json_value_error(format!("external upload error:{}", ret.as_ref().unwrap_err().to_string()).as_str(), results);
    results.medinfo.tj_additional = format!("对接错误:{}", ret.as_ref().unwrap_err().to_string());
  }

  return response_json_value_ok(1, results);
}

pub async fn update_personal_report_print_status(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeysDto>) -> Json<Value> {
  if dto.keys.is_empty() {
    return response_json_value_error("empty key, not allowed", 0);
  }
  info!("start to update print status for:{dto:?}");
  let ret = MedexamSvc::update_medexam_print_status(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), "");
  }
  response_json_value_ok(1, "")
}

pub async fn medexam_update_status(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<ExamStatusDto>) -> Json<Value> {
  info!("medexam update status dto:{:?}", &dto);
  let mut medinfo = dto.medinfo.to_owned();
  let ret = MedexamSvc::update_medexam_status(&mut medinfo, dto.updown, dto.newstatus, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), medinfo);
  }

  // let results = ret.unwrap();
  let databody = DataBody::new(1, medinfo);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn query_healthyinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyDto>) -> Json<Value> {
  if dto.key.is_empty() {
    return response_json_value_error("empty key, not allowed", 0);
  }
  let ret = HealthyinfoSvc::query_healthyinfos(&dto.key, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), QuestionnairDto { ..Default::default() });
  }
  let results = ret.unwrap();
  info!("question nair: {results:?}");
  response_json_value_ok(1, results)
  // response_json_value_ok(1, "")
}

pub async fn save_healthyinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<QuestionnairDto>) -> Json<Value> {
  // if dto.occupations.len() <= 0 {
  //   return response_json_value_error("empty ids, not allowed", 0);
  // }
  info!("Save healthy info......:{:#?}", &dto);
  let ret = HealthyinfoSvc::save_healthyinfos(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), "");
  }

  // let results = ret.unwrap();
  // response_json_value_ok(1, results)
  response_json_value_ok(1, "")
}

pub async fn query_medinfo_hazards_by_testid(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeysDto>) -> Json<Value> {
  info!("query medexam hazards:{dto:?}");
  let ret = MedexamSvc::query_medinfo_hazards_by_testid(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn update_medinfo_hazards_results(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<Vec<TjPatienthazards>>) -> Json<Value> {
  info!("update medexam hazards results:{dto:?}");
  let ret = MedexamSvc::update_medinfo_hazards_results(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(dto.len() as u64, results)
}

pub async fn update_medinfo_hazards(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MedexamHazardUpdateDto>) -> Json<Value> {
  info!("update medexam hazards:{dto:?}");
  let ret = MedexamSvc::update_medexam_hazards(&dto.medinfos, &dto.hdinfos, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  // let results = ret.unwrap();
  response_json_value_ok(1, 0)
}
