//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, De<PERSON>ult,PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "ss_area")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  #[sea_orm(unique)]
  pub area_code: String,
  pub area_name: String,
  pub area_fullname: String,
  pub area_level: i8,
  pub area_pcode: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
