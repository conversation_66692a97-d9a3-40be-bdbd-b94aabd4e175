use crate::entities::{prelude::*, tj_corpoccureport_info};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjCorpoccureportInfo {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<TjCorpoccureportInfo>> {
    let ret = TjCorpoccureportInfoEntity::find().filter(tj_corpoccureport_info::Column::TjTestId.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(code: &Vec<i64>, db: &DatabaseConnection) -> Result<Vec<TjCorpoccureportInfo>> {
    if code.len() <= 0 {
      return Err(anyhow!("报告编号不能为0"));
    }
    let ret = TjCorpoccureportInfoEntity::find()
      .filter(tj_corpoccureport_info::Column::TjReportId.is_in(code.to_owned()))
      .all(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_many_by_testids(testids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjCorpoccureportInfo>> {
    if testids.len() <= 0 {
      return Err(anyhow!("报告编号不能为0"));
    }
    let ret = TjCorpoccureportInfoEntity::find()
      .filter(tj_corpoccureport_info::Column::TjTestId.is_in(testids.to_owned()))
      .all(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save_many(info: &Vec<TjCorpoccureportInfo>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = info.len();
    let (insert_vals, update_vals): (Vec<TjCorpoccureportInfo>, Vec<TjCorpoccureportInfo>) = info.to_owned().into_iter().partition(|f| f.id == 0);

    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_corpoccureport_info::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_corpoccureport_info::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjCorpoccureportInfoEntity::insert_many(active_insert_values).exec(db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_corpoccureport_info::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    Ok(total as i64)
  }
  pub async fn delete_many(testids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    if testids.len() <= 0 {
      return Err(anyhow!("testid empty, not allowed"));
    }
    let conditions = Condition::all().add(tj_corpoccureport_info::Column::TjTestId.is_in(testids.to_owned()));

    let ret = TjCorpoccureportInfoEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
