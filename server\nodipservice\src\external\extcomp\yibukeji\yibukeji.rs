use anyhow::{anyhow, Result};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;

use super::*;
use crate::{common::constant::*, dto::ExternalDTO};

//urgent {1: 紧急, 2: 普通}
//classify {1: 门诊, 2: 住院, 3: 体检}

pub struct YibuKeji;

impl YibuKeji {
  pub async fn upload_medexaminfo(
    dto: &ExternalDTO,
    medinfo: &TjMedexaminfo,
    new_extitems: &Vec<ExtCheckiteminfo>,
    exist_items: &Vec<ExtCheckiteminfo>,
    server: &String,
    // uid: &mut UidgenService,
    force: i32,
    db: &DbConnection,
  ) -> Result<String> {
    if dto.exttype == ExtOpType::DEL as i32 {
      // info!("new items:{:?}", &new_extitems);
      // info!("exist_items:{:?},", &exist_items);
      //删除，发送取消检查的接口
      let mut medinfo = medinfo.to_owned();
      let ret = YibuKeji::send_fee_cancel_application(&medinfo, exist_items, server).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      medinfo.tj_ordno = "".to_string();
      let ret = TjMedexaminfo::save(&medinfo, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      return Ok("".to_string());
    }
    //删除项目
    let delitems: Vec<ExtCheckiteminfo> = exist_items
      .into_iter()
      .filter(|&p| new_extitems.iter().find(|f| f.itemid2.eq_ignore_ascii_case(&p.itemid2)).is_none())
      .map(|v| v.to_owned())
      .collect();
    info!("需要删除项目：{:?}", &delitems);

    //新增项目
    let mut newitems: Vec<ExtCheckiteminfo>; // = vec![];
    if force == YesOrNo::Yes as i32 {
      info!("强制全部同步？？？？？？");
      newitems = new_extitems.into_iter().map(|v| v.to_owned()).collect();
    } else {
      newitems = new_extitems
        .into_iter()
        .filter(|&p| exist_items.iter().find(|f| f.itemid2.eq_ignore_ascii_case(&p.itemid2)).is_none())
        .map(|v| v.to_owned())
        .collect();
    }
    info!("需要新增项目：{:?}", &newitems);

    if newitems.len() <= 0 && delitems.len() <= 0 {
      info!("不需要新增项目，也不需要删除项目，不需要对接......");
      return Ok("".to_string());
    }

    if delitems.len() > 0 {
      //发送cancel fee application
      let check_cancel: Vec<ExtCheckiteminfo> = delitems
        .iter()
        .filter(|&v| v.depttype == crate::common::constant::DeptType::Function as i32)
        .map(|val| val.to_owned())
        .collect();
      if check_cancel.len() > 0 {
        let ret = YibuKeji::send_check_cancel_application(&check_cancel, server).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
      let lis_cancel: Vec<ExtCheckiteminfo> = delitems
        .iter()
        .filter(|&f| f.depttype == crate::common::constant::DeptType::Lab as i32)
        .map(|v| v.to_owned())
        .collect();

      if lis_cancel.len() > 0 {
        let ret = YibuKeji::send_lis_cancel_application(&lis_cancel, server).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
    }

    if newitems.len() > 0 {
      // info!("开始发送建档和收费数据......:{:?}", &medinfo);
      let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let ptinfo = ret.unwrap();
      if ptinfo.is_none() {
        return Err(anyhow!("Can't find patient info by testid: {}", &medinfo.tj_testid));
      }
      let mut ptinfo = ptinfo.unwrap();
      //如果是新增，先建档
      if dto.exttype == ExtOpType::ADD as i32 {
        // if ptinfo.tj_patid.is_empty() {
        info!("开始准备发送建档数据......");
        let ret = YibuKeji::create_profile(&medinfo, &mut ptinfo, server, db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(" {}", ret.as_ref().unwrap_err().to_string()));
        }
        // }
      }
      //发送收费信息
      // if medinfo.tj_ordno.is_empty() {
      info!("开始准备发送收费信息......");
      let ret = YibuKeji::send_fee_application(&medinfo, &ptinfo, &newitems, server, db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      // }
      //发送检查项目
      info!("开始准备发送检查项目......");
      let ret = YibuKeji::send_check_application(&medinfo, &ptinfo, &mut newitems, &server).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      //发送检验项目
      info!("开始准备发送检验项目......");
      let ret = YibuKeji::send_lis_application(&medinfo, &ptinfo, &mut newitems, &server).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      //更新本地数据库
      info!("开始更新本地数据库......");
      let ret = YibuKeji::update_ext_checkiteminfos(&newitems, db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    info!("武义壶山医院，建档对接完成......");
    Ok("".to_string())
  }

  pub async fn receive_lis_result(medinfo: &TjMedexaminfo, server: &String, db: &DbConnection) -> Result<String> {
    //
    if medinfo.tj_testid.is_empty() {
      return Err(anyhow!("体检号为空，不能获取结果数据......"));
    }
    let dto = QueryResultsDto {
      number: medinfo.tj_testid.to_string(),
    };
    let yibuserver = format!("{}{}", server, super::YibuApi::QueryLisResults.to_string());
    let ret: Result<Vec<YibuLisResults>> = YibuClient::send_http_get_request(yibuserver.as_str(), &Some(dto)).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let results = ret.unwrap();
    // if res.code != super::YIBU_CODE_OK as i32 {
    //   return Err(anyhow!("{}", res.message.to_string()));
    // }
    // let results = res.result.unwrap_or_default();
    if results.len() <= 0 {
      return Err(anyhow!("Lis系统无该体检人员的结果数据"));
    }
    //insert into v_tj_lisresults;
    let mut lisresults: Vec<VTjLisresult> = vec![];
    for val in results.into_iter() {
      let lab = val.laboratory;
      let resultlist = val.resultlist;
      if resultlist.len() <= 0 {
        return Err(anyhow!("Lis系统无检查结果"));
      }
      let bgrq = match lab.implement_time {
        None => "".to_string(),
        Some(v) => utility::timeutil::format_timestamp(v),
      };
      let jyys = lab.execute_doctor_name.unwrap_or_default().to_string();
      let bgys = lab.audit_doctor_name.clone().unwrap_or_default().to_string();
      for val2 in resultlist.into_iter() {
        let lis_result = val2.result;
        if lis_result.is_none() {
          continue;
        }
        let lis_result = lis_result.unwrap_or_default();
        if lis_result.is_empty() {
          continue;
        }
        let gdbj = val2.status.unwrap_or_default();
        let lisret = VTjLisresult {
          id: 0,
          tjbh: medinfo.tj_testid.to_string(),
          brxm: "".to_string(),
          xmxh: val2.project_sn.unwrap_or_default().to_string(),
          xmmc: val2.project_name.unwrap_or_default().to_string(),
          xmdw: val2.unit.unwrap_or_default().to_string(),
          xmjg: lis_result.to_string(),
          sfyc: match gdbj.is_empty() {
            true => 0,
            false => 1,
          },
          gdbj: gdbj,
          ckdz: val2.lower.unwrap_or_default().to_string(),
          ckgz: val2.upper.unwrap_or_default().to_string(),
          ckfw: val2.scope.unwrap_or_default().to_string(),
          jyys: jyys.clone(),
          bgrq: bgrq.clone(),
          bgys: bgys.clone(),
        };
        lisresults.push(lisret);
      }
    }
    if lisresults.len() <= 0 {
      // let ret = VTjLisresult::delete_many(&medinfo.tj_testid, &db.get_connection()).await;
      // if ret.as_ref().is_err() {
      //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      // }
      return Err(anyhow!("Lis系统无结果数据"));
    }
    let xmxhs: Vec<String> = lisresults.iter().map(|v| v.xmxh.to_string()).collect();
    let ret = VTjLisresult::delete_many_by_xmxh(&vec![medinfo.tj_testid.to_string()], &xmxhs, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = VTjLisresult::save_many(&lisresults, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }

    Ok("".to_string())
  }

  pub async fn receive_pacs_result(medinfo: &TjMedexaminfo, server: &String, db: &DbConnection) -> Result<String> {
    //
    if medinfo.tj_testid.is_empty() {
      return Err(anyhow!("体检号为空，不能获取结果数据......"));
    }
    let dto = QueryResultsDto {
      number: medinfo.tj_testid.to_string(),
    };
    let yibuserver = format!("{}{}", server, super::YibuApi::QueryCheckResults.to_string());
    let ret: Result<Vec<YibuCheckresult>> = YibuClient::send_http_get_request(yibuserver.as_str(), &Some(dto)).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let results = ret.unwrap();
    // if res.code != super::YIBU_CODE_OK {
    //   return Err(anyhow!("{}", res.message));
    // }
    // let results = res.result.unwrap_or_default();
    if results.len() <= 0 {
      return Err(anyhow!("没有该体检编号的pacs结果数据"));
    }
    // let mut empty_result = 0;
    let mut pacsresults: Vec<VTjPacsresult> = vec![];
    for val in results.into_iter() {
      let pacs_result = val.result;
      if pacs_result.is_none() {
        // empty_result += 1;
        continue;
      }
      let pacs_result = pacs_result.unwrap_or_default();
      if pacs_result.is_empty() {
        // empty_result += 1;
        continue;
      }
      let imagepath = Some("".to_string());
      let datas = Some("".to_string());
      let pret = VTjPacsresult {
        id: 0,
        tjbh: medinfo.tj_testid.to_string(),
        brxm: "".to_string(),
        jclx: val.checktype.unwrap_or_default().to_string(),
        jcxm: val.items_sn.unwrap_or_default().to_string(),
        jcmc: val.checkname.unwrap_or_default().to_string(),
        imagesight: pacs_result.to_string(),
        imagediagnosis: val.diagnosis.unwrap_or_default().to_string(),
        jcys: val.reporter.clone().unwrap_or_default().to_string(),
        sxys: val.reporter.unwrap_or_default().to_string(),
        bgys: val.reviewer.unwrap_or_default().to_string(),
        bgrq: utility::timeutil::format_timestamp(val.result_time.unwrap_or_default()),
        sfyc: 0,
        imagepath,
        datas,
      };
      pacsresults.push(pret);
    }
    if pacsresults.len() <= 0 {
      let ret = VTjPacsresult::delete_many(&medinfo.tj_testid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      return Err(anyhow!("没有该体检编号的结果数据"));
    }
    let jclx: Vec<String> = pacsresults.iter().map(|v| v.jclx.to_string()).collect();

    let ret = VTjPacsresult::delete_many_by_jclx(&vec![medinfo.tj_testid.to_string()], &jclx, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = VTjPacsresult::save_many(&pacsresults, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok("".to_string())
  }

  pub async fn query_lis_xmxx(server: &String) -> Result<Vec<VLisXmxx>> {
    let dto: QueryHisItemsDto = QueryHisItemsDto {
      using_tag: 1,
      type_sns: "32,41".to_string(),
    };
    let server1 = format!("{}{}", server, super::YibuApi::QueryHisExamItems.to_string());
    let ret: Result<Vec<HisExamItems>> = YibuClient::send_http_get_request(&server1, &Some(dto)).await;
    // if ret.as_ref().is_err() {
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    let mut lisxmxx: Vec<VLisXmxx> = vec![];
    if ret.is_ok() {
      let result = ret.unwrap();
      for val in result {
        let xmxx = VLisXmxx {
          itemid: val.itemssn.to_string(),
          chinesename: val.itemsname.to_string(),
          englishab: val.initials.unwrap_or_default().to_string(),
          unit: val.unit.unwrap_or_default().to_string(),
        };
        lisxmxx.push(xmxx);
      }
    } else {
      error!("query pacs items error:{}", ret.as_ref().unwrap_err().to_string());
    }
    let server2 = format!("{}{}", server, super::YibuApi::QueryLisItems.to_string());
    let dto = QueryLisItemsDto { hospitalid: "1005".to_string() };
    let ret: Result<Vec<HisExamItems>> = YibuClient::send_http_post_request(&server2, &Some(dto)).await;
    // if ret.as_ref().is_err() {
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    if ret.is_ok() {
      let result = ret.unwrap();
      for val in result {
        let xmxx = VLisXmxx {
          itemid: val.itemssn.to_string(),
          chinesename: val.itemsname.to_string(),
          englishab: val.initials.unwrap_or_default().to_string(),
          unit: val.unit.unwrap_or_default().to_string(),
        };
        lisxmxx.push(xmxx);
      }
    } else {
      error!("query lis items error:{}", ret.as_ref().unwrap_err().to_string());
    }
    Ok(lisxmxx)
  }

  async fn create_profile(medinfo: &TjMedexaminfo, ptinfo: &mut TjPatient, server: &String, db: &DbConnection) -> Result<String> {
    let staffinfo = crate::SYSCACHE.get().unwrap().get_staff(medinfo.tj_recorder.parse().unwrap_or_default(), db).await;
    let corpinfo = crate::SYSCACHE.get().unwrap().get_corpinfos(&vec![medinfo.tj_corpnum], &"".to_string(), &vec![], db).await;
    let mut corpname = "".to_string();
    if corpinfo.len() > 0 {
      corpname = corpinfo[0].tj_corpname.to_string();
    }
    // let mut patient_sn: Option<_> = None;
    // if !ptinfo.tj_patid.is_empty() {
    //   patient_sn = Some(ptinfo.tj_patid.to_string());
    // }
    let profile = YibuProfile {
      //必需项目
      age: Some(medinfo.tj_age),
      classify: Some(BUSINESS_TYPE),
      gender: Some(ptinfo.tj_psex),
      patient_name: Some(ptinfo.tj_pname.to_string()),
      identity_number: Some(ptinfo.tj_pidcard.to_string()),
      number: Some(medinfo.tj_testid.to_string()),
      person_sn: Some(staffinfo.extsn.to_string()),
      person_name: Some(staffinfo.tj_staffname),
      bill_dept_sn: Some(DEPT_SN.to_string()),
      bill_dept_name: Some(DEPT_NAME.to_string()),
      //可选项目
      addr: Some(ptinfo.tj_paddress.to_string()),
      phone: Some(ptinfo.tj_pmobile.to_string()),
      enterprise_name: Some(corpname),
      birthday: Some(YibuKeji::get_birthdate(&ptinfo.tj_pbirthday.to_string())),
      // patient_sn,
      // email: todo!(),
      // work: todo!(),
      // state: todo!(),
      //default
      ..Default::default()
    };
    let yibu_server = format!("{}{}", server, YibuApi::MedexamProfile.to_string());
    info!("开始发送建档数据:{:?}", &profile);
    let ret: Result<YibuProfile> = YibuClient::send_http_post_request(&yibu_server, &Some(profile)).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("send create profile error: {}", ret.as_ref().unwrap_err().to_string()));
    }
    let result = ret.unwrap();
    // info!("create file response is:{:?}", &result);
    // if res.code != YIBU_CODE_OK {
    //   return Err(anyhow!("create error: {}", res.message));
    // }
    // let result = res.result.unwrap_or_default();
    if result.patient_sn.is_some() {
      // let mut patient = ptinfo.to_owned();
      ptinfo.tj_patid = result.patient_sn.unwrap_or_default().to_owned();
      let ret = TjPatient::save(&ptinfo, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("save patient error: {}", ret.unwrap_err().to_string());
      }
    }
    Ok("".to_string())
  }

  async fn send_fee_application(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, newitems: &Vec<ExtCheckiteminfo>, server: &String, db: &DbConnection) -> Result<String> {
    let mut feeitems: Vec<YibuFeeItem> = Vec::new();
    let mut total_amount = 0.0;
    let staffinfo = crate::SYSCACHE.get().unwrap().get_staff(medinfo.tj_recorder.parse().unwrap_or_default(), db).await;

    for item in newitems.into_iter() {
      // if item.depttype == DeptType::Check as i32 {
      //   continue;
      // }

      let mut porject_sn = item.itemid.to_string();
      if porject_sn.is_empty() {
        let ret = TjIteminfo::query(&item.itemid2, &db.get_connection()).await;
        if ret.is_ok() {
          let ret = ret.unwrap();
          if ret.is_some() {
            let iteminfo = ret.unwrap();
            porject_sn = iteminfo.tj_lisnum.to_string();
          }
        }
      }
      if porject_sn.is_empty() {
        continue;
      }
      let iteminfo = crate::SYSCACHE.get().unwrap().get_iteminfo(&item.itemid2, &db).await;
      total_amount += iteminfo.tj_itemprice;

      let feeitem = YibuFeeItem {
        source_sn: Some(item.tid.to_string()),
        project_sn: Some(porject_sn),
        project_name: Some(item.itemname.to_string()),
        num: Some(1),
        sale_unit: Some("次".to_string()),
        bookkeeping_time: Some(item.requestdate * 1000),
        bill_person_sn: Some(staffinfo.extsn.to_string()),
        bill_person_name: Some(item.requestername.to_string()),
        bill_dept_sn: Some(DEPT_SN.to_string()),
        bill_dept_name: Some(DEPT_NAME.to_string()),
        batch_number: Some("".to_string()),
        price: Some(iteminfo.tj_itemprice),
        payment: Some(iteminfo.tj_itemprice),
        specifications: Some("".to_string()),
      };

      feeitems.push(feeitem);
    }
    info!("收费项目信息:{:?}", &feeitems);
    if feeitems.len() > 0 {
      let paytype = match medinfo.tj_paymethod {
        2 => 2,
        _ => 1,
      };
      let enterprise = match paytype {
        2 => {
          let corpinfos = crate::SYSCACHE.get().unwrap().get_corpinfos(&vec![medinfo.tj_corpnum], &"".to_string(), &vec![], &db).await;
          if corpinfos.len() <= 0 {
            "".to_string()
          } else {
            corpinfos[0].tj_corpname.to_string()
          }
        }
        _ => "".to_string(),
      };
      if paytype == 2 && enterprise.is_empty() {
        return Err(anyhow!("没有找到企业信息"));
      }
      let feeapplication = YibuFee {
        system_sn: Some(SYSTEM_SN.to_string()),
        classify: Some(BUSINESS_TYPE),
        pay_type: Some(paytype),
        enterprise_name: Some(enterprise),
        number: Some(medinfo.tj_testid.to_string()),
        patient_sn: Some(ptinfo.tj_patid.to_string()),
        order_payment: Some(total_amount),
        self_payment: Some(total_amount),
        items_list: Some(feeitems),
        remark: Some("体检收费".to_string()),
        order_sn: Some("".to_string()),
        // ..Default::default()
      };

      // info!("发送收费信息");
      let yibu_server = format!("{}{}", server, YibuApi::MedexamFeeApplicaiton.to_string());
      // info!("fee applicaiton data:{:?}", &feeapplication);
      let ret: Result<YibuFee> = YibuClient::send_http_post_request(&yibu_server, &Some(feeapplication)).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let result = ret.unwrap();
      // info!("fee application: {:?}", &res);
      // if res.code != YIBU_CODE_OK {
      //   return Err(anyhow!("error: {}", res.message));
      // }
      // let result = res.result.unwrap_or_default();
      let mut medinfo = medinfo.to_owned();
      //更新订单号
      medinfo.tj_ordno = result.order_sn.unwrap_or_default().to_string();
      let ret = TjMedexaminfo::save(&medinfo, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("save medexaminfo error: {}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    Ok("".to_string())
  }

  async fn send_check_application(medinfo: &TjMedexaminfo, patient: &TjPatient, newitems: &mut Vec<ExtCheckiteminfo>, server: &String) -> Result<String> {
    //
    let mut checkitems: Vec<YibuCheckApplication> = vec![];
    let mut emptyitems: Vec<String> = vec![];
    for val in newitems.iter() {
      if val.depttype != crate::common::constant::DeptType::Function as i32 {
        continue;
      }
      if val.itemid.is_empty() {
        emptyitems.push(val.itemid2.to_string());
        continue;
      }
      let check = YibuCheckApplication {
        system_sn: Some(super::SYSTEM_SN.to_string()),
        source_sn: Some(val.tid.to_string()),
        code: Some(val.testid.to_string()),
        bar_code: Some(val.tid.to_string()),
        classify: Some(super::BUSINESS_TYPE),
        number: Some(val.testid.to_string()),
        patient_name: Some(val.testername.to_string()),
        gender: Some(val.psex),
        urgent: Some(2),
        check_type: Some(val.zdym.to_string()),
        items_sn: Some(val.itemid.to_string()),
        name: Some(val.itemname.to_string()),
        part_method: Some(val.itemname.to_string()),
        order_dept_name: Some(DEPT_SN.to_string()),
        order_person_name: Some(val.requestername.to_string()),
        order_time: Some(val.requestdate * 1000),
        patient_sn: Some(patient.tj_patid.to_string()),
        patient_number: Some(patient.tj_patid.to_string()),
        // id: todo!(),
        // sn: todo!(),
        order_sn: Some(medinfo.tj_ordno.to_string()),
        idcard: Some(patient.tj_pidcard.to_string()),
        birthday: Some(YibuKeji::get_birthdate(&patient.tj_pbirthday.to_string())),
        age: Some(medinfo.tj_age.to_string()),
        phone: Some(patient.tj_pphone.to_string()),
        // address: todo!(),
        // dept_sn: todo!(),
        // dept_name: todo!(),
        // area_sn: todo!(),
        // area_name: todo!(),
        // bed: todo!(),
        // icd: todo!(),
        // diagnosis: todo!(),
        // remark: todo!(),
        // order_dept_sn: todo!(),
        // order_person_sn: todo!(),
        // execute_dept_sn: todo!(),
        // execute_dept_name: todo!(),
        ..Default::default()
      };
      checkitems.push(check);
    }
    if emptyitems.len() > 0 {
      error!("以下检查项目未匹配代码:{}", emptyitems.join(","));
      // return Err(anyhow!("以下检查项目未匹配代码：{}", emptyitems.join(",")));
    }
    if checkitems.len() <= 0 {
      return Ok("".to_string());
    }
    // info!("开始发送检查数据信息......");
    let yibu_server = format!("{}{}", server, YibuApi::MedexamCheckApplication.to_string());
    let ret: Result<Vec<YibuCheckApplication>> = YibuClient::send_http_post_request(&yibu_server, &Some(checkitems)).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let res_data = ret.unwrap();
    // info!("check application: {:?}", &res_data);
    // if res.code != YIBU_CODE_OK {
    //   return Err(anyhow!("error: {}", res.message));
    // }
    // // let mut newitems = newitems.to_owned();
    // let res_data = res.result.unwrap_or_default();
    for val in res_data.into_iter() {
      // let val = val.clone();

      // if val.items_sn.is_none() {
      //   continue;
      // }
      let items_sn = val.items_sn.unwrap_or_default();
      if items_sn.is_empty() {
        continue;
      }
      // let itemid2 = items_sn.unwrap_or_default();
      let extsn = val.sn.unwrap_or_default();
      info!("ext sn is:{}", &extsn);

      // let _ = newitems.iter_mut().filter(|f| f.itemid2.eq_ignore_ascii_case(&items_sn)).map(|v| v.extsn = extsn.to_string());
      // info!("apply_sn is:{}", &val.apply_sn.clone().unwrap_or_default());
      // let itemid2 = val.items_sn.unwrap_or_default();
      for item in newitems.iter_mut() {
        if item.itemid2.eq_ignore_ascii_case(&items_sn) {
          item.extsn = extsn.clone();
        }
      }
    }

    Ok("".to_string())
  }

  async fn send_lis_application(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, newitems: &mut Vec<ExtCheckiteminfo>, server: &String) -> Result<String> {
    //发送检验申请的数据
    if newitems.len() <= 0 {
      return Ok("".to_string());
    }
    let mut emptyitems: Vec<String> = vec![];
    let mut lisitems: Vec<YibuLisApplication> = vec![];
    for val in newitems.iter() {
      if val.depttype != crate::common::constant::DeptType::Lab as i32 {
        continue;
      }
      if val.itemid.is_empty() {
        emptyitems.push(val.itemid2.clone());
        continue;
      }
      let lisapp = YibuLisApplication {
        system_sn: Some(super::SYSTEM_SN.to_string()),
        source_sn: Some(val.tid.to_string()),
        code: Some(val.tid.to_string()),
        bar_code: Some(val.tid.to_string()),
        classify: Some(super::BUSINESS_TYPE),
        number: Some(val.testid.to_string()),
        patient_name: Some(val.testername.to_string()),
        gender: Some(val.psex),
        urgent: Some(2),
        type_name: Some(val.sampletype.to_string()),
        items_sn: Some(val.itemid.to_string()),
        name: Some(val.itemname.to_string()),
        num: Some(1),
        patient_sn: Some(ptinfo.tj_patid.to_string()),
        patient_number: Some(ptinfo.tj_patid.to_string()),
        // id: todo!(),
        // apply_sn: todo!(),
        order_sn: Some(medinfo.tj_ordno.to_string()),
        // company: todo!(),
        idcard: Some(ptinfo.tj_pidcard.to_string()),
        birthday: Some(YibuKeji::get_birthdate(&ptinfo.tj_pbirthday)),
        age: Some(medinfo.tj_age.to_string()),
        phone: Some(ptinfo.tj_pphone.to_string()),
        // address: todo!(),
        // dept_sn: todo!(),
        // dept_name: todo!(),
        // area_sn: todo!(),
        // area_name: todo!(),
        // bed: todo!(),
        // icd: todo!(),
        // diagnosis: todo!(),
        // order_dept_sn: todo!(),
        // order_dept_name: todo!(),
        // order_doctor_sn: todo!(),
        // order_doctor_name: todo!(),
        // type_sn: todo!(),
        ..Default::default()
      };
      lisitems.push(lisapp);
    }
    if emptyitems.len() > 0 {
      error!("以下检验项目未匹配代码:{}", emptyitems.join(","));
      // return Err(anyhow!("以下项目未匹配代码:{}", emptyitems.join(",")));
    }
    if lisitems.len() <= 0 {
      return Ok("".to_string());
    }
    info!("开始发送检验数据信息");
    let yibu_server = format!("{}{}", server, YibuApi::MedexamLisApplication.to_string());
    let ret: Result<Vec<YibuLisApplication>> = YibuClient::send_http_post_request(&yibu_server, &Some(lisitems)).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(" {}", ret.as_ref().unwrap_err().to_string()));
    }
    let res = ret.unwrap();
    // info!("check application: {:?}", &res);
    // if res.code != YIBU_CODE_OK {
    //   return Err(anyhow!("error: {}", res.message));
    // }
    // let res = res.result.unwrap_or_default();
    for val in res.into_iter() {
      // if val.items_sn.is_none() {
      //   continue;
      // }
      info!("apply_sn is:{}", &val.apply_sn.clone().unwrap_or_default());
      let itemid = val.items_sn.unwrap_or_default();
      for item in newitems.iter_mut() {
        if item.itemid.eq_ignore_ascii_case(&itemid) {
          item.extsn = val.apply_sn.clone().unwrap_or_default();
        }
      }
      // let _ = newitems
      //   .iter_mut()
      //   .filter(|f| f.itemid2.eq_ignore_ascii_case(&itemid2))
      //   .map(|v| v.extsn = val.apply_sn.clone().unwrap_or_default());
    }
    Ok("".to_string())
  }

  async fn update_ext_checkiteminfos(newitems: &Vec<ExtCheckiteminfo>, db: &DbConnection) -> Result<String> {
    //do update
    info!("start to update extchecktiems:{:?}", &newitems);
    let ret = ExtCheckiteminfo::save_many(&newitems, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("update extcheckiteminfo error: {}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok("".to_string())
  }

  async fn send_fee_cancel_application(medinfo: &TjMedexaminfo, delitems: &Vec<ExtCheckiteminfo>, server: &String) -> Result<String> {
    //cancel fee
    let items = delitems.iter().filter(|&p| !p.itemid.is_empty()).map(|v| v.itemid.to_string()).collect::<Vec<String>>().join(",");
    let dto = YibuFeeCancel {
      order_sn: medinfo.tj_ordno.to_string(),
      project_sns: items,
    };
    info!("取消收费申请数据:{:?}", &dto);
    let yibu_server = format!("{}{}", server, YibuApi::MedexamFeeCancelApplicaiton.to_string());
    let ret: Result<YibuFeeCancelResponse> = YibuClient::send_http_post_request(&yibu_server, &Some(dto)).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("send lis cancel application error: {}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok("".to_string())
  }

  async fn send_check_cancel_application(delitems: &Vec<ExtCheckiteminfo>, server: &String) -> Result<String> {
    if delitems.len() <= 0 {
      return Ok("".to_string());
    }
    for val in delitems.into_iter() {
      if val.extsn.is_empty() {
        continue;
      }
      let cancel_dto = YibuCheckCancelApplication { sn: val.extsn.to_string() };
      let yibu_server = format!("{}{}", server, YibuApi::MedexamCheckCancelApplication.to_string());
      let ret: Result<Vec<YibuCheckApplication>> = YibuClient::send_http_post_request(&yibu_server, &Some(cancel_dto)).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("send check cancel application error: {}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    Ok("".to_string())
  }
  async fn send_lis_cancel_application(delitems: &Vec<ExtCheckiteminfo>, server: &String) -> Result<String> {
    if delitems.len() <= 0 {
      return Ok("".to_string());
    }
    for val in delitems.into_iter() {
      if val.extsn.is_empty() {
        continue;
      }
      let cancel_dto = YibuLisCancelApplication { apply_sn: val.extsn.to_string() };
      let yibu_server = format!("{}{}", server, YibuApi::MedexamLisCancelApplication.to_string());
      let ret: Result<Vec<YibuCheckApplication>> = YibuClient::send_http_post_request(&yibu_server, &Some(cancel_dto)).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("send lis cancel application error: {}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    Ok("".to_string())
  }

  fn get_birthdate(bth: &str) -> i64 {
    //
    let birthdate = utility::timeutil::convert_date_to_timestamp(bth, "");
    birthdate * 1000
  }
}
