use anyhow::{anyhow, Result};
use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjAudiogramrevise {
  pub id: i32,
  pub tj_freq: i32,
  pub tj_sex: i32,
  pub tj_startage: i32,
  pub tj_endage: i32,
  pub tj_revise: i32,
}
crud!(TjAudiogramrevise {}, "tj_audiogramrevise");

impl TjAudiogramrevise {
  pub async fn query_many(rb: &mut rbatis::RBatis) -> Result<Vec<TjAudiogramrevise>> {
    // let mut rb = db.get_connection_clone();
    let ret = TjAudiogramrevise::select_all(rb).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
