//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_hazardinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_tid: i32,
  #[sea_orm(unique)]
  pub tj_hname: String,
  pub tj_pyjm: String,
  pub tj_showorder: i32,
  pub tj_forbiden: String,
  pub tj_memo: String,
  pub tj_extcode: String,
  pub tj_status: i32,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
