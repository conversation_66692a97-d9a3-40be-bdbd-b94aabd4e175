use anyhow::{anyhow, Result};
use tracing::*;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use evalexpr::*;
// use rust_decimal::prelude::ToPrimitive;
// use std::collections::HashSet;

use crate::{
  common::constant::{self, Sex, YesOrNo},
  dto::{AutoSummaryDto, QuerySummaryDto, SummaryResponseDto},
  medexam::medexamsvc::MedexamSvc,
};

// use super::checkitemsvc::CheckitemSvc;

pub struct SummarySvc;

impl SummarySvc {
  pub async fn save_checkitem_results(infos: &Vec<TjCheckiteminfo>, db: &DbConnection) -> Result<()> {
    let ret = TjCheckiteminfo::save_many(&infos, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(())
  }

  pub async fn save_testsummary(dto: &TjTestsummary, db: &DbConnection) -> Result<i64> {
    //
    let ret = TjTestsummary::save(&dto, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let up_ret = MedexamSvc::update_medexam_status_updown(&dto.tj_testid, 0, 1, &db).await;
    if up_ret.as_ref().is_err() {
      error!("{}", up_ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", up_ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_testsummaries(dto: &QuerySummaryDto, db: &DbConnection) -> Result<Vec<TjTestsummary>> {
    let mut enddate = dto.enddate;
    if enddate > 0 {
      enddate = utility::timeutil::timestamp_add_days(enddate, 1);
    }
    // info!("end date is:{}", enddate);
    // let testids: Vec<&str> = dto.testids.iter().map(|v| v.as_str()).collect();
    // let deptids: Vec<&str> = dto.deptids.iter().map(|v| v.as_str()).collect();
    let ret = TjTestsummary::query_many(&dto.testids, &dto.deptids, dto.stdate, enddate, dto.status, &dto.staffid, dto.forceend, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let summaries = ret.unwrap();
    // summaries.sort_by(|a,b| a.)
    Ok(summaries)
  }

  pub async fn delete_testsummary(summary: &TjTestsummary, db: &DbConnection) -> Result<TjTestsummary> {
    info!("start to delete test summary......");
    let mut summary = summary.to_owned();
    if summary.tj_testid.is_empty() || summary.tj_deptid.is_empty() {
      return Err(anyhow!("testid and deptid are all empty, not allowed"));
    }
    if summary.id <= 0 {
      let ret = TjTestsummary::query_one(&summary.tj_testid, &summary.tj_deptid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let sum_ret = ret.unwrap();
      if sum_ret.is_none() {
        return Err(anyhow!("can't find testsummary"));
      }
      // summary.id = sum_ret.unwrap().id;
      summary = sum_ret.unwrap();
    }
    let mut default_summary = TjTestsummary { ..Default::default() };
    default_summary.tj_testid = summary.tj_testid.to_owned();
    default_summary.tj_deptid = summary.tj_deptid.to_owned();
    default_summary.tj_isfinished = constant::YesOrNo::No as i32;
    default_summary.id = summary.id;
    let ret = TjTestsummary::save(&default_summary, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    if summary.tj_isfinished == constant::YesOrNo::Yes as i32 {
      info!("删除小结，需要更新完成科室量......");
      let ret = TjMedexaminfo::query(&summary.tj_testid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let med_ret = ret.unwrap();
      if med_ret.is_none() {
        error!("can't find medexaminfo by testid:{}", &summary.tj_testid);
        return Err(anyhow!("can't find medexaminfo by testid:{}", &summary.tj_testid));
      }
      let mut medinfo = med_ret.unwrap();
      let ret = MedexamSvc::update_medexam_status(&mut medinfo, -1, 0, db).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    Ok(default_summary)
  }

  pub async fn auto_summary(dto: &AutoSummaryDto, staff: &TjStaffadmin, db: &DbConnection) -> SummaryResponseDto {
    info!("auto summary dto:{:?}", &dto);
    let mut response_dto = SummaryResponseDto {
      code: constant::YesOrNo::No as i32,
      msg: "".to_string(),
      ..Default::default()
    };
    if dto.medinfo.tj_testid.is_empty() {
      response_dto.msg = "没有体检信息，不能小结".to_string();
      return response_dto;
    }
    if dto.summary.tj_deptid.is_empty() {
      // return Err(anyhow!("没有需要小结的科室信息"));
      response_dto.msg = "没有需要小结的科室信息".to_string();
      return response_dto;
    }

    let testid = dto.medinfo.tj_testid.to_owned();
    let deptid = dto.summary.tj_deptid.to_owned();

    info!("需要小结的体检编号：{},小结科室：{}", &testid, &deptid);

    let mut summary = dto.summary.to_owned();

    if !staff.tj_staffname.is_empty() {
      if summary.tj_doctor.is_empty() {
        summary.tj_doctor = staff.tj_staffname.to_string();
      }
      if summary.tj_checkdoctor.is_empty() {
        summary.tj_checkdoctor = staff.tj_staffname.to_string();
      }
      if summary.tj_doctorid.is_empty() {
        summary.tj_doctorid = staff.tj_staffno.to_string();
      }
    }
    if summary.tj_date <= 0 {
      summary.tj_date = utility::timeutil::current_timestamp();
    }
    if summary.tj_checkdate <= 0 {
      summary.tj_checkdate = utility::timeutil::current_timestamp();
    }
    let mut patient_hazards = dto.pthazards.to_owned();
    let mut ciinfos = dto.ciinfos.to_owned();
    // info!("MEDINFOS:{:#?}", &medinfos);

    let ret = TjMedexaminfo::query(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      response_dto.msg = ret.as_ref().unwrap_err().to_string();
      return response_dto;
    }
    let exist_medinfo = ret.unwrap();
    if exist_medinfo.is_none() {
      response_dto.msg = "没有该体检号的体检信息".to_string();
      return response_dto;
    }
    let mut medinfo = exist_medinfo.unwrap();

    if ciinfos.len() <= 0 {
      let ret = TjCheckiteminfo::query_many(&vec![testid.clone()], &vec![deptid.clone()], -1, -1, &vec![], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        response_dto.msg = ret.as_ref().unwrap_err().to_string();
        return response_dto;
      }
      ciinfos = ret.unwrap();
      ciinfos.sort_by(|a, b| a.tj_synid.cmp(&b.tj_synid).then(a.tj_showorder.cmp(&b.tj_showorder)));
    }
    if ciinfos.len() <= 0 {
      // return Err(anyhow!("{}", "没有该科室检查信息,不能小结"));
      response_dto.msg = "没有该科室检查信息,不能小结".to_string(); //ret.as_ref().unwrap_err().to_string();
      return response_dto;
    }
    if ciinfos
      .iter()
      .find(|&f| f.tj_result.is_empty() && f.tj_combineflag == YesOrNo::No as i32 && f.tj_deptid.eq_ignore_ascii_case(&deptid))
      .is_some()
    {
      error!("没有完成所有项目的检查，不能小结");
      response_dto.msg = "没有完成所有项目的检查结果,不能小结".to_string(); //ret.as_ref().unwrap_err().to_string();
      if summary.tj_isfinished == YesOrNo::Yes as i32 {
        summary.tj_isfinished = YesOrNo::No as i32;
        summary.tj_doctor = "".to_string();
        summary.tj_date = 0;
        let ret = TjTestsummary::save(&summary, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          response_dto.msg = format!(
            "保存{}没有在科室{}的小结信息出错:{}",
            &medinfo.tj_testid,
            &summary.tj_deptid,
            ret.as_ref().unwrap_err().to_string()
          );
          return response_dto;
        }
      }
      return response_dto;
    }

    //检查小结信息
    let ret = TjTestsummary::query_one(&testid, &deptid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      response_dto.msg = ret.as_ref().unwrap_err().to_string();
      return response_dto;
    }
    let exist_summary = ret.unwrap();
    if exist_summary.is_none() {
      //有科室项目，但没有科室小结信息
      //save into database
      summary.id = 0;
      summary.tj_testid = testid.clone();
      let ret = TjTestsummary::save(&summary, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        response_dto.msg = format!(
          "保存{}没有在科室{}的小结信息出错:{}",
          &medinfo.tj_testid,
          &summary.tj_deptid,
          ret.as_ref().unwrap_err().to_string()
        );
        return response_dto;
      }
      summary.id = ret.unwrap();
    }
    let exist_summary = exist_summary.unwrap();
    if exist_summary.tj_isfinished == constant::YesOrNo::Yes as i32 && dto.ignore == constant::YesOrNo::Yes as i32 {
      response_dto.msg = "该体检者在该科室已经小结，忽略".to_string(); //ret.as_ref().unwrap_err().to_string();
      return response_dto;
    }
    summary.id = exist_summary.id;
    summary.tj_testid = testid.clone();
    summary.tj_isfinished = exist_summary.tj_isfinished;

    if patient_hazards.len() <= 0 {
      let ret = TjPatienthazards::query(&testid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        response_dto.msg = ret.as_ref().unwrap_err().to_string();
        return response_dto;
      }
      patient_hazards = ret.unwrap();
    }

    let mut med_ciinfos: Vec<TjCheckiteminfo> = ciinfos
      .iter_mut()
      .filter(|f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid) && f.tj_deptid.eq_ignore_ascii_case(&summary.tj_deptid))
      .map(|v| v.clone())
      .collect();

    let pthds = patient_hazards
      .iter()
      .filter(|f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid))
      .map(|v| v.clone())
      .collect();
    let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      response_dto.msg = ret.as_ref().unwrap_err().to_string();
      return response_dto;
    }
    let ptinfo = ret.unwrap().unwrap_or_default();
    let ret = SummarySvc::summary(&mut medinfo, &ptinfo, &mut summary, &mut med_ciinfos, &pthds, dto.saveci, dto.ignore, &dto.pacssum, &db).await;

    response_dto = ret;

    response_dto
  }

  pub async fn auto_summary_batch(dto: &AutoSummaryDto, db: &DbConnection) -> SummaryResponseDto {
    let mut response_dto = SummaryResponseDto {
      code: constant::YesOrNo::No as i32,
      msg: "".to_string(),
      ..Default::default()
    };
    if dto.medinfo.tj_testid.is_empty() {
      response_dto.msg = "没有体检信息，不能小结".to_string();
      return response_dto;
    }
    if dto.summary.tj_deptid.is_empty() {
      // return Err(anyhow!("没有需要小结的科室信息"));
      response_dto.msg = "没有需要小结的科室信息".to_string();
      return response_dto;
    }

    let deptids: Vec<String> = vec![dto.summary.tj_deptid.to_owned()]; //dto.summaries.iter().map(|v| v.tj_deptid.clone()).collect();
    let testids: Vec<String> = vec![dto.medinfo.tj_testid.to_owned()]; //dto.medinfos.iter().map(|v| v.tj_testid.clone()).collect();

    info!("需要小结的体检编号：{:?},小结科室：{:?}", &testids, &deptids);
    // let mut medinfos = dto.medinfos.to_owned();
    let mut summaries = vec![dto.summary.to_owned()];
    // let mut summary = dto.summary.to_owned();
    let mut patient_hazards = dto.pthazards.to_owned();
    let mut ciinfos = dto.ciinfos.to_owned();
    // info!("MEDINFOS:{:#?}", &medinfos);

    let ret = TjMedexaminfo::query_many(0, 0, &testids, &vec![], 0, -1, &vec![], &vec![], &vec![], -1, &"".to_string(), &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      response_dto.msg = ret.as_ref().unwrap_err().to_string();
      return response_dto;
    }
    let mut medinfos = ret.unwrap();

    let pids: Vec<String> = medinfos.iter().map(|v| v.tj_pid.clone()).collect();

    if ciinfos.len() <= 0 {
      let ret = TjCheckiteminfo::query_many(&testids, &deptids, -1, -1, &vec![], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        response_dto.msg = ret.as_ref().unwrap_err().to_string();
        return response_dto;
      }
      ciinfos = ret.unwrap();
      ciinfos.sort_by(|a, b| a.tj_synid.cmp(&b.tj_synid).then(a.tj_showorder.cmp(&b.tj_showorder)));
    }
    if ciinfos.len() <= 0 {
      // return Err(anyhow!("{}", "没有该科室检查信息,不能小结"));
      response_dto.msg = "没有该科室检查信息,不能小结".to_string(); //ret.as_ref().unwrap_err().to_string();
      return response_dto;
    }
    // if ciinfos.iter().find(|&f| f.tj_result.is_empty() && f.tj_combineflag == YesOrNo::No as i32).is_some() {
    //   response_dto.msg = "没有完成所有项目的检查结果,不能小结".to_string(); //ret.as_ref().unwrap_err().to_string();
    //   summary.tj_isfinished = YesOrNo::No as i32;
    //   summary.tj_doctor = "".to_string();
    //   summary.tj_date = 0;
    //   let ret = TjTestsummary::save(&summary, &db.get_connection()).await;
    //   if ret.as_ref().is_err() {
    //     response_dto.msg = format!(
    //       "保存{}没有在科室{}的小结信息出错:{}",
    //       &medinfo.tj_testid,
    //       &summary.tj_deptid,
    //       ret.as_ref().unwrap_err().to_string()
    //     );
    //     return response_dto;
    //   }
    //   return response_dto;
    // }

    //先检查小结信息
    let ret = TjTestsummary::query_many(&testids, &deptids, 0, 0, -1, "", -1, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      response_dto.msg = ret.as_ref().unwrap_err().to_string();
      return response_dto;
    }
    let exist_summaries = ret.unwrap();
    if exist_summaries.len() <= 0 {
      // return Err(anyhow!("没有科室小结信息"));
      response_dto.msg = "没有科室小结信息".to_string(); //ret.as_ref().unwrap_err().to_string();
      return response_dto;
    }

    if patient_hazards.len() <= 0 {
      let ret = TjPatienthazards::query_many(&testids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        response_dto.msg = ret.as_ref().unwrap_err().to_string();
        return response_dto;
      }
      patient_hazards = ret.unwrap();
    }

    let ret = TjPatient::query_many(&pids, "", &vec![], &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      response_dto.msg = ret.as_ref().unwrap_err().to_string();
      return response_dto;
    }
    let ptinfos = ret.unwrap();
    let mut summary_response: Vec<SummaryResponseDto> = Vec::new();
    for medinfo in medinfos.iter_mut() {
      for summary in summaries.iter_mut() {
        summary.id = 0;
        summary.tj_isfinished = constant::YesOrNo::No as i32;
        summary.tj_testid = medinfo.tj_testid.to_owned();

        let mut med_ciinfos: Vec<TjCheckiteminfo> = ciinfos
          .iter_mut()
          .filter(|f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid) && f.tj_deptid.eq_ignore_ascii_case(&summary.tj_deptid))
          .map(|v| v.clone())
          .collect();

        let exist_summary = exist_summaries
          .iter()
          .find(|f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid) && f.tj_deptid.eq_ignore_ascii_case(&summary.tj_deptid));

        if exist_summary.is_none() && med_ciinfos.len() <= 0 {
          response_dto.msg = format!("{}没有在科室{}的体检信息", &medinfo.tj_testid, &summary.tj_deptid);
          let sum_result: SummaryResponseDto = SummaryResponseDto {
            code: constant::YesOrNo::No as i32,
            msg: format!("{}没有在科室{}的体检信息", &medinfo.tj_testid, &summary.tj_deptid),
            ..Default::default()
          };
          summary_response.push(sum_result);
          continue;
        }

        if exist_summary.is_none() && med_ciinfos.len() > 0 {
          //有科室项目，但没有科室小结信息
          //save into database
          let ret = TjTestsummary::save(&summary, &db.get_connection()).await;
          if ret.as_ref().is_err() {
            response_dto.msg = format!(
              "保存{}没有在科室{}的小结信息出错:{}",
              &medinfo.tj_testid,
              &summary.tj_deptid,
              ret.as_ref().unwrap_err().to_string()
            );
            let sum_result: SummaryResponseDto = SummaryResponseDto {
              code: constant::YesOrNo::No as i32,
              msg: format!(
                "保存{}没有在科室{}的小结信息出错:{}",
                &medinfo.tj_testid,
                &summary.tj_deptid,
                ret.as_ref().unwrap_err().to_string()
              ),
              ..Default::default()
            };
            summary_response.push(sum_result);
            continue;
          }
          summary.id = ret.unwrap();
        }
        if exist_summary.is_some() {
          let exist_summary = exist_summary.unwrap();
          summary.id = exist_summary.id;
          summary.tj_isfinished = exist_summary.tj_isfinished;
        }

        let pthds = patient_hazards
          .iter()
          .filter(|f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid))
          .map(|v| v.clone())
          .collect();
        let ptinfo = ptinfos
          .iter()
          .find(|f| f.tj_pid.eq_ignore_ascii_case(&medinfo.tj_pid))
          .map_or(TjPatient { ..Default::default() }, |v| v.to_owned());
        let ret = SummarySvc::summary(medinfo, &ptinfo, summary, &mut med_ciinfos, &pthds, dto.saveci, dto.ignore, &dto.pacssum, &db).await;
        // response_dto.msg = "小结成功".to_string();
        // response_dto.code = constant::YesOrNo::Yes as i32;
        response_dto = ret;
        // summary_response.push(ret);
      }
    }

    info!("Final summary result:{:#?}", &response_dto);

    // return summary_response[0];
    response_dto
    // response_dto.code = constant::YesOrNo::Yes;
    // response_dto.msg = ret.as_ref().unwrap_err().to_string();
    // Ok(summary_response)
  }

  pub async fn summary(
    medinfo: &mut TjMedexaminfo,
    ptinfo: &TjPatient,
    summary: &mut TjTestsummary,
    ciinfos: &mut Vec<TjCheckiteminfo>,
    pthds: &Vec<TjPatienthazards>,
    saveci: i32,
    ignore: i32,
    pacssum: &String,
    // staff: &TjStaffadmin,
    db: &DbConnection,
  ) -> SummaryResponseDto {
    let mut sum_result: SummaryResponseDto = SummaryResponseDto {
      code: constant::YesOrNo::No as i32,
      msg: "".to_string(),
      ..Default::default()
    };

    // info!("科室小结信息:{:#?}", &summary);
    let new_summary = summary.tj_isfinished == constant::YesOrNo::No as i32;
    if summary.tj_isfinished == constant::YesOrNo::Yes as i32 && ignore == constant::YesOrNo::Yes as i32 {
      info!("{} {}", medinfo.tj_testid, "忽略已经小结的体检信息......");
      sum_result.msg = "该体检人员已经小结，忽略".to_string();
      sum_result.medinfo = medinfo.to_owned();
      sum_result.summary = summary.to_owned();
      return sum_result;
    }
    let testids: Vec<String> = vec![medinfo.tj_testid.clone()];
    let deptids: Vec<String> = vec![summary.tj_deptid.clone()];

    let diag_conditions = crate::SYSCACHE.get().unwrap().get_autodiag_conditions(&vec![], &db).await;
    let diseases = crate::SYSCACHE.get().unwrap().get_diseases(&vec![], &vec![], &vec![], &db).await;
    // let itemids = ciinfos.iter().map(|f| f.tj_itemid.clone()).collect::<Vec<String>>();
    // let empty_ids: Vec<i64> = vec![];
    // let empty_str_ids: Vec<String> = vec![];
    // let ret = TjAutodiagcondition::query_many( &empty_ids, &empty_str_ids).await;
    // if ret.as_ref().is_err() {
    //   sum_result.msg = ret.as_ref().unwrap_err().to_string();
    //   return sum_result;
    // }
    // let diag_conditions = ret.unwrap();
    // let disids: Vec<i64> = diag_conditions.iter().map(|v| v.tj_disid).collect::<HashSet<_>>().into_iter().collect();
    // let ret = TjDiseases::query_many( &empty_ids).await;
    // if ret.as_ref().is_err() {
    //   sum_result.msg = ret.as_ref().unwrap_err().to_string();
    //   return sum_result;
    // }
    // let diseases = ret.unwrap();

    summary.tj_suggestion = "".to_string();
    summary.tj_summary = "".to_string();
    //开始自动小结，先删除这些人的所有疾病信息
    // info!("开始删除疾病信息......");
    let ret = TjDiseaseinfo::delete(&testids, &deptids, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      sum_result.msg = ret.as_ref().unwrap_err().to_string();
      sum_result.medinfo = medinfo.to_owned();
      sum_result.summary = summary.to_owned();
      return sum_result;
    }

    if ciinfos.iter().find(|&f| f.tj_result.is_empty() && f.tj_combineflag == YesOrNo::No as i32).is_some() {
      sum_result.msg = "没有完成所有项目的检查结果,不能小结".to_string(); //ret.as_ref().unwrap_err().to_string();
      if summary.tj_isfinished == YesOrNo::Yes as i32 {
        summary.tj_isfinished = YesOrNo::No as i32;
        summary.tj_doctor = "".to_string();
        summary.tj_date = 0;
        let ret = TjTestsummary::save(&summary, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          sum_result.msg = format!(
            "保存{}没有在科室{}的小结信息出错:{}",
            &medinfo.tj_testid,
            &summary.tj_deptid,
            ret.as_ref().unwrap_err().to_string()
          );
          return sum_result;
        }
      }
      return sum_result;
    }

    // if summary.tj_forceend == constant::YesOrNo::Yes as i32 {
    //   log::info
    //   //强制结束
    //   sum_result.code = constant::YesOrNo::Yes as i32;
    //   sum_result.msg = "自动小结完成".to_string();
    //   sum_result.medinfo = medinfo.to_owned();
    //   sum_result.summary = summary.to_owned();
    //   sum_result.disinfos = vec![];
    //   return sum_result;
    // }

    let ret = SummarySvc::auto_summary_by_department(&diag_conditions, &diseases, &medinfo, &ptinfo, summary, ciinfos, &pthds, &pacssum, &db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      sum_result.msg = ret.as_ref().unwrap_err().to_string();
      sum_result.medinfo = medinfo.to_owned();
      sum_result.summary = summary.to_owned();
      return sum_result;
    }
    let disinfos = ret.unwrap();
    info!("自动小结后的疾病信息：{:?}", &disinfos);
    if disinfos.len() > 0 {
      // info!("start to save disease infos:{:?}", &disinfos);
      let ret = TjDiseaseinfo::save_many(&disinfos, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        sum_result.msg = ret.as_ref().unwrap_err().to_string();
        sum_result.medinfo = medinfo.to_owned();
        sum_result.summary = summary.to_owned();
        sum_result.disinfos = disinfos.to_owned();
        return sum_result;
      }
    }
    info!("保存自动小结信息:{:?}", &summary);
    // summary.tj_doctor = staff.tj_staffname.to_string();
    // summary.tj_checkdoctor = staff.tj_staffname.to_string();
    // summary.tj_doctorid = staff.tj_staffno.to_string();
    // summary.tj_date = utility::timeutil::current_timestamp();
    // summary.tj_checkdate = utility::timeutil::current_timestamp();
    let ret = TjTestsummary::save(&summary, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      sum_result.msg = ret.as_ref().unwrap_err().to_string();
      sum_result.medinfo = medinfo.to_owned();
      sum_result.summary = summary.to_owned();
      sum_result.disinfos = disinfos.to_owned();
      return sum_result;
    }
    // info!("开始保存结果信息......");
    //保存结果
    if saveci == constant::YesOrNo::Yes as i32 {
      //保存结果
      ciinfos.iter_mut().for_each(|v| {
        v.tj_checkdate = summary.tj_date;
        v.tj_checkdoctor = summary.tj_doctor.clone();
        v.tj_recheckdoctor = summary.tj_checkdoctor.clone();
        v.tj_recheckdate = summary.tj_checkdate;
      });
      let ret = TjCheckiteminfo::save_many(&ciinfos, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        sum_result.msg = ret.as_ref().unwrap_err().to_string();
        sum_result.medinfo = medinfo.to_owned();
        sum_result.summary = summary.to_owned();
        return sum_result;
      }
    }
    //新小结
    if new_summary || medinfo.tj_total - medinfo.tj_completed <= 4 {
      //update medexam status......
      info!("更新体检状态:{:?}", &medinfo);
      let ret = MedexamSvc::update_medexam_status(medinfo, 1, 0, &db).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        sum_result.msg = ret.as_ref().unwrap_err().to_string();
        sum_result.medinfo = medinfo.to_owned();
        sum_result.summary = summary.to_owned();
        sum_result.disinfos = disinfos.to_owned();
        return sum_result;
      }
      // info!("更新状态后的体检信息：{:#?}", &medinfo);
      // let updated_medinfo = ret.unwrap();
      // medinfo.tj_checkstatus = updated_medinfo.tj_checkstatus;
      // medinfo.tj_completed = updated_medinfo.tj_completed;
      // medinfo.tj_total = updated_medinfo.tj_total;
      // medinfo.tj_testdate = updated_medinfo.tj_testdate;
    }

    sum_result.code = constant::YesOrNo::Yes as i32;
    sum_result.msg = "自动小结完成".to_string();
    sum_result.medinfo = medinfo.to_owned();
    sum_result.summary = summary.to_owned();
    sum_result.disinfos = disinfos;

    // info!("Final summary result:{:#?}", &sum_result);
    sum_result
  }

  pub async fn auto_summary_by_department(
    diag_conditions: &Vec<TjAutodiagcondition>,
    diseases: &Vec<TjDiseases>,
    medinfo: &TjMedexaminfo,
    ptinfo: &TjPatient,
    summary: &mut TjTestsummary,
    ciinfos: &mut Vec<TjCheckiteminfo>,
    pthds: &Vec<TjPatienthazards>,
    pacssum: &String,
    db: &DbConnection,
  ) -> Result<Vec<TjDiseaseinfo>> {
    // let dptinfo = crate::SYSCACHE.get().unwrap().get_department(&summary.tj_deptid, &db).await;
    // if dptinfo.tj_depttype
    // for ci in ciinfos.iter_mut() {
    //   if ci.tj_abnormalflag == YesOrNo::No as i32 {
    //     CheckitemSvc::update_checktiem_result_flag(ci, &db).await;
    //   }
    // }

    //开始小结
    let mut ptdiseases: Vec<TjDiseaseinfo> = Vec::new();
    for dis in diseases.iter() {
      // info!("----> 开始自动诊断疾病：ID: {} Name: {}", &dis.id, &dis.tj_disname);
      //每个疾病进行诊断
      let mut temp_cond: Vec<&TjAutodiagcondition> = diag_conditions.iter().filter(|&f| f.tj_disid == dis.id).map(|v| v).collect();
      if temp_cond.len() <= 0 {
        continue;
      }
      if dis.tj_sex != Sex::All as i32 && dis.tj_sex != Sex::Unknown as i32 && dis.tj_sex != ptinfo.tj_psex {
        //性别限制
        continue;
      }
      // if !dis.tj_disname.eq_ignore_ascii_case("脂肪肝") {
      //   // info!("开始诊断脂肪肝的疾病信息......");
      //   continue;
      // }
      let eval_result = SummarySvc::diag_disease(&mut temp_cond, &medinfo, ciinfos, &pthds, &pacssum, &db).await;
      if eval_result {
        info!("😄 诊断疾病信息成立, ID: {} Name:{}", &dis.id, &dis.tj_disname);
        let is_exist = ptdiseases.iter().find(|f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid) && f.tj_disid == dis.id);
        if is_exist.is_none() {
          let ptdis: TjDiseaseinfo = TjDiseaseinfo {
            id: 0,
            tj_testid: medinfo.tj_testid.to_owned(),
            tj_disid: dis.id,
            tj_diseasenum: dis.tj_disnum.to_owned(),
            tj_diseasename: dis.tj_disname.to_owned(),
            tj_suggestion: dis.tj_content.to_owned(),
            tj_deptid: summary.tj_deptid.to_owned(),
            tj_isdisease: 1,
            tj_isoccu: dis.tj_occudiseaseflag.to_owned(),
            tj_typeid: dis.tj_typeid.to_owned(),
            tj_opinion: dis.tj_opinion.to_owned(),
            tj_showorder: dis.id,
          };
          ptdiseases.push(ptdis);
        }
      }
    }

    summary.tj_isfinished = constant::YesOrNo::Yes as i32;
    summary.tj_forceend = constant::YesOrNo::No as i32;
    let sum_val: Vec<String> = ciinfos
      .iter()
      .filter(|f| f.tj_deptid.eq(&summary.tj_deptid) && f.tj_testid.eq(&medinfo.tj_testid) && f.tj_abnormalflag == constant::YesOrNo::Yes as i32)
      .enumerate()
      .map(|(_idx, v)| format!("{}: {} {}", v.tj_itemname.to_owned(), v.tj_result.to_owned(), v.tj_abnormalshow.to_owned()))
      .collect();

    if sum_val.len() > 0 {
      summary.tj_summary = sum_val.join("\n");
    }
    if !pacssum.is_empty() {
      summary.tj_summary = pacssum.to_owned();
    }

    let sug_val: Vec<String> = ptdiseases
      .iter()
      .filter(|f| f.tj_deptid.eq(&summary.tj_deptid) && f.tj_testid.eq(&medinfo.tj_testid))
      .map(|v| format!("【{}】:{}", v.tj_diseasename.to_owned(), v.tj_suggestion.to_owned()))
      .collect();
    summary.tj_suggestion = sug_val.join("\n");

    let is_uncheck = ciinfos
      .iter()
      .find(|&p| !p.tj_result.eq_ignore_ascii_case("未检") && p.tj_combineflag == YesOrNo::No as i32)
      .is_some();

    if !is_uncheck {
      summary.tj_summary = "未检".to_string();
    }
    if summary.tj_summary.is_empty() {
      summary.tj_summary = "未见异常".to_string();
      summary.tj_suggestion = "".to_string();
    }

    // info!("自动小结结束，summary:{:#?}", &summary);
    Ok(ptdiseases)
  }

  pub async fn query_diseaseinfos(testids: &Vec<String>, deptids: &Vec<String>, db: &DbConnection) -> Result<Vec<TjDiseaseinfo>> {
    if testids.len() <= 0 {
      return Err(anyhow!("testid is empty, not allowed"));
    }
    // let testids: Vec<> = testids.iter().map(|v| v.as_str()).collect();
    // let deptids: Vec<&str> = deptids.iter().map(|v| v.as_str()).collect();
    let ret = TjDiseaseinfo::query_many(&testids, &deptids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_diseaseinfos(info: &Vec<TjDiseaseinfo>, db: &DbConnection) -> Result<i64> {
    info!("disease infos to be saved:{:?}", &info);
    let ret = TjDiseaseinfo::save_many(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_or_update_diseaseinfos(info: &mut TjDiseaseinfo, db: &DbConnection) -> Result<i64> {
    if info.id <= 0 {
      let ret = TjDiseaseinfo::query_by_testid_id(&info.tj_testid, info.tj_disid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let disret = ret.unwrap();
      info!("update disret result:{:?}", &disret);
      if disret.is_some() {
        info.id = disret.unwrap().id;
      } else {
        info.id = 0;
      }
    }
    let ret = TjDiseaseinfo::save(info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn delete_diseaseinfos_by_dto(testids: &Vec<String>, deptids: &Vec<String>, disid: &i64, db: &DbConnection) -> Result<u64> {
    // if testids.len() <= 0 && id <= 0 {
    //   return Err(anyhow!("ids are empty, not allowed"));
    // }
    // let testids: Vec<&str> = testids.iter().map(|v| v.as_str()).collect();
    // let deptids: Vec<&str> = deptids.iter().map(|v| v.as_str()).collect();
    let ret = TjDiseaseinfo::delete(&testids, &deptids, &vec![disid.to_owned()], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn delete_diseaseinfos(ids: &Vec<i64>, db: &DbConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let ret = TjDiseaseinfo::delete_by_ids(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn diag_disease(
    temp_cond: &mut Vec<&TjAutodiagcondition>,
    medinfo: &TjMedexaminfo,
    ciinfos: &mut Vec<TjCheckiteminfo>,
    pthds: &Vec<TjPatienthazards>,
    pacssum: &String,
    db: &DbConnection,
  ) -> bool {
    //排序
    // info!("全部疾病诊断条件:{:?}", &temp_cond);
    let mut abnormal_items: Vec<TjCheckiteminfo> = Vec::new();
    temp_cond.sort_by(|a, b| a.tj_order.cmp(&b.tj_order).then(b.tj_conditionsymbol.cmp(&a.tj_conditionsymbol)));
    // info!("排序后的诊断条件:{:?}", &temp_cond);
    let mut eval_str = "".to_string();
    // let mut itemids: Vec<String> = Vec::new();
    for cond in temp_cond.into_iter() {
      // info!("条件:{:?}", &cond);
      //1.毒害因素
      eval_str.push_str(&format!(" ( {} ", SummarySvc::get_hazard_result(&pthds, cond.tj_hazardfactor).as_str()));
      //2. 再判断体检类别
      if cond.tj_metype != 0 {
        eval_str.push_str(&format!(" && ( {} == {} ) ", medinfo.tj_testtype, cond.tj_metype));
      }
      //3. 最后是体检项目
      if cond.tj_itemid.eq_ignore_ascii_case(constant::SUMMARY_ITEMCODE) {
        // if cond.tj_disid == 128 {
        //   info!("pacssum:{:?}", &pacssum);
        //   info!("tj_operator:{:?}", &cond.tj_operator);
        //   info!("tj_refvalue:{:?}", &cond.tj_refvalue);
        // }
        eval_str.push_str(&format!(" && {} ) ", SummarySvc::get_compute_result(pacssum, &cond.tj_operator, &cond.tj_refvalue)));
      } else {
        let ciinfo = ciinfos.iter_mut().find(|f| f.tj_itemid.eq_ignore_ascii_case(&cond.tj_itemid));
        let mut sum_result = "false".to_string();
        if ciinfo.is_some() {
          let iteminfo = ciinfo.unwrap();
          let result = iteminfo.tj_result.to_lowercase().to_string(); //.as_str();
          let refval = cond.tj_refvalue.to_lowercase().to_string();

          sum_result = SummarySvc::get_compute_result(&result, &cond.tj_operator, &refval);
          if sum_result.eq_ignore_ascii_case("true") {
            info!(
              "自动诊断时得到true的结果，itemid:{}, result:{}，operator:{},refvalue:{}",
              &cond.tj_itemid, result, &cond.tj_operator, &cond.tj_refvalue
            );
            // iteminfo.tj_abnormalflag = constant::YesOrNo::Yes as i32;
            abnormal_items.push(iteminfo.to_owned());
            // }
          }
        }
        eval_str.push_str(&format!(" && {} ) ", sum_result));
        // eval_str.push_str(&format!(" && {} ) ", SummarySvc::get_compute_result(result, &cond.tj_operator, &cond.tj_refvalue)));
      }

      //4. 根据并且/或者，添加连接符
      if !cond.tj_conditionsymbol.is_empty() && !cond.tj_conditionsymbol.eq_ignore_ascii_case("null") {
        eval_str.push_str(&format!("{}", SummarySvc::get_and_or(&cond.tj_conditionsymbol)));
      }
    }
    // if temp_cond.len() > 0 && temp_cond[0].tj_disid == 128 {
    //   info!("诊断条件:{:?}", &eval_str);
    // }
    let ret = eval(&eval_str).unwrap_or(evalexpr::Value::from(false)).as_boolean().unwrap_or(false);
    if ret {
      info!("成立的条件：{}", &eval_str);
      if abnormal_items.len() > 0 {
        info!("满足诊断条件的项目是：{:?}", &abnormal_items);
        abnormal_items.iter_mut().for_each(|f| f.tj_abnormalflag = constant::YesOrNo::Yes as i32);
        ciinfos.iter_mut().for_each(|f| {
          if abnormal_items.iter().find(|v| v.tj_itemid.eq_ignore_ascii_case(&f.tj_itemid)).is_some() {
            f.tj_abnormalflag = constant::YesOrNo::Yes as i32
          }
        });

        let ret = TjCheckiteminfo::save_many(&abnormal_items, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().unwrap_err().to_string());
          return false;
        }
      }
    }
    ret
  }
  fn get_hazard_result(hdinfos: &Vec<TjPatienthazards>, hid: i64) -> String {
    if hid <= 0 {
      return "true".to_string();
    }

    let ret = hdinfos.iter().find(|f| f.tj_hid == hid).map_or("false", |_| "true");

    ret.to_string()
  }
  pub fn get_compute_result(result: &str, op: &str, refval: &str) -> String {
    let ret = match op.to_lowercase().as_str() {
      "like" => result.contains(refval).to_string(),
      // "like" => format!("str::regex_matches(\"{}\", \"(\\s|^){}(\\s|$)\")", result, refval),
      "nlike" => (!result.contains(refval)).to_string(),
      &_ => {
        let ret = result.parse::<f64>();
        if ret.as_ref().is_err() {
          return "false".to_string();
        }
        let f_result = ret.unwrap_or_default();
        let ret_str = format!("( {} {} {} )", f_result, op, refval);
        ret_str
      }
    };
    ret
  }
  fn get_and_or(op: &str) -> String {
    match op.to_lowercase().as_str() {
      "and" => "&&".to_owned(),
      "or" => "||".to_owned(),
      &_ => "".to_owned(),
    }
  }
}
