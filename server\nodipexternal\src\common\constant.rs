use strum::{AsRefStr, Display, EnumString, IntoStaticStr};

pub const EMPTY_STR: &str = "";
// pub const MESSAGE_OK: &str = "";

// pub const IGNORE_ROUTES: [&str; 3] = ["/api/ping", "/api/v1/login", "/api/pinyin"];
// pub(crate) const TOKEN: &str = "icryrainix";

#[derive(PartialEq, Clone, Debug)]
pub enum HttpCode {
  OK = 200,
  Error = 201,
}

#[derive(PartialEq)]
pub enum DatabaseType {
  MySql = 1,
  // Postgress = 2,
}

#[derive(Debug, Eq, Display, PartialEq, EnumString, AsRefStr, IntoStaticStr)]
pub enum MessageCode {
  #[strum(to_string = "ADT")]
  ADT,
  #[strum(to_string = "DFT")]
  DFT,
  #[strum(to_string = "ORM")]
  ORM,
  #[strum(to_string = "MFN")]
  MFN,
  #[strum(to_string = "ORU")]
  ORU,
  #[strum(to_string = "ORC")]
  ORC,
  #[strum(to_string = "OBR")]
  OBR,
  #[strum(to_string = "OBX")]
  OBX,
  #[strum(to_string = "ACK")]
  ACK,
  #[strum(to_string = "ORR")]
  ORR,
}

#[derive(Debug, Eq, Display, PartialEq, EnumString, AsRefStr, IntoStaticStr)]
pub enum EventCode {
  #[strum(to_string = "A01")]
  A01,
  #[strum(to_string = "A04")]
  A04,
  #[strum(to_string = "P03")]
  P03,
  #[strum(to_string = "O01")]
  O01,
  #[strum(to_string = "O02")]
  O02,
  #[strum(to_string = "R01")]
  R01,
}
