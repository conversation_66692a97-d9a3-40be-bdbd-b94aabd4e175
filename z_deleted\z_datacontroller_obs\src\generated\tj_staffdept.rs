//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, <PERSON>ialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_staffdept")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_staffid: i32,
    pub tj_deptid: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::tj_staffadmin::Entity",
        from = "Column::TjStaffid",
        to = "super::tj_staffadmin::Column::Id",
        on_update = "Restrict",
        on_delete = "Restrict"
    )]
    TjStaffadmin,
}

impl Related<super::tj_staffadmin::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjStaffadmin.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
