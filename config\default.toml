[database]
# url = "mysql://inxlion01:inxlion01@123!A@*************:41787/nodip_new"
username = "joedev"
password = "JoeSong0406!"
dbserver = "*************"
port = 8306
dbname = "nodip_new"
dbdriver = "mysql"

[application]
apphost = "0.0.0.0"
appport = 8012
enableauth = 1            # 是否启动认证 0：不启用， 1：启用
logfile = "./log/ego.log"
production = 0            #0表示开发测试 1：表示线上
areaprovince = "33"
uploader = "zj"           # 上报平台 默认是zj，gj：国家平台
pacsversion = "v1"        #pacs对接 v1:没有sfyc字段 v2：有sfyc字段

[cdcupload]
creditcode = "913303214111111"
agentId = "pwc0LtEF4"
appKey = "15fe1c1cff16d1cd70cF"
appSecret = "83215e658d33639928e1c496afb1aa44d76d12fa41662de133f63cfc49d560fb"
isSubmit = 1
rpturl = "https://oapi.zyws.net/api/physical/report"
filerul = "https://oapi.zyws.net/api/physical/reportFile"


[external]
exttype = ""
serverurl = "http://*************:8181/pinto-web/mvc"
appsn = "123456778"

[extlis]
[extlis.default]
#数据库类型 1：oracle 2：sqlserver 3:mysql
dbtype = 3
servicename = "dams"
dbserver = "*************"
dbport = 8306
username = "joedev"
password = "JoeSong0406!"

[extpacs]
[extpacs.default]
#数据库类型 1：oracle 2：sqlserver
dbtype = 3
servicename = "dams"
dbserver = "*************"
dbport = 8306
username = "joedev"
password = "JoeSong0406!"
