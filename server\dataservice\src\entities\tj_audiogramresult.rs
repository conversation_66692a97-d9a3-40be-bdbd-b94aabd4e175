//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, PartialEq,Default, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_audiogramresult")]
pub struct Model {
  #[sea_orm( primary_key)]
  pub id: i64,
  pub tj_testid: String,
  pub tj_avgsgp: f32,
  pub tj_avgyyp: f32,
  pub tj_avgzyp: f32,
  pub tj_avgsyp: f32,
  pub tj_avgygp: f32,
  pub tj_avgzgp: f32,
  pub tj_avgyty: f32,
  pub tj_avgzty: f32,
  pub tj_avgsty: f32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
