from sqlalchemy import (
    Column,
    Integer,
    String,
)
from dataentities.dbconn import Base


class TjMedexaminfo(Base):
    # 数据库中存储的表名
    __tablename__ = "tj_medexaminfo"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    tj_testid = Column(String(64), nullable=False)
    tj_age = Column(Integer, nullable=False)
    tj_pid = Column(String(64), nullable=False, unique=True)
    tj_empid = Column(String(128), nullable=False)
    tj_testcat = Column(Integer, nullable=False)
    tj_workage = Column(String(128), nullable=False)
    tj_testsource = Column(Integer, nullable=False)
    tj_testtype = Column(Integer, nullable=False)
    tj_corpnum = Column(Integer, nullable=False)
    tj_wtcode = Column(String(256), nullable=False)
    tj_worktype = Column(String(256), nullable=False)
    tj_poisionfactor = Column(String(512), nullable=False)
    tj_poisionage = Column(String(256), nullable=False)
    tj_recorddate = Column(Integer, nullable=False)
    tj_recorder = Column(String(256), nullable=False)
    tj_testdate = Column(Integer, nullable=False)
    tj_expdate = Column(Integer, nullable=False)
    tj_subdate = Column(Integer, nullable=False)
    tj_completed = Column(Integer, nullable=False)
    tj_total = Column(Integer, nullable=False)
    tj_checkstatus = Column(Integer, nullable=False)
    tj_printflag = Column(Integer, nullable=False)
    tj_printtimes = Column(Integer, nullable=False)
    tj_rptnum = Column(String(256), nullable=False)
    tj_peid = Column(Integer, nullable=False)
    tj_isrecheck = Column(Integer, nullable=False)
    tj_oldtestid = Column(String(256), nullable=False)
    tj_rechecktimes = Column(Integer, nullable=False)
    tj_push = Column(Integer, nullable=False)
    tj_num = Column(Integer, nullable=False)
    tj_pushstatus = Column(Integer, nullable=False)
    tj_upload = Column(Integer, nullable=False)
    tj_uploadtime = Column(Integer, nullable=False)
    tj_syncstatus = Column(Integer, nullable=False)
    tj_paymethod = Column(Integer, nullable=False)
    tj_packageid = Column(Integer, nullable=False)
    tj_packagename = Column(String(256), nullable=False)
    tj_additional = Column(String(256), nullable=False)
    p_medid = Column(Integer, nullable=False)
