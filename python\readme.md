# Report Generation API Server

This is a RESTful API server that wraps the functionality of the report generation system.

## Installation

1. Install the required dependencies:

```bash
pip install -r requirements.txt
```

2. Run the server:

```bash
python reportserver.py
```

The server will start on `http://localhost:8000`

## API Documentation

Once the server is running, you can access the interactive API documentation at:

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### Endpoints

#### POST /generate-report

Generates a report based on the provided parameters.

Request body:

```json
{
  "rptid": 123, // Report ID (integer)
  "reporttype": 0, // Report type (0 for PDF, 1 for DOCX)
  "pagestyle": 1, // Page style
  "splitinrow": 1, // Split in row
  "outdir": "./reports" // Output directory
}
```

Response:

```json
{
  "status": "success",
  "message": "Report generated successfully"
}
```

#### GET /health

Health check endpoint to verify the server is running.

Response:

```json
{
  "status": "healthy"
}
```

## Error Handling

The API will return appropriate HTTP status codes and error messages:

- 400: Bad Request (invalid input)
- 500: Internal Server Error (server-side errors)

## Notes

- Make sure the output directory exists and is writable
- The server uses the same database connection as the original report.py script
- All report generation logic remains unchanged, just wrapped in a REST API
