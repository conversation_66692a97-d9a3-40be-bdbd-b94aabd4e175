use crate::entities::{prelude::*, tj_labresult};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjLabresult {
  pub async fn query_many(testid: &str, db: &DatabaseConnection) -> Result<Vec<TjLabresult>> {
    let ret = TjLabresultEntity::find().filter(tj_labresult::Column::TjTestid.eq(testid)).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_many(info: &Vec<TjLabresult>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = info.len();
    let (insert_vals, update_vals): (Vec<TjLabresult>, Vec<TjLabresult>) = info.to_owned().into_iter().partition(|f| f.id == 0);

    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_labresult::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_labresult::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjLabresultEntity::insert_many(active_insert_values).exec(db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_labresult::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    Ok(total as i64)
  }

  pub async fn insert_many(info: &Vec<TjLabresult>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }

    let mut active_values: Vec<tj_labresult::ActiveModel> = Vec::new();
    for val in info {
      let ret = tj_labresult::ActiveModel::from_json(json!(val));
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      active_values.push(ret.unwrap());
    }
    let ret = TjLabresultEntity::insert_many(active_values).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().last_insert_id)
  }
  pub async fn delete(testids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjLabresultEntity::delete_many()
      .filter(Condition::all().add(tj_labresult::Column::TjTestid.is_in(testids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn delete_by_itemids(testids: &Vec<String>, itemids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    if testids.len() <= 0 && itemids.len() <= 0 {
      return Err(anyhow!("empty,not allowed"));
    }

    let mut conditions = Condition::all().add(tj_labresult::Column::TjTestid.is_in(testids.to_owned()));
    if itemids.len() > 0 {
      conditions = Condition::all().add(tj_labresult::Column::TjItemid.is_in(itemids.to_owned()));
    }
    let ret = TjLabresultEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn delete_by_analytes(testids: &Vec<String>, analytes: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    if testids.len() <= 0 && analytes.len() <= 0 {
      return Err(anyhow!("empty,not allowed"));
    }

    let mut conditions = Condition::all().add(tj_labresult::Column::TjTestid.is_in(testids.to_owned()));
    if analytes.len() > 0 {
      conditions = Condition::all().add(tj_labresult::Column::TjAnalyte.is_in(analytes.to_owned()));
    }
    let ret = TjLabresultEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn clear(date: i64, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjLabresultEntity::delete_many()
      .filter(Condition::all().add(tj_labresult::Column::TjImportdate.lt(date)))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
