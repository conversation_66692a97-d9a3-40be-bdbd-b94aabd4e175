from sqlalchemy import (
    Column,
    Integer,
    String,
)
from dataentities.dbconn import Base


class TjCorpinfo(Base):
    # 数据库中存储的表名
    __tablename__ = "tj_corpinfo"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    tj_corpid = Column(String(128), nullable=False)
    tj_corpname = Column(String(256), nullable=False)
    tj_contactor = Column(String(64), nullable=False, unique=True)
    tj_principle = Column(String(128), nullable=False)
    tj_phone = Column(String(128), nullable=False)
    tj_fax = Column(String(128), nullable=False)
    tj_areacode = Column(String(128), nullable=False)
    tj_address = Column(String(256), nullable=False)
    tj_postcode = Column(String(128), nullable=False)
    tj_industry2 = Column(String(128), nullable=False)
    tj_economic2 = Column(String(128), nullable=False)
    tj_pyjm = Column(String(256), nullable=False)
    tj_zdym = Column(String(128), nullable=False)
    tj_operator = Column(Integer, nullable=False)
    tj_adddate = Column(Integer, nullable=False)
    tj_testtype = Column(Integer, nullable=False)
    tj_password = Column(String(256), nullable=False)
    tj_mail = Column(String(128), nullable=False)
    tj_memo = Column(String(128), nullable=False)
    tj_orgcode = Column(String(128), nullable=False)
    tj_gbcode = Column(String(128), nullable=False)
    tj_ecotypeh = Column(Integer, nullable=False)
    tj_ecotype = Column(Integer, nullable=False)
    tj_industryh = Column(Integer, nullable=False)
    tj_industry = Column(Integer, nullable=False)
    tj_secondcode = Column(String(128), nullable=False)
    tj_secondname = Column(String(128), nullable=False)
    tj_corpscale = Column(Integer, nullable=False)
    tj_total = Column(Integer, nullable=False)
    tj_totalf = Column(Integer, nullable=False)
    tj_operationer = Column(Integer, nullable=False)
    tj_operationerf = Column(Integer, nullable=False)
    tj_hazarders = Column(Integer, nullable=False)
    tj_hazardersf = Column(Integer, nullable=False)
    tj_syncflag = Column(Integer, nullable=False)
    tj_status = Column(Integer, nullable=False)
    tj_monitortype = Column(String(128), nullable=False)
    p_cmpid = Column(Integer, nullable=False)
