//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_hazardinfo")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_tid: i32,
    pub tj_hname: String,
    pub tj_pyjm: String,
    pub tj_showorder: i32,
    pub tj_forbiden: String,
    pub tj_memo: String,
    pub tj_extcode: String,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::tj_hazarddisease::Entity")]
    TjHazarddisease,
    #[sea_orm(has_many = "super::tj_hazarditem::Entity")]
    TjHazarditem,
}

impl Related<super::tj_hazarddisease::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjHazarddisease.def()
    }
}

impl Related<super::tj_hazarditem::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjHazarditem.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
