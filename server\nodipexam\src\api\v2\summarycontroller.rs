use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok, DataBody, ResponseBody},
  auth::auth::Claims,
  common::constant::{self},
};
use axum::{Extension, Json};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{dto::*, medexam::summarysvc::SummarySvc, SYSCACHE};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;
//query_summaries
pub async fn query_summaries(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<QuerySummaryDto>) -> Json<Value> {
  info!("query summary dto:{:?}", &dto);
  let ret = SummarySvc::query_testsummaries(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjTestsummary>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn auto_summary(claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<AutoSummaryDto>) -> Json<Value> {
  // info!("auto summary dto:{:?}", &dto);
  info!("auto summary claims is:{claims:?}");

  let staff = SYSCACHE.get().unwrap().get_staff(claims.userid as i64, &db).await;
  let ret = SummarySvc::auto_summary(&dto, &staff, &db).await;
  let databody = DataBody::new(1, ret);
  // info!("auto summary response:{:?}", &databody);
  // response_json_value_ok(1, databody)
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn save_summary(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjTestsummary>) -> Json<Value> {
  info!("save summary dto:{:?}", &dto);
  let ret = SummarySvc::save_testsummary(&dto, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn delete_summary(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjTestsummary>) -> Json<Value> {
  info!("delete summary dto:{:?}", &dto);
  let ret = SummarySvc::delete_testsummary(&dto, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), RegisterResponseDto { ..Default::default() });
  }
  let databody = DataBody::new(1, ret.unwrap());
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

///
pub async fn query_diseaseinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TestSummaryQueryDto>) -> Json<Value> {
  let ret = SummarySvc::query_diseaseinfos(&dto.testids, &dto.deptids, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjDiseaseinfo>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_diseaseinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(info): Json<Vec<TjDiseaseinfo>>) -> Json<Value> {
  info!("start to save disease infos:{:?}", &info);
  let ret = SummarySvc::save_diseaseinfos(&info, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1, ret.unwrap())
}

pub async fn update_diseaseinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(mut info): Json<TjDiseaseinfo>) -> Json<Value> {
  // let mut info = info.to_owned();
  info!("disease info to be updated:{:?}", &info);
  let ret = SummarySvc::save_or_update_diseaseinfos(&mut info, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1, ret.unwrap())
}

pub async fn delete_diseaseinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultipleKeysDto>) -> Json<Value> {
  info!("delete diseases dto:{:?}", &dto);
  let ret = SummarySvc::delete_diseaseinfos_by_dto(&dto.keystr1, &dto.keystr2, &dto.keyint1, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1, ret.unwrap())
}
