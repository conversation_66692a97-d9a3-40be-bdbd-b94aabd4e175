//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Co<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, Debug, DeriveEntity)]
pub struct Entity;

impl EntityName for Entity {
  fn table_name(&self) -> &str {
    "ss_infoconfig_zwx"
  }
}

#[derive(<PERSON>lone, Debug, PartialEq, DeriveModel, DeriveActiveModel, Eq, Serialize, Deserialize)]
pub struct Model {
  pub id: i32,
  pub ss_code: String,
  pub ss_name: String,
  pub ss_pyjm: String,
  pub ss_type: i32,
  pub ss_parent: String,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveColumn)]
pub enum Column {
  #[sea_orm(column_name = "ID")]
  Id,
  SsCode,
  SsName,
  SsPyjm,
  SsType,
  SsParent,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, En<PERSON>I<PERSON>, DerivePrimaryKey)]
pub enum PrimaryKey {
  Id,
}

impl PrimaryKeyTrait for PrimaryKey {
  type ValueType = i32;
  fn auto_increment() -> bool {
    true
  }
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl ColumnTrait for Column {
  type EntityName = Entity;
  fn def(&self) -> ColumnDef {
    match self {
      Self::Id => ColumnType::Integer.def(),
      Self::SsCode => ColumnType::String(Some(45u32)).def(),
      Self::SsName => ColumnType::String(Some(65u32)).def(),
      Self::SsPyjm => ColumnType::String(Some(45u32)).def(),
      Self::SsType => ColumnType::Integer.def(),
      Self::SsParent => ColumnType::String(Some(45u32)).def(),
    }
  }
}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
