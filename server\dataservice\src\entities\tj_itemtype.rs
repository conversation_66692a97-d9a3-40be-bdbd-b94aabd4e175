//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_itemtype")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  #[sea_orm(unique)]
  pub tj_typeid: i32,
  pub tj_typename: String,
  pub tj_deptid: String,
  pub tj_type: i32,
  pub tj_outsource: i32,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_showorder: i32,
  pub tj_operator: String,
  pub tj_status: i32, //0:正常 -1：删除
  pub tj_moddate: i64,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
