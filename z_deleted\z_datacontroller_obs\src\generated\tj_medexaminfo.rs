//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_medexaminfo")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    #[sea_orm(unique)]
    pub tj_testid: String,
    pub tj_pid: String,
    pub tj_age: i32,
    pub tj_testcat: i32,
    pub tj_testsource: i32,
    pub tj_testtype: i32,
    pub tj_corpnum: i32,
    pub tj_empid: String,
    pub tj_workage: String,
    pub tj_wtcode: String,
    pub tj_worktype: String,
    pub tj_poisionfactor: String,
    pub tj_poisionage: String,
    pub tj_recorddate: i64,
    pub tj_recorder: String,
    pub tj_testdate: i64,
    pub tj_expdate: i64,
    pub tj_subdate: i64,
    pub tj_checkstatus: i32,
    pub tj_printflag: i32,
    pub tj_printtimes: i32,
    pub tj_rptnum: String,
    pub tj_peid: i32,
    pub tj_isrecheck: i32,
    pub tj_rechecktimes: i32,
    pub tj_push: i8,
    pub tj_num: i64,
    pub tj_pushstatus: i8,
    pub tj_upload: i8,
    pub tj_uploadtime: i64,
    pub tj_syncstatus: i8,
    pub tj_paymethod: i32,
    pub tj_packagename: String,
    pub tj_gjcdc_upload: i8,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
