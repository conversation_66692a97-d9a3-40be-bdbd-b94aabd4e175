
use anyhow::{anyhow, Result};
use rbatis::{crud,  py_sql};
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;


#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjHazardinfo {
  pub id: i64,
  pub tj_tid: i32,
  pub tj_hname: String,
  pub tj_pyjm: String,
  pub tj_showorder: i32,
  pub tj_forbiden: String,
  pub tj_memo: String,
  pub tj_extcode: String,
  pub tj_status: i32,
}
crud!(TjHazardinfo {}, "tj_hazardinfo");
// rbatis::impl_select!(TjHazardinfo{query_one_by_hname(hname:&String)->Option=>"`where tj_hname = #{hname}`"});
rbatis::impl_select!(TjHazardinfo{query_one_by_hname(hname:&str)->Option => "`where tj_hname = #{hname} `"});
rbatis::impl_select!(Tj<PERSON><PERSON><PERSON><PERSON>{query_many(typeids:&[i64],hdids:&[i64], hdnames:&[String]) => 
  "`where id > 0 `
  if !typeids.is_empty():
    ` and tj_tid in ${typeids.sql()} `
  if !hdids.is_empty():
    ` and id in ${hdids.sql()} `
  if !hdnames.is_empty():
    ` and tj_hname in ${hdnames.sql()} `
  "});

  impl TjHazardinfo {
    pub async fn save(rb: &mut rbatis::RBatis, info: &TjHazardinfo, ) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjHazardinfo::update_by_column( rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjHazardinfo::insert( rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
   pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<TjHazardinfo> ) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<TjHazardinfo>, Vec<TjHazardinfo>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = TjHazardinfo::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = TjHazardinfo::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }

  // #[py_sql("update tj_hazardinfo set tj_status = -1 where id in ${ids.sql()} ")]
  #[py_sql("delete from tj_hazardinfo where id in ${ids.sql()} ")]
  pub async fn delete(rb: &mut rbatis::RBatis, ids: &[i64] ) -> rbatis::Result<()> {
    impled!()
  }
  
  #[py_sql("update tj_hazardinfo set tj_tid = #{newtid} where tj_tid = #{oldtid} ")]
  pub async fn update_type(rb: &mut rbatis::RBatis, oldtid: &i64, newtid:&i64) -> rbatis::Result<i64> {
    impled!()
  }
  }