//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Co<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, Debug, DeriveEntity)]
pub struct Entity;

impl EntityName for Entity {
  fn table_name(&self) -> &str {
    "ss_area_zwx"
  }
}

#[derive(<PERSON>lone, Debug, PartialEq, DeriveModel, DeriveActiveModel, Eq, Serialize, Deserialize)]
pub struct Model {
  pub id: i32,
  pub area_code: String,
  pub area_name: String,
  pub area_fullname: String,
  pub area_level: i8,
  pub area_pcode: Option<String>,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveColumn)]
pub enum Column {
  Id,
  AreaCode,
  AreaName,
  AreaFullname,
  AreaLevel,
  AreaPcode,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumI<PERSON>, DerivePrimaryKey)]
pub enum PrimaryKey {
  Id,
}

impl PrimaryKeyTrait for PrimaryKey {
  type ValueType = i32;
  fn auto_increment() -> bool {
    true
  }
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter)]
pub enum Relation {}

impl ColumnTrait for Column {
  type EntityName = Entity;
  fn def(&self) -> ColumnDef {
    match self {
      Self::Id => ColumnType::Integer.def(),
      Self::AreaCode => ColumnType::String(Some(10u32)).def().unique(),
      Self::AreaName => ColumnType::String(Some(100u32)).def(),
      Self::AreaFullname => ColumnType::String(Some(100u32)).def(),
      Self::AreaLevel => ColumnType::TinyInteger.def(),
      Self::AreaPcode => ColumnType::String(Some(10u32)).def().null(),
    }
  }
}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
