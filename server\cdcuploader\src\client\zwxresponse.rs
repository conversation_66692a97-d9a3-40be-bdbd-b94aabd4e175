use anyhow::{anyhow, Result};
use yaserde::*;
// use yaserde::de::*;
pub struct ZwxResponse {}

impl ZwxResponse {
  pub fn decode_response(response: &str) -> Result<String> {
    let de_ret: Result<ResponseData, String> = yaserde::de::from_str(response);
    // info!("Result:{:#?}", &de_ret);
    if de_ret.as_ref().is_err() {
      return Err(anyhow!("{}", de_ret.as_ref().err().unwrap().to_string()));
    }
    let responseinfo = de_ret.unwrap();
    if responseinfo.returncode.eq_ignore_ascii_case(RESPONSE_OK) {
      return Ok("DONE".to_string());
    } else {
      //   let main_error = format!("",responseinfo.mainerror.mainerrordata.code.to_owned());
      let mainerror = responseinfo.mainerror.unwrap_or_default();
      let mainerrordata = mainerror.mainerrordata.unwrap_or_default();
      let errors = mainerrordata.errordatas;
      let mut error_msgs = mainerrordata.message.to_string();
      if errors.is_some() {
        error_msgs.push_str(
          &errors
            .unwrap()
            .errors
            .iter()
            .map(|x| format!("{}:{}->{}", x.code.to_owned(), x.orgcode.clone(), x.message.clone()))
            .collect::<Vec<String>>()
            .join(";"),
        );
      }
      // let error_msg = responseinfo.errorlist.errors.message;
      // let mainerror = responseinfo.mainerror.unwrap_or_default();
      // let mainerrordata = mainerror.mainerrordata.unwrap_or_default();
      return Err(anyhow!(
        "{} => {}:{}{}  {}",
        responseinfo.message.unwrap_or_default(),
        mainerrordata.code,
        mainerrordata.orgcode,
        mainerrordata.message,
        error_msgs
      ));
    }
  }
}

pub const RESPONSE_OK: &str = "00";

#[derive(YaDeserialize, Default, Debug, PartialEq)]
#[yaserde(rename = "BHKRESPONSEDATA")]
pub struct ResponseData {
  #[yaserde(rename = "RETCODE")]
  pub returncode: String,
  #[yaserde(rename = "DESC")]
  pub message: Option<String>,
  #[yaserde(rename = "ERRORS")]
  pub mainerror: Option<MainError>,
}

#[derive(YaDeserialize, Default, Debug, PartialEq)]
// #[yaserde(rename = "ERRORMAINDATA")]
pub struct MainError {
  #[yaserde(rename = "ERRORMAINDATA")]
  pub mainerrordata: Option<ErrorMainData>,
}

#[derive(YaDeserialize, Default, Debug, PartialEq)]
// #[yaserde(rename = "ERRORMAINDATA")]
pub struct ErrorMainData {
  #[yaserde(rename = "TABLE_NAME")]
  pub code: String,
  #[yaserde(rename = "UUID")]
  pub orgcode: String,
  #[yaserde(rename = "DESC")]
  pub message: String,
  #[yaserde(rename = "ERRORDATAS")]
  pub errordatas: Option<ErrorDatas>,
}

#[derive(YaDeserialize, Default, Debug, PartialEq)]
// #[yaserde(rename = "ERRORMAINDATA")]
pub struct ErrorDatas {
  #[yaserde(rename = "ERRORDATA")]
  pub errors: Vec<ErrorData>,
}

#[derive(YaDeserialize, Default, Debug, PartialEq)]
// #[yaserde(rename = "ERRORDATA")]
pub struct ErrorData {
  #[yaserde(rename = "TABLE_NAME")]
  pub code: String,
  #[yaserde(rename = "UUID")]
  pub orgcode: String,
  #[yaserde(rename = "DESC")]
  pub message: String,
}
