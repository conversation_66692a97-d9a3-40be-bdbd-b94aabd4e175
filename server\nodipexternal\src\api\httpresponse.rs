// use std::result;

use axum::extract::Json;
use serde::{Deserialize, Serialize};
use serde_json::Value;

use crate::common::constant::{HttpCode, EMPTY_STR};

#[derive(Debug, Serialize, Deserialize)]
pub struct ResponseBody<T> {
  pub code: i32,
  pub message: String,
  pub data: T,
}

impl<T> ResponseBody<T> {
  pub fn new(code: i32, message: &str, data: T) -> ResponseBody<T> {
    ResponseBody {
      code: code,
      message: message.to_string(),
      data,
    }
  }
  // pub fn response_error(error: &str, data: T) -> ResponseBody<T> {
  //   let databody = DataBody::new(1, data);
  //   ResponseBody::new(HttpCode::Error as i32, &error, databody)
  // }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DataBody<T> {
  pub total: u64,
  pub data: T,
}
impl<T> DataBody<T> {
  pub fn new(total: u64, data: T) -> DataBody<T> {
    DataBody { total: total, data: data }
  }
}

// pub fn response_json_ok(msg: &str) -> Json<Value> {
//   let res = ResponseBody::new(HttpCode::OK as i32, &msg, EMPTY_STR);
//   Json(serde_json::json!(res))
// }
// pub fn response_json_error(error: &str) -> Json<Value> {
//   let res = ResponseBody::new(HttpCode::Error as i32, &error, EMPTY_STR);
//   Json(serde_json::json!(res))
// }
// pub fn response_value_ok<T: serde::Serialize>(msg: &str, data: T) -> Json<Value> {
//   let res = ResponseBody::new(HttpCode::OK as i32, &msg, data);
//   Json(serde_json::json!(res))
// }
// pub fn response_value_error<T: serde::Serialize>(error: &str, data: T) -> Json<Value> {
//   let res = ResponseBody::new(HttpCode::Error as i32, &error, data);
//   Json(serde_json::json!(res))
// }

pub fn response_json_value_ok<T: serde::Serialize>(total: u64, data: T) -> Json<Value> {
  let databody = DataBody::new(total, data);
  let res = ResponseBody::new(HttpCode::OK as i32, EMPTY_STR, databody);
  Json(serde_json::json!(res))
}

pub fn response_json_value_error<T: serde::Serialize>(error: &str, data: T) -> Json<Value> {
  let databody = DataBody::new(0, data);
  let res = ResponseBody::new(HttpCode::Error as i32, &error, databody);
  Json(serde_json::json!(&res))
}
