
use anyhow::{anyhow, Result};
use rbatis::{crud,sql};
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjItemtype {
  pub id: i64,
  pub tj_typeid: i32,
  pub tj_typename: String,
  pub tj_deptid: String,
  pub tj_type: i32,
  pub tj_outsource: i32,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_showorder: i32,
  pub tj_operator: String,
  pub tj_status: i32, //0:正常 -1：删除
  pub tj_moddate: i64,
}
crud!(TjItemtype {}, "tj_itemtype");
rbatis::impl_select!(TjItemtype{query_many(typeids:&Vec<i64>) => 
  "`where id > 0 and tj_status >= 0`
  if !typeids.is_empty(): 
    ` and tj_typeid in ${typeids.sql()} `"}, "tj_itemtype");

impl TjItemtype {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjItemtype, ) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjItemtype::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjItemtype::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
  #[sql("update tj_itemtype set tj_status = -1 where id = ? ")]
  pub async fn delete(rb: &mut rbatis::RBatis, id: &i64, ) -> rbatis::Result<()> {
    impled!()
  }
}
