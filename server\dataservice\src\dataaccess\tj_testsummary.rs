use crate::entities::{prelude::*, tj_testsummary};
use anyhow::{anyhow, Result};
use sea_orm::{sea_query::Query, ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, DbBackend, EntityTrait, QueryFilter, QueryTrait, Set, TransactionTrait};
use serde_json::json;
use tracing::*;

impl TjTestsummary {
  pub fn new(testid: &str, deptid: &str) -> Self {
    TjTestsummary {
      tj_testid: testid.to_string(),
      tj_deptid: deptid.to_string(),
      ..Default::default()
    }
  }
  pub async fn query_one(testid: &str, deptid: &str, db: &DatabaseConnection) -> Result<Option<TjTestsummary>> {
    if testid.is_empty() || deptid.is_empty() {
      return Err(anyhow!("empty testid or deptid, not allowed"));
    }
    let mut conditions = Condition::all();
    if !testid.is_empty() {
      conditions = conditions.add(tj_testsummary::Column::TjTestid.eq(testid));
    }
    if !deptid.is_empty() {
      conditions = conditions.add(tj_testsummary::Column::TjDeptid.eq(deptid));
    }
    let ret = TjTestsummaryEntity::find().filter(conditions).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query(testid: &str, deptids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjTestsummary>> {
    let mut conditions = Condition::all();
    if !testid.is_empty() {
      conditions = conditions.add(tj_testsummary::Column::TjTestid.eq(testid));
    }
    if deptids.len() > 0 {
      conditions = conditions.add(tj_testsummary::Column::TjDeptid.is_in(deptids.to_owned()));
    }
    let ret = TjTestsummaryEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_many_with_filter(
    testids: &Vec<String>,
    deptids: &Vec<String>,
    stdate: i64,
    enddate: i64,
    status: i32,
    staffid: &str,
    db: &DatabaseConnection,
  ) -> Result<Vec<TjTestsummary>> {
    let mut conditions = Condition::all();
    if testids.len() > 0 {
      conditions = conditions.add(tj_testsummary::Column::TjTestid.is_in(testids.to_owned()));
    }
    if deptids.len() > 0 {
      conditions = conditions.add(tj_testsummary::Column::TjDeptid.is_in(deptids.to_owned()));
    }
    if status > -1 {
      conditions = conditions.add(tj_testsummary::Column::TjIsfinished.eq(status));
    }
    if stdate > 0 && enddate > 0 {
      conditions = conditions.add(
        tj_testsummary::Column::TjTestid.in_subquery(
          Query::select()
            .column(tj_testsummary::Column::TjTestid)
            .from(tj_testsummary::Entity)
            .and_where(tj_testsummary::Column::TjDate.gte(stdate))
            .and_where(tj_testsummary::Column::TjDate.lt(enddate))
            .to_owned(),
        ),
      );
    }

    if !staffid.is_empty() {
      conditions = conditions.add(tj_testsummary::Column::TjDoctorid.eq(staffid.to_owned()));
    }

    let query = TjTestsummaryEntity::find().filter(conditions);
    info!("query testsumamry string:{:?}", &query.build(DbBackend::MySql).to_string());
    let ret = query.all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_many(
    testids: &Vec<String>,
    deptids: &Vec<String>,
    stdate: i64,
    enddate: i64,
    status: i32,
    staffid: &str,
    forceend: i32,
    db: &DatabaseConnection,
  ) -> Result<Vec<TjTestsummary>> {
    // if testids.len() <= 0 && deptids.len() <= 0 {
    //   return Err(anyhow!("both testids and deptids are empty, not allowed......"));
    // }
    let mut conditions = Condition::all();
    if testids.len() > 0 {
      conditions = conditions.add(tj_testsummary::Column::TjTestid.is_in(testids.to_owned()));
    }
    if deptids.len() > 0 {
      conditions = conditions.add(tj_testsummary::Column::TjDeptid.is_in(deptids.to_owned()));
    }
    if status > -1 {
      conditions = conditions.add(tj_testsummary::Column::TjIsfinished.eq(status));
    }
    if stdate > 0 {
      conditions = conditions.add(tj_testsummary::Column::TjDate.gte(stdate));
    }
    if enddate > 0 {
      conditions = conditions.add(tj_testsummary::Column::TjDate.lt(enddate));
    }
    if !staffid.is_empty() {
      conditions = conditions.add(tj_testsummary::Column::TjDoctorid.eq(staffid.to_owned()));
    }
    if forceend > -1 {
      conditions = conditions.add(tj_testsummary::Column::TjForceend.eq(forceend));
    }
    let query = TjTestsummaryEntity::find().filter(conditions);
    info!("query testsumamry string:{:?}", &query.build(DbBackend::MySql).to_string());
    let ret = query.all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn insert_many(info: &Vec<TjTestsummary>, db: &DatabaseConnection) -> Result<i64> {
    // println!("{:#?}", &info);
    if info.len() <= 0 {
      // return Err(anyhow!("empty data"));
      return Ok(0);
    }

    let mut active_values: Vec<tj_testsummary::ActiveModel> = Vec::new();
    for val in info {
      let ret = tj_testsummary::ActiveModel::from_json(json!(val));
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      active_values.push(ret.unwrap());
    }
    let ret = TjTestsummaryEntity::insert_many(active_values).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().last_insert_id)
  }

  pub async fn save_many(info: &Vec<TjTestsummary>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      // return Err(anyhow!("empty data"));
      return Ok(0);
    }
    let total = info.len();
    let (insert_vals, update_vals): (Vec<TjTestsummary>, Vec<TjTestsummary>) = info.to_owned().into_iter().partition(|f| f.id == 0);
    let txn = db.begin().await.expect("start transaction error");
    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_testsummary::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_testsummary::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjTestsummaryEntity::insert_many(active_insert_values).exec(&txn).await;
      if ret.as_ref().is_err() {
        let _ = txn.rollback();
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_testsummary::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(&txn).await;
        if ret.as_ref().is_err() {
          let _ = txn.rollback();
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    let ret = txn.commit().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(total as i64)
  }

  pub async fn save(info: &TjTestsummary, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_testsummary::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    // info!("new val is:{:#?}", &newval);
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn delete(testids: &Vec<String>, deptids: &Vec<String>, db: &DatabaseConnection) -> Result<()> {
    if testids.len() <= 0 {
      // return Err(anyhow!("empty testids, not allowed"));
      return Ok(());
    }
    let mut conditions = Condition::all().add(tj_testsummary::Column::TjTestid.is_in(testids.to_owned()));
    if deptids.len() > 0 {
      conditions = conditions.add(tj_testsummary::Column::TjDeptid.is_in(deptids.to_owned()))
    }

    let ret = TjTestsummaryEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(())
  }
}
