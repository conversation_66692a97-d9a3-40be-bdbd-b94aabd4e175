use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjDiseaseinfo {
  pub id: i64,
  pub tj_testid: String,
  pub tj_disid: i64,
  pub tj_diseasenum: String,
  pub tj_diseasename: String,
  pub tj_suggestion: String,
  pub tj_deptid: String,
  pub tj_isdisease: i32,
  pub tj_isoccu: i32,
  pub tj_typeid: i32,
  pub tj_opinion: String,
  pub tj_showorder: i64,
}
crud!(TjDiseaseinfo {}, "tj_diseaseinfo");
rbatis::impl_select!(TjDiseaseinfo{query(testid:&str) => "`where tj_testid = #{testid} `"});
rbatis::impl_select!(TjDiseaseinfo{query_patient_diseaseinfo(testid:&String,disid:&i64) ->Option => "`where tj_testid = #{testid} and tj_disid = #{disid}`"});
rbatis::impl_select!(TjDiseaseinfo{query_many(testids:&[&str], deptids:&[&str]) => 
  "`where id > 0 `
  if !testids.is_empty():
    ` and tj_testid in ${testids.sql()} `
  if !deptids.is_empty():
    ` and tj_deptid in ${deptids.sql()} `"});
rbatis::impl_delete!(TjDiseaseinfo{delete_by_ids(ids:&[i64]) => "`where id in ${ids.sql()} `"});
rbatis::impl_delete!(TjDiseaseinfo{delete(testids:&[&str], deptids:&[&str], disids:&[i64]) => 
  "`where id > 0 `
  if !testids.is_empty():
    ` and tj_testid in ${testids.sql()} `
  if !deptids.is_empty():
    ` and tj_deptid in ${deptids.sql()} `
  if !disids.is_empty():
    ` and tj_disid in ${disids.sql()} `"});

impl TjDiseaseinfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &mut TjDiseaseinfo) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjDiseaseinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjDiseaseinfo::query_patient_diseaseinfo(rb, &info.tj_testid, &info.tj_disid).await;
      if ret.as_ref().is_err() {
        error!("save disease error:{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      let dis_ret = ret.unwrap();
      if dis_ret.is_some() {
        let disret = dis_ret.unwrap();
        info.id = disret.id;
        let ret = TjDiseaseinfo::update_by_column(rb, &info, "id").await;
        if ret.as_ref().is_err() {
          error!("save disease error:{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        retid = disret.id;
      } else {
        let ret = TjDiseaseinfo::insert(rb, &info).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
      }
    }
    Ok(retid)
  }
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<TjDiseaseinfo>) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    // info!("disease infos:{:?}", &infos);
    let not_zero_ids: Vec<TjDiseaseinfo> = infos.into_iter().filter(|&v| v.tj_disid > 0).map(|f| f.to_owned()).collect();
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<TjDiseaseinfo>, Vec<TjDiseaseinfo>) = not_zero_ids.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = TjDiseaseinfo::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = TjDiseaseinfo::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }
}
