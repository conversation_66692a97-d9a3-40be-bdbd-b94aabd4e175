//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_occupationhistory")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub tj_testid: String,
    pub tj_startdate: i32,
    pub tj_enddate: i32,
    pub tj_corpname: String,
    pub tj_workshop: String,
    pub tj_worktype: String,
    pub tj_worktypename: String,
    pub tj_harmful: String,
    pub tj_harmfulname: String,
    pub tj_protective: String,
    pub tj_raddaynum: String,
    pub tj_radtotalnum: String,
    pub tj_radoverdose: String,
    pub tj_radexposure: String,
    pub tj_radcode: String,
    pub tj_radtype: String,
    pub tj_radprotective: String,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
