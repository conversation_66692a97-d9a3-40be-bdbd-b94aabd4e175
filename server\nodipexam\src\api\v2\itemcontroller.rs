use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok, DataBody, ResponseBody}, auth::auth::Claims, common::constant::{self}
};
use axum::{Extension, Json};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{basic::itemsvc::ItemSvc, dto::*};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

////////////////
pub async fn query_itemtypes(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntsDto>) -> Json<Value> {
  // let itemid =
  let ret = ItemSvc::query_itemtypes(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjItemtype>::new());
  }
  let result = ret.unwrap();
  response_json_value_ok(result.len() as u64, result)
}

pub async fn save_itemtype(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjItemtype>) -> Json<Value> {
  // let itemid =
  let ret = ItemSvc::save_itemtype(&dto, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let result = ret.unwrap();
  response_json_value_ok(1, result)
}

pub async fn delete_itemtype(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjItemtype>) -> Json<Value> {
  info!("dto is:{:?}", &dto);
  let ret = ItemSvc::delete_itemtype(&dto, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let result = ret.unwrap();
  response_json_value_ok(1, result)
}

/// item range

pub async fn query_itemrangeinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultipleKeysDto>) -> Json<Value> {
  // let itemid =
  let ret = ItemSvc::query_itemrangeinfos(dto.keyint1 as i32, dto.keyint2 as i32, &dto.keystr1, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjItemtype>::new());
  }
  let result = ret.unwrap();
  response_json_value_ok(result.len() as u64, result)
}

pub async fn save_itemrangeinfo(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjItemrangeinfo>) -> Json<Value> {
  // let itemid =
  let ret = ItemSvc::save_itemrangeinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let result = ret.unwrap();
  response_json_value_ok(1, result)
}

pub async fn delete_itemrangeinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntsDto>) -> Json<Value> {
  info!("delete itemrange infos dto is:{:?}", &dto);
  let ret = ItemSvc::delete_itemrangeinfos(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let result = ret.unwrap();
  response_json_value_ok(1, result)
}

////////////////////////////////
pub async fn query_iteminfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<IteminfoQueryDto>) -> Json<Value> {
  let ret = ItemSvc::query_iteminfos(&dto, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjIteminfo>::new());
  }

  let results = ret.unwrap();

  response_json_value_ok(results.len() as u64, results)
  // let databody = DataBody::new(results.len() as u64, results);
  // Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}
pub async fn save_iteminfos(
  _claims: Claims,
  Extension(db): Extension<Arc<DbConnection>>,
  _tx: Extension<Arc<tokio::sync::mpsc::UnboundedSender<i32>>>,
  Json(dto): Json<SaveItemDto>,
) -> Json<Value> {
  info!("save iteminfos dto:{dto:?}");
  let ret = ItemSvc::save_iteminfo(&dto.iteminfo, &dto.combines, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  // let _ = tx.send(nodipservice::common::constant::CacheType::Item as i32);
  response_json_value_ok(1, results)
}

pub async fn delete_iteminfos(
  _claims: Claims,
  Extension(db): Extension<Arc<DbConnection>>,
  _tx: Extension<Arc<tokio::sync::mpsc::UnboundedSender<i32>>>,
  Json(dto): Json<TjIteminfo>,
) -> Json<Value> {
  info!("dto is:{:?}", &dto);
  let ret = ItemSvc::delete_iteminfos(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  // let _ = tx.send(nodipservice::common::constant::CacheType::Item as i32);
  response_json_value_ok(1, results)
}

pub async fn query_combine_iteminfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeysDto>) -> Json<Value> {
  let ret = ItemSvc::query_combine_items(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjCombineinfo>::new());
  }

  let results = ret.unwrap();
  let databody = DataBody::new(results.len() as u64, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn save_combine_iteminfos(
  _claims: Claims,
  Extension(db): Extension<Arc<DbConnection>>,
  tx: Extension<Arc<tokio::sync::mpsc::UnboundedSender<i32>>>,
  Json(dto): Json<TjCombineinfo>,
) -> Json<Value> {
  let ret = ItemSvc::save_combine_items(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  let _ = tx.send(nodipservice::common::constant::CacheType::Combine as i32);
  response_json_value_ok(1, results)
}

pub async fn delete_combine_iteminfos(
  _claims: Claims,
  Extension(db): Extension<Arc<DbConnection>>,
  tx: Extension<Arc<tokio::sync::mpsc::UnboundedSender<i32>>>,
  Json(dto): Json<TjCombineinfo>,
) -> Json<Value> {
  info!("dto is:{:?}", &dto);
  let ret = ItemSvc::delete_combine_items(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  let _ = tx.send(nodipservice::common::constant::CacheType::Combine as i32);
  response_json_value_ok(1, results)
}

pub async fn query_item_resultinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyDto>) -> Json<Value> {
  // let itemid =
  let ret = ItemSvc::query_item_resultinfos(&dto.key, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjItemresultinfo>::new());
  }
  let result = ret.unwrap();
  response_json_value_ok(result.len() as u64, result)
}

pub async fn save_item_resultinfo(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjItemresultinfo>) -> Json<Value> {
  // let itemid =
  let ret = ItemSvc::save_item_resultinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let result = ret.unwrap();
  response_json_value_ok(1, result)
}

pub async fn delete_item_resultinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjItemresultinfo>) -> Json<Value> {
  info!("dto is:{:?}", &dto);

  let ret = ItemSvc::delete_item_resultinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let result = ret.unwrap();
  response_json_value_ok(1, result)
}

// query_sampletypes
pub async fn query_sampletypes(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  // let itemid =
  let ret = ItemSvc::query_sampletypes(&db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjSampletype>::new());
  }
  let result = ret.unwrap();
  response_json_value_ok(result.len() as u64, result)
}
//update_iteminfo_lisnum
pub async fn update_iteminfo_lisnum(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjIteminfo>) -> Json<Value> {
  // let itemid =
  let ret = ItemSvc::update_iteminfo_lisnum(&dto.tj_itemid, &dto.tj_lisnum, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjSampletype>::new());
  }
  let result = ret.unwrap();
  response_json_value_ok(1, result)
}
