// use serde::{Deserialize, Serialize};

pub const CODE_OK: &str = "0";
pub const CODE_ERROR: &str = "1";

pub const AREA_CODE: &str = "01";

pub const SOAP_ACTION_SAMPLE1: &str = "http://tempuri.org/HEREN.ZYB.BS.ZYBCommonWS.Sample1";
pub const SOAP_ACTION_SAMPLE2: &str = "http://tempuri.org/HEREN.ZYB.BS.ZYBCommonWS.Sample2";

pub const SOAP_SAMPLE1_METHOD_EXAMAPPLYINFO: &str = "ExamApplyInfo";
pub const SOAP_SAMPLE1_METHOD_LABAPPLYINFO: &str = "LabApplyInfo";

pub const SERVICE_SYNC_PATIENT_BASIC_INFO: &str = "syncTJPatientBasicInfo";
pub const SERVICE_SYNC_CHARGE_INFO: &str = "syncTJPatientChargeInfo";

pub const SERVICE_VERSION: &str = "1.0";

pub const SAMPLE2_RESULT: &str = "Sample2Result";
pub const SAMPLE1_RESULT: &str = "Sample1Result";

#[derive(Debug, Clone)]
pub enum PatientType {
  Functional = 1,
  Lab = 2,
}

// #[derive(Debug, Serialize, Deserialize)]
// pub struct Request<T> {
//   #[serde(rename = "accessToken")]
//   pub accesstoken: String,
//   #[serde(rename = "serviceName")]
//   pub servicename: String,
//   #[serde(rename = "serviceVersion")]
//   pub serviceversion: String,
//   pub params: Params<T>,
// }

// #[derive(Debug, Serialize, Deserialize)]
// pub struct Params<T> {
//   pub args: T,
// }
