use base64::{engine::general_purpose, Engine as _};
// use dbopservice::dataaccess::prelude::*;
use dataservice::entities::prelude::*;
use encoding_rs::*;
use nodipservice::common::constant::*;

#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum JudgeMode {
  DingXing = 1,
  DingLiang = 2,
}

pub fn check_main_dust(extcode: &str) -> bool {
  let main_dust = vec![
    "110001".to_string(),
    "110002".to_string(),
    "110004".to_string(),
    "110007".to_string(),
    "110008".to_string(),
    "110011".to_string(),
    "110012".to_string(),
    "110013".to_string(),
    "110014".to_string(),
    "110015".to_string(),
    "110016".to_string(),
    "110017".to_string(),
    "110018".to_string(),
    "110019".to_string(),
    "110020".to_string(),
    "110021".to_string(),
    "110023".to_string(),
    "110024".to_string(),
    "110025".to_string(),
    "110028".to_string(),
    "110028".to_string(),
    "110029".to_string(),
    "110030".to_string(),
    "110038".to_string(),
    "110040".to_string(),
    "110042".to_string(),
    "110043".to_string(),
    "110044".to_string(),
    "110045".to_string(),
    "110046".to_string(),
    "110047".to_string(),
    "110050".to_string(),
    "110051".to_string(),
    "110052".to_string(),
    "110053".to_string(),
    "110056".to_string(),
    "110057".to_string(),
    "110060".to_string(),
  ];

  let is_exist = main_dust.iter().find(|&x| x.eq_ignore_ascii_case(extcode));
  if is_exist.is_some() {
    return true;
  }
  return false;
}

pub fn get_corp_scale(scale: i32) -> String {
  match scale {
    1 => "10000".to_string(),
    2 => "10001".to_string(),
    3 => "10002".to_string(),
    4 => "10004".to_string(),
    _ => "10003".to_string(),
  }
}

pub fn get_check_result_string(ttype: i32) -> String {
  match ttype {
    0 => "未见异常".to_string(),
    1 => "需复查".to_string(),
    2 => "职业禁忌证".to_string(),
    3 => "疑似职业病".to_string(),
    4 => "其他疾病或异常".to_string(),
    _ => "其他疾病或异常".to_string(),
  }
}

pub fn get_base64(str: &String) -> String {
  // str.to_owned()

  let gbk_ret = GBK.encode(&str);
  let gbk_content = gbk_ret.0;
  // base64::encode(gbk_content)
  general_purpose::STANDARD.encode(&gbk_content)
}

pub fn get_base64_decode(str: &String) -> String {
  // let ret = base64::decode(str);
  let ret = general_purpose::STANDARD.decode(&str);

  if ret.as_ref().is_err() {
    return "".to_string();
  }
  let content = ret.unwrap();
  let ret_str = encoding_rs::GBK.decode(&content);
  return ret_str.0.to_string();
}

pub fn get_rst_flag(ttype: i32, result: i32, itemcode: &String, drcode: &String) -> String {
  let drids: Vec<String> = drcode.split(",").map(|x| x.to_string()).collect();
  // info!("当前项目:{},胸片项目:{:?}", &itemcode, &drids);
  let dr_code = drids.iter().find(|&x| x.eq_ignore_ascii_case(itemcode.as_str()));
  if dr_code.is_none() {
    return "".to_string();
  } else {
    // 0：未见异常
    // 1：尘肺样改变
    // 2：其他异常
    // 3：未检查

    if ttype == 0 {
      return "0".to_string();
    } else {
      if result == 0 && ttype == 1 {
        return "2".to_string();
      }
      if result == 1 {
        return 1.to_string();
      }
      let ret = match result {
        1 => "0".to_string(),
        2 => "1".to_string(),
        3 => "2".to_string(),
        9 => "3".to_string(),
        _ => "0".to_string(),
      };
      return ret;
      // return result.to_string();
      // if result.contains("尘肺") {
      //   return "1".to_string();
      // }
      // if result.contains("未检") {
      //   return "3".to_string();
      // }
      // return "2".to_string();
    }
  }
}

pub fn get_data_version(iteminfo: &TjIteminfo, result: &String) -> String {
  if !iteminfo.tj_valuetype.eq_ignore_ascii_case(&ValueType::DingLiang.to_string()) || result.is_empty() {
    return "".to_string();
  }
  if iteminfo.tj_valuetype.is_empty() {
    return "".to_string();
  }
  if result.starts_with("<") || result.starts_with(">") {
    return "1002".to_string();
  }
  //   if iteminfo.tj_reftype.eq_ignore_ascii_case("=") {
  //     return "1001".to_string();
  //   }
  //   if iteminfo.tj_reftype.eq_ignore_ascii_case("<") {
  //     return "1002".to_string();
  //   }
  return "1001".to_string();
}

pub fn get_marriage_status(marriage: i32) -> i32 {
  match marriage {
    1 => 1,
    2 => 0,
    3 => 2,
    4 => 3,
    _ => 4,
  }
}

pub fn get_testtype_status(ttype: i32) -> String {
  match ttype {
    // 2 => "".to_string(),
    3 => "1001".to_string(), //sg
    4 => "1002".to_string(), //zg
    5 => "1003".to_string(), //lg
    6 => "1005".to_string(), //yj
    _ => "".to_string(),
  }
}

pub fn get_check_result(ttype: i32) -> String {
  match ttype {
    0 => "12001".to_string(),
    1 => "12002".to_string(),
    2 => "12004".to_string(),
    3 => "12003".to_string(), //
    4 => "12005".to_string(), //
    5 => "12001".to_string(), //
    _ => "".to_string(),
  }
}

//返回参考值，合格标记(i32)，偏高偏低标记(i32)和判断模式(i32)
pub fn get_reference_value(val: &TjIteminfo) -> String {
  if val.tj_valuetype != "2" {
    return "".to_string();
  }
  let ret = match val.tj_reftype.to_lowercase().trim() {
    ">x" => format!(">{}", val.tj_lowvalue),
    ">=x" => format!(">={}", val.tj_lowvalue),
    "<y" => format!(">={}", val.tj_uppervalue),
    "<=x" => format!(">={}", val.tj_uppervalue),
    "x-y" | "x_y" => format!("{}-{}", val.tj_lowvalue, val.tj_uppervalue),
    _ => format!("-99999-99999"),
  };

  ret

  // if val.tj_reftype.to_lowercase() == ">x" {
  //   return format!(">{}", val.tj_lowvalue);
  // } else if val.tj_reftype.to_lowercase() == "<y" {
  //   return format!("<{}", val.tj_uppervalue);
  // } else if val.tj_reftype.to_lowercase() == "x-y" || val.tj_reftype.to_lowercase() == "x_y" {
  //   return format!("{}-{}", val.tj_lowvalue, val.tj_uppervalue);
  // }
  // return "-99999-99999".to_string();
}

pub fn get_qualified_flag(ttype: i32) -> i32 {
  match ttype {
    0 => 1,
    1 => 0,
    _ => 0,
  }
}

pub fn get_up_low_flag(ckval: &TjCheckiteminfo, val: &TjIteminfo) -> String {
  if val.tj_valuetype != "2" {
    return "".to_string();
  }

  if val.tj_reftype.to_lowercase() == ">x" {
    if ckval.tj_result.parse::<f64>().unwrap_or_default() < val.tj_lowvalue.parse::<f64>().unwrap_or_default() {
      return "1".to_string();
    } else {
      return "0".to_string();
    }
  } else if val.tj_reftype.to_lowercase() == "<y" {
    if ckval.tj_result.parse::<f64>().unwrap_or_default() > val.tj_uppervalue.parse::<f64>().unwrap_or_default() {
      return "2".to_string();
    } else {
      return "0".to_string();
    }
  } else if val.tj_reftype.to_lowercase() == "x-y" || val.tj_reftype.to_lowercase() == "x_y" {
    if ckval.tj_result.parse::<f64>().unwrap_or_default() > val.tj_uppervalue.parse::<f64>().unwrap_or_default() {
      return "2".to_string();
    } else if ckval.tj_result.parse::<f64>().unwrap_or_default() < val.tj_lowvalue.parse::<f64>().unwrap_or_default() {
      return "1".to_string();
    } else {
      return "0".to_string();
    }
  }
  return "0".to_string();
}

pub fn get_judge_mode(val: &TjIteminfo) -> i32 {
  if val.tj_valuetype == "2" {
    return 2;
  } else {
    return 1;
  }
}

#[derive(Clone, Debug)]
#[allow(unused)]
pub struct AudioDetail {
  pub extcode: String,
  pub extname: String,
  pub freq: i32,
  pub transear: i32,
  pub transtype: i32,
  pub lowvalue: i32,
  pub highvalue: i32,
}

pub fn get_audio_detail(val: &TjAudiogramdetail) -> Option<AudioDetail> {
  let details = init_audio_details();
  let dt = details
    .iter()
    .find(|x| x.freq == val.tj_freq && x.transear == val.tj_ear && x.transtype == val.tj_adtype)
    .map(|x| x.clone());
  dt

  // None
}

pub fn get_audio_result(rttype: i32) -> Option<AudioDetail> {
  let adrets = init_audio_result();
  adrets.iter().find(|x| x.freq == rttype).map(|x| x.clone())
}

fn init_audio_details() -> Vec<AudioDetail> {
  let mut details: Vec<AudioDetail> = Vec::new();
  let yq500 = AudioDetail {
    extcode: "11432".to_string(),
    extname: "右耳500Hz".to_string(),
    freq: 500,
    transear: TransEar::Right as i32,
    transtype: TransType::Air as i32,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(yq500);
  let yg500 = AudioDetail {
    extcode: "11240".to_string(),
    extname: "右耳500Hz(骨导)".to_string(),
    freq: 500,
    transear: TransEar::Right as i32,
    transtype: TransType::Bone as i32,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(yg500);
  let yq1000 = AudioDetail {
    extcode: "11433".to_string(),
    extname: "右耳1000Hz".to_string(),
    freq: 1000,
    transear: TransEar::Right as i32,
    transtype: TransType::Air as i32,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(yq1000);
  let yg1000 = AudioDetail {
    extcode: "11241".to_string(),
    extname: "右耳1000Hz(骨导)".to_string(),
    freq: 1000,
    transear: TransEar::Right as i32,
    transtype: TransType::Bone as i32,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(yg1000);

  let yq2000 = AudioDetail {
    extcode: "11434".to_string(),
    extname: "右耳2000Hz".to_string(),
    freq: 2000,
    transear: TransEar::Right as i32,
    transtype: TransType::Air as i32,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(yq2000);

  let yg2000 = AudioDetail {
    extcode: "11242".to_string(),
    extname: "右耳2000Hz(骨导)".to_string(),
    freq: 2000,
    transear: TransEar::Right as i32,
    transtype: TransType::Bone as i32,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(yg2000);

  let yq3000 = AudioDetail {
    extcode: "11435".to_string(),
    extname: "右耳3000Hz".to_string(),
    freq: 3000,
    transear: TransEar::Right as i32,
    transtype: TransType::Air as i32,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(yq3000);

  let yg3000 = AudioDetail {
    extcode: "11243".to_string(),
    extname: "右耳3000Hz(骨导)".to_string(),
    freq: 3000,
    transear: TransEar::Right as i32,
    transtype: TransType::Bone as i32,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(yg3000);

  let yq4000 = AudioDetail {
    extcode: "11436".to_string(),
    extname: "右耳4000Hz".to_string(),
    freq: 4000,
    transear: TransEar::Right as i32,
    transtype: TransType::Air as i32,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(yq4000);

  let yg4000 = AudioDetail {
    extcode: "11244".to_string(),
    extname: "右耳4000Hz(骨导)".to_string(),
    freq: 4000,
    transear: TransEar::Right as i32,
    transtype: TransType::Bone as i32,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(yg4000);

  let yq6000 = AudioDetail {
    extcode: "11437".to_string(),
    extname: "右耳6000Hz".to_string(),
    freq: 6000,
    transear: TransEar::Right as i32,
    transtype: TransType::Air as i32,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(yq6000);

  let yg6000 = AudioDetail {
    extcode: "11245".to_string(),
    extname: "右耳6000Hz(骨导)".to_string(),
    freq: 6000,
    transear: TransEar::Right as i32,
    transtype: TransType::Bone as i32,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(yg6000);
  // ========LEFT========================================
  let zq500 = AudioDetail {
    extcode: "11426".to_string(),
    extname: "左耳500Hz".to_string(),
    freq: 500,
    transear: TransEar::Left as i32,
    transtype: TransType::Air as i32,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(zq500);
  let zg500 = AudioDetail {
    extcode: "11236".to_string(),
    extname: "左耳500Hz(骨导)".to_string(),
    freq: 500,
    transear: TransEar::Left as i32,
    transtype: TransType::Bone as i32,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(zg500);
  let zq1000 = AudioDetail {
    extcode: "11427".to_string(),
    extname: "左耳1000Hz".to_string(),
    freq: 1000,
    transear: TransEar::Left as i32,
    transtype: TransType::Air as i32,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(zq1000);
  let zg1000 = AudioDetail {
    extcode: "11237".to_string(),
    extname: "左耳1000Hz(骨导)".to_string(),
    freq: 1000,
    transear: TransEar::Left as i32,
    transtype: TransType::Bone as i32,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(zg1000);

  let zq2000 = AudioDetail {
    extcode: "11428".to_string(),
    extname: "左耳2000Hz".to_string(),
    freq: 2000,
    transear: TransEar::Left as i32,
    transtype: TransType::Air as i32,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(zq2000);

  let zg2000 = AudioDetail {
    extcode: "11238".to_string(),
    extname: "左耳2000Hz(骨导)".to_string(),
    freq: 2000,
    transear: TransEar::Left as i32,
    transtype: TransType::Bone as i32,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(zg2000);

  let zq3000 = AudioDetail {
    extcode: "11429".to_string(),
    extname: "左耳3000Hz".to_string(),
    freq: 3000,
    transear: TransEar::Left as i32,
    transtype: TransType::Air as i32,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(zq3000);

  let zg3000 = AudioDetail {
    extcode: "11515".to_string(),
    extname: "左耳3000Hz(骨导)".to_string(),
    freq: 3000,
    transear: TransEar::Left as i32,
    transtype: TransType::Bone as i32,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(zg3000);

  let zq4000 = AudioDetail {
    extcode: "11430".to_string(),
    extname: "左耳4000Hz".to_string(),
    freq: 4000,
    transear: TransEar::Left as i32,
    transtype: TransType::Air as i32,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(zq4000);

  let zg4000 = AudioDetail {
    extcode: "11513".to_string(),
    extname: "左耳4000Hz(骨导)".to_string(),
    freq: 4000,
    transear: TransEar::Left as i32,
    transtype: TransType::Bone as i32,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(zg4000);

  let zq6000 = AudioDetail {
    extcode: "11431".to_string(),
    extname: "左耳6000Hz".to_string(),
    freq: 6000,
    transear: TransEar::Left as i32,
    transtype: TransType::Air as i32,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(zq6000);

  let zg6000 = AudioDetail {
    extcode: "11239".to_string(),
    extname: "左耳6000Hz(骨导)".to_string(),
    freq: 6000,
    transear: TransEar::Left as i32,
    transtype: TransType::Bone as i32,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(zg6000);

  details
}

pub enum AdResultType {
  SYP = 0,
  ZYP = 1,
  YYP = 2,
  SGP = 3,
  YJQ = 4,
  ZJQ = 5,
}

fn init_audio_result() -> Vec<AudioDetail> {
  let mut details: Vec<AudioDetail> = Vec::new();
  let syp = AudioDetail {
    extcode: "11232".to_string(),
    extname: "双耳语频平均听阈".to_string(),
    freq: 0,
    transear: 0,
    transtype: 0,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(syp);
  let zyp = AudioDetail {
    extcode: "11230".to_string(),
    extname: "左耳语频平均听阈".to_string(),
    freq: 1,
    transear: 0,
    transtype: 0,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(zyp);

  let yyp = AudioDetail {
    extcode: "11231".to_string(),
    extname: "右耳语频平均听阈".to_string(),
    freq: 2,
    transear: 0,
    transtype: 0,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(yyp);

  let sgp = AudioDetail {
    extcode: "11233".to_string(),
    extname: "双耳高频平均听阈".to_string(),
    freq: 3,
    transear: 0,
    transtype: 0,
    lowvalue: -40,
    highvalue: 40,
  };
  details.push(sgp);

  let yjq = AudioDetail {
    extcode: "11235".to_string(),
    extname: "右耳听阈加权".to_string(),
    freq: 4,
    transear: 0,
    transtype: 0,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(yjq);

  let zjq = AudioDetail {
    extcode: "11234".to_string(),
    extname: "左耳听阈加权".to_string(),
    freq: 5,
    transear: 0,
    transtype: 0,
    lowvalue: -25,
    highvalue: 25,
  };
  details.push(zjq);

  // let syp = AudioDetail {
  //     extcode: "11232".to_string(),
  //     extname: "双耳语频平均听阈".to_string(),
  //     freq: 0,
  //     transear: 0,
  //     transtype: 0,
  //     lowvalue: -25,
  //     highvalue: 25,
  // };
  // details.push(syp);

  // let syp = AudioDetail {
  //     extcode: "11232".to_string(),
  //     extname: "双耳语频平均听阈".to_string(),
  //     freq: 0,
  //     transear: 0,
  //     transtype: 0,
  //     lowvalue: -25,
  //     highvalue: 25,
  // };
  // details.push(syp);

  details
}
