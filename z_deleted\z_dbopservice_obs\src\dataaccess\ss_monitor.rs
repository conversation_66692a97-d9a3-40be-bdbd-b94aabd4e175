use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bug, PartialEq, Serialize, Deserialize)]
pub struct SsMonitor {
  pub id: i32,
  pub ss_jobcode: String,
  pub ss_jobname: String,
  pub ss_industrycode: String,
  pub ss_industryname: String,
  pub ss_memo: String,
}
crud!(SsMonitor {}, "ss_monitor");
// rbatis::impl_select!(SsMonitor{query_many(typeid:i32) =>
//   "`where id > 0 `
//   if typeid > 0:
//     ` and ss_type = #{typeid} `"});
