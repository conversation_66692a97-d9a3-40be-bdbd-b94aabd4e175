//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_staffadmin")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  #[sea_orm(unique)]
  pub tj_staffno: String,
  pub tj_staffname: String,
  pub tj_sex: i32,
  pub tj_deptid: i32,
  pub tj_groupid: i32,
  pub tj_password: String,
  pub tj_role: i32,
  pub tj_checkallflag: i32,
  pub tj_title: String,
  pub tj_status: i32,
  pub tj_operator: String,
  pub tj_moddate: i64,
  pub tj_memo: String,
  pub tj_isadmin: i32,
  pub login_session: String,
  pub tj_esign: String,
  pub extsn: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
