
use anyhow::{anyhow, Result};
use rbatis::crud;
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;

#[derive(C<PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjTestsummary {
  pub id: i64,
  pub tj_testid: String,
  pub tj_deptid: String,
  pub tj_summary: String,
  pub tj_suggestion: String,
  pub tj_isfinished: i32,
  pub tj_doctorid: String,
  pub tj_doctor: String,
  pub tj_date: i64,
  pub tj_forceend: i32,
  pub tj_checkdoctor: String,
  pub tj_checkdate: i64,
}
crud!(TjTestsummary {}, "tj_testsummary");
rbatis::impl_select!(TjTestsummary{query_one(testid:&str, deptid:&str) ->Option => "`where tj_testid = #{testid} and tj_deptid = #{deptid} limit 1 `"});
rbatis::impl_select!(TjTestsummary{query_by_testids_deptids(testids:&[String], deptids:&[String]) => "`where tj_testid in ${testids.sql()} and tj_deptid in ${deptids.sql()} `"});
// rbatis::impl_delete!(TjTestsummary{delete_many(testid:&str) => "`where tj_testid = #{testid} and tj_deptid = #{deptid}`"});
// rbatis::impl_delete!(TjTestsummary{delete_many_by_ids(ids:&[i64]) => "`where id in ${ids.sql()} `"});
rbatis::impl_select!(TjTestsummary{query(testid:&str, deptids:&[&str]) => 
  "`where id > 0 `
  if testid != '':
    ` and tj_testid = #{testid} `
  if !deptids.is_empty():
    ` and tj_deptid in ${deptids.sql()} `"});
rbatis::impl_select!(TjTestsummary{query_many(testids:&[&str], deptids:&[&str], stdate:i64, enddate:i64, status:i32, staffid:&str, forceend: i32) => 
  "`where id > 0 `
  if !testids.is_empty():
    ` and tj_testid in ${testids.sql()} `
  if !deptids.is_empty():
    ` and tj_deptid in ${deptids.sql()} `
  if stdate > 0:
    ` and tj_date >= #{stdate} `
  if enddate > 0:
    ` and tj_date < #{enddate} `
  if status > -1:
    ` and tj_isfinished = #{status} `
  if staffid != '':
    ` and tj_doctorid = #{staffid} `
  if forceend > 0:
    ` and tj_forceend = #{forceend} `
  "});
rbatis::impl_delete!(TjTestsummary{delete(testids:&[&str], deptids:&[&str]) => 
  "`where id > 0 and tj_testid in ${testids.sql()} `
  if !deptids.is_empty():
    ` and tj_deptid in ${deptids.sql()} `
  "});

impl TjTestsummary {
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<TjTestsummary>) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<TjTestsummary>, Vec<TjTestsummary>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = TjTestsummary::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = TjTestsummary::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjTestsummary) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjTestsummary::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjTestsummary::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
}
