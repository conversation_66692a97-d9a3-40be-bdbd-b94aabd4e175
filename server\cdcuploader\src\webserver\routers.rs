use std::sync::Arc;

use crate::config::settings::Settings;

use super::httpcontroller::*;

use axum::{
  extract::Extension,
  // handler::Handler,
  // http::StatusCode,
  // response::IntoResponse,
  routing::{post, MethodRouter},
  Router,
};

// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
// use nodipservice::common::syscache::SysCache;
// use odipservice::prelude::DictService;
use tower::ServiceBuilder;

pub fn create_route(dbconn: Arc<DbConnection>, setting: Arc<Settings>) -> Router {
  let v1_route = create_v1_route();

  // let dbconn = Arc::new(db);
  let app = Router::new()
    .nest("/apic", v1_route)
    // .layer(middleware::from_fn(print_request_response))
    .layer(ServiceBuilder::new().layer(Extension(dbconn)))
    .layer(ServiceBuilder::new().layer(Extension(setting)))
    // .layer(ServiceBuilder::new().layer(Extension(dictservice)))
    ;
  // add a fallback service for handling routes to unknown paths
  // let app = app.fallback(handler_404.into_service());
  app
}

fn create_v1_route() -> Router {
  let app_route = Router::new().merge(upload_data());

  let v1_route = Router::new().nest("/v1", app_route);
  v1_route
}

fn upload_data() -> Router {
  route("/upload", post(do_upload_data)).route("/query", post(do_query_refused_profiles))
}

// fn upload_corpinfo() -> Router {
//     route("/upload/corpinfo", post(upload_corpinfo_data))
// }

fn route(path: &str, method_router: MethodRouter) -> Router {
  Router::new().route(path, method_router)
}

// pub async fn handler_404() -> impl IntoResponse {
//   (StatusCode::NOT_FOUND, "nothing to see here")
// }
