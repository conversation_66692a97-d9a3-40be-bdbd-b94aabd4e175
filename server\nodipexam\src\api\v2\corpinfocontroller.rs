use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok},
  auth::auth::Claims,
};
use axum::{extract::Path, Extension, Json};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{basic::corpsvc::CorpSvc, dto::*};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

//get_corpinfo
pub async fn get_corpinfo(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Path(dto): Path<i64>) -> Json<Value> {
  info!("start to get corpinfos by dto:{:?}", &dto);

  let ret = CorpSvc::get_corpinfo(dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), TjCorpinfo { ..Default::default() });
  }

  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn query_corp_infos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeysDto>) -> Json<Value> {
  info!("start to query corpinfos by dto:{:?}", &dto);

  let ret = CorpSvc::query_corpinfos(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjCorpinfo>::new());
  }

  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_corpinfo(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjCorpinfo>) -> Json<Value> {
  info!("start to save corpinfo by dto:{:?}", &dto);

  let ret = CorpSvc::save_corpinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), TjCorpinfo { ..Default::default() });
  }

  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn delete_corpinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeysDto>) -> Json<Value> {
  info!("delete corpinfos by dto:{:?}", &dto);

  let ret = CorpSvc::delete_corpinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }

  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
