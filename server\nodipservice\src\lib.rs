use common::syscache::SysCache;
use once_cell::sync::OnceCell;

pub mod basic;
pub mod client;
pub mod common;
pub mod dto;
pub mod external;
pub mod medexam;
pub mod sync;

#[cfg(test)]
mod test;

pub static SYSCACHE: OnceCell<SysCache> = OnceCell::new();

// pub static GLOBAL_CONFIG: OnceCell<Settings> = OnceCell::const_new();
// pub static GLOBAL_CONFIG: Lazy<> = Lazy::new(|| RwLock::new(HashMap::new()));
