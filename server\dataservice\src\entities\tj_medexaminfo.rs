//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(C<PERSON>, Debug, Default, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_medexaminfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  #[sea_orm(unique)]
  pub tj_testid: String,
  pub tj_pid: String,
  pub tj_age: i32,
  pub tj_testcat: i32,
  pub tj_testsource: i32,
  pub tj_testtype: i32,
  pub tj_corpnum: i64,
  pub tj_empcorp: i64, //用工单位编号
  pub tj_empid: String,
  pub tj_workage: String,
  pub tj_wtcode: String,
  pub tj_worktype: String,
  pub tj_workshop: String,
  pub tj_monitortype: String,
  pub tj_poisionfactor: String,
  pub tj_poisionage: String,
  pub tj_radiationtype: String,
  pub tj_recorddate: i64,
  pub tj_recorder: String,
  pub tj_testdate: i64,
  pub tj_expdate: i64,
  pub tj_subdate: i64,
  pub tj_completed: i32,
  pub tj_total: i32,
  pub tj_checkstatus: i32,
  pub tj_printflag: i32,
  pub tj_printtimes: i32,
  pub tj_rptnum: String,
  pub tj_peid: i32,
  pub tj_isrecheck: i32,
  pub tj_oldtestid: String,
  pub tj_rechecktimes: i32,
  pub tj_push: i32,
  pub tj_num: i64,
  pub tj_pushstatus: i32,
  pub tj_upload: i32,
  pub tj_uploadtime: i64,
  pub tj_syncstatus: i32,
  pub tj_paymethod: i32,
  pub tj_packageid: i64,
  pub tj_packagename: String,
  pub tj_additional: String,
  pub p_medid: i64,
  pub tj_protective: String,
  pub tj_ordno: String,
  pub tj_chargestatus: i32,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
