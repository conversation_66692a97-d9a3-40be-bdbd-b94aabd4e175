use std::collections::HashSet;

use crate::basic::hazardsvc::HazardSvc;
use crate::basic::itemsvc::ItemSvc;
use crate::common::constant::{self, YesOrNo};
use crate::SYSCACHE;
use tracing::*;

use crate::basic::{barcodesvc::BarCodeSvc, dictsvc::DictSvc};
use crate::dto::{CheckitemsQueryDto, MedQueryDto, MedexamResultDetail, RegisterResponseDto};
use crate::medexam::patientsvc::PatientSvc;
use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use rust_decimal::prelude::ToPrimitive;
use utility::timeutil;

use super::checkitemsvc::CheckitemSvc; //::tj_patient::TjPatient;
pub struct MedexamSvc;

impl MedexamSvc {
  pub async fn query_medinfo_by_testid(testid: &str, db: &DbConnection) -> Result<Option<TjMedexaminfo>> {
    if testid.is_empty() {
      return Err(anyhow!("体检号不能为空"));
    }
    let ret = TjMedexaminfo::query(testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn query_medinfos_by_testids(testids: &Vec<String>, db: &DbConnection) -> Result<Vec<TjMedexaminfo>> {
    if testids.len() <= 0 {
      return Err(anyhow!("体检号不能为空"));
    }
    let ret = TjMedexaminfo::query_by_testids(testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_medinfo_hazards_by_testid(testids: &Vec<String>, db: &DbConnection) -> Result<Vec<TjPatienthazards>> {
    if testids.len() <= 0 {
      return Err(anyhow!("体检号不能为空"));
    }
    let ret = TjPatienthazards::query_many(&testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn update_medinfo_hazards_results(results: &Vec<TjPatienthazards>, db: &DbConnection) -> Result<i64> {
    let ret = TjPatienthazards::save_many(&results, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn update_medexam_hazards(medinfos: &Vec<TjMedexaminfo>, hdinfos: &Vec<TjHazardinfo>, db: &DbConnection) -> Result<()> {
    //
    if medinfos.len() <= 0 {
      return Ok(());
    }
    info!("体检信息：{:?}", &medinfos);
    info!("毒害因素信息：{:?}", &hdinfos);
    let mut poision_factor = medinfos[0].tj_poisionfactor.to_owned();
    if hdinfos.len() > 0 {
      poision_factor = hdinfos.iter().map(|v| v.tj_hname.to_string()).collect::<Vec<String>>().join("、");
    }
    let testids = medinfos.iter().map(|v| v.tj_testid.to_string()).collect::<Vec<String>>();
    for medinfo in medinfos.iter() {
      // TjMedexaminfo::
      let ret = MedexamSvc::save_patient_hazards(medinfo, hdinfos, db).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }

    let ret = TjMedexaminfo::update_medinfo_poisionfactors(&poision_factor, &testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    Ok(())
  }

  pub async fn query_by_testid(testid: &str, db: &DbConnection) -> Result<MedexamResultDetail> {
    if testid.is_empty() {
      return Err(anyhow!("体检号不能为空"));
    }

    let ret = TjMedexaminfo::query(testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = ret.unwrap();
    if ret.is_none() {
      return Err(anyhow!("不能找到该体检号的体检信息......"));
    }
    let medinfo = ret.unwrap();
    info!("medinfo:{:?}", &medinfo);

    let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = ret.unwrap();
    if ret.is_none() {
      return Err(anyhow!("不能找到该体检号的人员信息......"));
    }
    let patient = ret.unwrap();

    let ret = TjCheckallnew::query(testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = ret.unwrap();
    let mut checkall = TjCheckallnew { ..Default::default() };
    if ret.is_some() {
      // return Err(anyhow!("不能找到该体检号的人员信息......"));
      checkall = ret.unwrap();
    }
    // let checkall = ret.unwrap();
    // let deptids: Vec<&str> = Vec::new();
    let ret = TjTestsummary::query(testid, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let summaries = ret.unwrap();

    let ret = TjPatienthazards::query(testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let pthazards = ret.unwrap();

    let ret = TjDiseaseinfo::query(testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let diseases = ret.unwrap();

    //获取总检疾病相关的危害因素的疾病信息
    //把tj_patienthazards中的疾病信息，按照逗号分隔，然后去重
    let mut disids: HashSet<i64> = HashSet::new();
    for pthd in pthazards.iter() {
      if pthd.tj_typeid == checkall.tj_typeid {
        let disids2: Vec<&str> = pthd.tj_diseases.split(",").collect();
        for disid in disids2.iter() {
          disids.insert(disid.parse::<i64>().unwrap_or_default());
        }
      }
    }

    let disname = SYSCACHE
      .get()
      .unwrap()
      .get_diseases(&disids.iter().cloned().collect(), &vec![], &vec![], db)
      .await
      .iter()
      .map(|v| v.tj_disname.to_owned())
      .collect::<Vec<String>>()
      .join(";");

    checkall.tj_diseasename = disname;

    let result = MedexamResultDetail {
      idx: 1,
      patient,
      checkall,
      summaries,
      pthazards,
      medinfo,
      diseases,
    };
    // results.push(result);
    // }
    Ok(result)
  }

  pub async fn query_medexaminfos_by_dto(dto: &MedQueryDto, db: &DbConnection) -> Result<Vec<MedexamResultDetail>> {
    let dtstart = dto.dtstart;
    let mut dtend = dto.dtend;
    if dtend > 0 {
      dtend = timeutil::timestamp_add_days(dtend, 1);
    }

    let mut oldtestids: Vec<String> = dto.oldtestids.iter().map(|v| v.to_owned()).collect();
    let mut testids: Vec<String> = dto.testids.iter().map(|v| v.to_owned()).collect();
    let mut old_testids: Vec<String> = Vec::new();
    //查找复查信息
    if dto.rptid > 0 && dto.isrecheck == constant::YesOrNo::Yes as i32 {
      let rptids: Vec<i64> = vec![dto.rptid];
      let ret = TjCorpoccureportInfo::query_many(&rptids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        info!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      old_testids = ret.unwrap().into_iter().map(|v| v.tj_test_id).collect();
    }
    oldtestids.extend(old_testids.iter().map(|v| v.to_owned()).collect::<Vec<String>>());
    // info!("old testids:{:?}", &oldtestids);

    if dto.isrecheck == constant::YesOrNo::Yes as i32 && dto.rptid > 0 && oldtestids.len() <= 0 {
      //查需复查人员，但是没有需复查
      info!("该报告没有任何需复查人员信息");
      return Ok(vec![]);
    }
    // let pids: Vec<&str> = dto.pids.iter().map(|v| v.as_str()).collect();

    let ret = TjMedexaminfo::query_many(
      dtstart,
      dtend,
      &testids,
      &dto.pids,
      dto.corpid,
      dto.isrecheck,
      &oldtestids,
      &dto.testtypes,
      &dto.status,
      dto.filter,
      &dto.pname,
      &db.get_connection(),
    )
    .await;
    if ret.as_ref().is_err() {
      error!("query error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let mut medinfos = ret.unwrap();
    if medinfos.len() <= 0 {
      return Ok(vec![]);
    }
    testids = medinfos.iter().map(|f| f.tj_testid.clone()).collect();
    // info!("体检信息：{:?}", &testids);
    // let mut summaries: Vec<TjTestsummary> = Vec::new();
    // if dto.deptids.len() > 0 {
    //查找所有的小结信息
    let deptids: Vec<String> = dto.deptids.iter().map(|v| v.to_owned()).collect();
    let ret = TjTestsummary::query_many(&testids, &deptids, 0, 0, dto.deptstatus, "", -1, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let summaries = ret.unwrap();
    // info!("Final summaries:{:#?}", &summaries);
    //过滤科室
    if deptids.len() > 0 {
      medinfos.retain(|f| summaries.iter().find(|&x| x.tj_testid.eq_ignore_ascii_case(&f.tj_testid)).is_some());
      testids = medinfos.iter().map(|f| f.tj_testid.clone()).collect();
    }
    // }
    if testids.len() <= 0 {
      return Ok(vec![]);
    }
    // info!("final 查找结果:{:?}", &testids);
    let ret = TjCheckallnew::query_many(&testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      info!("query checkall new error:{:?}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let checkalls = ret.unwrap();

    let pids: Vec<String> = medinfos.iter().map(|f| f.tj_pid.clone()).collect();
    let empids: Vec<String> = vec![];
    let ret = TjPatient::query_many(&pids, "", &empids, &empids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      info!("query patient error:{:?}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let patients = ret.unwrap();

    let ret = TjPatienthazards::query_many(&testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      info!("query patient hazards error:{:?}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let pthazards = ret.unwrap();

    let ret = TjDiseaseinfo::query_many(&testids, &deptids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      info!("query diseases error:{:?}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ptdisease = ret.unwrap();

    let mut results: Vec<MedexamResultDetail> = Vec::new();
    let mut idx = 1;
    for val in medinfos {
      // let result:MedexamResultDetail{}
      let patient = patients
        .iter()
        .find(|&f| f.tj_pid.eq_ignore_ascii_case(&val.tj_pid))
        .map_or(TjPatient::new(&val.tj_pid), |v| v.to_owned());
      let mut checkall = checkalls
        .iter()
        .find(|&f| f.tj_testid.eq_ignore_ascii_case(&val.tj_testid))
        .map_or(TjCheckallnew::new(&val.tj_testid), |v| v.to_owned());
      let summary: Vec<TjTestsummary> = summaries.iter().filter(|&p| p.tj_testid.eq_ignore_ascii_case(&val.tj_testid)).map(|v| v.to_owned()).collect();
      let pthds: Vec<TjPatienthazards> = pthazards.iter().filter(|&p| p.tj_testid.eq_ignore_ascii_case(&val.tj_testid)).map(|v| v.to_owned()).collect();
      let diseases: Vec<TjDiseaseinfo> = ptdisease.iter().filter(|&p| p.tj_testid.eq_ignore_ascii_case(&val.tj_testid)).map(|v| v.to_owned()).collect();

      //获取总检疾病相关的危害因素的疾病信息
      //把tj_patienthazards中的疾病信息，按照逗号分隔，然后去重
      let mut disids: HashSet<i64> = HashSet::new();
      for pthd in pthds.iter() {
        if pthd.tj_typeid == checkall.tj_typeid {
          let disids2: Vec<&str> = pthd.tj_diseases.split(",").collect();
          for disid in disids2.iter() {
            disids.insert(disid.parse::<i64>().unwrap_or_default());
          }
        }
      }

      if disids.len() > 0 {
        let disname = SYSCACHE
          .get()
          .unwrap()
          .get_diseases(&disids.iter().cloned().collect(), &vec![], &vec![], db)
          .await
          .iter()
          .map(|v| v.tj_disname.to_owned())
          .collect::<Vec<String>>()
          .join(";");

        checkall.tj_diseasename = disname;
      }
      // info!("总检疾病名称:{}", &checkall.tj_diseasename);

      let result = MedexamResultDetail {
        idx,
        patient,
        checkall,
        summaries: summary,
        pthazards: pthds,
        medinfo: val.to_owned(),
        diseases,
      };
      results.push(result);
      idx += 1;
    }
    // info!("结果：{:#?}", &results);
    info!("体检信息查找结束，共:{}条记录", &results.len());
    Ok(results)
  }

  pub async fn update_medexam_status_updown(testid: &str, newstatus: i32, updown: i32, db: &DbConnection) -> Result<()> {
    let ret = TjMedexaminfo::query(testid, &db.get_connection()).await;
    if ret.is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret_op = ret.unwrap();
    if ret_op.is_none() {
      return Err(anyhow!("medexaminfo not existed"));
    }
    let mut medinfo = ret_op.unwrap();

    MedexamSvc::update_medexam_status(&mut medinfo, updown, newstatus, db).await
  }

  pub async fn update_medexam_status(medinfo: &mut TjMedexaminfo, updown: i32, newstatus: i32, db: &DbConnection) -> Result<()> {
    if medinfo.id <= 0 {
      info!("体检信息小于等于0，需要冲数据库读取体检信息");
      let ret = TjMedexaminfo::query(&medinfo.tj_testid, &db.get_connection()).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let ret_op = ret.unwrap();
      if ret_op.is_none() {
        return Err(anyhow!("medexaminfo not existed"));
      }
      let exist_medinfo = ret_op.unwrap();
      medinfo.tj_total = exist_medinfo.tj_total;
      medinfo.tj_completed = exist_medinfo.tj_completed;
      medinfo.tj_checkstatus = exist_medinfo.tj_checkstatus;
      medinfo.tj_testdate = exist_medinfo.tj_testdate;
      medinfo.id = exist_medinfo.id;
    }
    //直接更新状态
    if updown == 0 && newstatus > 0 {
      medinfo.tj_checkstatus = newstatus;
    } else {
      //根据项目来更新
      if medinfo.tj_total <= 0 || (medinfo.tj_total - medinfo.tj_completed).abs() <= 5 {
        let ret = TjTestsummary::query_many(&vec![medinfo.tj_testid.clone()], &vec![], 0, 0, -1, "", -1, &db.get_connection()).await;
        if ret.is_err() {
          error!("{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
        let summaries = ret.unwrap();
        medinfo.tj_total = summaries.len() as i32;
        medinfo.tj_completed = summaries.iter().filter(|&f| f.tj_isfinished == 1).count() as i32;
        if medinfo.tj_completed >= medinfo.tj_total {
          medinfo.tj_checkstatus = constant::ExamStatus::Examined as i32;
          medinfo.tj_completed = medinfo.tj_total;
        } else {
          medinfo.tj_checkstatus = constant::ExamStatus::Examining as i32;
        }
        info!("更新体检状态，已完成：{}，总数：{}，新状态:{}", medinfo.tj_completed, medinfo.tj_total, medinfo.tj_checkstatus);
      } else {
        medinfo.tj_completed += updown;
        if medinfo.tj_completed <= 0 {
          medinfo.tj_completed = 0;
        }
        if medinfo.tj_completed >= medinfo.tj_total {
          medinfo.tj_completed = medinfo.tj_total;
          medinfo.tj_checkstatus = constant::ExamStatus::Examined as i32;
        } else if medinfo.tj_completed > 0 && medinfo.tj_completed < medinfo.tj_total {
          medinfo.tj_checkstatus = constant::ExamStatus::Examining as i32;
        } else {
          medinfo.tj_checkstatus = constant::ExamStatus::Register as i32;
        }
      }
      let ret = SYSCACHE
        .get()
        .unwrap()
        .get_dict(constant::DictType::DictSysparm as i32, constant::SysParm::UpdateTestDate as i32, db)
        .await;
      let up_type = ret.ss_short.parse::<i32>().unwrap_or_default(); //ret.map_or(0, |f| f.ss_short.parse::<i32>().unwrap_or_default());
      if up_type == constant::UpdateTestDateType::First as i32 && medinfo.tj_completed <= 2 {
        //以第一次为准
        medinfo.tj_testdate = timeutil::current_timestamp();
        info!("更新体检时间，以第一次的体检的体检时间为准, 时间:{}", medinfo.tj_testdate);
      }
      if up_type == constant::UpdateTestDateType::Last as i32 && medinfo.tj_completed <= medinfo.tj_total {
        info!("更新体检时间，以最后一次的体检的体检时间为准");
        medinfo.tj_testdate = timeutil::current_timestamp();
      }
    }
    info!("save medexam infos:{:?}", &medinfo);
    let ret = TjMedexaminfo::update_status(
      medinfo.tj_checkstatus,
      medinfo.tj_completed,
      medinfo.tj_total,
      medinfo.tj_testdate,
      &medinfo.tj_testid,
      &db.get_connection(),
    )
    .await;
    // let ret = TjMedexaminfo::save(&medinfo).await;
    if ret.is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(())
  }

  pub async fn save_medexaminfo(ptinfo: &TjPatient, medinfo: &TjMedexaminfo, hdinfos: &Vec<TjHazardinfo>, db: &DbConnection) -> Result<MedexamResultDetail> {
    info!("testid is:{:?}", &medinfo.tj_testid);
    if medinfo.tj_testid.is_empty() {
      // return Err(anyhow!("testid is empty, not allowed"));
      let pkginfo = TjPackageinfo { ..Default::default() };
      let ret = MedexamSvc::medexam_quick_register(&ptinfo, &medinfo, &pkginfo, &hdinfos, &"".to_string(), &db).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let res = ret.unwrap();
      let redeail = MedexamResultDetail {
        medinfo: res.medinfo.to_owned(),
        patient: res.ptinfo.to_owned(),
        ..Default::default()
      };

      return Ok(redeail);
    }
    let mut ptinfo = ptinfo.to_owned();
    let mut medinfo = medinfo.to_owned();
    if medinfo.tj_monitortype.is_empty() {
      medinfo.tj_monitortype = "01".to_string();
    }
    if medinfo.tj_empcorp == 2 {
      medinfo.tj_empcorp = medinfo.tj_corpnum;
    }
    if medinfo.tj_wtcode.eq_ignore_ascii_case(crate::common::constant::OTHER_WORKTYPE) {
      let worktypes = crate::SYSCACHE
        .get()
        .unwrap()
        .get_worktype_by_name_and_monitortype(&medinfo.tj_worktype, &medinfo.tj_monitortype)
        .await;
      if worktypes.len() > 0 {
        medinfo.tj_wtcode = worktypes[0].ss_code.to_string();
      }
    }
    let ret = PatientSvc::save_patient(&ptinfo, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    ptinfo = ret.unwrap();
    medinfo.tj_pid = ptinfo.tj_pid.to_owned();

    let ret = TjMedexaminfo::save(&medinfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let id = ret.unwrap();
    medinfo.id = id;
    let ret = MedexamSvc::save_patient_hazards(&medinfo, &hdinfos, &db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let ret = TjPatienthazards::query(&medinfo.tj_testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let pthazards = ret.unwrap();
    let ret = TjCheckallnew::query(&medinfo.tj_testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let ckret = ret.unwrap();
    let mut checkall = TjCheckallnew { ..Default::default() };
    if ckret.is_some() {
      checkall = ckret.unwrap();
    }
    let ret = TjTestsummary::query(&medinfo.tj_testid, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let summaries = ret.unwrap();
    let ret = TjDiseaseinfo::query(&medinfo.tj_testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let diseases = ret.unwrap();
    let resp = MedexamResultDetail {
      idx: id,
      medinfo,
      patient: ptinfo.to_owned(),
      pthazards,
      checkall,
      summaries,
      diseases,
    };
    Ok(resp)
  }

  //快速登记，通过套餐
  pub async fn medexam_quick_register(
    ptinfo: &TjPatient,
    medinfo: &TjMedexaminfo,
    package: &TjPackageinfo,
    hdinfos: &Vec<TjHazardinfo>,
    _splitter: &String,
    db: &DbConnection,
  ) -> Result<RegisterResponseDto> {
    // info!("1）开始快速登记......");
    if medinfo.tj_testtype == 0 {
      return Err(anyhow!("体检类型不能为空"));
    }
    // let empty_ids: Vec<String> = Vec::new();
    let mut ptinfo = ptinfo.to_owned();
    let mut medinfo = medinfo.to_owned();

    if medinfo.tj_monitortype.is_empty() {
      medinfo.tj_monitortype = "01".to_string();
    }
    if medinfo.tj_wtcode.eq_ignore_ascii_case(crate::common::constant::OTHER_WORKTYPE) {
      let worktypes = crate::SYSCACHE
        .get()
        .unwrap()
        .get_worktype_by_name_and_monitortype(&medinfo.tj_worktype, &medinfo.tj_monitortype)
        .await;
      if worktypes.len() > 0 {
        medinfo.tj_wtcode = worktypes[0].ss_code.to_string();
      }
    }
    ptinfo.tj_popdate = timeutil::current_timestamp();
    ptinfo.tj_ptestnum = 0;
    ptinfo.tj_syncflag = 0;
    ptinfo.tj_cryptflag = 0;
    ptinfo.p_wkid = 0;

    info!("开始保存体检人员信息:{:?}......", &ptinfo);
    let ret = PatientSvc::save_patient(&ptinfo, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    ptinfo = ret.unwrap();

    // info!("体检人员信息处理完成，开始处理体检信息......");
    medinfo.tj_pid = ptinfo.tj_pid.clone();

    let now = timeutil::current_timestamp();
    let stdate = timeutil::timestamp_add_days(now, -30);

    if !ptinfo.tj_pidcard.is_empty() {
      info!("身份证号不为空，检查体检信息是否存在");
      let ret = MedexamSvc::check_existing_medexam_completed(&medinfo.tj_pid, medinfo.tj_testtype, stdate, medinfo.tj_corpnum, &db).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let exist_medinfos = ret.unwrap();

      if exist_medinfos.len() > 0 {
        medinfo.id = exist_medinfos[0].id;
        medinfo.tj_testid = exist_medinfos[0].tj_testid.to_owned();
        info!("有类似的体检信息，采用以前的体检信息：{}", &medinfo.tj_testid);
      }
    }
    if medinfo.tj_testid.is_empty() {
      //每个体检类型采用不同的前缀......
      let ret = DictSvc::get_id_by_testtype(medinfo.tj_testtype, db).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      medinfo.tj_testid = ret.unwrap();
      info!("生成新体检号结束，新体检号：{}", &medinfo.tj_testid);
    } else {
      //有体检号，并且是不存在的
      if medinfo.id <= 0 {
        let ret = TjMedexaminfo::query(&medinfo.tj_testid, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
        }
        let med_ret = ret.unwrap();
        if med_ret.is_some() {
          let med_ret = med_ret.unwrap();
          medinfo.id = med_ret.id;
        }
      }
    }

    // medinfo.tj_packagename = package.tj_pname.to_owned();
    medinfo.tj_checkstatus = constant::ExamStatus::Appoint as i32;
    medinfo.tj_recorddate = utility::timeutil::current_timestamp();
    medinfo.tj_subdate = utility::timeutil::current_timestamp();
    medinfo.tj_expdate = utility::timeutil::timestamp_add_days(medinfo.tj_subdate, 30);
    if medinfo.tj_testdate <= 0 {
      medinfo.tj_testdate = timeutil::current_timestamp();
    }
    medinfo.tj_testcat = 1;
    medinfo.tj_testsource = 0;
    medinfo.tj_printflag = 0;
    medinfo.tj_printtimes = 0;
    medinfo.tj_peid = 0;
    medinfo.tj_num = 0;
    medinfo.tj_completed = 0;
    medinfo.tj_total = 0;
    medinfo.tj_syncstatus = 0;
    medinfo.tj_upload = 0;
    medinfo.tj_push = 0;
    // if medinfo.tj_age <= 0 {
    //计算年龄......
    if !ptinfo.tj_pbirthday.is_empty() {
      medinfo.tj_age = timeutil::get_age_from_birthdate(&ptinfo.tj_pbirthday, medinfo.tj_testdate, "%Y-%m-%d");
    }
    // }
    info!("开始保存体检信息:{:?}", &medinfo);
    //处理tj_medinfo
    let ret = TjMedexaminfo::save(&medinfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    medinfo.id = ret.unwrap();

    //处理tj_patienthazards
    info!("开始保存体检人员的毒害因素信息：{},{:?}", &medinfo.tj_poisionfactor, &hdinfos);
    let ret = MedexamSvc::save_patient_hazards(&medinfo, &hdinfos, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    // let ret = TjIteminfo::query_many(&itemids, -1, -1, &empty_ids).await;
    // let items = ret.unwrap();
    //如果套餐的ID为0，则认为是预约
    if package.id <= 0 {
      let response_dto = RegisterResponseDto {
        medinfo: medinfo.to_owned(),
        ptinfo: ptinfo.to_owned(),
        checkitems: vec![],
      };
      return Ok(response_dto);
    }
    let ret = TjPackagedetail::query_many(&vec![package.id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let itemids: Vec<String> = ret.unwrap_or(Vec::new()).into_iter().map(|f| f.tj_itemid).collect();
    info!("套餐包含的项目内容:{:?}", &itemids);
    if itemids.len() <= 0 {
      error!("套餐:{}没有任何项目", package.tj_pname);
      return Err(anyhow!("该套餐没有任何项目"));
    }
    // let itemids_str: Vec<&str> = itemids.iter().map(|v| v.as_str()).collect();
    // let items = SYSCACHE.get().unwrap().get_iteminfos(&itemids, &db).await;
    let empty_str: Vec<String> = vec![];
    let ret = TjIteminfo::query_many(&itemids, -1, -1, -1, &empty_str, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let items = ret.unwrap();
    // info!("套餐包含的项目信息:{:#?}", &items);

    MedexamSvc::medexam_register(&ptinfo, &medinfo, &items, db).await
  }

  /// 登记需要做的事情
  //insert or update ptinfo
  //insert or update medinfo
  //insert patienthazards
  //insert checkiteminfos
  //insert testsummaries
  //insert checkallnew
  //external upload
  ///登记结束
  pub async fn medexam_register(ptinfo: &TjPatient, medinfo: &TjMedexaminfo, items: &Vec<TjIteminfo>, db: &DbConnection) -> Result<RegisterResponseDto> {
    if medinfo.tj_testtype == 0 {
      return Err(anyhow!("Error:体检类型不能为空"));
    }
    if medinfo.tj_testid.is_empty() {
      return Err(anyhow!("Error:体检编号不能为空"));
    }
    if items.len() <= 0 {
      return Err(anyhow!("Error: 体检项目为空"));
    }

    let ptinfo = ptinfo.to_owned();
    let mut medinfo = medinfo.to_owned();
    if medinfo.tj_wtcode.eq_ignore_ascii_case(crate::common::constant::OTHER_WORKTYPE) {
      let worktypes = crate::SYSCACHE
        .get()
        .unwrap()
        .get_worktype_by_name_and_monitortype(&medinfo.tj_worktype, &medinfo.tj_monitortype)
        .await;
      if worktypes.len() > 0 {
        medinfo.tj_wtcode = worktypes[0].ss_code.to_string();
      }
    }
    // let worktypes = crate::SYSCACHE
    //   .get()
    //   .unwrap()
    //   .get_worktype_by_name_and_monitortype(&medinfo.tj_worktype, &medinfo.tj_monitortype)
    //   .await;
    // if worktypes.len() > 0 {
    //   medinfo.tj_wtcode = worktypes[0].ss_code.to_string();
    // }

    if medinfo.id <= 0 {
      let ret = TjMedexaminfo::query(&medinfo.tj_testid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let med_ret = ret.unwrap();
      if med_ret.is_none() {
        error!("找不到体检号为{}的体检信息", &medinfo.tj_testid);
        return Err(anyhow!("找不到体检号为{}的体检信息", &medinfo.tj_testid));
      }
      let mut med_ret = med_ret.unwrap();
      med_ret.tj_additional = medinfo.tj_additional.to_owned();
      med_ret.tj_packagename = medinfo.tj_packagename.to_owned();
      medinfo = med_ret;
    }

    // info!("开始处理体检项目信息......");
    //处理tj_checkiteminfos
    // let empty_ids: Vec<String> = Vec::new();
    let empty_i64: Vec<i64> = Vec::new();
    //filter by sex
    let exam_items: Vec<TjIteminfo> = items
      .into_iter()
      .filter(|f| f.tj_sex == constant::Sex::All as i32 || f.tj_sex == constant::Sex::Unknown as i32 || f.tj_sex == ptinfo.tj_psex)
      .map(|v| v.to_owned())
      .collect();

    // info!("after filtered by sex, total items:{}", exam_items.len());

    if exam_items.len() <= 0 {
      return Err(anyhow!("没有满足条件的项目信息"));
    }
    //组合项目编号，用于获取所有组合的明细
    let mut exam_itemids: Vec<String> = exam_items.iter().map(|f| f.tj_itemid.clone()).collect();
    // info!("all combine itemids:{:?}", &exam_itemids);

    let ret = TjCombineinfo::query_many(&exam_itemids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let combine_infos = ret.unwrap();
    let detail_itemids: Vec<String> = combine_infos.iter().map(|f| f.tj_itemid.clone()).collect();
    let detail_itemids_str: Vec<String> = detail_itemids.iter().map(|v| v.to_string()).collect();
    exam_itemids.extend(detail_itemids.into_iter());
    let ret = TjIteminfo::query_many(&exam_itemids, -1, -1, -1, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    //所有的明细
    let detail_iteminfos = ret.unwrap();
    info!("所有明细项目数:{}", detail_iteminfos.len());

    let ret = TjItemrangeinfo::query_many(-1, -1, &detail_itemids_str, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut item_rangeinfos = ret.unwrap();
    item_rangeinfos.sort_by(|x, y| x.tj_sex.cmp(&y.tj_sex));
    // info!("项目参考值明细：{item_rangeinfos:?}");

    //所有的科室信息
    let ret = TjDeptitem::query_many(&exam_itemids, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let deptitems = ret.unwrap();
    let dept_ids: Vec<String> = deptitems.iter().map(|f| f.tj_deptid.clone()).collect::<HashSet<_>>().into_iter().collect();
    // let dept_ids: Vec<&str> = dept_ids.iter().map(|v| v.as_str()).collect();
    let ret = TjDepartinfo::query_many(&empty_i64, &dept_ids, 0, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let deptinfos = ret.unwrap();
    // info!("涉及的科室:{:?}", &deptinfos);

    medinfo.tj_checkstatus = constant::ExamStatus::Register as i32;
    medinfo.tj_total = deptinfos.len() as i32;

    //更新体检信息
    let ret = TjMedexaminfo::save(&medinfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let mut checkitems: Vec<TjCheckiteminfo> = Vec::new();
    for item in detail_iteminfos.iter() {
      if item.tj_sex != constant::Sex::Unknown as i32 && item.tj_sex != constant::Sex::All as i32 && item.tj_sex != ptinfo.tj_psex {
        continue;
      }
      let mut combid = item.tj_itemid.clone();
      if item.tj_combineflag == constant::YesOrNo::No as i32 {
        combid = combine_infos
          .iter()
          .find(|&f| f.tj_itemid.eq_ignore_ascii_case(&item.tj_itemid))
          .unwrap_or(&TjCombineinfo { ..Default::default() })
          .tj_combid
          .clone();
      }
      let deptid = deptitems
        .iter()
        .find(|&f| f.tj_itemid.eq_ignore_ascii_case(&combid))
        .unwrap_or(&TjDeptitem { ..Default::default() })
        .tj_deptid
        .clone();
      if deptid.is_empty() {
        info!("can not find dept item by itemid:{}", &combid);
        continue;
      }
      let deptinfo = deptinfos
        .iter()
        .find(|&f| f.tj_deptid.eq_ignore_ascii_case(&deptid))
        .map_or(TjDepartinfo { ..Default::default() }, |f| f.to_owned());
      //sex: i32, age: i32, itemid: &String, itemranges: &Vec<TjItemrangeinfo>
      let rangeinfo = ItemSvc::find_itemrangeinfo(ptinfo.tj_psex, medinfo.tj_age, &item.tj_itemid, &item_rangeinfos);
      // if item.tj_itemid.eq_ignore_ascii_case("120012") {
      //   info!("参考值信息：{rangeinfo:?}");
      // }
      let ci = MedexamSvc::convert_iteminfo_to_checkiteminfo(&medinfo.tj_testid, &combid, &deptinfo, item, &rangeinfo);

      checkitems.push(ci);
    }

    if checkitems.len() <= 0 {
      return Err(anyhow!("没有满足条件的项目信息"));
    }

    //生成条码
    let ret = BarCodeSvc::generate_bar_code(&mut checkitems, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    info!("开始处理体检项目......");
    let ret = TjCheckiteminfo::delete_many(&medinfo.tj_testid, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = TjCheckiteminfo::save_many(&checkitems, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    info!("体检项目处理完成，开始处理科室小结......");
    let mut summaries: Vec<TjTestsummary> = Vec::new();
    //处理testsummary
    for val in deptinfos.into_iter() {
      //开始生成科室小结信息
      if val.tj_sex != constant::Sex::Unknown as i32 && val.tj_sex != constant::Sex::All as i32 && val.tj_sex != ptinfo.tj_psex {
        //处理性别限制
        continue;
      }
      if summaries
        .iter()
        .find(|f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid) && f.tj_deptid.eq(&val.tj_deptid))
        .is_none()
      {
        let summary: TjTestsummary = TjTestsummary {
          id: 0,
          tj_testid: medinfo.tj_testid.clone(),
          tj_deptid: val.tj_deptid.clone(),
          tj_summary: "".to_string(),
          tj_suggestion: "".to_string(),
          tj_isfinished: 0,
          tj_doctorid: "".to_string(),
          tj_doctor: "".to_string(),
          tj_date: 0,
          tj_forceend: 0,
          tj_checkdoctor: "".to_string(),
          tj_checkdate: 0,
        };
        summaries.push(summary);
      }
    }
    info!("开始保存科室小结......");
    // let deptids: Vec<String> = summaries.iter().map(|v| v.tj_deptid.to_string()).collect();
    let ret = TjTestsummary::delete(&vec![medinfo.tj_testid.to_string()], &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("delete test summary error: {}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = TjTestsummary::save_many(&summaries, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    //处理checkallnew
    info!("科室小结处理完成，开始处理总检信息......");
    let ret = TjCheckallnew::query(&medinfo.tj_testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let checkall_ret = ret.unwrap();

    let mut checkallnew = TjCheckallnew {
      id: 0,
      tj_testid: medinfo.tj_testid.clone(),
      tj_typeid: 0,
      tj_discode: "".to_string(),
      tj_itemcode: "".to_string(),
      tj_hazardcode: "".to_string(),
      tj_diseasename: "".to_string(),
      tj_drret: 1,
      tj_ocuabnormal: "".to_string(),
      tj_othabnormal: "".to_string(),
      tj_ocuconclusion: "".to_string(),
      tj_othconclusion: "".to_string(),
      tj_ocusuggestion: "".to_string(),
      tj_othsuggestion: "".to_string(),
      tj_ocuopinion: "".to_string(),
      tj_othopinion: "".to_string(),
      tj_staffid: 0,
      tj_checkdate: 0,
      tj_castatus: 0,
    };

    if checkall_ret.is_some() {
      checkallnew.id = checkall_ret.unwrap().id;
    }

    info!("开始保存总检信息......");
    let ret = TjCheckallnew::save(&checkallnew, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    //处理external upload
    //外部对接还没有处理好......
    //
    // **************************************/
    info!("登记完成......，对项目信息进行排序");
    checkitems.sort_by(|a, b| {
      a.tj_synid.cmp(&b.tj_synid).then(a.tj_showorder.cmp(&b.tj_showorder))
      // if a.tj_synid == b.tj_synid {
      //   a.tj_synid.cmp(&b.tj_synid)
      // } else {
      //   a.tj_showorder.cmp(&b.tj_showorder)
      // }
    });
    let response_dto: RegisterResponseDto = RegisterResponseDto { medinfo, ptinfo, checkitems };
    Ok(response_dto)
  }

  pub async fn medexam_recheck_register(testid: &str, testdate: i64, db: &DbConnection) -> Result<RegisterResponseDto> {
    //1.query medinfo
    let ret = TjMedexaminfo::query(&testid, &db.get_connection()).await;
    if ret.is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let medret = ret.unwrap();
    if medret.is_none() {
      return Err(anyhow!("没有该体检号的体检信息"));
    }
    let mut medinfo = medret.unwrap();
    if medinfo.tj_checkstatus < constant::ExamStatus::Allchecked as i32 {
      return Err(anyhow!("该体检者未总检，不能复查登记"));
    }
    //2.query from checkall
    let ret = TjCheckallnew::query(testid, &db.get_connection()).await;
    if ret.is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let med_ret = ret.unwrap();
    if med_ret.is_none() {
      return Err(anyhow!("不能根据体检号：{}找到总检信息", testid));
    }
    let checkall = med_ret.unwrap();
    if checkall.tj_castatus == constant::YesOrNo::No as i32 {
      return Err(anyhow!("体检号：{}未总检，不能进行复查登记", testid));
    }

    //details
    let ret = TjPatienthazards::query(testid, &db.get_connection()).await;
    if ret.is_err() {
      return Err(anyhow!("不能根据体检号：{}找到详细的危害因素信息", testid));
    }
    let mut pthazards = ret.unwrap();

    let mut is_recheck = false;
    if checkall.tj_typeid == constant::CheckResultType::Recheck as i32 || pthazards.iter().find(|&f| f.tj_typeid == constant::CheckResultType::Recheck as i32).is_some() {
      is_recheck = true;
    }

    let mut recheckitemids: HashSet<String> = HashSet::new();
    //根据危害因素的结论
    for val in pthazards.iter() {
      info!("危害因素的结论:{:?}", &val);
      if val.tj_typeid == constant::CheckResultType::Recheck as i32 {
        if !val.tj_recheckitems.is_empty() {
          let itemids: Vec<&str> = val.tj_recheckitems.split(",").collect();
          for itemid in itemids.into_iter() {
            recheckitemids.insert(itemid.to_string());
            // let ci = TjCheckiteminfo {
            //   tj_itemid: itemid.to_string(),
            //   ..Default::default()
            // };
            // checkitems.push(ci);
          }
        }
      }
    }

    // if !is_recheck {
    //check hazardinfo
    // let ret = TjPatienthazards::query(testid, &db.get_connection()).await;
    // if ret.is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let hdrets = ret.unwrap();
    // for val in hdrets.into_iter() {
    //   if val.tj_typeid == constant::CheckResultType::Recheck as i32 {
    //     is_recheck = true;
    //     break;
    //   }
    // }
    // }

    if !is_recheck {
      return Err(anyhow!("体检号：{}的结论不是需复查，不能复查登记", testid));
    }

    let ret = TjMedexaminfo::query_many(
      0,
      0,
      &vec![],
      &vec![],
      -1,
      1,
      &vec![testid.to_string()],
      &vec![],
      &vec![],
      -1,
      &"".to_string(),
      &db.get_connection(),
    )
    .await;
    if ret.is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let exist_medinfos = ret.unwrap();
    if exist_medinfos.len() > 0 {
      // let exist_medinfo = &exist_medinfos[0];
      for val in exist_medinfos.iter() {
        if val.tj_checkstatus >= constant::ExamStatus::Appoint as i32 {
          return Err(anyhow!("体检号：{}已经复查登记，请不要重复登记，新体检号：{}", testid, &val.tj_testid));
        }
      }
    }

    medinfo.id = 0;
    medinfo.tj_testid = DictSvc::get_id_by_testtype(medinfo.tj_testtype, db).await.expect("generate id error");
    medinfo.tj_oldtestid = testid.to_string();
    medinfo.tj_isrecheck = constant::YesOrNo::Yes as i32;
    medinfo.tj_testdate = testdate;
    medinfo.tj_expdate = utility::timeutil::timestamp_add_days(medinfo.tj_testdate, 90);
    medinfo.tj_subdate = utility::timeutil::current_timestamp();
    medinfo.tj_checkstatus = constant::ExamStatus::Appoint as i32;
    medinfo.tj_recorddate = utility::timeutil::current_timestamp();
    medinfo.tj_total = 0;
    medinfo.tj_completed = 0;
    medinfo.tj_printflag = 0;
    medinfo.tj_printtimes = 0;
    medinfo.tj_rptnum = "".to_string();
    medinfo.tj_rechecktimes = medinfo.tj_rechecktimes + 1;
    medinfo.tj_packagename = "".to_string();
    medinfo.tj_upload = 0;
    medinfo.tj_uploadtime = 0;
    medinfo.tj_syncstatus = 0;

    // medinfo.tj_recorder =

    let ret = TjMedexaminfo::save(&medinfo, &db.get_connection()).await;
    if ret.is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    medinfo.id = ret.unwrap();
    //毒害因素处理
    // let ret = TjPatienthazards::query(testid, &db.get_connection()).await;
    // if ret.is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let mut pthds = ret.unwrap();
    if pthazards.len() > 0 {
      pthazards.iter_mut().for_each(|v| {
        v.id = 0;
        v.tj_testid = medinfo.tj_testid.clone();
        v.tj_diseases = "".to_string();
        v.tj_typeid = 0;
      });
      let ret = TjPatienthazards::save_many(&pthazards, &db.get_connection()).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    //3.query ptinfo
    let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
    if ret.is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let med_ret = ret.unwrap();
    if med_ret.is_none() {
      return Err(anyhow!("不能根据体检号：{}找到体检信息", testid));
    }
    let ptinfo = med_ret.unwrap();
    //generate iteminfo
    if !checkall.tj_itemcode.is_empty() {
      let itemids: Vec<&str> = checkall.tj_itemcode.split(",").collect();
      for itemid in itemids.into_iter() {
        recheckitemids.insert(itemid.to_string());
        // let ci = TjCheckiteminfo {
        //   tj_itemid: itemid.to_string(),
        //   ..Default::default()
        // };
        // checkitems.push(ci);
      }
    }

    info!("需要复查的项目信息:{:?}", &recheckitemids);
    let mut checkitems: Vec<TjCheckiteminfo> = Vec::new();
    for itemid in recheckitemids.iter() {
      let ci = TjCheckiteminfo {
        tj_itemid: itemid.to_string(),
        ..Default::default()
      };
      checkitems.push(ci);
    }
    Ok(RegisterResponseDto { medinfo, ptinfo, checkitems })
  }

  pub async fn update_medexam_print_status(testids: &Vec<String>, db: &DbConnection) -> Result<()> {
    // let ret = TjMedexaminfo::select_in_column( "tj_testid", &testids, &db.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let mut medinfos = ret.unwrap();
    // medinfos.iter_mut().for_each(|v| {
    //   v.tj_printflag = 1;
    //   v.tj_printtimes += 1;
    // });
    //update
    let ret = TjMedexaminfo::update_medexam_print_status(&testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(())
  }

  async fn save_patient_hazards(medinfo: &TjMedexaminfo, hdinfos: &Vec<TjHazardinfo>, db: &DbConnection) -> Result<()> {
    // info!("体检人员危害因素:{}", &medinfo.tj_poisionfactor);
    // info!("危害因素列表:{:?}", &hdinfos);
    if medinfo.tj_poisionfactor.is_empty() && hdinfos.len() <= 0 {
      //删除
      let ret = TjPatienthazards::delete(&vec![medinfo.tj_testid.to_string()], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      return Ok(());
    }
    let mut hdinfos = hdinfos.to_owned();

    if hdinfos.len() <= 0 {
      let poision = medinfo.tj_poisionfactor.replace("）", ")").replace("（", "(");
      // let hdnames: Vec<&str> = poision.split(|c| split.contains(c)).filter(|&f| f != "").map(|s| s).collect();
      let hdnames = utility::string::split_by_regex(&poision).unwrap_or_default();
      if hdnames.len() <= 0 {
        return Ok(());
      }
      // let hdnames: Vec<String> = hdnames.into_iter().map(|v| v).collect::<HashSet<_>>().into_iter().map(|v| v.to_string()).collect();
      info!("分割后的毒害因素:{:?}", &hdnames);
      let ret = HazardSvc::query_hazardinfos(&hdnames, db).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      hdinfos = ret.unwrap();
    }
    info!("更新后的毒害因素信息：{:?}", &hdinfos);
    //先从数据库查找当前的毒害因素
    let ret = TjPatienthazards::query(&medinfo.tj_testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let exist_pthdinfos = ret.unwrap();
    let mut new_pthdinfos: Vec<TjPatienthazards> = Vec::new();
    for hdinfo in hdinfos.iter() {
      if hdinfo.id <= 0 {
        continue;
      }
      let pthd: TjPatienthazards = TjPatienthazards {
        id: 0,
        tj_testid: medinfo.tj_testid.clone(),
        tj_hid: hdinfo.id,
        tj_poisionage: medinfo.tj_poisionage.clone(),
        tj_typeid: 0,
        tj_diseases: "".to_string(),
        tj_recheckitems: "".to_string(),
        tj_olcorpname: "".to_string(),
      };
      if new_pthdinfos.iter().find(|&p| p.tj_hid == pthd.tj_hid).is_none() {
        new_pthdinfos.push(pthd);
      }
    }
    //先删除不存在的
    let del_pthdinfos: Vec<TjPatienthazards> = exist_pthdinfos
      .iter()
      .filter(|&f| new_pthdinfos.iter().find(|&h| h.tj_hid == f.tj_hid).is_none())
      .map(|v| v.to_owned())
      .collect();
    let del_pthd_ids: Vec<i64> = del_pthdinfos.iter().map(|v| v.id).collect();
    info!("需要删除的毒害因素信息:{del_pthdinfos:?}");
    if del_pthd_ids.len() > 0 {
      let ret = TjPatienthazards::delete_by_ids(&del_pthd_ids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let add_hdinfos: Vec<TjPatienthazards> = new_pthdinfos
      .iter()
      .filter(|&f| exist_pthdinfos.iter().find(|&p| p.tj_hid == f.tj_hid).is_none())
      .map(|v| v.to_owned())
      .collect();
    info!("需要新增的毒害因素：{add_hdinfos:?}");
    if add_hdinfos.len() > 0 {
      let ret = TjPatienthazards::save_many(&add_hdinfos, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    //update poision age
    let update_hdinfos: Vec<TjPatienthazards> = new_pthdinfos
      .iter()
      .filter(|&f| exist_pthdinfos.iter().find(|&p| p.tj_hid == f.tj_hid && f.tj_poisionage != medinfo.tj_poisionage).is_some())
      .map(|v| v.to_owned())
      .collect();
    info!("Update hazard infos:{:?}", &update_hdinfos);
    if update_hdinfos.len() > 0 {
      let ret = TjPatienthazards::save_many(&update_hdinfos, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    Ok(())
  }

  pub async fn delete_medexaminfos(testids: &Vec<String>, db: &DbConnection) -> Result<i64> {
    // let new_testids: Vec<&str> = testids.iter().map(|v| v.as_str()).collect();
    let ret = TjMedexaminfo::delete(&testids, &db.get_connection()).await;
    if ret.is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    return Ok(ret.unwrap().to_i64().unwrap_or_default());
  }

  async fn check_existing_medexam_completed(pid: &str, testtype: i32, stdate: i64, corpid: i64, db: &DbConnection) -> Result<Vec<TjMedexaminfo>> {
    // let dtstart = stdate.to_string();
    let dtend = timeutil::timestamp_add_days(timeutil::current_timestamp(), 60);
    // let statuslow = 0;
    // let statushight = constant::ExamStatus::Allchecked as i32;
    let status = vec![
      constant::ExamStatus::Noprogress as i32,
      constant::ExamStatus::Appoint as i32,
      constant::ExamStatus::Register as i32,
      constant::ExamStatus::Examining as i32,
    ];
    // let status = (constant::ExamStatus::Noprogress as i32..constant::ExamStatus::Examining as i32).collect();
    let isrecheck = 0;
    let pids = vec![pid.to_string()];
    let testids: Vec<String> = Vec::new();
    let testtypes: Vec<i32> = vec![testtype];
    let ret = TjMedexaminfo::query_many(
      stdate,
      dtend,
      &testids,
      &pids,
      corpid,
      isrecheck,
      &vec![],
      &testtypes,
      &status,
      -1,
      &"".to_string(),
      &db.get_connection(),
    )
    .await;
    if ret.is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let medinfos = ret.unwrap();

    Ok(medinfos)
  }

  pub async fn query_checkitems(dto: &CheckitemsQueryDto, db: &DbConnection) -> Result<Vec<TjCheckiteminfo>> {
    // let testids: Vec<&str> = dto.testids.iter().map(|v| v.as_str()).collect();
    // let deptids: Vec<&str> = dto.deptids.iter().map(|v| v.as_str()).collect();
    let ret = TjCheckiteminfo::query_many(&dto.testids, &dto.deptids, dto.flag, dto.combined, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut datas = ret.unwrap();
    datas.sort_by(|a, b| a.tj_synid.cmp(&b.tj_synid).then(a.tj_showorder.cmp(&b.tj_showorder)));
    Ok(datas)
  }

  pub async fn save_checkitems(dto: &Vec<TjCheckiteminfo>, db: &DbConnection) -> Result<u64> {
    let mut ckitems = dto.to_owned();
    for ci in ckitems.iter_mut() {
      if ci.tj_abnormalflag == YesOrNo::No as i32 {
        CheckitemSvc::update_checktiem_result_flag(ci, 0, 0, &db).await;
      }
    }

    let ret = TjCheckiteminfo::save_many(&dto, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn update_checkitems(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, items: &Vec<TjIteminfo>, db: &DbConnection) -> Result<TjMedexaminfo> {
    //1）从数据库查找该用户的所有检查项目
    //2）新增的insert
    //2）不存在的delete
    let mut medinfo = medinfo.to_owned();
    info!("更新检查项目，体检信息：{:?}", &medinfo);
    let testid = medinfo.tj_testid.to_owned();
    if testid.is_empty() {
      return Err(anyhow!("testid is empty..."));
    }
    if medinfo.id <= 0 {
      let ret = TjMedexaminfo::query(&testid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      if let Some(ret_med) = ret.unwrap() {
        medinfo = ret_med;
      }
    }
    if medinfo.id <= 0 {
      return Err(anyhow!("不能找到该体检者的体检信息"));
    }
    if items.len() <= 0 {
      info!("没有任何项目信息，即将删除全部体检信息......");
      // return Err(anyhow!("items is empty"));
      let ret = TjCheckiteminfo::delete_many(&testid, &vec![], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let ret = TjTestsummary::delete(&vec![testid.to_string()], &vec![], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      return Ok(medinfo);
    }
    //所有新项目的组合编号
    let new_itemids: Vec<String> = items
      .iter()
      .filter(|f| f.tj_combineflag == constant::YesOrNo::Yes as i32)
      .map(|v| v.tj_itemid.clone())
      .collect::<HashSet<String>>()
      .into_iter()
      .collect();
    // let total_new_itemids: Vec<&str> = new_itemids.iter().map(|v| v.as_str()).collect();
    info!("new combine ids:{:?}", &new_itemids);

    let ret = TjDeptitem::query_many(&new_itemids, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let deptitems = ret.unwrap();

    let deptids: Vec<String> = deptitems.iter().map(|v| v.tj_deptid.to_owned()).collect::<HashSet<String>>().into_iter().collect();
    // let deptids: Vec<&str> = deptids.iter().map(|v| v.as_str()).collect();
    let ret = TjDepartinfo::query_many(&vec![], &deptids, -1, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    //新项目的所有科室信息
    let mut deptinfos = ret.unwrap();
    deptinfos.retain(|f| f.tj_sex == constant::Sex::All as i32 || f.tj_sex == constant::Sex::Unknown as i32 || f.tj_sex == ptinfo.tj_psex);

    //先处理项目信息
    let ret = TjCheckiteminfo::query(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let exist_ckitems = ret.unwrap();
    let exist_itemids: Vec<String> = exist_ckitems.iter().map(|v| v.tj_synid.to_owned()).collect::<HashSet<_>>().into_iter().collect();
    //新项目没有，原项目有，则删除
    let del_itemids: Vec<String> = exist_itemids
      .iter()
      .filter(|f| new_itemids.iter().find(|v| v.eq_ignore_ascii_case(&f)).is_none())
      .map(|m| m.clone())
      .collect();
    info!("to del itemids:{:?}", &del_itemids);
    if del_itemids.len() > 0 {
      // let del_itemids: Vec<&str> = del_itemids.iter().map(|v| v.as_str()).collect();
      let ret = TjCheckiteminfo::delete_many(&testid, &del_itemids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    //新项目有，原项目没有，则增加
    let add_itemids: Vec<String> = new_itemids
      .iter()
      .filter(|f| exist_itemids.iter().find(|v| v.eq_ignore_ascii_case(&f)).is_none())
      .map(|v| v.clone())
      .collect();
    info!("to add itemids:{:?}", &add_itemids);
    if add_itemids.len() > 0 {
      // let add_itemids: Vec<&str> = add_itemids.iter().map(|v| v.as_str()).collect();
      let ret = TjCombineinfo::query_many(&add_itemids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let combine_infos = ret.unwrap();

      let mut new_item_ids: Vec<String> = combine_infos.iter().map(|v| v.tj_itemid.to_owned()).collect::<HashSet<_>>().into_iter().collect();
      info!("new itemids to be inserted:{:?}", &new_item_ids);

      let ret = TjItemrangeinfo::query_many(-1, -1, &new_item_ids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let mut item_rangeinfos = ret.unwrap();
      item_rangeinfos.sort_by(|x, y| x.tj_sex.cmp(&y.tj_sex));
      // info!("项目参考值：{:?}", &item_rangeinfos);

      // let mut new_item_ids: Vec<&str> = new_item_ids.iter().map(|v| v.as_str()).collect();
      new_item_ids.extend(add_itemids);

      let ret = TjIteminfo::query_many(&new_item_ids, constant::YesOrNo::Yes as i32, -1, -1, &vec![], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let new_items = ret.unwrap();
      let mut new_checkitems: Vec<TjCheckiteminfo> = Vec::new();
      for item in new_items.iter() {
        if item.tj_sex != constant::Sex::Unknown as i32 && item.tj_sex != constant::Sex::All as i32 && item.tj_sex != ptinfo.tj_psex {
          continue;
        }
        let mut combid = item.tj_itemid.clone();
        if item.tj_combineflag == constant::YesOrNo::No as i32 {
          combid = combine_infos
            .iter()
            .find(|&f| f.tj_itemid.eq_ignore_ascii_case(&item.tj_itemid))
            .unwrap_or(&TjCombineinfo { ..Default::default() })
            .tj_combid
            .clone();
        }

        if combid.is_empty() {
          error!("can't find combine info for item:{}", &item.tj_itemid);
          continue;
        }
        let deptid = deptitems
          .iter()
          .find(|&f| f.tj_itemid.eq_ignore_ascii_case(&combid))
          .unwrap_or(&TjDeptitem { ..Default::default() })
          .tj_deptid
          .clone();
        if deptid.is_empty() {
          error!("can not find dept item by itemid:{}", &item.tj_itemid);
          continue;
        }
        let deptinfo = deptinfos
          .iter()
          .find(|&f| f.tj_deptid.eq_ignore_ascii_case(&deptid))
          .map_or(TjDepartinfo { ..Default::default() }, |f| f.to_owned());

        if deptinfo.tj_deptid.is_empty() {
          error!("can't find deptinfo by deptid:{}", &deptid);
          continue;
        }
        let rangeinfo = ItemSvc::find_itemrangeinfo(ptinfo.tj_psex, medinfo.tj_age, &item.tj_itemid, &item_rangeinfos);
        let ci = MedexamSvc::convert_iteminfo_to_checkiteminfo(&medinfo.tj_testid, &combid, &deptinfo, item, &rangeinfo);

        new_checkitems.push(ci);
      }
      //生成条码
      let ret = BarCodeSvc::generate_bar_code(&mut new_checkitems, db).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }

      //insert new items into database
      let ret = TjCheckiteminfo::save_many(&new_checkitems, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    //处理科室小结信息
    let mut new_summaries: Vec<TjTestsummary> = Vec::new();
    for val in deptinfos.iter() {
      let summary = TjTestsummary {
        tj_testid: testid.clone(),
        tj_deptid: val.tj_deptid.clone(),
        ..Default::default()
      };
      new_summaries.push(summary);
    }
    let ret = TjTestsummary::query(&testid, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let exist_summaries = ret.unwrap();
    // info!("存在的科室小结信息:{:?}", &exist_summaries);
    //先删除不存在的小结信息
    let del_summaries: Vec<String> = exist_summaries
      .iter()
      .filter(|f| new_summaries.iter().find(|f2| f2.tj_deptid.eq_ignore_ascii_case(&f.tj_deptid)).is_none())
      .map(|v| v.tj_deptid.to_owned())
      .collect();
    // info!("要删除的科室小结信息:{:?}", &del_summaries);
    if del_summaries.len() > 0 {
      // let del_summaries: Vec<&str> = del_summaries.iter().map(|v| v.as_str()).collect();
      let ret = TjTestsummary::delete(&vec![testid.to_string()], &del_summaries, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    //插入需要新插入的科室信息
    let insert_summaries: Vec<TjTestsummary> = new_summaries
      .iter()
      .filter(|f| exist_summaries.iter().find(|f2| f2.tj_deptid.eq_ignore_ascii_case(&f.tj_deptid)).is_none())
      .map(|v| v.to_owned())
      .collect();
    // info!("要插入的科室小结信息:{:?}", &insert_summaries);
    if insert_summaries.len() > 0 {
      let ret = TjTestsummary::save_many(&insert_summaries, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    //更新已经存在的小结的状态
    // let mut update_summaries: Vec<TjTestsummary> = exist_summaries
    //   .iter()
    //   .filter(|f| new_summaries.iter().find(|f2| f2.tj_deptid.eq_ignore_ascii_case(&f.tj_deptid)).is_some())
    //   .map(|v| v.clone())
    //   .collect();
    // if update_summaries.len() > 0 {
    //   update_summaries.iter_mut().for_each(|f| {
    //     f.tj_isfinished = 0;
    //     f.tj_forceend = 0;
    //   });
    //   let ret = TjTestsummary::save_many(&update_summaries).await;
    //   if ret.as_ref().is_err() {
    //     error!("{}", ret.as_ref().unwrap_err().to_string());
    //     return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    //   }
    // }
    //更新体检状态
    let ret = TjTestsummary::query_many(&vec![medinfo.tj_testid.to_string()], &vec![], 0, 0, -1, "", -1, &db.get_connection()).await;
    if ret.is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let summaries = ret.unwrap();
    medinfo.tj_total = summaries.len() as i32;
    medinfo.tj_completed = summaries.iter().filter(|&f| f.tj_isfinished == 1).count() as i32;
    info!("开始更新体检状态，total:{},completed:{}", medinfo.tj_total, medinfo.tj_completed);
    if medinfo.tj_completed == 0 {
      medinfo.tj_checkstatus = constant::ExamStatus::Register as i32;
    } else if medinfo.tj_completed > 0 && medinfo.tj_completed < medinfo.tj_total {
      medinfo.tj_checkstatus = constant::ExamStatus::Examining as i32;
    } else {
      medinfo.tj_checkstatus = constant::ExamStatus::Examined as i32;
      medinfo.tj_completed = medinfo.tj_total;
    }
    // info!("medinfo:{:?}", &medinfo);
    let ret = TjMedexaminfo::save(&medinfo, &db.get_connection()).await;
    if ret.is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }

    Ok(medinfo)
  }

  fn convert_iteminfo_to_checkiteminfo(testid: &String, combid: &String, deptinfo: &TjDepartinfo, info: &TjIteminfo, rangeinfo: &Option<TjItemrangeinfo>) -> TjCheckiteminfo {
    let mut tr = TjCheckiteminfo {
      id: 0,
      tj_testid: testid.clone(),
      tj_itemid: info.tj_itemid.clone(),
      tj_synid: combid.clone(),
      tj_itemname: info.tj_itemname.clone(),
      tj_result: info.tj_defaultresult.clone(),
      tj_itemunit: info.tj_itemunit.clone(),
      tj_abnormalflag: 0,
      tj_barflag: info.tj_barflag,
      tj_abnormalshow: "".to_string(),
      tj_combineflag: info.tj_combineflag,
      tj_deptorder: deptinfo.tj_showorder,
      tj_showorder: info.tj_showorder,
      tj_combineorder: 0,
      tj_deptid: deptinfo.tj_deptid.clone(),
      tj_lowvalue: info.tj_lowvalue.to_string(),
      tj_uppervalue: info.tj_uppervalue.to_string(),
      ..Default::default()
    };
    if rangeinfo.is_some() {
      let irangeinfo = rangeinfo.to_owned().unwrap();
      tr.tj_lowvalue = irangeinfo.tj_lowvalue.to_string();
      tr.tj_uppervalue = irangeinfo.tj_uppervalue.to_string();
    }

    let range = match info.tj_reftype.to_ascii_uppercase().as_str() {
      "X-Y" | "X_Y" | "X<>Y" => format!("{}-{}", tr.tj_lowvalue, tr.tj_uppervalue),
      "<Y" | "<=Y" => info.tj_reftype.replace("Y", &tr.tj_uppervalue),
      ">X" | ">=X" => info.tj_reftype.replace("X", &tr.tj_lowvalue),
      _ => tr.tj_lowvalue.to_string(),
    };

    // if info.tj_valuetype.eq_ignore_ascii_case(&constant::ValueType::DingLiang.to_string()) {
    tr.tj_itemrange = range;
    //   if info.tj_reftype.eq_ignore_ascii_case("X-Y") || info.tj_reftype.eq_ignore_ascii_case("X_Y") {
    //     tr.tj_itemrange = format!("{}-{}", tr.tj_lowvalue, tr.tj_uppervalue);
    //   } else if info.tj_reftype.eq_ignore_ascii_case("<Y") {
    //     tr.tj_itemrange = format!("<{}", tr.tj_uppervalue);
    //   } else if info.tj_reftype.eq_ignore_ascii_case(">X") {
    //     tr.tj_itemrange = format!(">{}", tr.tj_lowvalue);
    //   } else {
    //     // error!("unknow item reference value:{}", &ti.tj_reftype);
    //     tr.tj_itemrange = "".to_string();
    //   }
    // }

    tr
  }
}
