//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Co<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, Debug, DeriveEntity)]
pub struct Entity;

impl EntityName for Entity {
  fn table_name(&self) -> &str {
    "s_cdc_checkitem_zwx"
  }
}

#[derive(<PERSON>lone, Debug, PartialEq, DeriveModel, DeriveActiveModel, Eq, Serialize, Deserialize)]
pub struct Model {
  pub id: i32,
  pub cdc_itemcode: String,
  pub cdc_item_name: String,
  pub cdc_deptid: i32,
  pub cdc_type: i32,
  pub tj_itemid: String,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveColumn)]
pub enum Column {
  Id,
  CdcItemcode,
  CdcItemName,
  CdcDeptid,
  CdcType,
  TjItemid,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, En<PERSON><PERSON><PERSON>, DerivePrimaryKey)]
pub enum PrimaryKey {
  Id,
}

impl PrimaryKeyTrait for PrimaryKey {
  type ValueType = i32;
  fn auto_increment() -> bool {
    true
  }
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl ColumnTrait for Column {
  type EntityName = Entity;
  fn def(&self) -> ColumnDef {
    match self {
      Self::Id => ColumnType::Integer.def(),
      Self::CdcItemcode => ColumnType::String(Some(10u32)).def(),
      Self::CdcItemName => ColumnType::String(Some(100u32)).def(),
      Self::CdcDeptid => ColumnType::Integer.def(),
      Self::CdcType => ColumnType::Integer.def(),
      Self::TjItemid => ColumnType::String(Some(20u32)).def(),
    }
  }
}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
