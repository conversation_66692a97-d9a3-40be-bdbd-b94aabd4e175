//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_organization")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_orgcode: String,
  pub tj_orgname: String,
  pub tj_orgabbrname: Option<String>,
  pub tj_orglphone: Option<String>,
  pub tj_orgzphone: Option<String>,
  pub tj_orgcontactor: Option<String>,
  pub tj_orgweb: Option<String>,
  pub tj_orgaddress: Option<String>,
  pub tj_orgpublicbus: Option<String>,
  pub tj_orgreportnum: Option<String>,
  pub tj_orgnreportnum: Option<String>,
  pub tj_orgemail: Option<String>,
  pub tj_orgpostcode: Option<String>,
  pub tj_orgshowlogo: i32,
  pub tj_orglogo: Option<String>,
  pub tj_orgfax: Option<String>,
  pub tj_orgdjh: Option<String>,
  pub tj_orgtjyj: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
