use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok},
  auth::auth::Claims,
  config::settings::Settings, // external::externalservice::ExtService,
};
use axum::{Extension, Json};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{
  dto::*,
  external::externalservice::ExtService,
  medexam::{checkitemsvc::CheckitemSvc, medexamsvc::MedexamSvc},
};
use serde_json::Value;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::*;
use utility::uidservice::UidgenService;
//query_summaries
pub async fn query_checkitems(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<CheckitemsQueryDto>) -> Json<Value> {
  info!("query checkitems dto:{:?}", &dto);

  let ret = MedexamSvc::query_checkitems(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjCheckiteminfo>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_checkitems(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<Vec<TjCheckiteminfo>>) -> Json<Value> {
  // info!("save checkitems results dto:{:?}", &dto);

  let ret = MedexamSvc::save_checkitems(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn update_checkitem_result_flag(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<CheckitemResultFlagDto>) -> Json<Value> {
  info!("update_checkitem_result_flag dto:{:?}", &dto);
  let mut tr = dto.checkitem.to_owned();
  let age = dto.age;
  let sex = dto.sex;
  CheckitemSvc::update_checktiem_result_flag(&mut tr, age, sex, &db).await;
  // if ret.as_ref().is_err() {
  //   return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  // }
  // let results = ret.unwrap();
  response_json_value_ok(1, tr)
}

pub async fn update_checkitems(
  _claims: Claims,
  Extension(db): Extension<Arc<DbConnection>>,
  config: Extension<Arc<Settings>>,
  uid: Extension<Arc<RwLock<UidgenService>>>,
  Json(dto): Json<CheckitemUpdateDto>,
) -> Json<Value> {
  // info!("update checkitems dto:{:?}", &dto);

  let ret = MedexamSvc::update_checkitems(&dto.medinfo, &dto.ptinfo, &dto.items, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), TjMedexaminfo { ..Default::default() });
  }
  let results = ret.unwrap();
  //外部对接
  let dto = ExternalDTO {
    testid: dto.medinfo.tj_testid.to_string(),
    exttype: nodipservice::common::constant::ExtOpType::EDIT as i32,
    additional: dto.medinfo.tj_additional.to_string(),
  };
  let mut idgen = uid.write().await;
  let ret = ExtService::do_external_upload(
    &db,
    &config.external.exttype,
    // &config.application.proxyserver,
    &config.external.serverurl,
    &config.audiogram.dburi,
    &mut idgen,
    0,
    &dto,
  )
  .await; //
  if ret.as_ref().is_err() {
    return response_json_value_error(
      format!("external upload error:{}", ret.as_ref().unwrap_err().to_string()).as_str(),
      TjMedexaminfo { ..Default::default() },
    );
  }

  response_json_value_ok(1, results)
}
