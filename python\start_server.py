#!/usr/bin/env python3
# coding=utf-8

"""
Report Server Startup Script

This script starts the Report Server with proper configuration and error handling.
"""

import os
import sys
import logging
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import APIConfig
from reportserver import app, logger, initialize_database_connection, close_database


def setup_logging(config: APIConfig):
    """Setup logging configuration"""
    log_level = getattr(logging, config.log_level.upper(), logging.INFO)

    # Create log directory if it doesn't exist
    log_dir = os.path.dirname(config.log_file)
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)

    # Configure logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )


def check_dependencies():
    """Check if all required dependencies are available"""
    required_modules = [
        'flask',
        'flask_cors',
        'reportlab',
        'sqlalchemy',
        'docx',
        'pymysql'
    ]

    # Optional modules (nice to have but not required)
    optional_modules = [
        'tomli'  # TOML support - can fallback to JSON
    ]

    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)

    # Check optional modules and warn if missing
    missing_optional = []
    for module in optional_modules:
        try:
            __import__(module)
        except ImportError:
            missing_optional.append(module)

    if missing_modules:
        print("Error: Missing required dependencies:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\nPlease install missing dependencies using:")
        print("  pip install -r requirements.txt")
        return False

    if missing_optional:
        print("Warning: Missing optional dependencies:")
        for module in missing_optional:
            print(f"  - {module} (will use fallback)")

    return True


def check_environment():
    """Check if the environment is properly configured"""
    # Check if config files exist
    config_files = [
        "./config/nodipexam.toml"
    ]

    missing_files = []
    for config_file in config_files:
        if not os.path.exists(config_file):
            missing_files.append(config_file)

    if missing_files:
        print("Warning: Missing configuration files:")
        for file in missing_files:
            print(f"  - {file}")
        print("The server may not function properly without these files.")

    # Check if fonts directory exists
    fonts_dir = "./fonts"
    if not os.path.exists(fonts_dir):
        print(f"Warning: Fonts directory '{fonts_dir}' not found.")
        print("Report generation may fail without proper fonts.")

    return True


def main():
    """Main function to start the server"""
    print("=" * 60)
    print("Report Server - Starting Up")
    print("=" * 60)

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    # Check environment
    check_environment()

    # Load configuration
    try:
        config = APIConfig()
        if not config.validate():
            print("Configuration validation failed!")
            sys.exit(1)
    except Exception as e:
        print(f"Error loading configuration: {e}")
        sys.exit(1)

    # Setup logging
    setup_logging(config)

    # Print configuration
    logger.info("Configuration loaded successfully:")
    logger.info(str(config))

    # Ensure output directory exists
    os.makedirs(config.output_directory, exist_ok=True)
    logger.info(f"Output directory: {os.path.abspath(config.output_directory)}")

    # Initialize database connection
    if not initialize_database_connection():
        logger.error("Failed to initialize database connection. Server will start but may not function properly.")
        logger.error("Please check your database configuration and try again.")

    # Start the server
    try:
        logger.info(f"Starting Report Server on {config.host}:{config.port}")
        logger.info(f"Debug mode: {config.debug}")
        logger.info("Server is ready to accept requests!")
        print(f"\nServer running at: http://{config.host}:{config.port}")
        print("API Documentation:")
        print(f"  Health Check: http://{config.host}:{config.port}/api/health")
        print(f"  Report Types: http://{config.host}:{config.port}/api/reports/types")
        print(f"  Generate Report: POST http://{config.host}:{config.port}/api/reports/generate")
        print("\nPress Ctrl+C to stop the server")
        print("=" * 60)

        # Suppress Werkzeug development server warning
        import logging
        log = logging.getLogger('werkzeug')
        log.setLevel(logging.ERROR)

        print("⚠️  Using Flask development server - not recommended for production")
        print("   For production, use: gunicorn --config gunicorn.conf.py wsgi:app")

        app.run(
            host=config.host,
            port=config.port,
            debug=config.debug,
            threaded=True
        )

    except KeyboardInterrupt:
        logger.info("Server stopped by user")
        print("\nServer stopped.")
    except Exception as e:
        logger.error(f"Server error: {e}", exc_info=True)
        print(f"Server error: {e}")
        sys.exit(1)
    finally:
        # Clean up database connection on shutdown
        logger.info("Shutting down server...")
        close_database()


if __name__ == '__main__':
    main()
