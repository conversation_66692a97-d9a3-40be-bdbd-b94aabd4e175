use datacontroller::entities::tj_staffadmin::TjStaffadmin;
use jsonwebtoken::{errors::<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, TokenD<PERSON>, Validation};
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
// use serde_json::json;

type TokenResult = Result<TokenData<Claims>, Error>;
static KEY: [u8; 16] = *include_bytes!("./secret.key");
static VALIDATION: Lazy<Validation> = Lazy::new(Validation::default);
static HEADER: Lazy<Header> = Lazy::new(Header::default);

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
  pub exp: usize, // Expiration time (as UTC timestamp). validate_exp defaults to true in validation
  pub iat: usize, // Issued at (as UTC timestamp)
  pub user: TjStaffadmin,
}
impl Claims {
  pub fn new(user: TjStaffadmin) -> Self {
    Self {
      exp: (chrono::Local::now() + chrono::Duration::days(30)).timestamp() as usize,
      iat: chrono::Local::now().timestamp() as usize,
      user: user,
    }
  }
}

#[derive(Debug, Serialize)]
pub struct AuthBody {
  pub access_token: String,
  pub token_type: String,
}
impl AuthBody {
  pub fn new(access_token: String) -> Self {
    Self {
      access_token,
      token_type: "Bearer".to_string(),
    }
  }
}
#[derive(Debug, Deserialize)]
pub struct AuthPayload {
  pub client_id: String,
  pub client_secret: String,
}

#[derive(Debug)]
pub enum AuthError {
  WrongCredentials,
  MissingCredentials,
  TokenCreation,
  InvalidToken,
}
pub struct Keys {
  encoding: EncodingKey,
  decoding: DecodingKey,
}

impl Keys {
  fn new(secret: &[u8]) -> Self {
    Self {
      encoding: EncodingKey::from_secret(secret),
      decoding: DecodingKey::from_secret(secret),
    }
  }
}

pub fn create(user: TjStaffadmin) -> Result<String, Error> {
  let encoding_key = EncodingKey::from_secret(&KEY);
  let claims = Claims::new(user);

  jsonwebtoken::encode(&HEADER, &claims, &encoding_key)
}

pub fn decode(token: &str) -> TokenResult {
  let decoding_key = DecodingKey::from_secret(&KEY);

  jsonwebtoken::decode::<Claims>(token, &decoding_key, &VALIDATION)
}
