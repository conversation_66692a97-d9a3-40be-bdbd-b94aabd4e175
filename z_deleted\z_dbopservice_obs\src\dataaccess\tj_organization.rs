use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub struct TjOrganization {
  pub id: i32,
  pub tj_orgcode: String,
  pub tj_orgname: String,
  pub tj_orgabbrname: String,
  pub tj_orglphone: String,
  pub tj_orgzphone: String,
  pub tj_orgcontactor: String,
  pub tj_orgweb: String,
  pub tj_orgaddress: String,
  pub tj_orgpublicbus: String,
  pub tj_orgreportnum: String,
  pub tj_orgnreportnum: String,
  pub tj_orgemail: String,
  pub tj_orgpostcode: String,
  pub tj_orgshowlogo: i32,
  pub tj_orglogo: String,
  pub tj_orgfax: String,
  pub tj_orgdjh: String,
  pub tj_orgtjyj: String,
}
crud!(TjOrganization {}, "tj_organization");
rbatis::impl_select!(TjOrganization{query(code:&str) -> Option => "`where tj_orgcode = #{code} limit 1 `"});
