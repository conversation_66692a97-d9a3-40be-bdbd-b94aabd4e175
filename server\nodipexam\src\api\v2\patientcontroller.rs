use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok, DataBody, ResponseBody}, auth::auth::Claims, common::constant::{self}
};
use axum::{extract::Path, Extension, Json};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{dto::*, medexam::patientsvc::PatientSvc};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

pub async fn query_patient_info(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Path(idcard): Path<String>) -> Json<Value> {
  info!("start to query patient by idcard:{}", &idcard);

  let ret = PatientSvc::query_patient_by_idcard(&idcard, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), Vec::<TjPatient>::new());
  }
  let results = ret.unwrap();
  if results.is_none() {
    let databody = DataBody::new(0, TjPatient { ..Default::default() });
    Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
  } else {
    let results = results.unwrap();
    let databody = DataBody::new(1, results);
    Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
  }
}

pub async fn query_patient_infos(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<PatientQueryDto>) -> Json<Value> {
  info!("start to query patients by dto:{:?}", &dto);

  let ret = PatientSvc::query_patients(&dto, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), Vec::<TjPatient>::new());
  }

  let results = ret.unwrap();
  let databody = DataBody::new(results.len() as u64, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn update_patient(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjPatient>) -> Json<Value> {
  let ret = PatientSvc::update_patient(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn save_patient(_claims: Claims,Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjPatient>) -> Json<Value> {
  let ret = PatientSvc::save_patient(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
