//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, PartialEq, Default, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "dams_appoint")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub sex: i32,
  pub age: i32,
  pub marry: i32,
  pub job_type: i32,
  pub physical_examination_package: i32,
  pub physical_examination_type: i32,
  pub create_date: i64,
  pub order_no: String,
  pub package_number: String,
  pub name: String,
  pub id_card: String,
  pub phone: String,
  pub company: String,
  pub comnanrld: String,
  pub hospital_name: String,
  pub check_time: String,
  pub working_years: String,
  pub contact_damage: String,
  pub hazardous_factors: String,
  pub reservestr1: String,
  pub reservestr2: String,
  pub reservestr3: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
