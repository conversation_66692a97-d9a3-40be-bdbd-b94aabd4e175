use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub struct TjCorpoccureportFc {
  pub id: i64,
  pub tj_old_testid: String,
  pub tj_report_id: i64,
  pub tj_old_rptnum: String,
  pub tj_new_testid: String,
  pub cdatetime: i64,
  pub tj_new_rptnum: String,
}
crud!(TjCorpoccureportFc {}, "tj_corpoccureport_fc");
rbatis::impl_select!(TjCorpoccureportFc{query(newtestid:&str) ->Option => "`where tj_new_testid = #{newtestid} limit 1 `"});
rbatis::impl_select!(TjCorpoccureportFc{query_many(id:&i64) => "`where tj_report_id = #{id} `"});
