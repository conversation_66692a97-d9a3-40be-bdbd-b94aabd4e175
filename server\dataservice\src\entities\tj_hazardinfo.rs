//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_hazardinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_tid: i32,
  #[sea_orm(unique)]
  pub tj_hname: String,
  pub tj_pyjm: String,
  pub tj_showorder: i32,
  pub tj_forbiden: String,
  pub tj_memo: String,
  pub tj_extcode: String,
  pub tj_status: i32,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
