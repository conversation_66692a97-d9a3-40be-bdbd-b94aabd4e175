//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_occucondition")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_testtype: Option<i32>,
  pub tj_poision: Option<i32>,
  pub tj_sex: Option<i32>,
  pub tj_itemid: Option<String>,
  pub tj_condition: Option<String>,
  pub tj_refvalue: Option<String>,
  pub tj_connector: Option<String>,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
