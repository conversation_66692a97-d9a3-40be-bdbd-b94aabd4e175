use std::{
  collections::{HashMap, HashSet},
  hash::Hash,
};

use crate::{
  dataview::{medinfoview::MedexaminfosView, nxmedexaminfo::MedexamSyncView},
  prelude::{DictService, ExamStatus, PAYMETHOD, PID, TESTID},
  service::hazardsvc,
};
use anyhow::{anyhow, Result};
use datacontroller::{
  datasetup::DbConnection,
  entities::{tj_corpinfo::TjCorpinfo, tj_medexaminfo::TjMedexaminfo, tj_patient::TjPatient, tj_patienthazards::TjPatienthazards, tj_staffadmin::TjStaffadmin},
};
use utility::timeutil::{self, *};
pub struct MedexamService;
impl MedexamService {
  pub async fn medexam_appoint(userinfo: &TjStaffadmin, medviews: &mut Vec<MedexaminfosView>, dictsvc: &DictService, db: &DbConnection) -> Result<Vec<MedexaminfosView>> {
    info!("medexaminfoviews:{:?}", &medviews);
    let pcmpids: Vec<i64> = medviews.iter().map(|x| x.p_cmpid).collect::<HashSet<_>>().into_iter().collect::<Vec<i64>>();

    //1 处理企业信息
    let ret = TjCorpinfo::query_many_by_cmpid(&pcmpids, &db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let corpinfos = ret.unwrap();
    let corpids: Vec<i64> = corpinfos.iter().map(|x| x.id).collect();
    info!("Corp ids:{:?}", &corpids);
    //2 根据身份证号码检查体检人员是否存在
    //2.1 如果不存在，则需要插入新的人员信息
    let idcards: Vec<String> = medviews.iter().map(|x| x.pidcard.clone()).collect::<HashSet<_>>().into_iter().collect::<Vec<String>>();
    info!("patient idcards:{:?}", &idcards);
    let ret = TjPatient::query_many_by_idcard(&idcards, &db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    //所有的体检人员信息，根据身份证信息查找
    let mut patients = ret.unwrap();
    info!("patient infos:{:#?}", &patients);
    let pids: Vec<String> = patients.iter().map(|x| x.tj_pid.clone()).collect();
    info!("初始的patient ids:{:?}", pids);

    let mut medinfos: Vec<TjMedexaminfo> = Vec::new();
    let mut ptinfos: HashMap<String, TjPatient> = HashMap::new();
    for val in medviews.iter() {
      let (mut medinfo, mut ptinfo) = MedexamService::split_medexaminfo_views(val);
      // let med_corp = corpinfos.iter().find(|x| x.tj)
      let pat_info = patients.iter().find(|x| x.tj_pidcard.eq_ignore_ascii_case(&ptinfo.tj_pidcard));
      if pat_info.is_none() || ptinfo.tj_pidcard.is_empty() {
        info!("体检人员信息不存在,写入新的体检人员信息和体检信息，idcard：{}", &val.pidcard);
        //该体检人员信息不存在，那体检信息也肯定不存在
        let ret = dictsvc.generate_new_id(PID, db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        ptinfo.id = 0;
        ptinfo.tj_pid = ret.unwrap();
        ptinfo.tj_popdate = current_timestamp();
        ptinfo.tj_staffid = userinfo.id;
        medinfo.tj_pid = ptinfo.tj_pid.to_owned();
        ptinfos.insert(ptinfo.tj_pidcard.clone(), ptinfo.clone());
        patients.push(ptinfo);
        // let ret = dictsvc.generate_new_id(TESTID, db).await;
        // if ret.as_ref().is_err() {
        //   return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        // }
        medinfo.id = 0;
        // medinfo.tj_testid = ret.unwrap();
        medinfos.push(medinfo);

        continue;
      }

      //体检人员信息已经存在
      let patient_info = pat_info.unwrap();
      info!("找到人员信息:{:?}", &patient_info);
      medinfo.tj_pid = patient_info.tj_pid.to_owned();
      ptinfo.id = patient_info.id;
      ptinfo.tj_pid = patient_info.tj_pid.to_owned();
      ptinfo.tj_staffid = userinfo.id;
      // update_ptinfos.push(ptinfo);
      ptinfos.insert(ptinfo.tj_pidcard.clone(), ptinfo.clone());

      medinfo.id = 0;
      medinfos.push(medinfo);
    }
    info!("需要处理的medinfos:{:#?}", &medinfos);
    info!("需要处理的patients:{:#?}", &ptinfos);
    //保存体检人员信息
    let ptinfos = ptinfos.values().map(|x| x.clone()).collect::<Vec<TjPatient>>();
    let ret = TjPatient::save_many(&ptinfos, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let pids: Vec<String> = ptinfos.iter().map(|x| x.tj_pid.clone()).collect();
    info!("处理后的patient ids:{:?}", pids);

    //根据企业编号，体检类型，体检人员ID，检查3个月以内有没有未完成的体检。
    let current = timeutil::current_timestamp();
    let current = timeutil::timestamp_add_days(current, 90);
    let past = timeutil::timestamp_add_days(current, -90);
    let testids: Vec<String> = Vec::new();
    let ret = TjMedexaminfo::query_many_by_dto(
      past,
      current,
      &testids,
      -1,
      &corpids,
      0,
      ExamStatus::Noprogress as i32,
      ExamStatus::Examining as i32,
      &pids,
      "",
      -1,
      "",
      &db,
    )
    .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    //现有的体检人员，未结束的
    let exist_medinfos = ret.unwrap();
    info!("已经存在的体检信息:{:?}", &exist_medinfos);

    for medinfo in medinfos.iter_mut() {
      medinfo.tj_recorddate = current_timestamp();
      medinfo.tj_recorder = userinfo.id.to_string();
      //1）检查体检信息是否存在,根据企业，体检类型和pid
      let is_exist = exist_medinfos
        .iter()
        .find(|x| x.tj_testtype == medinfo.tj_testtype && x.tj_pid == medinfo.tj_pid && x.tj_corpnum == medinfo.tj_corpnum);
      if is_exist.is_some() {
        info!(
          "pid:{},corp:{},testtype:{}的体检信息已经存在，仅作更新......",
          medinfo.tj_pid,
          medinfo.tj_corpnum,
          medinfo.tj_testtype
        );
        //update medinfo
        let e_med = is_exist.unwrap();
        medinfo.tj_testid = e_med.tj_testid.to_owned();
        medinfo.id = e_med.id;
        continue;
      }
      let ret = dictsvc.generate_new_id(TESTID, db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      medinfo.tj_testid = ret.unwrap();
      medinfo.id = 0;
    }

    //保存体检信息
    info!("最新的体检信息:{:#?}", &medinfos);
    let ret = TjMedexaminfo::save_many(&medinfos, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    //保存毒害因素信息
    let poision_factors: Vec<String> = medinfos.iter().map(|x| x.tj_poisionfactor.clone()).collect::<HashSet<_>>().into_iter().collect();
    info!("poision factors:{:?}", &poision_factors);
    let ret = hazardsvc::HazardSvc::check_hazardinfos(&poision_factors, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let hdinfos = ret.unwrap();
    //生成体检人员毒害因素信息
    let mut patient_hazards: Vec<TjPatienthazards> = Vec::new();
    for med in medinfos.iter() {
      let hds: Vec<String> = hazardsvc::HazardSvc::split_hazards(&med.tj_poisionfactor);
      for hd in hds.iter() {
        let ret = hdinfos.iter().find(|p| p.tj_hname.eq_ignore_ascii_case(hd));
        let hdinfo;
        if ret.is_none() {
          //generate new hdinfos//理论上不会有这种事情，最好再处理以下，以保证安全
          let ret = hazardsvc::HazardSvc::insert_new_hazardinfo(&hd, 1, db).await;
          if ret.as_ref().is_err() {
            error!("{}", ret.as_ref().err().unwrap().to_string());
            return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
          }
          hdinfo = ret.unwrap();
        } else {
          hdinfo = ret.unwrap().to_owned();
        }
        let pthd = TjPatienthazards {
          id: 0,
          tj_testid: med.tj_testid.to_owned(),
          tj_hid: hdinfo.id,
          tj_poisionage: med.tj_poisionage.to_owned(),
          ..Default::default()
        };
        patient_hazards.push(pthd);
      }
    }
    let ret = MedexamService::update_patient_hazards(&patient_hazards, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    Ok(medviews.clone())
  }

  //体检信息登记
  //1)根据身份证号查找人员信息
  pub async fn medexam_register(userinfo: &TjStaffadmin, medviews: &Vec<MedexaminfosView>) -> Result<()> {
    for medinfo in medviews.iter() {
      //start to register
    }
    Ok(())
  }

  pub async fn medexam_query() -> Result<()> {
    Ok(())
  }

  pub async fn update_patient_hazards(pthds: &Vec<TjPatienthazards>, db: &DbConnection) -> Result<()> {
    let testids: Vec<String> = pthds.iter().map(|x| x.tj_testid.clone()).collect::<HashSet<_>>().into_iter().collect();
    let ret = TjPatienthazards::delete_many(&testids, db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    //insert batch
    // info!("patient hazard:{:#?}", &pthds);
    let ret = TjPatienthazards::insert_many(pthds, db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    Ok(())
  }

  pub fn split_medexaminfo_views(medview: &MedexaminfosView) -> (TjMedexaminfo, TjPatient) {
    let medinfo = TjMedexaminfo {
      id: 0,
      tj_testid: medview.testid.to_owned(),
      tj_pid: medview.pid.to_owned(),
      tj_age: medview.age,
      tj_testcat: medview.testcatid,
      tj_testsource: 0,
      tj_testtype: medview.testtypeid,
      tj_corpnum: medview.corpnum,
      tj_empid: medview.empid.to_owned(),
      tj_workage: medview.workage.to_owned(),
      tj_wtcode: medview.worktypecode.to_owned(),
      tj_worktype: medview.worktypename.to_owned(),
      tj_poisionfactor: medview.poisionfactor.to_owned(),
      tj_poisionage: medview.poisionage.to_owned().to_owned(),
      tj_recorddate: current_timestamp(),
      tj_recorder: "".to_owned(),
      tj_testdate: medview.testdate,
      tj_expdate: timestamp_add_days(medview.testdate, 30),
      tj_subdate: medview.testdate,
      tj_checkstatus: medview.checkstatusid,
      tj_printflag: medview.printflagid,
      tj_printtimes: medview.printtimes,
      tj_rptnum: medview.reportnum.to_owned(),
      tj_peid: 0,
      tj_isrecheck: medview.isrecheck,
      tj_oldtestid: "".to_string(),
      tj_rechecktimes: medview.rechecktimes,
      tj_push: 0,
      tj_num: 0,
      tj_pushstatus: 0,
      tj_upload: medview.upload,
      tj_uploadtime: 0,
      tj_syncstatus: medview.syncstatus,
      tj_paymethod: medview.paymethod,
      tj_packagename: medview.packagename.to_owned(),
      p_medid: medview.p_medid,
    };
    let ptinfo = TjPatient {
      id: 0,
      tj_pid: medview.pid.to_owned(),
      tj_pname: medview.pname.to_owned(),
      tj_psex: medview.sexid,
      tj_pmarriage: medview.pmarriageid,
      tj_ptestnum: 0,
      tj_paddress: medview.paddress.to_owned(),
      tj_pphone: medview.pmobile.to_owned(),
      tj_pemail: "".to_string(),
      tj_pbirthday: medview.pbirthday.to_owned(),
      tj_nation: medview.nation.to_owned(),
      tj_pidcard: medview.pidcard.to_owned(),
      tj_pcareer: medview.pcareer.to_owned(),
      tj_pmobile: medview.pmobile.to_owned(),
      tj_photo: medview.photo.to_owned(),
      tj_cryptflag: 0,
      tj_popdate: current_timestamp(),
      tj_staffid: 0,
      tj_pmemo: "".to_string(),
      tj_syncflag: 0,
      p_wkid: medview.p_wkid,
    };
    (medinfo, ptinfo)
  }
}
