use anyhow::{anyhow, Result};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use rust_decimal::Decimal;
use tracing::*;
use utility::timeutil;
use uuid::Uuid;

use crate::{
  common::constant::{self, DictType, ExtOpType, SysParm, YesOrNo},
  dto::ExternalDTO,
  external::MessageService,
  SYSCACHE,
};

use super::*;

pub struct Herenkj;

impl Herenkj {
  #[tracing::instrument(name = "upload_medexaminfo >>", skip_all)]
  #[allow(unused_variables)]
  pub async fn upload_medexaminfo(
    dto: &ExternalDTO,
    medinfo: &TjMedexaminfo,
    new_extitems: &Vec<ExtCheckiteminfo>,
    exist_items: &Vec<ExtCheckiteminfo>,
    server: &str,
    force: i32,
    db: &DbConnection,
  ) -> Result<String> {
    let ptinfo = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
    if ptinfo.as_ref().is_err() {
      return Err(anyhow!("{}", ptinfo.as_ref().unwrap_err().to_string()));
    }
    let ptinfo = ptinfo.unwrap();
    if ptinfo.is_none() {
      return Err(anyhow!("Can't find patient by pid:{}", &medinfo.tj_pid));
    }
    let mut ptinfo = ptinfo.unwrap();
    let mut page = medinfo.tj_age.to_string();
    if !ptinfo.tj_pbirthday.is_empty() {
      let age = utility::timeutil::get_age_from_birthdate(&ptinfo.tj_pbirthday, medinfo.tj_recorddate, "%Y-%m-%d");
      page = age.to_string();
    }
    let mut errors: Vec<String> = vec![];
    //1 同步病人基本信息
    // if ptinfo.tj_patid.is_empty() {
    //建档
    let ret = Herenkj::sync_patient_basic_info(&medinfo, &mut ptinfo, server, db).await;
    if ret.as_ref().is_err() {
      error!("同步建档信息失败:{}", ret.as_ref().unwrap_err().to_string());
      errors.push(format!("同步建档信息失败:{}", ret.as_ref().unwrap_err().to_string()));
    }
    // }
    let performedby = SYSCACHE.get().unwrap().get_dict(DictType::DictSysparm as i32, SysParm::ExtDeptCode as i32, db).await.ss_name;
    let mut amount: Decimal = Decimal::from(0);

    if medinfo.tj_packageid > 0 {
      if let Ok(ret) = TjPackageinfo::query(medinfo.tj_packageid, db.get_connection()).await {
        if let Some(pkg) = ret {
          amount = Decimal::from_str_exact(&pkg.tj_discount).unwrap_or_default();
        }
      }
    }
    if amount.eq(&Decimal::from(0)) {
      //accumulate price in new items
      for ci in new_extitems.iter() {
        amount += Decimal::from_str_exact(&ci.price).unwrap_or_default();
      }
    }
    //2 同步结算费用信息 -> 接收收费通知
    if dto.exttype == ExtOpType::ADD as i32 || dto.exttype == ExtOpType::DEL as i32 {
      let cancelflag = match dto.exttype {
        x if x == ExtOpType::DEL as i32 => 1,
        _ => 0,
      };
      // 收费信息
      let ret = Herenkj::sync_charge_info(medinfo, &ptinfo, cancelflag, amount, &performedby, server, db).await;
      if ret.as_ref().is_err() {
        error!("同步结算费用信息:{}", ret.as_ref().unwrap_err().to_string());
        errors.push(format!("同步结算费用信息失败:{}", ret.as_ref().unwrap_err().to_string()));
        // return Err(anyhow!("同步结算费用信息{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    //3 同步体检申请单
    let to_add_items: Vec<ExtCheckiteminfo>;
    if force == YesOrNo::Yes as i32 {
      to_add_items = new_extitems.into_iter().filter(|&p| p.extsn.is_empty()).map(|v| v.to_owned()).collect();
    } else {
      to_add_items = new_extitems
        .into_iter()
        .filter(|&p| exist_items.iter().find(|f| f.itemid2.eq_ignore_ascii_case(&p.itemid2)).is_none() && p.extsn.is_empty())
        .map(|v| v.to_owned())
        .collect();
    }
    info!("to_add_items:{:#?}", &to_add_items);

    let to_del_items: Vec<ExtCheckiteminfo> = exist_items
      .iter()
      .filter(|&p| new_extitems.iter().find(|f| f.itemid2.eq_ignore_ascii_case(&p.itemid2)).is_none() && !p.extsn.is_empty())
      .map(|v| v.to_owned())
      .collect();
    info!("to_del_items:{:#?}", &to_del_items);
    // if dto.exttype == ExtOpType::ADD as i32 || dto.exttype == ExtOpType::EDIT as i32 {
    let checkstatus = 1; //新增
    for ci in to_add_items.iter() {
      if ci.depttype == crate::common::constant::DeptType::Function as i32 {
        let ret = Herenkj::sync_functional_exam_application(medinfo, &ptinfo, ci, server, checkstatus, &page, db).await;
        if ret.as_ref().is_err() {
          error!("同步检查信息失败:{}", ret.as_ref().unwrap_err().to_string());
          errors.push(format!("同步检查信息失败:{}", ret.as_ref().unwrap_err().to_string()));
          // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
      if ci.depttype == crate::common::constant::DeptType::Lab as i32 {
        let ret = Herenkj::sync_lab_application(medinfo, &ptinfo, ci, server, constant::YesOrNo::No as i32, &page, db).await;
        if ret.as_ref().is_err() {
          error!("同步检验信息失败:{}", ret.as_ref().unwrap_err().to_string());
          errors.push(format!("同步检验信息失败:{}", ret.as_ref().unwrap_err().to_string()));
          // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
    }
    let checkstatus = 2; //作废
    for ci in to_del_items.iter() {
      if ci.depttype == crate::common::constant::DeptType::Function as i32 {
        let ret = Herenkj::sync_functional_exam_application(medinfo, &ptinfo, ci, server, checkstatus, &page, db).await;
        if ret.as_ref().is_err() {
          error!("作废检查信息失败:{}", ret.as_ref().unwrap_err().to_string());
          errors.push(format!("作废检查信息失败:{}", ret.as_ref().unwrap_err().to_string()));
          // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
      if ci.depttype == crate::common::constant::DeptType::Lab as i32 {
        if !ci.extsn.is_empty() {
          let ret = Herenkj::sync_lab_application(medinfo, &ptinfo, ci, server, constant::YesOrNo::Yes as i32, &page, db).await;
          if ret.as_ref().is_err() {
            error!("作废检验信息失败:{}", ret.as_ref().unwrap_err().to_string());
            // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
          }
        }
      }
    }
    // }
    if errors.len() <= 0 {
      return Ok("".to_string());
    } else {
      return Err(anyhow!("{}", errors.join(";")));
    }
    // Ok("".to_string())
  }

  pub async fn sync_patient_basic_info(medinfo: &TjMedexaminfo, ptinfo: &mut TjPatient, server: &str, db: &DbConnection) -> Result<String> {
    let basic_info = Herenkj::create_profile(medinfo, ptinfo, db).await;
    info!("basic_info:{}", &serde_json::to_string(&basic_info).unwrap());
    let request = InboundRequestDto {
      accesstoken: Uuid::new_v4().simple().to_string(),
      servicename: SERVICE_SYNC_PATIENT_BASIC_INFO.to_string(),
      serviceversion: SERVICE_VERSION.to_string(),
      params: Params { args: basic_info },
    };
    let request_str = serde_json::to_string(&request).unwrap();
    info!("request_str:{}", &request_str);

    //get xml wrapper
    let xml_wrapper = HerenClient::get_sample2_xml_wrapper(&request_str);
    info!("xml_wrapper:{}", &xml_wrapper);

    let url = format!("{}", server);
    // let ret = HerenClient::send_request(&url, SOAP_ACTION_SAMPLE2, &serde_json::to_string(&xml_wrapper).unwrap()).await;
    let ret = HerenClient::send_request(&url, SOAP_ACTION_SAMPLE2, &xml_wrapper).await;
    info!("request response:{:?}", &ret);
    if ret.as_ref().is_err() {
      return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret_text = ret.unwrap();
    info!("response:{}", &ret_text);
    let ret = herenclient::HerenClient::decode_response(&ret_text, SAMPLE2_RESULT);
    if ret.as_ref().is_err() {
      return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let ret = ret.unwrap();
    if ret.is_none() {
      return Err(anyhow!("处理返回数据出错:{ret_text}"));
    }

    let ret = ret.unwrap();

    let ptresponse: OutboundResponseDto<SyncPatientResult> = serde_json::from_str(&ret).unwrap();
    if ptresponse.result_code.eq_ignore_ascii_case(CODE_OK) {
      if let Some(result) = ptresponse.result {
        ptinfo.tj_patid = result.patient_id.clone();
        let ret = TjPatient::save(ptinfo, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("save patient error: {}", ret.unwrap_err().to_string());
        }
        let mut medinfo2 = medinfo.to_owned();
        medinfo2.tj_ordno = result.visit_no.clone();
        let ret = TjMedexaminfo::save(&medinfo2, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("save medinfo error: {}", ret.unwrap_err().to_string());
        }
        // return Ok("".to_string());
      }
      // let patientid = ptresponse.patient_id.unwrap_or_default();
      // ptinfo.tj_patid = patientid.clone();
      // let ret = TjPatient::save(ptinfo, &db.get_connection()).await;
      // if ret.as_ref().is_err() {
      //   error!("save patient error: {}", ret.unwrap_err().to_string());
      // }
    } else {
      return Err(anyhow!("{}", ptresponse.desc_message));
    }

    Ok("".to_string())
  }

  pub async fn sync_charge_info(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, cancelflag: i32, amount: Decimal, performby: &str, server: &str, db: &DbConnection) -> Result<String> {
    let chargeinfo = Herenkj::create_charge_application(medinfo, ptinfo, &cancelflag.to_string(), &amount.to_string(), performby, db).await;
    info!("charge info:{:?}", &chargeinfo);
    // let chargeinfo_str = serde_json::to_string(&chargeinfo).unwrap();

    let request = InboundRequestDto {
      accesstoken: Uuid::new_v4().simple().to_string(),
      servicename: SERVICE_SYNC_CHARGE_INFO.to_string(),
      serviceversion: SERVICE_VERSION.to_string(),
      params: Params { args: chargeinfo },
    };
    let request_str = serde_json::to_string(&request).unwrap();
    info!("request_str:{}", &request_str);

    let xml_wrapper = HerenClient::get_sample2_xml_wrapper(&request_str);
    info!("xml_wrapper:{}", &xml_wrapper);

    let url = format!("{}", server);
    let ret = HerenClient::send_request(&url, SOAP_ACTION_SAMPLE2, &xml_wrapper).await;
    info!("request response:{:?}", &ret);
    if ret.as_ref().is_err() {
      return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret_text = ret.unwrap();
    info!("response:{}", &ret_text);
    let ret = herenclient::HerenClient::decode_response(&ret_text, SAMPLE2_RESULT);
    if ret.as_ref().is_err() {
      return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let ret = ret.unwrap();
    if ret.is_none() {
      return Err(anyhow!("处理返回数据错误:{ret_text}"));
    }

    let ret = ret.unwrap();
    let response: OutboundResponseDto<String> = serde_json::from_str(&ret).unwrap();
    if !response.result_code.eq_ignore_ascii_case(CODE_OK) {
      return Err(anyhow!("{}", response.desc_message));
    }

    Ok("".to_string())
  }

  pub async fn sync_functional_exam_application(
    // dto: &ExternalDTO,
    medinfo: &TjMedexaminfo,
    ptinfo: &TjPatient,
    ci: &ExtCheckiteminfo,
    // newitems: &Vec<ExtCheckiteminfo>,
    // existitems: &Vec<ExtCheckiteminfo>,
    server: &str,
    // force: i32,
    checkstatus: i32,
    page: &str,
    db: &DbConnection,
  ) -> Result<String> {
    //创建检查申请单
    let orderinfo = Herenkj::create_function_orderinfo(medinfo, ptinfo, ci, checkstatus, db).await;
    let patientinfo = Herenkj::create_patient_info(ptinfo, page, PatientType::Functional as i32).await;

    let patvisitinfo = Herenkj::create_patient_visit_info().await;

    let gmspecimen = Herenkj::create_gm_specimen().await;

    let applicationinfo = MedexamFunctionApplication {
      orderinfo,
      patientinfo,
      patvisitinfo,
      gmspecimen,
    };

    let application_str = serde_json::to_string(&applicationinfo).unwrap_or_default();

    let xml_wrapper = herenclient::HerenClient::get_sample1_xml_wrapper(SOAP_SAMPLE1_METHOD_EXAMAPPLYINFO, application_str.as_str());
    info!("xml_wrapper:{}", &xml_wrapper);

    let url = format!("{}", server);
    let ret = HerenClient::send_request(&url, SOAP_ACTION_SAMPLE1, &&xml_wrapper).await;
    info!("request response:{:?}", &ret);
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret_text = ret.unwrap();
    info!("response:{}", &ret_text);
    let ret = herenclient::HerenClient::decode_response(&ret_text, SAMPLE1_RESULT);
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }

    let ret = ret.unwrap();
    if ret.is_none() {
      return Err(anyhow!("处理返回数据错误:{ret_text}"));
    }

    let ret = ret.unwrap();
    let response: InboundResponseDto = serde_json::from_str(&ret).unwrap();
    if !response.result_code.eq_ignore_ascii_case(CODE_OK) {
      return Err(anyhow!("{}", response.desc_message));
    }
    if let Some(result) = response.result {
      let resp_result = serde_json::from_str::<InboundResponseResult>(&result).unwrap();
      if let Some(orderid) = resp_result.orderid {
        let mut extci = ci.to_owned();
        extci.extsn = orderid.clone();
        let ret = ExtCheckiteminfo::save_many(&vec![extci.clone()], &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("save extcheckiteminfo error: {}", ret.as_ref().unwrap_err().to_string());
        }
      }
    }
    Ok("".to_string())
  }

  pub async fn sync_lab_application(
    // dto: &ExternalDTO,
    medinfo: &TjMedexaminfo,
    ptinfo: &TjPatient,
    ci: &ExtCheckiteminfo,
    // newitems: &Vec<ExtCheckiteminfo>,
    // existitems: &Vec<ExtCheckiteminfo>,
    server: &str,
    cancelflag: i32,
    page: &str,
    db: &DbConnection,
  ) -> Result<String> {
    //创建检验申请单
    let orderinfo = Herenkj::create_lab_orderinfo(medinfo, ptinfo, ci, cancelflag, db).await;
    let patientinfo = Herenkj::create_patient_info(ptinfo, page, PatientType::Lab as i32).await;
    let patvisitinfo = Herenkj::create_patient_visit_info().await;

    let lisapplication = MedexamLisApplication {
      orderinfo,
      patientinfo,
      patvisitinfo,
    };
    info!("lis application:{:?}", &lisapplication);
    let lisapplication_str = serde_json::to_string(&lisapplication).unwrap();
    info!("lis application str:{}", &lisapplication_str);
    let xml_wrapper = herenclient::HerenClient::get_sample1_xml_wrapper(SOAP_SAMPLE1_METHOD_LABAPPLYINFO, lisapplication_str.as_str());
    info!("xml_wrapper:{}", &xml_wrapper);

    let url = format!("{}", server);
    let ret = HerenClient::send_request(&url, SOAP_ACTION_SAMPLE1, &xml_wrapper).await;
    info!("request response:{:?}", &ret);
    if ret.as_ref().is_err() {
      return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret_text = ret.unwrap();
    info!("response:{}", &ret_text);
    let ret = herenclient::HerenClient::decode_response(&ret_text, SAMPLE1_RESULT);
    if ret.as_ref().is_err() {
      return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let ret = ret.unwrap();
    if ret.is_none() {
      return Err(anyhow!("处理返回数据错误:{ret_text}"));
    }

    let ret = ret.unwrap();
    let response: InboundResponseDto = serde_json::from_str(&ret).unwrap();
    if !response.result_code.eq_ignore_ascii_case(CODE_OK) {
      return Err(anyhow!("error:{}", response.desc_message));
    }
    if let Some(result) = response.result {
      let resp_result = serde_json::from_str::<InboundResponseResult>(&result).unwrap();
      if let Some(orderid) = resp_result.orderid {
        let mut extci = ci.to_owned();
        extci.extsn = orderid.clone();
        let ret = ExtCheckiteminfo::save_many(&vec![extci.clone()], &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("save extcheckiteminfo error: {}", ret.as_ref().unwrap_err().to_string());
        }
      }
    }
    Ok("".to_string())
  }

  async fn create_profile(medinfo: &TjMedexaminfo, ptinfo: &mut TjPatient, db: &DbConnection) -> PatBasicInfo {
    let mut corpname = "".to_string();
    if let Some(corpinfo) = crate::SYSCACHE.get().unwrap().get_corpinfo(medinfo.tj_corpnum, &db).await {
      corpname = corpinfo.tj_corpname.to_string();
    }
    let patinfo = PatBasicInfo {
      patname: ptinfo.tj_pname.clone(),
      sex: Herenkj::get_sex(ptinfo.tj_psex),
      idcard: ptinfo.tj_pidcard.clone(),
      phonenumber: ptinfo.tj_pphone.clone(),
      physicaltype: Herenkj::get_physicaltype(medinfo.tj_corpnum),
      birthday: ptinfo.tj_pbirthday.clone(),
      regdate: utility::timeutil::format_timestamp(medinfo.tj_recorddate),
      companyname: corpname,
      idtype: "01".to_string(),
      address: ptinfo.tj_paddress.clone(),
      professionalcode: "".to_string(),
      professionalname: "".to_string(),
      physical_exam_id: medinfo.tj_testid.to_string(),
    };
    patinfo
  }

  async fn create_charge_application(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, cancelflag: &str, amount: &str, performby: &str, db: &DbConnection) -> ChargeInfo {
    let corpinfo = crate::SYSCACHE.get().unwrap().get_corpinfo(medinfo.tj_corpnum, &db).await;

    let chargeinfo = ChargeInfo {
      physicalid: medinfo.tj_testid.to_string(),
      physicaltype: Herenkj::get_physicaltype(medinfo.tj_corpnum),
      patname: ptinfo.tj_pname.clone(),
      sex: Herenkj::get_sex(ptinfo.tj_psex),
      age: medinfo.tj_age.to_string(),
      companyname: corpinfo.unwrap_or_default().tj_corpname.clone(),
      enterdatetime: timeutil::format_timestamp(medinfo.tj_recorddate),
      charges: amount.to_string(),
      operationid: medinfo.tj_recorder.clone(),
      performedby: performby.to_string(),
      patientid: ptinfo.tj_patid.to_string(),
      cancelledflag: cancelflag.to_string(),
      vipcharges: amount.to_string(),
      ordered_emp_id: "".to_string(),
      vipflag: "0".to_string(),
    };

    chargeinfo
  }

  //检查状态 checkstatus 1、开立 2、作废 3、修改
  async fn create_function_orderinfo(
    medinfo: &TjMedexaminfo,
    ptinfo: &TjPatient,
    /*ciinfos: &Vec<ExtCheckiteminfo>,*/ ci: &ExtCheckiteminfo,
    checkstatus: i32,
    db: &DbConnection,
  ) -> FunctionalOrderInfo {
    let staff = crate::SYSCACHE.get().unwrap().get_staff(medinfo.tj_recorder.parse().unwrap_or_default(), db).await;

    let mut total_costs: f64 = 0.0; // = ciinfos.iter().map(|ci| ci.itemprice).sum::<f64>();
    let mut examitems: Vec<ExamItem> = vec![];
    // for ci in ciinfos.iter() {
    let iteminfo = crate::SYSCACHE.get().unwrap().get_iteminfo(&ci.itemid2, db).await;
    let mut exam_item_code = ci.itemid.to_string();
    if exam_item_code.is_empty() {
      exam_item_code = iteminfo.tj_itemid.clone();
    }
    total_costs += iteminfo.tj_itemprice as f64;
    let examitem = ExamItem {
      item_no: iteminfo.id.to_string(),
      exam_item: ci.itemname.to_string(),
      exam_item_code,
      exam_sub_class: iteminfo.tj_zdym.to_string(),
      exam_third_item_code: "".to_string(),
      ref_order_no: "".to_string(),
      ref_order_sub_no: "".to_string(),
      collection_volume: "".to_string(),
      specimen_action_code: "".to_string(),
      relevant_clinical_info: "".to_string(),
      specimen_source_name: "".to_string(),
      specimen_source_part: iteminfo.tj_itemname.to_string(),
      numbers_of_sample_containers: "".to_string(),
      collector_comment: "".to_string(),
      costs: iteminfo.tj_itemprice.to_string(),
    };
    examitems.push(examitem);
    // }
    let order_dept_code = SYSCACHE.get().unwrap().get_dict(DictType::DictSysparm as i32, SysParm::ExtDeptCode as i32, &db).await;
    let order_dept_name = SYSCACHE.get().unwrap().get_dict(DictType::DictSysparm as i32, SysParm::ExtDeptName as i32, &db).await;

    let orderinfo = FunctionalOrderInfo {
      physicalid: medinfo.tj_testid.to_string(),
      apply_no: medinfo.tj_testid.to_string(),
      patient_id: ptinfo.tj_patid.to_string(),
      charge_type: "".to_string(),
      costs: total_costs.to_string(),
      visit_date: utility::timeutil::format_timestamp_with_format(medinfo.tj_testdate, "%Y%m%d %H:%M:%S"),
      order_no: format!("{}", ci.itemid2),
      clin_diag: "体检".to_string(),
      phys_sign: "体检".to_string(),
      clin_symp: "体检".to_string(),
      relevant_diag: "体检".to_string(),
      exam_reason: "体检".to_string(),
      notice: "".to_string(),
      status: checkstatus.to_string(),
      charge_indicator: medinfo.tj_chargestatus.to_string(),
      patient_source: "".to_string(),
      exam_class_code: "".to_string(),
      exam_class_name: "".to_string(),
      req_dept_no: order_dept_code.ss_name.to_string(),
      req_dept_name: order_dept_name.ss_name.to_string(),
      req_physician_id: medinfo.tj_recorder.to_string(),
      req_physician: staff.tj_staffname.to_string(),
      req_date_time: utility::timeutil::format_timestamp(medinfo.tj_recorddate),
      req_memo: "".to_string(),
      priority: "0".to_string(),
      sch_date_time: utility::timeutil::format_timestamp_with_format(medinfo.tj_testdate, "%Y-%m-%d %H:%M:%S"),
      sch_memo: "".to_string(),
      performed_by: "".to_string(),
      performed_by_name: "".to_string(),
      req_area_code: "01".to_string(),
      performed_area_code: "01".to_string(),
      technician_id: "".to_string(),
      technician: "".to_string(),
      exam_date_time: utility::timeutil::format_timestamp_with_format(medinfo.tj_testdate, "%Y-%m-%d %H:%M:%S"),
      verifier_doc_no: "".to_string(),
      verifier_doc_name: "".to_string(),
      verifier_doc_phone_no: "".to_string(),
      verifier_date_time: "".to_string(),
      baby_no: "".to_string(),
      phone_no: ptinfo.tj_pphone.to_string(),
      chief_complaint: "".to_string(),
      physical_exam: "".to_string(),
      delivery_date: utility::timeutil::format_timestamp(medinfo.tj_recorddate),
      attending_date: utility::timeutil::format_timestamp(medinfo.tj_testdate),
      diagnosis_type: "体检".to_string(),
      slice_amount: "".to_string(),
      memo: "".to_string(),
      laboratory_exam: "".to_string(),
      imaging_exam: "".to_string(),
      preoperative_radiotherapy_date: "".to_string(),
      postoperative_radiotherapy: "".to_string(),
      postoperative_radiotherapy_date: "".to_string(),
      exam_purpose: "".to_string(),
      special_exam_item: "".to_string(),
      specialized_exam: "".to_string(),
      leafage_node_id: "".to_string(),
      empty: "".to_string(),
      sch_time: "".to_string(),
      history_summary: "".to_string(),
      yj_remark: "".to_string(),
      patient_type: "T".to_string(),
      register_flag: "0".to_string(),
      ward_code: "".to_string(),
      ward_name: "".to_string(),
      exam_items: examitems,
    };
    orderinfo
  }

  async fn create_patient_info(ptinfo: &TjPatient, page: &str, pttype: i32) -> PatientInfo {
    let mut patientinfo = PatientInfo {
      pat_sex: Herenkj::get_sex(ptinfo.tj_psex),
      name: ptinfo.tj_pname.to_string(),
      marital_status: Herenkj::get_marial_status_name(ptinfo.tj_pmarriage),
      marital_status_code: Herenkj::get_marial_status(ptinfo.tj_pmarriage),
      birth_place: "".to_string(),
      birth_place_code: "".to_string(),
      present_address_province: "".to_string(),
      present_address_city: "".to_string(),
      present_address_county: "".to_string(),
      present_address_others: ptinfo.tj_paddress.to_string(),
      present_address_zipcode: "".to_string(),
      id_no: ptinfo.tj_pidcard.to_string(),
      phone_number: ptinfo.tj_pphone.to_string(),
      phone_number_business: "".to_string(),
      phone_number_home: "".to_string(),
      birth_place_name: "".to_string(),
      nation: ptinfo.tj_nation.to_string(),
      nation_code: "".to_string(),
      citizenship_name: "中国".to_string(),
      citizenship_code: "CN".to_string(),
      unit_in_contract_name: "".to_string(),
      job_code: "".to_string(),
      job_name: "".to_string(),
      next_of_kin: "".to_string(),
      next_of_kin_phone: "".to_string(),
      next_of_kin_zip_code: "".to_string(),
      next_of_kin_address: "".to_string(),
      relationship_code: "".to_string(),
      relationship: "".to_string(),
      health_card_type: "".to_string(),
      health_card_no: "".to_string(),
      pat_age: page.to_string(),
      dob: ptinfo.tj_pbirthday.to_string(),
      ..Default::default()
    };
    if pttype == 1 {
      patientinfo.address = Some(ptinfo.tj_paddress.to_string());
      patientinfo.mailing_address = Some(ptinfo.tj_pemail.to_string());
      patientinfo.zip_code = Some("".to_string());
    } else {
      patientinfo.hospital_card_type = Some("".to_string());
      patientinfo.hospital_card_no = Some("".to_string());
      patientinfo.vip_indicator = Some("".to_string());
      patientinfo.charge_type = Some("".to_string());
    }
    patientinfo
  }

  async fn create_patient_visit_info() -> PatVisitInfo {
    let patvisitinfo = PatVisitInfo {
      patient_class: "T".to_string(),
      attending_doctor_id: "".to_string(),
      attending_doctor_name: "".to_string(),
      referring_doctor_id: "".to_string(),
      referring_doctor_name: "".to_string(),
      vip_indicator: "".to_string(),
      admitting_doctor_id: "".to_string(),
      admitting_doctor_name: "".to_string(),
      discharge_date_time: "".to_string(),
      blood_type: "".to_string(),
      blood_type_rh: "".to_string(),
    };
    patvisitinfo
  }

  async fn create_gm_specimen() -> GmSpecimen {
    let gmspecimen = GmSpecimen { ..Default::default() };
    gmspecimen
  }

  async fn create_lab_orderinfo(
    medinfo: &TjMedexaminfo,
    ptinfo: &TjPatient,
    ci: &ExtCheckiteminfo,
    cancelflag: i32,
    /*ciinfos: &Vec<ExtCheckiteminfo>,*/ db: &DbConnection,
  ) -> LabOrderInfo {
    let staff = crate::SYSCACHE.get().unwrap().get_staff(medinfo.tj_recorder.parse().unwrap_or_default(), db).await;

    let mut total_costs: f64 = 0.0; // = ciinfos.iter().map(|ci| ci.itemprice).sum::<f64>();
    let mut laborderitems: Vec<LabOrderItem> = vec![];
    // for ci in ciinfos.iter() {
    let iteminfo = crate::SYSCACHE.get().unwrap().get_iteminfo(&ci.itemid2, db).await;
    let mut item_no = ci.itemid.to_string();
    if item_no.is_empty() {
      item_no = iteminfo.tj_itemid.clone();
    }
    let mut item_code = ci.itemid.to_string();
    if item_code.is_empty() {
      item_code = iteminfo.tj_lisnum.clone();
    }
    total_costs += iteminfo.tj_itemprice as f64;
    let laborderitem = LabOrderItem {
      item_no,
      item_name: ci.itemname.to_string(),
      item_code,
      spcm_sample_date_time: utility::timeutil::format_timestamp(medinfo.tj_recorddate),
      spcm_received_date_time: "".to_string(),
      execute_date: "".to_string(),
      notes: "".to_string(),
    };
    laborderitems.push(laborderitem);
    // }

    let order_dept_code = SYSCACHE.get().unwrap().get_dict(DictType::DictSysparm as i32, SysParm::ExtDeptCode as i32, &db).await;
    let order_dept_name = SYSCACHE.get().unwrap().get_dict(DictType::DictSysparm as i32, SysParm::ExtDeptName as i32, &db).await;

    let mut specimen_name = "".to_string();
    if !iteminfo.sample_code.is_empty() {
      specimen_name = SYSCACHE.get().unwrap().get_sampletype(&iteminfo.sample_code, &db).await.sample_name;
    }
    let mut order_id = "".to_string();
    if cancelflag == crate::common::constant::YesOrNo::Yes as i32 {
      order_id = ci.extsn.to_string();
    }
    let orderinfo = LabOrderInfo {
      order_id, //medinfo.tj_testid.to_string(),
      physical_id: medinfo.tj_testid.to_string(),
      apply_no: medinfo.tj_testid.to_string(),
      priority_indicator: "0".to_string(),
      patient_id: ptinfo.tj_patid.to_string(),
      patient_source: "".to_string(),
      subject_code: "".to_string(),
      subject_name: "".to_string(),
      test_cause_code: "".to_string(),
      test_cause_name: "".to_string(),
      relevant_clinic_diag: "体检".to_string(),
      notes_for_spcm: "".to_string(),
      spcm_sample_date_time: "".to_string(),
      spcm_received_date_time: "".to_string(),
      requested_date_time: utility::timeutil::format_timestamp(medinfo.tj_recorddate),
      ordering_dept_code: order_dept_code.ss_name.to_string(),
      ordering_dept_name: order_dept_name.ss_name.to_string(),
      ordering_provider_id: staff.tj_staffno.to_string(),
      ordering_provider_name: staff.tj_staffname.to_string(),
      performed_by: "".to_string(),
      performed_by_code: "".to_string(),
      execute_doctor_id: "".to_string(),
      execute_doctor_name: "".to_string(),
      req_area_code: AREA_CODE.to_string(),
      performed_area_code: AREA_CODE.to_string(),
      charge_indicate: "1".to_string(),
      verified_date_time: "".to_string(),
      verified_by: "".to_string(),
      verified_by_id: "".to_string(),
      audit_date_time: "".to_string(),
      audit_doctor: "".to_string(),
      audit_doctor_id: "".to_string(),
      costs: total_costs.to_string(),
      charge_type: "".to_string(),
      specimen_code: iteminfo.sample_code.to_string(),
      specimen_name,
      charges: total_costs.to_string(),
      purpose_of_inspection: "".to_string(),
      drug_resistance_type: "".to_string(),
      sample_character: "".to_string(),
      rcptno: "".to_string(),
      lab_order_items: laborderitems,
    };
    orderinfo
  }

  //---------------------------------- 体检系统提供的接口 ----------------------------------
  //2.4.3.	UpdateChargeFlag收费通知（体检系统提供，集成平台调用）
  #[tracing::instrument(name = "update_charge_flag >", skip_all)]
  pub async fn update_charge_flag(charge: &ChargeStatus, db: &DbConnection) -> OutboundResponseDto<String> {
    let testid = charge.physicalid.to_string();
    // let chargeflag = &charge.chargeflag;
    let _ret = MessageService::save_message(&testid, "收费通知", &serde_json::to_string(&charge).unwrap_or_default(), db).await;

    let chargestatus = match charge.chargeflag.as_str() {
      "0" => 1,
      "1" => 2,
      _ => 0,
    };
    let mut res = OutboundResponseDto {
      result_code: "0".to_string(),
      desc_message: "更新成功".to_string(),
      ..Default::default()
    };
    let ret = TjMedexaminfo::update_charge_status(&testid, chargestatus, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      res.result_code = "1".to_string();
      res.desc_message = format!("更新收费状态错误:{}", ret.as_ref().unwrap_err().to_string());
      // return Err(anyhow!("更新收费状态错误:{}", ret.as_ref().unwrap_err().to_string()));
    }

    res
  }

  //*******.	调用服务更新检查申请状态
  #[tracing::instrument(name = "update_function_exam_status >", skip_all)]
  pub async fn update_function_exam_status(args: &ExamStatus, db: &DbConnection) -> OutboundResponseDto<String> {
    info!("update function application status args:{:?}", args);
    let res = OutboundResponseDto {
      result_code: "0".to_string(),
      desc_message: "更新成功".to_string(),
      ..Default::default()
    };

    let _ret = MessageService::save_message(&args.visit_no, "更新检查申请状态", &serde_json::to_string(&args).unwrap_or_default(), db).await;
    res
  }

  //*******.	调用服务写检查报告
  #[tracing::instrument(name = "update_function_exam_reports >", skip_all)]
  pub async fn update_function_exam_reports(args: &PacsResultRecord, db: &DbConnection) -> OutboundResponseDto<String> {
    let mut res = OutboundResponseDto {
      result_code: constdefine::CODE_OK.to_string(),
      desc_message: "更新成功".to_string(),
      ..Default::default()
    };
    let ret = MessageService::save_message(&args.visit_no.to_string(), "检查报告书写", &serde_json::to_string(&args).unwrap_or_default(), db).await;
    if ret.as_ref().is_err() {
      error!("保存消息错误:{}", ret.as_ref().unwrap_err().to_string());
    }
    let testids: Vec<String> = vec![args.visit_no.to_string()];

    if args.reports.len() == 0 {
      // return Err(anyhow!("没有检查报告数据"));
      res.result_code = constdefine::CODE_ERROR.to_string();
      res.desc_message = "没有检查报告数据".to_string();
      return res;
    }
    let mut pacsresults: Vec<VTjPacsresult> = vec![];
    let reports = args.reports.to_owned();
    let orderid: String = args.order_id.to_string();
    info!(orderid, "Order No");

    //query from extcheckiteminfo by orderid
    let ret = ExtCheckiteminfo::query_by_extsn(&orderid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("查询检查项目错误:{}", ret.as_ref().unwrap_err().to_string());
    }
    let extcis = ret.unwrap();
    if extcis.is_none() {
      error!(orderid, "没有找到检查项目");
      res.result_code = constdefine::CODE_ERROR.to_string();
      res.desc_message = "没有找到检查项目".to_string();
      return res;
    }

    let extcis = extcis.unwrap();

    let jclxs: Vec<String> = vec![extcis.itemid2.to_string()];
    // let jclx = args.order_id.to_string();
    // let orderno_chaifen: Vec<&str> = orderno.split("-").collect();
    // if orderno_chaifen.len() >= 2 {
    //   jclx.push(value);
    // }

    for report in reports.iter() {
      // jclx.push(report.exam_para.to_string());
      let is_abnormal: i32;

      // if let Ok(_isnum) = report.is_abnormal.parse::<i32>() {
      if report.is_abnormal == "1" {
        is_abnormal = 1;
      } else {
        is_abnormal = 2;
      }
      // for report in args.reports.iter() {
      let pacsresult = VTjPacsresult {
        id: 0,
        tjbh: args.visit_no.to_string(),
        brxm: args.patient_id.to_string(),
        jclx: extcis.itemid2.to_string(),
        jcxm: report.exam_para.to_string(),
        jcmc: report.exam_para.to_string(),
        imagesight: report.description.to_string(),
        imagediagnosis: report.impression.to_string(),
        jcys: args.technician.to_string(),
        sxys: args.verifier_doctor_name.to_string(),
        bgys: args.report_doctor_name.to_string(),
        bgrq: args.report_time.to_string(),
        sfyc: is_abnormal,
        imagepath: Some(match report.urlpath.as_ref() {
          Some(path) => path.to_string(),
          None => "".to_string(),
        }),
        datas: Some("".to_string()),
      };
      pacsresults.push(pacsresult);
      // }
    }
    info!("接收检查报告数据: {:?}", &pacsresults);
    let ret = VTjPacsresult::delete_many_by_jclx(&testids, &jclxs, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("删除检查报告数据错误:{}", ret.unwrap_err().to_string());
    }
    let ret = VTjPacsresult::save_many(&pacsresults, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      res.result_code = constdefine::CODE_ERROR.to_string();
      res.desc_message = format!("插入检查报告数据错误:{}", ret.as_ref().unwrap_err().to_string());
      return res;
    }

    info!("接收检查报告数据完成......");
    res
  }

  //*******.	调用服务更新检验申请状态
  #[tracing::instrument(name = "update_lab_status >", skip_all)]
  pub async fn update_lab_status(args: &LabStatusRecord, db: &DbConnection) -> OutboundResponseDto<String> {
    info!("update_lab_status args:{:?}", args);
    let res = OutboundResponseDto {
      result_code: "0".to_string(),
      desc_message: "更新成功".to_string(),
      ..Default::default()
    };
    let _ret = MessageService::save_message(&args.visit_no, "检验申请状态", &serde_json::to_string(&args).unwrap_or_default(), db).await;

    res
  }

  //*******.	调用服务更新检验报告
  #[tracing::instrument(name = "update_lab_results >", skip_all)]
  pub async fn update_lab_results(args: &LabResultRecord, db: &DbConnection) -> OutboundResponseDto<String> {
    let mut res = OutboundResponseDto {
      result_code: "0".to_string(),
      desc_message: "更新成功".to_string(),
      ..Default::default()
    };
    let _ret = MessageService::save_message(&args.apply_no, "检验报告书写", &serde_json::to_string(&args).unwrap_or_default(), db).await;

    // let mut labresults: Vec<TjLabresult> = vec![];
    let mut labresults: Vec<VTjLisresult> = vec![];
    let testid: String = args.apply_no.to_string();

    for lab in args.results.iter() {
      let sfyc = match lab.abnormal_indicator.as_str() {
        "N" | "阴性" | "正常" | "-" | "阴性(-)" | "未见异常" => 0,
        _ => 1,
      };
      let gdbj = match lab.abnormal_indicator.as_str() {
        "N" => "",
        "L" => "↓",
        "H" => "↑",
        _ => "",
      };

      let labresult = VTjLisresult {
        id: 0,
        tjbh: args.apply_no.to_string(),
        brxm: args.patient_id.to_string(),
        xmxh: lab.item_code.to_string(),
        xmmc: lab.item_name.to_string(),
        xmdw: lab.units.to_string(),
        xmjg: lab.result.to_string(),
        sfyc: sfyc, //lab.abnormal_indicator.to_string(),
        gdbj: gdbj.to_string(),
        ckdz: "".to_string(),
        ckgz: "".to_string(),
        ckfw: lab.upper_and_lower_limits.to_string(),
        jyys: args.execute_doctor_name.to_string(),
        bgrq: args.report_time.to_string(),
        bgys: args.verifier_doctor_name.to_string(),
      };
      // let labresult = TjLabresult {
      //   id: 0,
      //   tj_clinicid: args.visit_no.to_string(),
      //   tj_testid: args.visit_no.to_string(),
      //   tj_patientname: args.patient_id.to_string(),
      //   tj_sex: "".to_string(),
      //   tj_origrec: lab.item_code.to_string(),
      //   tj_itemid: lab.item_code.to_string(),
      //   tj_analyte: lab.item_name.to_string(),
      //   tj_shortname: lab.item_name.to_string(),
      //   tj_units: lab.units.to_string(),
      //   tj_final: lab.result.to_string(),
      //   tj_rn10: lab.result.to_string(),
      //   tj_ckfw_l: "".to_string(),
      //   tj_ckfw_h: "".to_string(),
      //   tj_ckfw: lab.upper_and_lower_limits.to_string(),
      //   tj_abnormalflag: 0, //lab.abnormal_indicator.to_string(),
      //   tj_displowhigh: "".to_string(),
      //   tj_senddate: 0,
      //   tj_ordno: lab.item_code.to_string(),
      //   tj_testgroup: "".to_string(),
      //   tj_checkdoctor: args.operator_name.to_string(),
      //   tj_recheckdoctor: args.verifier_doctor_name.to_string(),
      //   tj_importer: "".to_string(),
      //   tj_importdate: timeutil::current_timestamp(),
      //   tj_isreceived: 0,
      //   tj_receivdate: 0,
      //   tj_checkdate: args.report_time.to_string(),
      //   tj_recheckdate: args.verifier_time.to_string(),
      // };
      labresults.push(labresult);
    }
    info!("接收检验数据详情: {:?}", labresults);
    // info!("接收检验数据}");
    let ret = VTjLisresult::delete_many(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("删除报告数据错误:{}", ret.unwrap_err().to_string());
    }
    // let ret = TjLabresult::save_many(&labresults, &db.get_connection()).await;
    let ret = VTjLisresult::save_many(&labresults, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      res.result_code = constdefine::CODE_ERROR.to_string();
      res.desc_message = format!("插入检验报告数据错误:{}", ret.as_ref().unwrap_err().to_string());
      return res;
    }
    info!("接收检验数据完成......");

    res
  }

  fn get_sex(sex: i32) -> String {
    match sex {
      1 => "男".to_string(),
      2 => "女".to_string(),
      _ => "未知".to_string(),
    }
  }

  fn get_physicaltype(physicaltype: i64) -> String {
    match physicaltype {
      1 => "1".to_string(),
      _ => "2".to_string(),
    }
  }

  fn get_marial_status(marialstatus: i32) -> String {
    match marialstatus {
      1 => "M".to_string(),
      2 => "B".to_string(),
      3 => "D".to_string(),
      4 => "W".to_string(),
      _ => "O".to_string(),
    }
  }
  fn get_marial_status_name(marialstatus: i32) -> String {
    match marialstatus {
      1 => "已婚".to_string(),
      2 => "未婚".to_string(),
      3 => "离婚".to_string(),
      4 => "丧偶".to_string(),
      _ => "其他".to_string(),
    }
  }
  // *********************** 检查申请单 ***************************
  // async fn create_function_application(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, newitems: &Vec<ExtCheckiteminfo>, db: &DbConnection) -> MedexamFunctionApplication {}
  // ********************* end of application ************************

  // ================== lis 检验申请单 ========================
  // async fn create_lis_application() -> MedexamLisApplication {

  // }
  // ============== end of lis application ========================

  // async fn _create_function_patient_info(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, page: &str) -> FunctionalPatientInfo {
  //   // let mut page = medinfo.tj_age.to_string();
  //   // if !ptinfo.tj_pidcard.is_empty() {
  //   //   page = utility::get_age_from_birthdate(&ptinfo.tj_pbirthday, medinfo.tj_testdate, "%Y-%m-%d").to_string();
  //   // }
  //   let patientinfo = FunctionalPatientInfo {
  //     pat_sex: Herenkj::get_sex(ptinfo.tj_psex),
  //     name: ptinfo.tj_pname.to_string(),
  //     marital_status: Herenkj::get_marial_status_name(ptinfo.tj_pmarriage),
  //     marital_status_code: Herenkj::get_marial_status(ptinfo.tj_pmarriage),
  //     address: ptinfo.tj_paddress.to_string(),
  //     mailing_address: ptinfo.tj_pemail.to_string(),
  //     zip_code: "".to_string(),
  //     birth_place: "".to_string(),
  //     birth_place_code: "".to_string(),
  //     present_address_province: "".to_string(),
  //     present_address_city: "".to_string(),
  //     present_address_county: "".to_string(),
  //     present_address_others: ptinfo.tj_paddress.to_string(),
  //     present_address_zipcode: "".to_string(),
  //     id_no: ptinfo.tj_pidcard.to_string(),
  //     phone_number: ptinfo.tj_pphone.to_string(),
  //     phone_number_business: "".to_string(),
  //     phone_number_home: "".to_string(),
  //     birth_place_name: "".to_string(),
  //     nation: ptinfo.tj_nation.to_string(),
  //     nation_code: "".to_string(),
  //     citizenship_name: "".to_string(),
  //     citizenship_code: "".to_string(),
  //     unit_in_contract_name: "".to_string(),
  //     job_code: "".to_string(),
  //     job_name: "".to_string(),
  //     next_of_kin: "".to_string(),
  //     next_of_kin_phone: "".to_string(),
  //     next_of_kin_zip_code: "".to_string(),
  //     next_of_kin_address: "".to_string(),
  //     relationship_code: "".to_string(),
  //     relationship: "".to_string(),
  //     health_card_type: "".to_string(),
  //     health_card_no: "".to_string(),
  //     pat_age: page.to_string(),
  //     dob: ptinfo.tj_pbirthday.to_string(),
  //   };
  //   patientinfo
  // }

  // async fn _create_lab_patientinfo(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, page: &str) -> LabPatientInfo {
  //   let patientinfo = LabPatientInfo {
  //     pat_sex: Herenkj::get_sex(ptinfo.tj_psex),
  //     name: ptinfo.tj_pname.to_string(),
  //     charge_type: "".to_string(),
  //     marital_status: Herenkj::get_marial_status_name(ptinfo.tj_pmarriage),
  //     marital_status_code: Herenkj::get_marial_status(ptinfo.tj_pmarriage),
  //     birth_place: ptinfo.tj_paddress.to_string(),
  //     birth_place_code: "".to_string(),
  //     present_address_province: "".to_string(),
  //     present_address_city: "".to_string(),
  //     present_address_county: "".to_string(),
  //     present_address_others: "".to_string(),
  //     present_address_zipcode: "".to_string(),
  //     id_no: ptinfo.tj_pidcard.to_string(),
  //     phone_number: ptinfo.tj_pphone.to_string(),
  //     phone_number_business: "".to_string(),
  //     phone_number_home: "".to_string(),
  //     birth_place_name: "".to_string(),
  //     nation: ptinfo.tj_nation.to_string(),
  //     nation_code: "".to_string(),
  //     citizenship_name: "".to_string(),
  //     citizenship_code: "".to_string(),
  //     unit_in_contr_name: "".to_string(),
  //     job_code: "".to_string(),
  //     job_name: "".to_string(),
  //     next_of_kin: "".to_string(),
  //     next_of_kin_phone: "".to_string(),
  //     next_of_kin_zip_code: "".to_string(),
  //     next_of_kin_addr: "".to_string(),
  //     relationship_code: "".to_string(),
  //     relationship: "".to_string(),
  //     health_card_type: "".to_string(),
  //     health_card_no: "".to_string(),
  //     hospital_card_type: "".to_string(),
  //     hospital_card_no: "".to_string(),
  //     pat_age: page.to_string(),
  //     dob: ptinfo.tj_pbirthday.to_string(),
  //     vip_indicator: "".to_string(),
  //   };
  //   patientinfo
  // }
}
