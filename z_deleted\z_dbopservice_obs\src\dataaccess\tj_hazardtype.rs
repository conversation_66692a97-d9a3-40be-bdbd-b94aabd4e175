use anyhow::{anyhow, Result};
use rbatis::crud;
use serde::{Deserialize, Serialize};
#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjHazardtype {
  pub id: i64,
  pub tj_tname: String,
  pub tj_pyjm: String,
  pub tj_showorder: i32,
  pub tj_memo: String,
}
crud!(TjHazardtype {}, "tj_hazardtype");
impl TjHazardtype {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjHazardtype) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjHazardtype::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjHazardtype::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
}
