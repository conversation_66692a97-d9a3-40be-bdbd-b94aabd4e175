use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjDeptitem {
  pub id: i64,
  pub tj_itemid: String,
  pub tj_deptid: String,
  pub tj_showorder: i32,
}
crud!(TjDeptitem {}, "tj_deptitem");
rbatis::impl_select!(TjDeptitem{query_many(itemids:&[&str], deptids:&[&str]) => 
  "`where id > 0 `
   if !deptids.is_empty():
    ` and tj_deptid in ${deptids.sql()} `   
   if !itemids.is_empty():
    ` and tj_itemid in ${itemids.sql()} `  
 "});
rbatis::impl_delete!(TjDeptitem{delete(itemid:&String, deptid:&String) => "`where tj_itemid = #{itemid} and tj_deptid = #{deptid} `"});

impl TjDeptitem {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjDeptitem) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjDeptitem::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      // ret.unwrap().rows_affected
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjDeptitem::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
}
