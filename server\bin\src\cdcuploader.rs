use axum::extract::Request;
use cdcuploader::config::settings::Settings;
use chrono::prelude::*;
// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
use hyper::body::Incoming;
use hyper_util::rt::TokioIo;
use nodipservice::SYSCACHE;
use nodipservice::basic::akasvc::AkaSvc;
use nodipservice::common::syscache::SysCache;
use std::fs;
use std::sync::Arc;
use strum::IntoEnumIterator;
use tokio::signal;
use tokio::sync::watch;
use tower::Service;
use log::*;

#[tokio::main]
async fn main() {
  let cfg = "config/cdcuploader.yaml";
  // loggings::log_init(log_cfg);
  log4rs::init_file(cfg, Default::default()).unwrap();

  //init start up

  let sys_config = Settings::init().expect("init configuration error");
  info!("Configuration file:{:?}", &sys_config);

  init_startup(&sys_config);
  let http_url = format!("{}:{}", sys_config.application.apphost, sys_config.application.appport);
  // let httpaddr = http_url.as_str().parse().unwrap();
  let dbconn = DbConnection::new(&sys_config.database.uri.as_str(), 1).await;
  let dbconn_clone = dbconn.clone();
  // let dictservice = DictService::init(&sys_config.system.area, &dbconn).await;
  // let dictservice = SysCache::new(20 as u64, &sys_config.system.area, &dbconn).await;
  let syscache = SysCache::new(&sys_config.system.area, &dbconn).await;
  // let syscache_clone = syscache.clone();
  SYSCACHE.set(syscache).expect("set global value error");
  // let uidgen = UidgenService::new(1, 1);

  // info!("init dict service done");
  let app = cdcuploader::webserver::routers::create_route(Arc::new(dbconn), Arc::new(sys_config));

  let version = option_env!("VERSION").unwrap_or("Unknown");
  info!("server started on {}, version:{}, {}", &http_url, &version, env!("CARGO_PKG_DESCRIPTION"));

  // let listener = tokio::net::TcpListener::bind(&http_url).await.expect("bind error");
  // axum::serve(listener, app.into_make_service_with_connect_info::<SocketAddr>()).await.expect("start server error");
  let mut time_interval = tokio::time::interval(std::time::Duration::from_secs(3600));
  tokio::spawn(async move {
    time_interval.tick().await;
    loop {
      tokio::select! {
      _=time_interval.tick()=>{
              //触发定时任务，可以清除本地数据库中一些不用的临时数据，比如tj_labresult
            let local: DateTime<Local> = Local::now();
              if local.hour() > 1 && local.hour() <= 2 {
                info!("1-2点，更新缓存......");
                AkaSvc::clear_data(&dbconn_clone).await;
                for ct in nodipservice::common::constant::CacheType::iter(){
                  SYSCACHE.get().unwrap().update_caches(ct as i32, &dbconn_clone).await;
                }
              }
          }
        }
    }
  });
  let listener = tokio::net::TcpListener::bind(&http_url).await.expect("bind error");

  // Create a watch channel to track tasks that are handling connections and wait for them to complete.
  let (close_tx, close_rx) = watch::channel(());

  // axum::serve(listener, app.into_make_service_with_connect_info::<SocketAddr>()).await.expect("start server error");
  loop {
    let (socket, remote_addr) = tokio::select! {
        // Either accept a new connection...
        result = listener.accept() => {
            result.unwrap()
        }
        // ...or wait to receive a shutdown signal and stop the accept loop.
        _ = shutdown_signal() => {
            info!("signal received, not accepting new connections");
            break;
        }
    };

    info!("connection {remote_addr} accepted");
    // We don't need to call `poll_ready` because `Router` is always ready.
    let tower_service = app.clone();
    // Clone the watch receiver and move it into the task.
    let close_rx = close_rx.clone();
    // Spawn a task to handle the connection. That way we can serve multiple connections
    // concurrently.
    tokio::spawn(async move {
      // Hyper has its own `AsyncRead` and `AsyncWrite` traits and doesn't use tokio.
      // `TokioIo` converts between them.
      let socket = TokioIo::new(socket);

      // Hyper also has its own `Service` trait and doesn't use tower. We can use
      // `hyper::service::service_fn` to create a hyper `Service` that calls our app through
      // `tower::Service::call`.
      let hyper_service = hyper::service::service_fn(move |mut request: Request<Incoming>| {
        // We have to clone `tower_service` because hyper's `Service` uses `&self` whereas
        // tower's `Service` requires `&mut self`.
        // request.headers_mut().insert(HOST, remote_addr);
        request.extensions_mut().insert(remote_addr);
        // We don't need to call `poll_ready` since `Router` is always ready.
        tower_service.clone().call(request)
      });

      // `hyper_util::server::conn::auto::Builder` supports both http1 and http2 but doesn't
      // support graceful so we have to use hyper directly and unfortunately pick between
      // http1 and http2.
      let conn = hyper::server::conn::http1::Builder::new()
        .serve_connection(socket, hyper_service)
        // `with_upgrades` is required for websockets.
        .with_upgrades();

      // `graceful_shutdown` requires a pinned connection.
      let mut conn = std::pin::pin!(conn);

      loop {
        tokio::select! {
            // Poll the connection. This completes when the client has closed the
            // connection, graceful shutdown has completed, or we encounter a TCP error.
            result = conn.as_mut() => {
                if let Err(err) = result {
                    info!("failed to serve connection: {err:#}");
                }
                break;
            }
            // Start graceful shutdown when we receive a shutdown signal.
            //
            // We use a loop to continue polling the connection to allow requests to finish
            // after starting graceful shutdown. Our `Router` has `TimeoutLayer` so
            // requests will finish after at most 10 seconds.
            _ = shutdown_signal() => {
                info!("signal received, starting graceful shutdown");
                conn.as_mut().graceful_shutdown();
            }
        }
      }

      info!("connection {remote_addr} closed");

      // Drop the watch receiver to signal to `main` that this task is done.
      drop(close_rx);
    });
  }
  // We only care about the watch receivers that were moved into the tasks so close the residual
  // receiver.
  drop(close_rx);

  // Close the listener to stop accepting new connections.
  drop(listener);

  // Wait for all tasks to complete.
  info!("waiting for {} tasks to finish", close_tx.receiver_count());
  close_tx.closed().await;
  info!("server exited successfully");

  // axum::Server::bind(&httpaddr)
  //   .serve(app.into_make_service())
  //   .with_graceful_shutdown(shutdown_signal())
  //   .await
  //   .unwrap();
}

fn init_startup(config: &Settings) {
  //create directory???
  fs::create_dir_all(&config.system.file_path).expect("create files directory error");
}

async fn shutdown_signal() {
  let ctrl_c = async {
    signal::ctrl_c().await.expect("failed to install Ctrl+C handler");
  };
  #[cfg(unix)]
  let terminate = async {
    signal::unix::signal(signal::unix::SignalKind::terminate())
      .expect("failed to install signal handler")
      .recv()
      .await;
  };
  #[cfg(not(unix))]
  let terminate = std::future::pending::<()>();
  tokio::select! {
      _ = ctrl_c => {},
      _ = terminate => {},
  }
  // info!("signal received, starting graceful shutdown");
}
