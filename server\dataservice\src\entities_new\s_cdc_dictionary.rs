//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "s_cdc_dictionary")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub dictionary_no: i32,
  pub lemma_item: i32,
  pub item_content: Option<String>,
  pub allow_edit: i8,
  pub item_status: i8,
  pub sort_no: Option<i32>,
  pub item_remark: Option<String>,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
