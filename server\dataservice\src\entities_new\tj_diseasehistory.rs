//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_diseasehistory")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_testid: String,
  pub tj_disname: String,
  pub tj_date: i32,
  pub tj_orgname: String,
  pub tj_curve: String,
  pub tj_isrecover: i32,
  pub tj_finalcode: String,
  pub tj_isoccu: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
