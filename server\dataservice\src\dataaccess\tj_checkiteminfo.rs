use crate::entities::{prelude::*, tj_checkiteminfo};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set, TransactionTrait};
use serde_json::json;

impl TjCheckiteminfo {
  pub async fn query_many(testids: &Vec<String>, deptids: &Vec<String>, flag: i32, combined: i32, syncids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjCheckiteminfo>> {
    if testids.len() <= 0 {
      return Err(anyhow!("empty testids to query checkitems, not allowed"));
    }
    let mut conditions = Condition::all().add(tj_checkiteminfo::Column::TjTestid.is_in(testids.to_owned()));
    if deptids.len() > 0 {
      conditions = conditions.add(tj_checkiteminfo::Column::TjDeptid.is_in(deptids.to_owned()));
    }
    if flag > -1 {
      conditions = conditions.add(tj_checkiteminfo::Column::TjAbnormalflag.eq(flag));
    }
    if combined > -1 {
      conditions = conditions.add(tj_checkiteminfo::Column::TjCombineflag.eq(combined));
    }
    if syncids.len() > 0 {
      conditions = conditions.add(tj_checkiteminfo::Column::TjSynid.is_in(syncids.to_owned()))
    }
    let ret = TjCheckiteminfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query(testid: &String, db: &DatabaseConnection) -> Result<Vec<TjCheckiteminfo>> {
    if testid.is_empty() {
      return Err(anyhow!("empty testids to query checkitems, not allowed"));
    }
    let conditions = Condition::all().add(tj_checkiteminfo::Column::TjTestid.eq(testid.to_owned()));

    let ret = TjCheckiteminfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn insert_many(info: &Vec<TjCheckiteminfo>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data to insert checkitems, not allowed"));
    }
    let mut active_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
    for val in info {
      let ret = tj_checkiteminfo::ActiveModel::from_json(json!(val));
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      active_values.push(ret.unwrap());
    }
    let ret = TjCheckiteminfoEntity::insert_many(active_values).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().last_insert_id)
  }

  pub async fn save_many(infos: &Vec<TjCheckiteminfo>, db: &DatabaseConnection) -> Result<u64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len();
    let (insert_vals, update_vals): (Vec<TjCheckiteminfo>, Vec<TjCheckiteminfo>) = infos.to_owned().into_iter().partition(|f| f.id == 0);
    // info!("插入数据：{}，更新数据：{}", &insert_vals.len(), &update_vals.len());
    let txn = db.begin().await.expect("start transaction error");
    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_checkiteminfo::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      // let max_size = 200;
      // let sub2: Vec<Vec<tj_checkiteminfo::ActiveModel>> = active_insert_values.chunks(max_size).map(|v| v.into()).collect();
      // for val in sub2.into_iter() {
      //   let ret = TjCheckiteminfoEntity::insert_many(val).exec(&txn).await;
      //   if ret.as_ref().is_err() {
      //     let _ = txn.rollback();
      //     return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      //   }
      // }
      let ret = TjCheckiteminfoEntity::insert_many(active_insert_values).exec(&txn).await;
      if ret.as_ref().is_err() {
        let _ = txn.rollback();
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_checkiteminfo::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(&txn).await;
        if ret.as_ref().is_err() {
          let _ = txn.rollback();
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    let ret = txn.commit().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    // info!("all iteminfos are done......");
    Ok(total as u64)
  }

  pub async fn delete_many(testid: &String, synids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    if testid.is_empty() {
      return Err(anyhow!("testid empty, not allowed"));
    }
    let mut conditions = Condition::all().add(tj_checkiteminfo::Column::TjTestid.eq(testid));
    if synids.len() > 0 {
      conditions = conditions.add(tj_checkiteminfo::Column::TjSynid.is_in(synids.to_owned()))
    }

    let ret = TjCheckiteminfoEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
