
use anyhow::{anyhow, Result};
use rbatis::crud;
use serde::{Deserialize, Serialize};
// use rbatis::rbatis_codegen::IntoSql;

#[derive(C<PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjHealthycardinfo {
  pub id: i64,
  pub tj_testid: String,
  pub tj_pid: String,
  pub tj_pname: String,
  pub tj_pidcard: String,
  pub tj_meddate: i64,
  pub tj_expdate: i64,
  pub tj_opdate: i64,
  pub tj_operator: i32,
}
crud!(TjHealthycardinfo {}, "tj_healthycardinfo");
rbatis::impl_select!(TjHealthycardinfo{query_latest(idcard:&str) -> Option=> 
  "`where id > 0 `
    ` and tj_pidcard = #{idcard} `
    ` order by id desc limit 1 `"});
rbatis::impl_select!(TjHealthycardinfo{query_many(idcard:&str, testid:&str) => 
  "`where id > 0 `
  if idcard != '':
    ` and tj_pidcard = #{idcard} `
  if testid != '':
    ` and tj_testid = #{testid} `"});
rbatis::impl_delete!(TjHealthycardinfo{delete(testid:&str) => "`where tj_testid = #{testid} `"});

impl TjHealthycardinfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjHealthycardinfo, ) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjHealthycardinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjHealthycardinfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
}
