//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_testsummary")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_testid: String,
    pub tj_deptid: String,
    pub tj_summary: String,
    pub tj_suggestion: String,
    pub tj_isfinished: i32,
    #[sea_orm(column_name = "tj_doctorID")]
    pub tj_doctor_id: String,
    pub tj_doctor: String,
    pub tj_date: i64,
    pub tj_forceend: i32,
    pub tj_checkdoctor: String,
    pub tj_checkdate: i64,
}

#[derive(Co<PERSON>, C<PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
