use crate::entities::{prelude::*, tj_diseaseinfo};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;
use log::*;

impl TjDiseaseinfo {
  pub async fn query(testid: &str, db: &DatabaseConnection) -> Result<Vec<TjDiseaseinfo>> {
    if testid.is_empty() {
      return Err(anyhow!("testid is empty, not allowed"));
    }
    // if id > 0 {
    //   conditions = conditions.add(tj_diseaseinfo::Column::Id.eq(id));
    // }
    // conditions = conditions.add(tj_diseaseinfo::Column::TjTestid.eq(testid.to_owned()));

    // if deptids.len() > 0 {
    //   conditions = conditions.add(tj_diseaseinfo::Column::TjDeptid.is_in(deptids.to_owned()));
    // }
    // if disid > 0 {
    //   conditions = conditions.add(tj_diseaseinfo::Column::TjDisid.eq(disid));
    // }

    let conditions = Condition::all().add(tj_diseaseinfo::Column::TjTestid.eq(testid.to_owned()));
    let ret = TjDiseaseinfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_by_testid_id(testid: &str, did: i64, db: &DatabaseConnection) -> Result<Option<TjDiseaseinfo>> {
    if testid.is_empty() && did <= 0 {
      return Err(anyhow!("conditions are empty"));
    }
    let conditions = Condition::all()
      .add(tj_diseaseinfo::Column::TjTestid.eq(testid.to_owned()))
      .add(tj_diseaseinfo::Column::TjDisid.eq(did));
    info!("conditions:{:?}", &conditions);
    let ret = TjDiseaseinfoEntity::find().filter(conditions).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn query_many(testids: &Vec<String>, deptids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjDiseaseinfo>> {
    let mut conditions = Condition::all();
    if testids.len() <= 0 {
      return Err(anyhow!("testid is empty, not allowed"));
    }
    // if id > 0 {
    //   conditions = conditions.add(tj_diseaseinfo::Column::Id.eq(id));
    // }
    if testids.len() > 0 {
      conditions = conditions.add(tj_diseaseinfo::Column::TjTestid.is_in(testids.to_owned()));
    }

    if deptids.len() > 0 {
      conditions = conditions.add(tj_diseaseinfo::Column::TjDeptid.is_in(deptids.to_owned()));
    }
    // if disid > 0 {
    //   conditions = conditions.add(tj_diseaseinfo::Column::TjDisid.eq(disid));
    // }

    let ret = TjDiseaseinfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjDiseaseinfo, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_diseaseinfo::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    Ok(val_id.unwrap())
  }

  pub async fn save_many(info: &Vec<TjDiseaseinfo>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = info.len();
    let (insert_vals, update_vals): (Vec<TjDiseaseinfo>, Vec<TjDiseaseinfo>) = info.to_owned().into_iter().partition(|f| f.id == 0);

    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_diseaseinfo::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_diseaseinfo::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjDiseaseinfoEntity::insert_many(active_insert_values).exec(db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_diseaseinfo::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    Ok(total as i64)
  }

  pub async fn insert_many(info: &Vec<TjDiseaseinfo>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let mut active_values: Vec<tj_diseaseinfo::ActiveModel> = Vec::new();
    for val in info {
      let ret = tj_diseaseinfo::ActiveModel::from_json(json!(val));
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      active_values.push(ret.unwrap());
    }
    let ret = TjDiseaseinfoEntity::insert_many(active_values).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().last_insert_id)
  }

  pub async fn delete(testids: &Vec<String>, deptids: &Vec<String>, disids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    if testids.len() <= 0 && disids.len() <= 0 {
      return Err(anyhow!("Error! empty values"));
    }
    let mut conditions = Condition::all();
    if testids.len() > 0 {
      conditions = conditions.add(tj_diseaseinfo::Column::TjTestid.is_in(testids.to_owned()));
    }
    if disids.len() > 0 {
      conditions = conditions.add(tj_diseaseinfo::Column::TjDisid.is_in(disids.to_owned()));
    }
    if deptids.len() > 0 {
      conditions = conditions.add(tj_diseaseinfo::Column::TjDeptid.is_in(deptids.to_owned()));
    }
    let ret = TjDiseaseinfoEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
  pub async fn delete_by_ids(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let conditions = Condition::all().add(tj_diseaseinfo::Column::Id.is_in(ids.to_owned()));
    let ret = TjDiseaseinfoEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
