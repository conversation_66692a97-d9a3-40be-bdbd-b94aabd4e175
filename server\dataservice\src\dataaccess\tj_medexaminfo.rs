use crate::entities::{prelude::*, tj_medexaminfo, tj_patient};
use anyhow::{anyhow, Result};
use sea_orm::{
  sea_query::{Expr, Query},
  ActiveModelTrait,
  ActiveValue::NotSet,
  ColumnTrait, Condition, ConnectionTrait, DatabaseBackend, DatabaseConnection, DbBackend, EntityTrait, QueryFilter, QueryTrait, Set, Statement,
};
use serde_json::json;
use tracing::*;

impl TjMedexaminfo {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<TjMedexaminfo>> {
    let ret = TjMedexaminfoEntity::find().filter(Condition::all().add(tj_medexaminfo::Column::TjTestid.eq(code))).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_by_testids(testids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjMedexaminfo>> {
    let ret = TjMedexaminfoEntity::find()
      .filter(Condition::all().add(tj_medexaminfo::Column::TjTestid.is_in(testids.to_owned())))
      .all(db)
      .await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many_by_idcard_testtype(idcard: &str, testtype: i32, status: i32, db: &DatabaseConnection) -> Result<Vec<TjMedexaminfo>> {
    let mut conditions = Condition::all().add(tj_medexaminfo::Column::Id.gt(0));

    if testtype >= 0 {
      conditions = conditions.add(tj_medexaminfo::Column::TjTesttype.eq(testtype));
    }

    if status >= 0 {
      conditions = conditions.add(tj_medexaminfo::Column::TjCheckstatus.gte(status));
    }

    if !idcard.is_empty() {
      conditions = conditions.add(
        tj_medexaminfo::Column::TjPid.in_subquery(
          Query::select()
            .column(tj_patient::Column::TjPid)
            .from(tj_patient::Entity)
            .and_where(tj_patient::Column::TjPidcard.eq(idcard.to_owned()))
            .to_owned(),
        ),
      );
    }

    let query = TjMedexaminfoEntity::find().filter(conditions);
    // info!("query_many_by_idcard_testtype: {:?}", &query.build(DbBackend::MySql).to_string());
    let ret = query.all(db).await;

    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_many(
    dtstart: i64,
    dtend: i64,
    testids: &Vec<String>,
    pids: &Vec<String>,
    corpid: i64,
    isrecheck: i32,
    oldtestids: &Vec<String>,
    testtypes: &Vec<i32>,
    status: &Vec<i32>,
    filter: i32,
    pname: &String,
    db: &DatabaseConnection,
  ) -> Result<Vec<TjMedexaminfo>> {
    let mut conditions = Condition::all().add(tj_medexaminfo::Column::TjCheckstatus.gt(-1));
    if dtstart > 0 {
      conditions = conditions.add(tj_medexaminfo::Column::TjTestdate.gte(dtstart));
    }
    if dtend > 0 {
      conditions = conditions.add(tj_medexaminfo::Column::TjTestdate.lt(dtend));
    }
    if testids.len() > 0 {
      conditions = conditions.add(tj_medexaminfo::Column::TjTestid.is_in(testids.to_owned()));
    }
    if pids.len() > 0 {
      conditions = conditions.add(tj_medexaminfo::Column::TjPid.is_in(pids.to_owned()));
    }
    if corpid > 0 {
      if corpid == 2 {
        conditions = conditions.add(tj_medexaminfo::Column::TjCorpnum.gte(2));
      } else {
        conditions = conditions.add(tj_medexaminfo::Column::TjCorpnum.eq(corpid));
      }
    }
    if isrecheck >= 0 {
      conditions = conditions.add(tj_medexaminfo::Column::TjIsrecheck.eq(isrecheck));
    }

    if oldtestids.len() > 0 {
      conditions = conditions.add(tj_medexaminfo::Column::TjOldtestid.is_in(oldtestids.to_owned()));
    }
    if filter == 1 {
      conditions = conditions.add(tj_medexaminfo::Column::TjCompleted.gt(0));
    }
    if !pname.is_empty() {
      conditions = conditions.add(
        tj_medexaminfo::Column::TjPid.in_subquery(
          Query::select()
            .column(tj_patient::Column::TjPid)
            .from(tj_patient::Entity)
            .and_where(tj_patient::Column::TjPname.contains(pname))
            .to_owned(),
        ),
      );
    }

    if testtypes.len() > 0 {
      conditions = conditions.add(tj_medexaminfo::Column::TjTesttype.is_in(testtypes.to_owned()));
    }

    if status.len() > 0 {
      conditions = conditions.add(tj_medexaminfo::Column::TjCheckstatus.is_in(status.to_owned()));
    }

    let query = TjMedexaminfoEntity::find().filter(conditions);
    info!("query medexaminfos string: {:?}", &query.build(DbBackend::MySql).to_string());
    let ret = query.all(db).await;

    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjMedexaminfo, db: &DatabaseConnection) -> Result<i64> {
    // info!("save medexaminfo, data is:{:?}", &info);
    let ret = tj_medexaminfo::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }

    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn update_status(status: i32, completed: i32, total: i32, testdate: i64, testid: &str, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjMedexaminfoEntity::update_many()
      .col_expr(tj_medexaminfo::Column::TjCheckstatus, Expr::value(status))
      .col_expr(tj_medexaminfo::Column::TjCompleted, Expr::value(completed))
      .col_expr(tj_medexaminfo::Column::TjTotal, Expr::value(total))
      .col_expr(tj_medexaminfo::Column::TjTestdate, Expr::value(testdate))
      .filter(Condition::all().add(tj_medexaminfo::Column::TjTestid.eq(testid)))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }

  pub async fn update_report_numbers(testids: &Vec<String>, rptnum: &str, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjMedexaminfoEntity::update_many()
      .col_expr(tj_medexaminfo::Column::TjRptnum, Expr::value(rptnum))
      .filter(Condition::all().add(tj_medexaminfo::Column::TjTestid.is_in(testids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
  //update_medexam_print_status
  pub async fn update_medexam_print_status(testids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjMedexaminfoEntity::update_many()
      .col_expr(tj_medexaminfo::Column::TjPrintflag, Expr::value(1))
      .col_expr(tj_medexaminfo::Column::TjPrinttimes, Expr::cust("tj_printtimes+1"))
      .filter(Condition::all().add(tj_medexaminfo::Column::TjTestid.is_in(testids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }

  pub async fn update_medinfo_poisionfactors(factors: &String, testids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjMedexaminfoEntity::update_many()
      .col_expr(tj_medexaminfo::Column::TjPoisionfactor, Expr::value(factors))
      .filter(Condition::all().add(tj_medexaminfo::Column::TjTestid.is_in(testids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
  //update_medinfo_upload_status
  pub async fn update_medinfo_upload_status(upstatus: i32, uptime: i64, checkstatus: i32, testids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjMedexaminfoEntity::update_many()
      .col_expr(tj_medexaminfo::Column::TjUpload, Expr::value(upstatus))
      .col_expr(tj_medexaminfo::Column::TjUploadtime, Expr::value(uptime))
      .col_expr(tj_medexaminfo::Column::TjCheckstatus, Expr::value(checkstatus))
      .filter(Condition::all().add(tj_medexaminfo::Column::TjTestid.is_in(testids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }

  pub async fn update_charge_status(testid: &str, chargestatus: i32, db: &DatabaseConnection) -> Result<u64> {
    //
    let ret = TjMedexaminfoEntity::update_many()
      .col_expr(tj_medexaminfo::Column::TjChargestatus, Expr::value(chargestatus))
      .filter(Condition::all().add(tj_medexaminfo::Column::TjTestid.eq(testid)))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }

  pub async fn delete(testids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjMedexaminfoEntity::update_many()
      .col_expr(tj_medexaminfo::Column::TjCheckstatus, Expr::value(-10))
      .filter(Condition::all().add(tj_medexaminfo::Column::TjTestid.is_in(testids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }

  pub async fn query_distinct_corps(dtstart: i64, dtend: i64, db: &DatabaseConnection) -> Result<Vec<i64>> {
    let query_corp_sql = format!("SELECT DISTINCT tj_corpnum FROM `tj_medexaminfo` WHERE `tj_checkstatus` > -1 AND `tj_testdate` >= {} AND `tj_testdate` < {} AND `tj_isrecheck` = 0 AND `tj_completed` > 0 AND `tj_testtype` IN (2, 3, 4, 5, 6) AND `tj_checkstatus` IN (4, 5, 6, 7)",dtstart,dtend);
    let ret = db.query_all(Statement::from_string(DatabaseBackend::MySql, query_corp_sql)).await?;

    let mut distinct_corps: Vec<i64> = vec![];
    for val in ret {
      distinct_corps.push(val.try_get_by_index(0).unwrap_or_default());
    }

    Ok(distinct_corps)
  }
  pub async fn query_distinct_testids(dtstart: i64, dtend: i64, db: &DatabaseConnection) -> Result<Vec<String>> {
    let query_corp_sql = format!(
      "SELECT DISTINCT tj_testid FROM `tj_medexaminfo` WHERE `tj_testdate` >= {} AND `tj_testdate` < {} AND `tj_checkstatus` >= 4",
      dtstart, dtend
    );
    let ret = db.query_all(Statement::from_string(DatabaseBackend::MySql, query_corp_sql)).await?;

    let mut distinct_values: Vec<String> = vec![];
    for val in ret {
      distinct_values.push(val.try_get_by_index(0).unwrap_or_default());
    }

    Ok(distinct_values)
  }
}
