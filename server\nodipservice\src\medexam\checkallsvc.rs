use std::collections::HashSet;

use crate::{
  common::constant::{self, AUDIOGRAM_DEPTID, AUDIO_HAZARD_ID},
  dto::{CheckallOutputDto, CheckallQueryDto, KeyDto},
  medexam::{audiogramsvc::AudiogramSvc, summarysvc::SummarySvc},
  SYSCACHE,
};
use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;
use indexmap::IndexMap;

use super::{healthycardsvc::HealthycardSvc, medexamsvc::MedexamSvc};

pub struct CheckallSvc;

impl CheckallSvc {
  pub async fn query_checkall_by_testid(testid: &String, db: &DbConnection) -> Result<Option<TjCheckallnew>> {
    let ret = TjCheckallnew::query(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  //自动总检
  pub async fn auto_checkall(dto: &KeyDto, db: &DbConnection) -> Result<CheckallOutputDto> {
    let ret = CheckallSvc::query_checkall_by_testid(&dto.key, &db).await?;
    let mut checkall = TjCheckallnew::default();
    checkall.tj_testid = dto.key.clone();
    if ret.is_some() {
      // checkall = ret.unwrap();
      let ckall = ret.unwrap();
      checkall.id = ckall.id;
      checkall.tj_testid = checkall.tj_testid;
    }

    //medinfo
    let ret = TjMedexaminfo::query(&dto.key, &db.get_connection()).await?;
    if ret.is_none() {
      return Err(anyhow!("不能找到该体检编号的体检信息"));
    }
    let medinfo = ret.unwrap();
    //检查项目
    let checkitems = TjCheckiteminfo::query(&dto.key, &db.get_connection()).await?;

    //testsummary
    let summaries = TjTestsummary::query(&dto.key, &vec![], &db.get_connection()).await?;

    //patient info
    let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await?;
    if ret.is_none() {
      return Err(anyhow!("不能找到该体检编号对应的体检人员信息"));
    }
    let ptinfo = ret.unwrap();

    //危害因素
    let mut pthazards = TjPatienthazards::query(&medinfo.tj_testid, &db.get_connection()).await?;

    //疾病信息
    let mut ptdiseases = TjDiseaseinfo::query(&medinfo.tj_testid, &db.get_connection()).await?;

    //职业异常判定
    // let occuconds = SYSCACHE.get().unwrap().get_occuconditions().await;

    // let audiodict = SYSCACHE
    //   .get()
    //   .unwrap()
    //   .get_dict(constant::DictType::DictSysparm as i32, constant::SysParm::Audio as i32, &db)
    //   .await;
    // let audio_default_ocu = match audiodict.ss_short.eq_ignore_ascii_case("1") {
    //   true => true,
    //   false => false,
    // };

    let deptids: Vec<String> = summaries.iter().map(|v| v.tj_deptid.to_owned()).collect();
    //科室信息
    let mut deptinfos = TjDepartinfo::query_many(&vec![], &deptids, -1, &db.get_connection()).await?;
    deptinfos.sort_by(|a, b| a.tj_depttype.cmp(&b.tj_depttype).then(a.tj_showorder.cmp(&b.tj_showorder)));
    info!("Sorted deptinfos:{:?}", &deptinfos);

    let dict_pacsusedept = SYSCACHE
      .get()
      .unwrap()
      .get_dict(constant::DictType::DictSysparm as i32, constant::SysParm::PacsUseDept as i32, &db)
      .await;

    let mut forceend_items: Vec<TjCheckiteminfo> = vec![];
    let mut occu_hazardds: HashSet<i64> = HashSet::new();

    let mut normal_abnormals: IndexMap<String, Vec<String>> = IndexMap::new(); //保存异常项目信息，key为科室，value为异常项目信息
    let mut occu_abnormals: IndexMap<String, Vec<String>> = IndexMap::new(); //保存异常项目信息，key为组合项目名称，value为异常项目信息
    let mut normal_diseases: IndexMap<i64, TjDiseaseinfo> = IndexMap::new(); //疾病信息
    let mut occu_diseases: IndexMap<i64, TjDiseaseinfo> = IndexMap::new();
    
    // let mut normal_abnormals: HashMap<String, Vec<String>> = HashMap::new(); //保存异常项目信息，key为科室，value为异常项目信息
    // let mut occu_abnormals: HashMap<String, Vec<String>> = HashMap::new(); //保存异常项目信息，key为组合项目名称，value为异常项目信息
    // let mut occu_hazardds: HashSet<i64> = HashSet::new();
    // let mut normal_diseases: HashMap<i64, TjDiseaseinfo> = HashMap::new(); //疾病信息
    // let mut occu_diseases: HashMap<i64, TjDiseaseinfo> = HashMap::new();


    //按照科室顺序进行总检
    for dept in deptinfos.iter() {
      let mut has_function_dept_abnormal = false;
      info!("处理科室:{:?}的总检",&dept.tj_deptname);
      //1） 处理异常项目
      let ret = summaries.iter().find(|&f| f.tj_deptid.eq_ignore_ascii_case(&dept.tj_deptid));
      if ret.is_none() {
        continue;
      }
      let suminfo = ret.unwrap().to_owned();
      info!("该科室的小结信息:{:?}",&suminfo);

      //正常，不需要小结
      if suminfo.tj_summary.contains("未见异常")||suminfo.tj_summary.contains("未见明显异常")||suminfo.tj_summary.contains("正常范围心电图"){
        continue;
      }

      // let has_abnormal = checkitems.iter().find(|&f| f.tj_deptid.eq_ignore_ascii_case(&dept.tj_deptid) && f.tj_abnormalflag == 1).is_some();
      // if !dept.tj_deptid.eq_ignore_ascii_case(&AUDIOGRAM_DEPTID) && !has_abnormal{
      //   continue;
      // }

      let mut disease_infos: Vec<TjDiseaseinfo> = ptdiseases.iter().filter(|&f| f.tj_deptid.eq_ignore_ascii_case(&dept.tj_deptid)).map(|v| v.to_owned()).collect();

      //整合该科室的组合项目
      let combine_checkitems: Vec<TjCheckiteminfo> = checkitems
        .iter()
        .filter(|&f| f.tj_combineflag == constant::YesOrNo::Yes as i32 && f.tj_deptid.eq_ignore_ascii_case(&dept.tj_deptid))
        .map(|v| v.to_owned())
        .collect();
      if suminfo.tj_forceend == constant::YesOrNo::Yes as i32 {
        forceend_items.extend(combine_checkitems); //强制结束项目
        continue;
      }

      if dept.tj_deptid.eq_ignore_ascii_case(&AUDIOGRAM_DEPTID) {
        let ret = AudiogramSvc::auto_summary_audiogram_details(&medinfo, &ptinfo, &db).await;
        if ret.as_ref().is_err(){
          continue;
        }
        let audio_summary  = ret.unwrap();
        if audio_summary.tj_summary.is_empty() || audio_summary.tj_summary.contains("未见异常")||audio_summary.tj_summary.contains("强制结束"){
          continue;
        }
        //这里处理电测听的小结
        let audiogram_item_name = match combine_checkitems.len() > 0 {
          true => combine_checkitems[0].tj_itemname.to_owned(),
          false => "电测听".to_owned(),
        };
        let audiogram_summary = suminfo.tj_summary.split("\n").collect::<Vec<&str>>();
        for audio_ret in audiogram_summary.iter() {
          if audio_ret.is_empty() {
            continue;
          }
          if audio_ret.contains("未见异常") || audio_ret.contains("强制结束") {
            continue;
          }
          let abnormal = format!("{}", /*audiogram_item_name,*/ audio_ret.replace("\r\n", ";").replace("\n", ";")).trim().to_string();
          if audio_ret.contains("噪声聋") || audio_ret.contains("听力损失") || audio_ret.contains("听力下降") {
            occu_hazardds.insert(AUDIO_HAZARD_ID);
            occu_abnormals
              .entry(audiogram_item_name.to_owned())
              .and_modify(|v| {
                if v.iter().find(|&f| f.eq_ignore_ascii_case(&abnormal)).is_none() {
                  v.push(abnormal.clone())
                }
              })
              .or_insert(vec![abnormal]);
          } else {
            normal_abnormals
              .entry(audiogram_item_name.to_owned())
              .and_modify(|v| {
                if v.iter().find(|&f| f.eq_ignore_ascii_case(&abnormal)).is_none() {
                  v.push(abnormal.clone())
                }
              })
              .or_insert(vec![abnormal]);
          }
        }
        for dis in disease_infos.iter() {
          if dis.tj_isoccu == constant::YesOrNo::Yes as i32 {
            occu_diseases.insert(dis.id, dis.to_owned());
            if dis.tj_typeid >= checkall.tj_typeid {
              checkall.tj_typeid = dis.tj_typeid;
              checkall.tj_diseasename = dis.tj_diseasename.to_owned();
              checkall.tj_discode = dis.tj_diseasenum.to_owned();
              checkall.tj_ocusuggestion = dis.tj_suggestion.to_owned();
            }
          } else {
            normal_diseases.insert(dis.id, dis.to_owned());
          }
        }

        continue;
      }

      //处理项目异常
      for combine_item in combine_checkitems.into_iter() {
        let mut detail_items = checkitems
          .iter()
          .filter(|&f| f.tj_synid.eq_ignore_ascii_case(&combine_item.tj_itemid) && f.tj_combineflag == constant::YesOrNo::No as i32)
          .map(|v| v.to_owned())
          .collect::<Vec<TjCheckiteminfo>>(); //所有的详细结果
        detail_items.sort_by(|a, b| a.tj_showorder.cmp(&b.tj_showorder)); //排序

        for detail in detail_items.iter() {
          if detail.tj_result.is_empty() {
            continue;
          }
          let occuconditions = SYSCACHE.get().unwrap().find_occuconditions(&pthazards, &detail.tj_itemid).await;
          for occucond in occuconditions.into_iter() {
            if occucond.tj_sex == constant::Sex::Unknown as i32 || occucond.tj_sex == constant::Sex::All as i32 || occucond.tj_sex == ptinfo.tj_psex {
              let comp_str = SummarySvc::get_compute_result(&detail.tj_result, &occucond.tj_condition, &occucond.tj_refvalue);
              info!("根据{:?}计算职业异常的判定条件:{:?}", &occucond, &comp_str);
              let comp_result = evalexpr::eval(&comp_str).unwrap_or(evalexpr::Value::from(false)).as_boolean().unwrap_or(false);
              if comp_result {
                let abnormal = format!("{}:{} {} {}", detail.tj_itemname, detail.tj_result, detail.tj_itemunit, detail.tj_abnormalshow)
                  .trim()
                  .to_string();
                info!("以上判定条件成立......");
                occu_hazardds.insert(occucond.tj_poision as i64);
                occu_abnormals
                  .entry(dept.tj_deptname.to_owned())
                  .and_modify(|v| {
                    if v.iter().find(|&f| f.eq_ignore_ascii_case(&abnormal)).is_none() {
                      v.push(abnormal.to_string())
                    }
                  })
                  .or_insert(vec![abnormal.to_string()]);
                // continue;
              }
            }
          }

          //处理本身就显示异常的项目
          if detail.tj_abnormalflag == constant::YesOrNo::Yes as i32 {
            // info!("{}的异常项目:{:?}",&dept.tj_deptname, &detail);
            if dept.tj_depttype == constant::DeptType::Function as i32 {
              has_function_dept_abnormal = true;
            }
            let abnormal: String;
            if dict_pacsusedept.ss_short.eq_ignore_ascii_case("1") && dept.tj_depttype == constant::DeptType::Function as i32 {
              abnormal = format!("{}", suminfo.tj_summary.replace("\r\n", ";").replace("\n", ";").trim());
            } else {
              abnormal = format!("{}:{} {} {}", detail.tj_itemname, detail.tj_result, detail.tj_itemunit, detail.tj_abnormalshow).replace("\r\n", ";").replace("\n", ";").to_string()
                .trim()
                .to_string();
            }
            // info!("**********异常信息:{:?}",&abnormal);
            normal_abnormals
              .entry(dept.tj_deptname.to_owned())
              .and_modify(|v| {
                if v.iter().find(|&f| f.eq_ignore_ascii_case(&abnormal)).is_none() {
                  // info!("插入新的异常数据:{:?}",&abnormal);
                  v.push(abnormal.clone())
                }
              })
              .or_insert(vec![abnormal]);
            // info!("科室:{}的异常项目信息:{:?}", &dept.tj_deptname, &normal_abnormals);
          }
        }
      }
      
      if !has_function_dept_abnormal && dept.tj_depttype == constant::DeptType::Function as i32 {
        info!("根据小结信息来总检功能科室--判断条件:has_function_dept_abnormal && dept.tj_depttype == constant::DeptType::Function as i32----------------");
           
        let diag_conditions = crate::SYSCACHE.get().unwrap().get_autodiag_conditions(&vec![], &db).await;
        let diseases = crate::SYSCACHE.get().unwrap().get_diseases(&vec![], &vec![], &vec![], &db).await;
        let mut ciinfos =  checkitems
          .iter()
          .filter(|&f| f.tj_deptid.eq_ignore_ascii_case(&dept.tj_deptid) && f.tj_combineflag == constant::YesOrNo::No as i32)
          .map(|v| v.to_owned())
          .collect::<Vec<TjCheckiteminfo>>(); //所有的详细结果

        for dis in diseases.iter(){
          let mut temp_cond: Vec<&TjAutodiagcondition> = diag_conditions.iter().filter(|&f| f.tj_disid == dis.id).map(|v| v).collect();
          if temp_cond.len() <= 0 {
            continue;
          }
          
          if dis.tj_sex != constant::Sex::All as i32 && dis.tj_sex != constant::Sex::Unknown as i32 && dis.tj_sex != ptinfo.tj_psex {
            //性别限制
            continue;
          }
          // if dis.tj_disname.contains("肝囊肿"){
          // info!("判定疾病:{:?}",dis);
          // info!("诊断条件:{:?}",&temp_cond);
          // }
          let eval_result = SummarySvc::diag_disease(&mut temp_cond, &medinfo, &mut ciinfos, &pthazards, &suminfo.tj_summary, &db).await;
          if eval_result {
            info!("😄 诊断疾病信息成立, ID: {} Name:{}", &dis.id, &dis.tj_disname);
            let is_exist = ptdiseases.iter().find(|f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid) && f.tj_disid == dis.id);
            if is_exist.is_none() {
              let mut ptdis: TjDiseaseinfo = TjDiseaseinfo {
                id: 0,
                tj_testid: medinfo.tj_testid.to_owned(),
                tj_disid: dis.id,
                tj_diseasenum: dis.tj_disnum.to_owned(),
                tj_diseasename: dis.tj_disname.to_owned(),
                tj_suggestion: dis.tj_content.to_owned(),
                tj_deptid: dept.tj_deptid.to_owned(),
                tj_isdisease: 1,
                tj_isoccu: dis.tj_occudiseaseflag.to_owned(),
                tj_typeid: dis.tj_typeid.to_owned(),
                tj_opinion: dis.tj_opinion.to_owned(),
                tj_showorder: dis.id,
              };
              info!("发现新疾病:{:?}",&ptdis);
              if ptdiseases.iter().find(|&f| f.tj_disid.eq(&ptdis.tj_disid)).is_none(){
                let ret = TjDiseaseinfo::save(&ptdis, &db.get_connection()).await;
                if ret.as_ref().is_ok(){
                  ptdis.id = ret.unwrap_or_default();
                  ptdiseases.push(ptdis.clone());
                  disease_infos.push(ptdis);
                  // normal_diseases.insert(ptdis.id, ptdis);
                }
              }
              // ptdiseases.push(ptdis);
            }
          }
        }
        
        //没有异常的功能科室项目，则需要从小结中处理
        let abkey_name = SYSCACHE
          .get()
          .unwrap()
          .get_dict(constant::DictType::DictSysparm as i32, constant::SysParm::PacsAbkey as i32, &db)
          .await;
        let pacs_abnormal_key = abkey_name.ss_name.split(",").collect::<Vec<&str>>();
        info!("abnormal keys:{:?}",&pacs_abnormal_key);
        for key in pacs_abnormal_key.iter() {
          if suminfo.tj_summary.contains(key) {
            let abnormal = format!("{}", suminfo.tj_summary.replace("\r\n", ";").replace("\n", ";").trim()).trim().to_string();
            normal_abnormals
              .entry(dept.tj_deptname.to_owned())
              .and_modify(|v| {
                if v.iter().find(|&f| f.eq_ignore_ascii_case(&abnormal)).is_none() {
                  v.push(abnormal.clone())
                }
              })
              .or_insert(vec![abnormal]);
          }
        }
      }
      //2) 处理疾病信息
      for dis in disease_infos.iter() {
        if dis.tj_isoccu == constant::YesOrNo::Yes as i32 {
          occu_diseases.insert(dis.id, dis.to_owned());
          if dis.tj_typeid >= checkall.tj_typeid {
            checkall.tj_typeid = dis.tj_typeid;
            checkall.tj_diseasename = dis.tj_diseasename.to_owned();
            checkall.tj_discode = dis.tj_diseasenum.to_owned();
          }
        } else {
          normal_diseases.insert(dis.id, dis.to_owned());
        }
      }
    }
    // normal_abnormals
    info!("其他异常信息:{:?}", &normal_abnormals);
    info!("职业异常信息:{:?}", &occu_abnormals);
    info!("其他疾病信息:{:?}", &normal_diseases);
    info!("职业疾病信息:{:?}", &occu_diseases);
    info!("职业异常的危害因素:{:?}", &occu_hazardds);

    if normal_abnormals.len() <= 0 && occu_abnormals.len() <= 0 && normal_diseases.len() <= 0 && occu_diseases.len() <= 0 {
      checkall.tj_typeid = constant::CheckResultType::Normal as i32;
      // checkall.tj_othconclusion = "目前未见异常".to_string();
      // checkall.tj_othabnormal = "无".to_string();
      // checkall.tj_othopinion = "无".to_string();
      // checkall.tj_othsuggestion = "无".to_string();
    }
    if checkall.tj_typeid == constant::CheckResultType::Normal as i32 && normal_diseases.len() > 0 && occu_diseases.len() <= 0 {
      checkall.tj_typeid = constant::CheckResultType::Other as i32;
      // checkall.tj_othconclusion = "其他疾病或异常".to_string();
    }
    if occu_hazardds.len() > 0 {
      match medinfo.tj_testtype {
        x if x == constant::TestType::SG as i32 => {
          checkall.tj_typeid = constant::CheckResultType::Forbidden as i32;
        }
        _ => {
          checkall.tj_typeid = constant::CheckResultType::Recheck as i32;
        }
      }
    }
    let other_abnormal: String = normal_abnormals
      .iter()
      .map(|(k, v)|  format!("*[{}]: {}", k, v.join("; "))
      // match v.len() {
      //   1 => format!("{}", v[0]),
      //   _ => format!("{}: {}", k, v.join("; ")),
      // }
      )
      .collect::<Vec<String>>()
      .join("\n");

    // format!("【{}】: {}", k, v.join("; "))).collect::<Vec<String>>().join("\n");
    // info!("Other abnoural:{}", &other_abnormal);

    // checkall.tj_othconclusion = match other_abnormal.is_empty() {
    //   true => "目前未见异常".to_string(),
    //   false => "其他疾病或异常".to_string(),
    // };
    // checkall.tj_othopinion = "无".to_string();
    checkall.tj_othabnormal = other_abnormal;
    let other_suggestion = normal_diseases
      .iter()
      .map(|(_, v)| format!("【{}】:{}", v.tj_diseasename, v.tj_suggestion.replace("\r", "").replace("\n", "")))
      .collect::<Vec<String>>()
      .join("\n");

    checkall.tj_othsuggestion = other_suggestion;
    // match other_suggestion.is_empty() {
    //   true => "无".to_string(),
    //   _ => other_suggestion,
    // };

    if occu_abnormals.len() > 0 && medinfo.tj_testtype == constant::TestType::SG as i32 {
      checkall.tj_typeid = constant::CheckResultType::Forbidden as i32;
      checkall.tj_ocuopinion = "目前不得从事该危害因素的岗位作业。".to_string();
    }

    let occu_abnormal: String = occu_abnormals
      .iter()
      .map(|(k, v)| match v.len() {
        1 => format!("{}", v[0]),
        _ => format!("*[{}]: {}", k, v.join("; ")),
      })
      .collect::<Vec<String>>()
      .join("\n");

    checkall.tj_ocuabnormal = 
    // occu_abnormal;
    match occu_abnormal.is_empty() {
      true => "无".to_string(),
      _ => occu_abnormal,
    };

    if (checkall.tj_typeid == constant::CheckResultType::Normal as i32 || checkall.tj_typeid == constant::CheckResultType::Other as i32) && forceend_items.len() > 0{
      checkall.tj_typeid = constant::CheckResultType::Addional as i32;
    }

    let ocu_suggestion = occu_diseases
      .iter()
      .map(|(_, v)| format!("【{}】:{}", v.tj_diseasename, v.tj_suggestion))
      .collect::<Vec<String>>()
      .join("\n");
    checkall.tj_ocusuggestion = ocu_suggestion;
    if checkall.tj_ocusuggestion.is_empty(){
      checkall.tj_ocusuggestion = "无".to_string();
    }

    if checkall.tj_othsuggestion.is_empty(){
      checkall.tj_othsuggestion = "-".to_string();
    }
    if checkall.tj_othconclusion.is_empty(){
      checkall.tj_othconclusion = "-".to_string();
    }

    if checkall.tj_ocuopinion.is_empty() {
      checkall.tj_ocuopinion = CheckallSvc::get_ocuopinion(checkall.tj_typeid, medinfo.tj_testtype, &pthazards, db).await;
    }
    if checkall.tj_othopinion.is_empty(){
      checkall.tj_othopinion = "-".to_string();
    }

    if checkall.tj_checkdate <= 0 {
      checkall.tj_checkdate = utility::timeutil::current_timestamp();
    }
    
    info!("强制结束项目信息:{:?}",&forceend_items);

    if forceend_items.len() > 0 {
      let sup_items = forceend_items.iter().map(|v| v.tj_itemname.to_owned()).collect::<Vec<String>>().join(",");
      let sep = match checkall.tj_ocuopinion.is_empty() {
        true => "",
        false => ";",
      };
      checkall.tj_ocuopinion = format!("{}{} 需补检:{}", checkall.tj_ocuopinion, sep, sup_items);
    }

    if checkall.tj_typeid == constant::CheckResultType::Normal as i32 && !checkall.tj_othabnormal.is_empty(){
      checkall.tj_typeid = constant::CheckResultType::Other as i32;
    }
    let otherconclusion = match checkall.tj_typeid{
      x if x== constant::CheckResultType::Normal as i32=>{
        "目前未见异常"
      }
      x if x == constant::CheckResultType::Other as i32=>{
        "其他疾病或异常"
      }
      x if x == constant::CheckResultType::Addional as i32=>"需补检",
      _=>""
    };
    checkall.tj_othconclusion = otherconclusion.to_string();
    //处理所有的危害因素的结论
    if checkall.tj_typeid == constant::CheckResultType::Normal as i32 {
      pthazards.iter_mut().for_each(|v| {
        v.tj_typeid = constant::CheckResultType::Normal as i32;
        v.tj_diseases = "".to_string();
        v.tj_recheckitems = "".to_string();
      });
    } else if checkall.tj_typeid == constant::CheckResultType::Other as i32  {
      pthazards.iter_mut().for_each(|v| {
        v.tj_typeid = constant::CheckResultType::Other as i32;
        v.tj_diseases = ptdiseases.iter().map(|v| v.tj_disid.to_string()).collect::<Vec<String>>().join(",");
        v.tj_recheckitems = "".to_string();
      });
    } else if checkall.tj_typeid == constant::CheckResultType::Recheck as i32 {
      pthazards.iter_mut().for_each(|v| {
        if occu_hazardds.len() > 0 {
          if occu_hazardds.iter().find(|&f| f.eq(&v.tj_hid)).is_some() {
            v.tj_typeid = constant::CheckResultType::Recheck as i32;
          } else {
            if checkall.tj_othabnormal.is_empty() || checkall.tj_othabnormal.eq_ignore_ascii_case("无") {
              v.tj_typeid = constant::CheckResultType::Normal as i32;
            } else {
              v.tj_typeid = constant::CheckResultType::Other as i32;
              v.tj_diseases = ptdiseases.iter().map(|v| v.tj_disid.to_string()).collect::<Vec<String>>().join(",");
            }
          }
        } else {
          v.tj_typeid = checkall.tj_typeid;
        }
      });
    }else if checkall.tj_typeid == constant::CheckResultType::Addional as i32{
      pthazards.iter_mut().for_each(|v| {
        v.tj_typeid = constant::CheckResultType::Addional as i32;
        v.tj_diseases = ptdiseases.iter().map(|v| v.tj_disid.to_string()).collect::<Vec<String>>().join(",");
        v.tj_recheckitems = "".to_string();
      });
    } else {
      pthazards.iter_mut().for_each(|v| {
        if occu_hazardds.len() > 0 {
          if occu_hazardds.iter().find(|&f| f.eq(&v.tj_hid)).is_some() {
            v.tj_typeid = constant::CheckResultType::Forbidden as i32;
          } else {
            if checkall.tj_othabnormal.is_empty() || checkall.tj_othabnormal.eq_ignore_ascii_case("无") {
              v.tj_typeid = constant::CheckResultType::Normal as i32;
            } else {
              v.tj_typeid = constant::CheckResultType::Other as i32;
              v.tj_diseases = ptdiseases.iter().map(|v| v.tj_disid.to_string()).collect::<Vec<String>>().join(",");
            }
          }
        } else {
          v.tj_typeid = checkall.tj_typeid;
        }
      });
    }

    if checkall.tj_othabnormal.is_empty(){
      checkall.tj_othabnormal = "无".to_string();
    }
    if checkall.tj_othsuggestion.is_empty(){
      checkall.tj_othsuggestion = "无".to_string();
    }

    info!("总检结果:{:?},{:?}", &checkall, &pthazards);
    Ok(CheckallOutputDto { checkall, pthazards })
  }

  pub async fn query_checkalls(dto: &CheckallQueryDto, db: &DbConnection) -> Result<Vec<TjCheckallnew>> {
    let testids: Vec<String> = dto.testids.iter().map(|v| v.clone()).collect();
    let ret = TjCheckallnew::query_many_by_dto(dto.stdate, dto.enddate, &testids, &dto.typeids, dto.castatus, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_checkalls(info: &TjCheckallnew, db: &DbConnection) -> Result<i64> {
    let mut info = info.to_owned();
    if info.tj_othconclusion.is_empty()
      && (info.tj_typeid == crate::common::constant::CheckResultType::Normal as i32 || info.tj_typeid == crate::common::constant::CheckResultType::Other as i32)
    {
      let ret = crate::SYSCACHE
        .get()
        .unwrap()
        .get_dict(crate::common::constant::DictType::DictCheckall as i32, info.tj_typeid, &db)
        .await;
      info.tj_othconclusion = ret.ss_name.to_string();
    }
    let ret = TjCheckallnew::save(&info, &db.get_connection()).await;

    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }

    //处理健康证
    if info.tj_typeid == constant::CheckResultType::Normal as i32 || info.tj_typeid == constant::CheckResultType::Other as i32 {
      //do something
      let ret = MedexamSvc::query_medinfo_by_testid(&info.tj_testid, db).await;

      if let Ok(medret) = ret {
        if let Some(medinfo) = medret {
          if medinfo.tj_testtype == constant::TestType::JKZ as i32 {
            //generate jkz
            let ckret = HealthycardSvc::save_hcardinfo(&medinfo, &info, db).await;
            if ckret.as_ref().is_err() {
              error!("{}", ckret.as_ref().unwrap_err().to_string());
              return Err(anyhow!("{}", ckret.as_ref().unwrap_err().to_string()));
            }
          }
        }
      }
    }

    Ok(ret.unwrap())
  }
  pub async fn delete_checkall(info: &TjCheckallnew, db: &DbConnection) -> Result<i64> {
    // let testids = vec![info.tj_testid.to_owned()];
    let ret = TjCheckallnew::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  async fn get_ocuopinion(rettypeid: i32, testtype: i32, pthazards: &Vec<TjPatienthazards>, db: &DbConnection) -> String {
    let customer_dict = SYSCACHE
      .get()
      .unwrap()
      .get_dict(constant::DictType::DictSysparm as i32, constant::SysParm::Customer as i32, db)
      .await;
    if customer_dict.ss_short.to_ascii_lowercase().eq_ignore_ascii_case("txsy") {
      return "".to_string();
    }
    let mut ocuopinion = "".to_string();
    match testtype {
      x if x == constant::TestType::SG as i32 => match rettypeid {
        x if x == constant::CheckResultType::Normal as i32 || x == constant::CheckResultType::Other as i32 => {
          ocuopinion = "可以从事该危害因素的岗位作业。".to_string();
        }
        x if x == constant::CheckResultType::Forbidden as i32 => {
          let hdids = pthazards.iter().map(|v| v.tj_hid).collect::<Vec<i64>>();
          let hdinfo = SYSCACHE.get().unwrap().get_hazardinfos(&vec![], &hdids, &vec![], db).await;
          if hdinfo.len() <= 0 {
            ocuopinion = "目前不得从事该岗位的作业。".to_string();
          } else {
            ocuopinion = format!("目前不得从事{}作业。", hdinfo.iter().map(|v| v.tj_hname.to_owned()).collect::<Vec<String>>().join(","));
          }
        }
        _ => {}
      },
      x if x == constant::TestType::ZG as i32 => match rettypeid {
        x if x == constant::CheckResultType::Normal as i32 || x == constant::CheckResultType::Other as i32 => {
          ocuopinion = "可以继续从事原岗位作业。".to_string();
        }
        x if x == constant::CheckResultType::Recheck as i32 => {
          ocuopinion = "需复查".to_string();
        }
        x if x == constant::CheckResultType::Forbidden as i32 => {
          let hdids = pthazards.iter().map(|v| v.tj_hid).collect::<Vec<i64>>();
          let hdinfo = SYSCACHE.get().unwrap().get_hazardinfos(&vec![], &hdids, &vec![], db).await;
          if hdinfo.len() <= 0 {
            ocuopinion = "目前不得从事该岗位的作业".to_string();
          } else {
            ocuopinion = format!("目前不得继续从事{}作业。", hdinfo.iter().map(|v| v.tj_hname.to_owned()).collect::<Vec<String>>().join(","));
          }
        }
        x if x == constant::CheckResultType::Oculike as i32 => {
          ocuopinion = "二周内安排到相应依法承担职业病诊断医疗卫生机构进行职业病诊断。".to_string();
        }
        _ => {}
      },
      x if x == constant::TestType::LG as i32 => match rettypeid {
        x if x == constant::CheckResultType::Normal as i32 || x == constant::CheckResultType::Other as i32 => {
          ocuopinion = "目前未见异常，可以离岗".to_string();
        }
        x if x == constant::CheckResultType::Forbidden as i32 => {
          ocuopinion = "".to_string();
        }
        _ => {}
      },
      _ => {
        //
      }
    }
    ocuopinion
  }
}
