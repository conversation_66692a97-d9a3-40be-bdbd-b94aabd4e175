use crate::entities::{prelude::*, tj_audiogramdetail};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjAudiogramdetail {
  pub async fn query(testid: &String, db: &DatabaseConnection) -> Result<Vec<TjAudiogramdetail>> {
    if testid.is_empty() {
      return Err(anyhow!("testids is empty"));
    }
    let ret = TjAudiogramdetailEntity::find().filter(tj_audiogramdetail::Column::TjTestid.eq(testid)).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_many(testids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjAudiogramdetail>> {
    if testids.len() <= 0 {
      return Err(anyhow!("testids is empty"));
    }
    let ret = TjAudiogramdetailEntity::find()
      .filter(tj_audiogramdetail::Column::TjTestid.is_in(testids.to_owned()))
      .all(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_many(info: &Vec<TjAudiogramdetail>, db: &DatabaseConnection) -> Result<i32> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = info.len();
    let (insert_vals, update_vals): (Vec<TjAudiogramdetail>, Vec<TjAudiogramdetail>) = info.to_owned().into_iter().partition(|f| f.id == 0);

    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_audiogramdetail::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_audiogramdetail::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjAudiogramdetailEntity::insert_many(active_insert_values).exec(db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_audiogramdetail::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_audiogramdetail::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    Ok(total as i32)

    // let testids: Vec<String> = info.iter().map(|f| f.tj_testid.to_owned()).collect();
    // let ret = TjAudiogramdetail::delete(&testids, &db).await;
    // if ret.as_ref().is_err() {
    //   return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    // }
    // let mut active_values: Vec<tj_audiogramdetail::ActiveModel> = Vec::new();
    // for val in info {
    //   let ret = tj_audiogramdetail::ActiveModel::from_json(json!(val));
    //   if ret.as_ref().is_err() {
    //     return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    //   }
    //   active_values.push(ret.unwrap());
    // }
    // let ret = TjAudiogramdetailEntity::insert_many(active_values).exec(db).await;
    // if ret.as_ref().is_err() {
    //   return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    // }
  }

  pub async fn insert_many(info: &Vec<TjAudiogramdetail>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let testids: Vec<String> = info.iter().map(|f| f.tj_testid.to_owned()).collect();
    let ret = TjAudiogramdetail::delete(&testids, &db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut active_values: Vec<tj_audiogramdetail::ActiveModel> = Vec::new();
    for val in info {
      let ret = tj_audiogramdetail::ActiveModel::from_json(json!(val));
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      active_values.push(ret.unwrap());
    }
    let ret = TjAudiogramdetailEntity::insert_many(active_values).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().last_insert_id)
  }

  pub async fn delete(testids: &Vec<String>, db: &DatabaseConnection) -> Result<()> {
    if testids.len() <= 0 {
      return Err(anyhow!("testids are empty, not allowed"));
    }
    let ret = TjAudiogramdetailEntity::delete_many()
      .filter(tj_audiogramdetail::Column::TjTestid.is_in(testids.to_owned()))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(())
  }
  pub async fn delete_by_ids(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<()> {
    if ids.len() <= 0 {
      return Err(anyhow!("Empty, not allowed"));
    }
    let ret = TjAudiogramdetailEntity::delete_many().filter(tj_audiogramdetail::Column::Id.is_in(ids.to_owned())).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(())
  }
}
