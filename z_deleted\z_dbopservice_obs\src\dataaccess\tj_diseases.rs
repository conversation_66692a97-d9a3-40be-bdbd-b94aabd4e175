use anyhow::{anyhow, Result};
use rbatis::{
  crud
};
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjDiseases {
  pub id: i64,
  pub tj_discode: String,
  pub tj_disname: String,
  pub tj_disnum: String,
  pub tj_deptid: i32,
  pub tj_type: String,
  pub tj_content: String,
  pub tj_occudiseaseflag: i32,
  pub tj_startage: i32,
  pub tj_endage: i32,
  pub tj_sex: i32,
  pub tj_career: String,
  pub tj_marriage: i32,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_typeid: i32,
  pub tj_opinion: String,
  pub tj_status: i32, //1:有效 0:无效
}
crud!(TjDiseases {}, "tj_diseases");
rbatis::impl_select!(TjDiseases{query_many(disids:&[i64]) => 
  "`where id > 0 and tj_status >= 0 `
  if !disids.is_empty():
    ` and id in ${disids.sql()} `
 "});
// rbatis::impl_delete!(TjDiseases{delete(ids:&[i64]) => "`where id in ${ids.sql()} `"});

impl TjDiseases {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjDiseases) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjDiseases::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjDiseases::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }

  #[rbatis::sql("update tj_diseases set tj_status = -1 where id = ? ")]
  pub async fn delete(rb: &rbatis::RBatis, id: &i64) -> rbatis::Result<()> {
    impled!()
  }
}
