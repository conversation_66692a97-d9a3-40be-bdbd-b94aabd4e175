#[allow(unused)]
use crate::rusthl7::{Field, Hl7ParseError, Message, Separators};
use anyhow::Result;
use std::fmt::Display;

#[derive(Debug, PartialEq)]
pub struct OrcSegment<'a> {
  pub source: &'a str,
  //this initial layout largely stolen from the _other_ hl7 crate: https://github.com/njaremko/hl7
  pub msg_encoding_characters: Separators,
  pub orc_1_order_control: Field<'a>,
  pub orc_2_placer_order_number: Option<Field<'a>>,
  pub orc_3_filler_order_number: Option<Field<'a>>,
  pub orc_4_placer_group_number: Option<Field<'a>>,
  pub orc_5_order_status: Option<Field<'a>>,
  pub orc_6_response_flag: Option<Field<'a>>,
  pub orc_7_quantity_timing: Option<Field<'a>>,
  pub orc_8_parent_order: Option<Field<'a>>,
  pub orc_9_date_time_of_transaction: Option<Field<'a>>,
  pub orc_10_entered_by: Option<Field<'a>>,
  pub orc_11_verified_by: Option<Field<'a>>,
  pub orc_12_ordering_provider: Option<Field<'a>>,
  pub orc_13_enterers_location: Option<Field<'a>>,
  pub orc_14_call_back_phone_number: Option<Field<'a>>,
  pub orc_15_order_effective_date_time: Option<Field<'a>>,
  pub orc_16_order_control_code_reason: Option<Field<'a>>,
  pub orc_17_entering_organization: Option<Field<'a>>,
  pub orc_18_entering_device: Option<Field<'a>>,
  pub orc_19_action_by: Option<Field<'a>>,
  pub orc_20_advanced_beneficiary_notice_code: Option<Field<'a>>,
  pub orc_21_ordering_facility_name: Option<Field<'a>>,
  pub orc_22_ordering_facility_address: Option<Field<'a>>,
  pub orc_23_ordering_facility_phone_number: Option<Field<'a>>,
  pub orc_24_ordering_provider_address: Option<Field<'a>>,
  pub orc_25_order_status_modifier: Option<Field<'a>>,
  // pub orc_26_advanced_beneficiary_notice_override_reason: Option<Field<'a>>,
  // pub orc_27_fillers_expected_availability_date_time: Option<Field<'a>>,
  // pub orc_28_confidentiality_code: Option<Field<'a>>,
  // pub orc_29_order_type: Option<Field<'a>>,
  // pub orc_30_enterer_authorization_mode: Option<Field<'a>>,
  // pub orc_31_parent_universal_service_identifier: Option<Field<'a>>,
  // pub orc_32_advanced_beneficiary_notice_date: Option<Field<'a>>,
  // pub orc_33_alternate_placer_order_number: Option<Field<'a>>,
  // pub orc_34_order_workflow_profile: Option<Field<'a>>,
}

impl<'a> OrcSegment<'a> {
  pub fn parse<S: Into<&'a str>>(input: S, delims: &Separators) -> Result<OrcSegment<'a>, Hl7ParseError> {
    let input = input.into();

    let mut fields = input.split(delims.field);

    assert!(fields.next().unwrap() == "ORC");

    let seg = OrcSegment {
      source: input,
      msg_encoding_characters: delims.to_owned(),
      orc_1_order_control: Field::parse_mandatory(fields.next(), delims)?,
      orc_2_placer_order_number: Field::parse_optional(fields.next(), delims)?,
      orc_3_filler_order_number: Field::parse_optional(fields.next(), delims)?,
      orc_4_placer_group_number: Field::parse_optional(fields.next(), delims)?,
      orc_5_order_status: Field::parse_optional(fields.next(), delims)?,
      orc_6_response_flag: Field::parse_optional(fields.next(), delims)?,
      orc_7_quantity_timing: Field::parse_optional(fields.next(), delims)?,
      orc_8_parent_order: Field::parse_optional(fields.next(), delims)?,
      orc_9_date_time_of_transaction: Field::parse_optional(fields.next(), delims)?,
      orc_10_entered_by: Field::parse_optional(fields.next(), delims)?,
      orc_11_verified_by: Field::parse_optional(fields.next(), delims)?,
      orc_12_ordering_provider: Field::parse_optional(fields.next(), delims)?,
      orc_13_enterers_location: Field::parse_optional(fields.next(), delims)?,
      orc_14_call_back_phone_number: Field::parse_optional(fields.next(), delims)?,
      orc_15_order_effective_date_time: Field::parse_optional(fields.next(), delims)?,
      orc_16_order_control_code_reason: Field::parse_optional(fields.next(), delims)?,
      orc_17_entering_organization: Field::parse_optional(fields.next(), delims)?,
      orc_18_entering_device: Field::parse_optional(fields.next(), delims)?,
      orc_19_action_by: Field::parse_optional(fields.next(), delims)?,
      orc_20_advanced_beneficiary_notice_code: Field::parse_optional(fields.next(), delims)?,
      orc_21_ordering_facility_name: Field::parse_optional(fields.next(), delims)?,
      orc_22_ordering_facility_address: Field::parse_optional(fields.next(), delims)?,
      orc_23_ordering_facility_phone_number: Field::parse_optional(fields.next(), delims)?,
      orc_24_ordering_provider_address: Field::parse_optional(fields.next(), delims)?,
      orc_25_order_status_modifier: Field::parse_optional(fields.next(), delims)?,
      // orc_26_advanced_beneficiary_notice_override_reason: Field::parse_optional(fields.next(), delims)?,
      // orc_27_fillers_expected_availability_date_time: Field::parse_optional(fields.next(), delims)?,
      // orc_28_confidentiality_code: Field::parse_optional(fields.next(), delims)?,
      // orc_29_order_type: Field::parse_optional(fields.next(), delims)?,
      // orc_30_enterer_authorization_mode: Field::parse_optional(fields.next(), delims)?,
      // orc_31_parent_universal_service_identifier: Field::parse_optional(fields.next(), delims)?,
      // orc_32_advanced_beneficiary_notice_date: Field::parse_optional(fields.next(), delims)?,
      // orc_33_alternate_placer_order_number: Field::parse_optional(fields.next(), delims)?,
      // orc_34_order_workflow_profile: Field::parse_optional(fields.next(), delims)?,
    };

    Ok(seg)
  }
}
/// Common formatter trait implementation for the strongly-typed segment
impl<'a> Display for OrcSegment<'a> {
  /// Required for to_string() and other formatter consumers
  fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
    write!(f, "{}", self.source)
  }
}
/// Common clone trait implementation for the strongly-typed segment
impl<'a> Clone for OrcSegment<'a> {
  /// Creates a new Message object using _the same source_ slice as the original.
  fn clone(&self) -> Self {
    let delims = self.msg_encoding_characters;
    OrcSegment::parse(self.source, &delims).unwrap()
  }
}
/// Extracts header element for external use
pub fn _orc<'a>(msg: &Message<'a>) -> Result<OrcSegment<'a>, Hl7ParseError> {
  let seg = msg.segments_by_identifier("ORC").unwrap()[0];
  let segment = OrcSegment::parse(seg.source, &msg.get_separators()).expect("Failed to parse ORC segment");
  Ok(segment)
}
