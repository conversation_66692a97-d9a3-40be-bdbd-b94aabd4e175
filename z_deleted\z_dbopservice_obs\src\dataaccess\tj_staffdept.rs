use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjStaffdept {
  pub id: i64,
  pub tj_staffid: i64,
  pub tj_deptid: String,
}
crud!(TjStaffdept {}, "tj_staffdept");
rbatis::impl_select!(TjStaffdept{query_many(staffid:&i64) => "`where tj_staffid = #{staffid} `"});

rbatis::impl_select!(TjStaffdept{query(staffid:&i64) => 
  "`where id > 0 `
  if staffid > 0:
    ` and tj_staffid = #{staffid} `"});

rbatis::impl_delete!(TjStaffdept{delete(ids:&[i64]) => "`where id in ${ids.sql()} `"});
rbatis::impl_delete!(TjStaffdept{delete_many(staffids:&[i64], deptids:&[&str]) => "`where id in ${staffids.sql()} and tj_deptid in ${deptids.sql()}`"});

impl TjStaffdept {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjStaffdept) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjStaffdept::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjStaffdept::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
}
