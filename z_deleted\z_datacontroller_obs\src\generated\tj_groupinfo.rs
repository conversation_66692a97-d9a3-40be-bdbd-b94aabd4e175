//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, <PERSON>ialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_groupinfo")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_gname: String,
    pub tj_gmemo: String,
    pub tj_goperator: String,
    pub tj_gmoddate: i64,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::tj_groupright::Entity")]
    TjGroupright,
}

impl Related<super::tj_groupright::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjGroupright.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
