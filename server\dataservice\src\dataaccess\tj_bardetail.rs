use crate::entities::{prelude::*, tj_bardetail};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjBardetail {
  // pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<TjBardetail>> {
  //   let ret = TjBardetailEntity::find().filter(tj_bardetail::Column::TjBinum.eq(code)).one(db).await;
  //   if ret.as_ref().is_err() {
  //     // error!("query error:{}", ret.err().unwrap().to_string());
  //     return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
  //   }
  //   Ok(ret.unwrap())
  // }
  pub async fn query_many(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<Vec<TjBardetail>> {
    let mut conditions = Condition::all();
    if ids.len() > 0 {
      conditions = conditions.add(tj_bardetail::Column::Id.is_in(ids.to_owned()));
    }
    let ret = TjBardetailEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjBardetail, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_bardetail::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn delete_by_itemid(itemid: &String, db: &DatabaseConnection) -> Result<u64> {
    if itemid.is_empty() {
      return Err(anyhow!("itemid is empty, not allowed"));
    }
    let conditions = Condition::all().add(tj_bardetail::Column::TjItemid.eq(itemid));
    let ret = TjBardetailEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn delete(itemid: &str, binum: &str, db: &DatabaseConnection) -> Result<u64> {
    if itemid.is_empty() && binum.is_empty() {
      return Err(anyhow!("itemid and barnum is empty, not allowed"));
    }

    let mut conditions = Condition::all();
    if !binum.is_empty() {
      conditions = conditions.add(tj_bardetail::Column::TjBinum.eq(binum));
    }
    if !itemid.is_empty() {
      conditions = conditions.add(tj_bardetail::Column::TjItemid.eq(itemid));
    }
    let ret = TjBardetailEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
