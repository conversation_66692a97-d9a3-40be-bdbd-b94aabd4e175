use crate::{api::httpresponse::{response_json_value_error, response_json_value_ok}, auth::auth::Claims};
use axum::{Extension, Json};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{
  dto::{CorpQueryDto, KeyIntsDto, KeysDto},
  medexam::reportsvc::ReportSvc,
};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

///query report
pub async fn query_reports(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<CorpQueryDto>) -> Json<Value> {
  info!("query reports dto:{dto:?}");
  let ret = ReportSvc::query_reports(&dto, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), Vec::<TjCorpoccureport>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

///save or update report
pub async fn save_report(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjCorpoccureport>) -> Json<Value> {
  let ret = ReportSvc::save_report(&dto, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), 0);
  }

  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

///delete report
pub async fn delete_report(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntsDto>) -> Json<Value> {
  info!("Start to delete report by ids:{:?}", &dto.keys);
  let ret = ReportSvc::delete_report(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), 0);
  }

  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn save_reportinfos_detail(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(infos): Json<Vec<TjCorpoccureportInfo>>) -> Json<Value> {
  info!("save report infos:{:?}", &infos);

  let ret = ReportSvc::save_reportinfos_detail(&infos, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), 0);
  }

  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

//qury_report_infos
pub async fn qury_reportinfos_detail(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntsDto>) -> Json<Value> {
  info!("query report infos:{:?}", &dto.keys);
  let ret = ReportSvc::qury_reportinfos_detail(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), Vec::<TjCorpoccureportInfo>::new());
  }

  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

///remove testers from report
pub async fn remove_from_report(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeysDto>) -> Json<Value> {
  info!("Start to remove medinfos from report by ids:{:?}", &dto.keys);
  let ret = ReportSvc::remove_from_report(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), 0);
  }

  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

//query_reportinfo_by_testids
pub async fn query_reportinfo_by_testids(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeysDto>) -> Json<Value> {
  info!("query report infos:{:?}", &dto.keys);
  let ret = ReportSvc::query_reportinfo_by_testids(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    // return response_json_error(ret.as_ref().unwrap_err().to_string().as_str());
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), Vec::<TjCorpoccureportInfo>::new());
  }

  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}
