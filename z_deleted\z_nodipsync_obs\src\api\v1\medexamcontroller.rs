use crate::api::respons::response_json_error;
use crate::common::filesvc::FileSvc;
use crate::config::settings::Settings;
// use crate::nxmiddleware::token::Claims;
use axum::extract::{Extension, Multipart};
use axum::Json;
use datacontroller::datasetup::DbConnection;
use datacontroller::entities::tj_staffadmin::TjStaffadmin;

use odipservice::dto::dto::IdsDto;
use odipservice::prelude::*;
use odipservice::response::httpresponse::{HttpCode, ResponseBody};
use odipservice::service::medexamsyncsvc::MedexamSyncSvc;
use serde_json::Value;
use std::fs::File;
use std::io::prelude::*;
use std::sync::Arc;

pub async fn do_upload_data(
  Extension(db): Extension<Arc<DbConnection>>,
  Extension(config): Extension<Arc<Settings>>,
  Extension(dictservice): Extension<Arc<DictService>>,
  Json(payload): Json<serde_json::Value>,
) -> Json<Value> {
  info!("upload_corpinfo_data payload is: {:?}", &payload);

  let res = ResponseBody::new(HttpCode::OK as i32, "成功", "");
  Json(serde_json::json!(res))
}

//从polar平台获取预约的体检信息
// claim: Claims,
pub async fn sync_medinfo(Extension(db): Extension<Arc<DbConnection>>, Extension(config): Extension<Arc<Settings>>, Extension(dictsvc): Extension<Arc<DictService>>) -> Json<Value> {
  info!("start to sync medinfos from saas server......");
  let userinfo = TjStaffadmin {
    id: 1,
    tj_staffno: "admin".to_owned(),
    tj_staffname: "admin".to_owned(),
    ..Default::default()
  };
  // let userinfo = claim.user;
  let ret = MedexamSyncSvc::sync_medinfo_and_corpinfos(&userinfo, config.system.orgid.to_string().as_str(), &config.system.syncserver, &dictsvc, &db).await;
  if ret.as_ref().is_err() {
    return response_json_error(&ret.err().unwrap().to_string());
  }
  let result = ret.unwrap();
  let res = ResponseBody::new(HttpCode::OK as i32, "OK", result);
  Json(serde_json::json!(res))
}

pub async fn update_medstatus(Extension(db): Extension<Arc<DbConnection>>, Extension(config): Extension<Arc<Settings>>, Json(payload): Json<IdsDto>) -> Json<Value> {
  info!("start to sync mestatsu to saas server......");
  let userinfo = TjStaffadmin {
    id: 1,
    tj_staffno: "admin".to_owned(),
    tj_staffname: "admin".to_owned(),
    ..Default::default()
  };
  let ret = MedexamSyncSvc::update_med_status(&userinfo, &payload, &config.system.orgid.to_string(), &config.system.syncserver, &db).await;
  if ret.as_ref().is_err() {
    return response_json_error(&ret.err().unwrap().to_string());
  }
  Json(serde_json::json!(ResponseBody::new(HttpCode::OK as i32, "OK", "")))
}

pub async fn upload_medresults(
  // claim: Claims,
  Extension(db): Extension<Arc<DbConnection>>,
  Extension(config): Extension<Arc<Settings>>,
  Json(payload): Json<IdsDto>,
) -> Json<Value> {
  info!("======> start to upload med results to saas server......payload:{:?}", &payload);
  if payload.ids.len() <= 0 {
    return response_json_error("没有需要上传的数据");
  }
  let userinfo = TjStaffadmin {
    id: 1,
    tj_staffno: "admin".to_owned(),
    tj_staffname: "admin".to_owned(),
    ..Default::default()
  };
  // let mut uidgen = idgen.write().await;
  let pid = payload.ids.get(0).unwrap();
  let ret = MedexamSyncSvc::upload_medresults(&userinfo, &pid, "", config.system.orgid, &config.system.syncserver, &config.system.photodir, &db).await;
  if ret.as_ref().is_err() {
    return response_json_error(&ret.err().unwrap().to_string());
  }

  let res = ResponseBody::new(HttpCode::OK as i32, "OK", "");
  Json(serde_json::json!(res))
}

pub async fn upload_medexam_results_with_report(
  // claim: Claims,
  Extension(db): Extension<Arc<DbConnection>>,
  Extension(config): Extension<Arc<Settings>>,
  Extension(dictsvc): Extension<Arc<DictService>>,
  multipart: Multipart,
) -> Json<Value> {
  info!("start to upload med results to saas server......multipart:{:?}", &multipart);

  let ret = FileSvc::do_upload("", &config.system.uploaddir, multipart).await;
  info!("upload report result:{:?}", &ret);
  if ret.as_ref().is_err() {
    return response_json_error(&ret.err().unwrap().to_string());
  }
  let upload_ret = ret.unwrap();
  let testid = upload_ret.0;
  if testid.is_empty() {
    return response_json_error("testid is empty");
  }
  let filename = upload_ret.1;
  //同步文件和内容到后台
  let userinfo = TjStaffadmin {
    id: 1,
    tj_staffno: "admin".to_owned(),
    tj_staffname: "admin".to_owned(),
    ..Default::default()
  };

  let ret = MedexamSyncSvc::upload_medresults(&userinfo, &testid, &filename, config.system.orgid, &config.system.syncserver, &config.system.photodir, &db).await;
  if ret.as_ref().is_err() {
    return response_json_error(&ret.err().unwrap().to_string());
  }
  let res = ResponseBody::new(HttpCode::OK as i32, "OK", "");
  Json(serde_json::json!(res))
}

pub async fn upload_corpreports(Extension(db): Extension<Arc<DbConnection>>, Extension(config): Extension<Arc<Settings>>, mut multipart: Multipart) -> Json<Value> {
  // info!("start to upload corp results to saas server......multipart:{:?}", &multipart);

  if let Some(field) = multipart.next_field().await.unwrap() {
    info!("Fields:{:?}", &field);
    //保存文件
    info!("upload corp report file name is:{:?}", &field.file_name());

    let ret = field.file_name();
    if ret.is_none() {
      return response_json_error("file name is empty");
    }
    let filename = ret.unwrap().to_owned();

    let ret = field.name();
    if ret.is_none() {
      return response_json_error("report num is empty");
    }
    let rptid = ret.unwrap().to_owned();

    let ret = field.bytes().await;
    if ret.as_ref().is_err() {
      return response_json_error(&ret.err().unwrap().to_string());
    }
    let data = ret.unwrap();
    let upload_file = format!("{}/{}", config.system.uploaddir, filename);
    info!("report file is:{}", upload_file);
    let ret = File::create(&upload_file);
    if ret.as_ref().is_err() {
      return response_json_error(&ret.err().unwrap().to_string());
    }
    let mut f = ret.unwrap();
    let ret = f.write_all(&data);
    if ret.as_ref().is_err() {
      return response_json_error(&ret.err().unwrap().to_string());
    }
    let ret = f.sync_all();
    if ret.as_ref().is_err() {
      return response_json_error(&ret.err().unwrap().to_string());
    }

    //同步文件和内容到后台
    // let userinfo = TjStaffadmin {
    //   id: 1,
    //   tj_staffno: "admin".to_owned(),
    //   tj_staffname: "admin".to_owned(),
    //   ..Default::default()
    // };
    info!("start to upload corp report......");
    let rptids: Vec<String> = vec![rptid];
    let ret = MedexamSyncSvc::upload_corp_report(&rptids, &upload_file, &config.system.orgid.to_string(), &config.system.syncserver, &db).await;
    // let ret = MedexamSyncSvc::upload_medresults_with_report(&userinfo, &testid, &upload_file, &config.system.orgid.to_string(), &config.system.syncserver, &dictsvc, &db).await;
    if ret.as_ref().is_err() {
      return response_json_error(&ret.err().unwrap().to_string());
    }
    let res = ResponseBody::new(HttpCode::OK as i32, "OK", "");
    Json(serde_json::json!(res))
  } else {
    return response_json_error("multipart error");
  }
}

pub async fn sync_corpinfo() -> Json<Value> {
  info!("start to sync medinfos from saas server......");
  Json(serde_json::json!("sync_corpinfo"))
}

pub async fn sync_corpstatus() -> Json<Value> {
  info!("start to sync mestatsu from saas server......");
  Json(serde_json::json!("sync_corpstatus"))
}
