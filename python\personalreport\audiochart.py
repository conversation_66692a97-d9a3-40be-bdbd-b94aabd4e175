
# import sys, os
# from sys import platform
# if sys.platform == "linux" or sys.platform == "linux2":
#     sys.path.insert(0, os.path.join(os.path.abspath(sys.path[0]), ".", "llib"))
# else:
# sys.path.insert(0, os.path.join(os.path.abspath(sys.path[0]), ".", "lib"))

from pychartdir import *

import constant

setLicenseCode("RDST-34YX-GU2L-QB5X-CF82-A2C4")
class AudioChart:
    def __init__(self,rets) -> None:
        self.details = rets

    def drawchart(self,ear):
        title = "右耳"
        if ear == constant.Ear.Left.value:
            title = "左耳"
        if len(self.details) <= 0:
            testid = "empty"
        else:
            testid = self.details[0].tj_testid 
        # setLicenseCode("")
        c = XYChart(800, 600)

        self.details.sort(key=lambda x:x.tj_freq)
        # print("New details are: ",self.details.__dicts__)

        datax = [500, 1000, 2000, 3000, 4000, 6000]
        dataYA = []
        PointYA = []
        dataYB = []
        PointYB = []
        for freq in datax:
            air = next((
                dt
                for dt in self.details
                if dt.tj_freq == freq and dt.tj_ear == ear and dt.tj_adtype == constant.Trans.Air.value),None
            )
            if air is not None:
                dataYA.append(air.tj_result)
                PointYA.append(air.tj_cover)
            else:
                dataYA.append(NoValue)
                PointYA.append(NoValue)
            bone = next((
                dt
                for dt in self.details
                if dt.tj_freq == freq and dt.tj_ear == ear and dt.tj_adtype == constant.Trans.Bone.value),None
            )
            if bone is not None:
                dataYB.append(bone.tj_result)
                PointYB.append(bone.tj_cover)
            else:
                dataYB.append(NoValue)
                PointYB.append(NoValue)
        # for freq in datax:
        #     ret = next((
        #         dt
        #         for dt in self.details
        #         if dt.tj_freq == freq and dt.tj_ear == ear),None
        #     )
        #     # print("Result is:",ret.__dict__)
        #     if ret is None:
        #         dataYA.append(NoValue)
        #         PointYA.append(NoValue)
        #         dataYB.append(NoValue)
        #         PointYB.append(NoValue)
        #     else:
        #         if ret.tj_adtype == constant.Trans.Air.value:
        #             dataYA.append(ret.tj_result)
        #             PointYA.append(ret.tj_cover)
        #         else:
        #             dataYB.append(ret.tj_result)
        #             PointYB.append(ret.tj_cover)


        c.setXAxisOnTop()
        # Add a title to the chart using 18pt Times Bold Italic font
        c.addTitle(title,"simsun.ttc", 14)

        # Set the plotarea at (50, 55) and of 500 x 280 pixels in size. Use a vertical gradient color from
        # light blue (f9f9ff) to sky blue (aaccff) as background. Set border to transparent and grid lines
        # to white (ffffff).
        c.setPlotArea(50, 65, 700, 500, 0xFFFFFF, -1, Transparent, 0xCCCCCC, 0xCCCCCC)

        # Add a legend box at (50, 28) using horizontal layout. Use 10pt Arial Bold as font, with
        # transparent background.
        legendbox = c.addLegend(c.getWidth() / 2, c.getHeight() + 5, 0, "simsun.ttc", 10)
        legendbox.setBackground(Transparent)
        legendbox.setAlignment(BottomCenter)

        # Set the x axis labels
        # c.xAxis().setLabels(labels)
        c.xAxis().setLinearScale(0, 6000, 1000, 1000)
        # Set axis label style to 8pt Arial Bold
        # c.xAxis().setLabelStyle("Arial Bold", 8)
        c.xAxis().setLabelStep(1, 1)
        c.xAxis().setTitle("频率 (Hz)", "simsun.ttc",  10)

        # Set y-axis tick density to 30 pixels. ChartDirector auto-scaling will use this as the guideline
        # when putting ticks on the y-axis.
        c.yAxis().setTickDensity(20)
        c.yAxis().setReverse()
        c.yAxis().setLinearScale(-10, 100, 10, 10)
        c.xAxis().addMark(500, 0xC0C0C0, "")
        c.yAxis().setTitle("分贝 (db)", "simsun.ttc",  10)

        # Set axis line width to 2 pixels
        c.xAxis().setWidth(2)
        c.yAxis().setWidth(2)

        # layer1 = c.addScatterLayer(datax, dataYA, "气导",self.GetDataSymbol(ear,1),10,0xffffff)
        #气导
        c.addScatterLayer(datax, ArrayMath(dataYA).selectEQZ(ArrayMath(PointYA).result(),NoValue).result(), "气导",self.GetDataSymbol(ear,0),10,0xffffff)
        c.addScatterLayer(datax, ArrayMath(dataYA).selectEQZ(ArrayMath(PointYA).sub(1).result(),NoValue).result(), "气导掩蔽",self.GetDataSymbol(ear,1),10,0xffffff)
        # layer1.getDataSet(0).setDataSymbol(GetCustomDataSymbol(0))
        
        # layer2 = c.addScatterLayer(datax, dataYB, "骨导")
        layerbone1 = c.addScatterLayer(datax, ArrayMath(dataYB).selectEQZ(ArrayMath(PointYB).result(),NoValue).result(), "骨导",SquareShape,10,0xffffff)
        layerbone1.getDataSet(0).setDataSymbol(self.GetCustomDataSymbol(ear,0))

        layerbone2 = c.addScatterLayer(datax, ArrayMath(dataYB).selectEQZ(ArrayMath(PointYB).sub(1).result(),NoValue).result(), "骨导掩蔽",SquareShape,10,0xffffff)
        layerbone2.getDataSet(0).setDataSymbol(self.GetCustomDataSymbol(ear,1))
       
        # Add a line layer to the chart
        layerYA = c.addLineLayer(dataYA,0x3333ff)
        # Set the line width to 3 pixels
        layerYA.setLineWidth(2)
        layerYA.setXData(datax)

        # Add a line layer to the chart
        layerYB = c.addLineLayer(dataYB,0xff3333)
        # Set the line width to 3 pixels
        layerYB.setLineWidth(2)
        layerYB.setXData(datax)
        # Output the chart
        charname = "./audiochart/"+testid+"_"+str(ear)+".png"
        c.makeChart(charname)        
        return charname
    
    def GetDataSymbol(self, ear, iscover):
        if ear == constant.Ear.Left.value:
            if iscover == constant.YesOrNo.No.value:
                return Cross2Shape(0.1)
            else:
                return SquareShape
        else:
            if iscover == constant.YesOrNo.No.value:
                return CircleSymbol
            else:
                return TriangleShape
            
    def GetCustomDataSymbol(self, ear, iscover):
        symbols = ["audiogram/rightbone.png" ,"audiogram/leftbone.png", "audiogram/rbcover.png" ,"audiogram/lbcover.png" ]
        if ear == constant.Ear.Left.value:
            if iscover == constant.YesOrNo.No.value:
                return symbols[1]
            else:
                return symbols[3]
        else:
            if iscover == constant.YesOrNo.No.value:
                return symbols[0]
            else:
                return symbols[2]