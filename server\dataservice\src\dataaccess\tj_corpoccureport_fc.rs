use crate::entities::{prelude::*, tj_corpoccureport_fc};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter};

impl TjCorpoccureportFc {
  pub async fn query(newtestid: &str, db: &DatabaseConnection) -> Result<Option<TjCorpoccureportFc>> {
    let ret = TjCorpoccureportFcEntity::find().filter(tj_corpoccureport_fc::Column::TjNewTestid.eq(newtestid)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(code: &str, db: &DatabaseConnection) -> Result<Vec<TjCorpoccureportFc>> {
    let ret = TjCorpoccureportFcEntity::find().filter(tj_corpoccureport_fc::Column::TjReportId.eq(code)).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
