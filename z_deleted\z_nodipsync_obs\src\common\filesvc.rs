// use std::{fs::File, io::Write, sync::Arc};

use anyhow::{anyhow, Result};
use axum::{body::StreamBody, extract::Multipart};
use headers::HeaderMap;
use hyper::header;
use tokio::{fs::File, io::AsyncWriteExt};
use tokio_util::io::ReaderStream;
pub struct FileSvc;

impl FileSvc {
  pub async fn do_upload(newname: &str, directory: &str, mut multipart: Multipart) -> Result<(String, String)> {
    let mut new_filename = "".to_string();
    let mut field_name = "".to_string();
    while let Some(field) = multipart.next_field().await.unwrap() {
      info!("Field file name:{:?}", &field.file_name());
      info!("field name: {:?}", &field.name());

      let fname = field.name();
      if fname.is_some() {
        field_name = fname.unwrap().to_string();
      }
      let filename = field.file_name();
      if filename.is_none() {
        return Err(anyhow!("file name is empty"));
      }
      let filename = filename.unwrap().to_owned();
      let filenames = filename.split(".").collect::<Vec<&str>>();
      if filenames.len() != 2 {
        return Err(anyhow!("file name error"));
      }

      let orifilename = filenames[0];
      let extension = filenames.last().unwrap();
      info!("file extension is:{}", extension);

      let mut newname = newname.to_owned();
      if newname.is_empty() {
        // if field.name().is_some() {
        //   newname = field.name().unwrap().to_string();
        // } else {
        newname = orifilename.to_string();
        // }
      }

      let ret = field.bytes().await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      }
      let data = ret.unwrap();

      new_filename = format!("{}/{}.{}", directory, newname, extension);
      let ret = File::create(&new_filename).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      }
      let mut f = ret.unwrap();
      let ret = f.write_all(&data).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      }
      let ret = f.sync_all().await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      }
    }
    Ok((field_name, new_filename))
  }

  pub async fn do_download(filename: &str) -> Result<(HeaderMap, StreamBody<ReaderStream<File>>)> {
    let file = match tokio::fs::File::open(&filename).await {
      Ok(file) => file,
      Err(err) => {
        error!("{} not found", &filename);
        return Err(anyhow!("{} not found", filename));
      } // Err(err) => return response_json_error("can't find file specified"),
    };
    // convert the `AsyncRead` into a `Stream`
    let stream = ReaderStream::new(file);
    // convert the `Stream` into an `axum::body::HttpBody`
    let body = StreamBody::new(stream);

    let mut headers = HeaderMap::new();
    headers.insert("Content-Type", "image/jpeg".parse().unwrap());
    let file_onely_name = filename.split('/').last();
    info!("file only name is:{:?}", &file_onely_name);
    let mut file_only_name = "";
    if file_onely_name.is_some() {
      file_only_name = file_onely_name.unwrap();
    }
    headers.insert(header::CONTENT_DISPOSITION, format!("attachment; filename=\"{}\"", file_only_name).parse().unwrap());
    // headers.insert(header::CONTENT_DISPOSITION, "attachment;filename=\"report.pdf\"".parse().unwrap());

    Ok((headers, body))
  }
}
