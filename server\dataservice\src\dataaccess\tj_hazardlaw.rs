use crate::entities::{prelude::*, tj_hazardlaw};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set, TransactionTrait};
use serde_json::json;

impl TjHazardlaw {
  pub async fn query_many_by_hdid(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<Vec<TjHazardlaw>> {
    let mut conditions = Condition::all();

    if ids.len() > 0 {
      conditions = conditions.add(tj_hazardlaw::Column::TjHdid.is_in(ids.to_owned()));
    }

    let ret = TjHazardlawEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjHazardlaw, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_hazardlaw::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn save_many(infos: &Vec<TjHazardlaw>, db: &DatabaseConnection) -> Result<i64> {
    if infos.len() <= 0 {
      // return Err(anyhow!("empty data"));
      return Ok(0);
    }
    let total = infos.len();
    let (insert_vals, update_vals): (Vec<TjHazardlaw>, Vec<TjHazardlaw>) = infos.to_owned().into_iter().partition(|f| f.id == 0);
    let txn = db.begin().await.expect("start transaction error");
    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_hazardlaw::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_hazardlaw::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjHazardlawEntity::insert_many(active_insert_values).exec(&txn).await;
      if ret.as_ref().is_err() {
        let _ = txn.rollback();
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_packagedetail::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_hazardlaw::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(&txn).await;
        if ret.as_ref().is_err() {
          let _ = txn.rollback();
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    let ret = txn.commit().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(total as i64)
  }

  pub async fn delete_by_hdids(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjHazardlawEntity::delete_many()
      .filter(Condition::all().add(tj_hazardlaw::Column::TjHdid.is_in(ids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjHazardlawEntity::delete_many()
      .filter(Condition::all().add(tj_hazardlaw::Column::Id.is_in(ids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
  pub async fn clear(db: &DatabaseConnection) -> Result<()> {
    let ret = TjHazardlawEntity::delete_many().exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(())
  }
}
