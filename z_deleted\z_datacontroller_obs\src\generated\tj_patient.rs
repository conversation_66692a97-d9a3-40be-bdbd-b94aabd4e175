//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_patient")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    #[sea_orm(unique)]
    pub tj_pid: String,
    pub tj_pname: String,
    pub tj_psex: i32,
    pub tj_pmarriage: i32,
    pub tj_ptestnum: i32,
    pub tj_paddress: String,
    pub tj_pphone: String,
    pub tj_pemail: String,
    pub tj_pbirthday: String,
    pub tj_nation: String,
    pub tj_pidcard: String,
    pub tj_pcareer: String,
    pub tj_pmobile: String,
    pub tj_photo: String,
    pub tj_cryptflag: i8,
    pub tj_popdate: i64,
    pub tj_staffid: i32,
    pub tj_pmemo: String,
    pub tj_syncflag: i8,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
