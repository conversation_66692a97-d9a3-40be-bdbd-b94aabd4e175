//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "ext_checkiteminfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub testid: String,
  pub tid: String,
  pub testername: String,
  pub idcard: String,
  pub psex: i32,
  pub csex: String,
  pub birthdate: String,
  pub phone: String,
  pub age: i32,
  pub itemname: String,
  pub itemid: String,
  pub itemid2: String,
  pub deptid: String,
  pub deptname: String,
  pub depttype: i32,
  pub requesterid: String,
  pub requestername: String,
  pub requestdate: i32,
  pub requestdate2: String,
  pub paytype: i32,
  pub packagename: String,
  pub zdym: String,
  pub sampletype: String,
  pub corpnum: i32,
  pub syncstatus: i32,
  pub uid: i64,
  pub pacs: i32,
  pub lis: i32,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
