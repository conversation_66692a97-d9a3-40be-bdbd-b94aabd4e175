use anyhow::{anyhow, Result};
use rbatis::rbatis_codegen::IntoSql;
use rbatis::{crud, py_sql};
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjCheckiteminfo {
  pub id: i64,
  pub tj_testid: String,
  pub tj_itemid: String,
  pub tj_synid: String,
  pub tj_itemname: String,
  pub tj_result: String,
  pub tj_lowvalue: String,
  pub tj_uppervalue: String,
  pub tj_itemrange: String,
  pub tj_itemunit: String,
  pub tj_abnormalflag: i32,
  pub tj_barflag: i32,
  pub tj_abnormalshow: String,
  pub tj_combineflag: i32,
  pub tj_deptorder: i32,
  pub tj_showorder: i32,
  pub tj_combineorder: i32,
  pub tj_deptid: String,
  pub tj_barnum: String,
  pub tj_barcode: String,
  pub tj_checkdate: i64,
  pub tj_checkdoctor: String,
  pub tj_recheckdoctor: String,
  pub tj_recheckdate: i64,
  // pub shengqingzt: i32,
}
crud!(TjCheckiteminfo {}, "tj_checkiteminfo");
rbatis::impl_select!(TjCheckiteminfo{query(testid:&str) => "`where tj_testid = #{testid} `"});
rbatis::impl_update!(TjCheckiteminfo{update(id:&i64) => "`where id = #{id} `"});
// rbatis::impl_select!(TjCheckiteminfo{query_many(testis:&[&str]) => "`where tj_testid in ${testids.sql()} `"});
// rbatis::impl_delete!(TjCheckiteminfo{delete_many(testid:&str, synids:&[&str]) => "`where tj_testid = #{testid} and tj_synid in ${synids.sql()} `"});
rbatis::impl_delete!(TjCheckiteminfo{delete_many(testid:&str, synids:&[&str]) =>
  "`where id > 0 `
  if testid != '':
    ` and tj_testid = #{testid} `
  if !synids.is_empty():
    ` and tj_synid in ${synids.sql()} `"});
rbatis::impl_delete!(TjCheckiteminfo{delete(testid:&str) => "`where tj_testid = #{testid} `"});

impl TjCheckiteminfo {
  pub fn new(testid: &str) -> Self {
    TjCheckiteminfo {
      tj_testid: testid.to_string(),
      ..Default::default()
    }
  }

  #[py_sql(
    "`select * from tj_checkiteminfo where id > 0 `
                  if flag > 0:
                    ` and tj_abnormalflag = #{flag} `
                  if combined > 0:
                    ` and tj_combineflag = #{combined} `
                  if !testids.is_empty():
                    ` and tj_testid in ${testids.sql()} `         
                  if !deptids.is_empty():
                    ` and tj_deptid in ${deptids.sql()} `              
                  if !syncids.is_empty():
                    ` and tj_synid in ${syncids.sql()} `
                  "
  )]
  pub async fn query_many(rb: &mut rbatis::RBatis, testids: &[&str], deptids: &[&str], flag: i32, combined: i32, syncids: &[&str]) -> Result<Vec<TjCheckiteminfo>, rbatis::Error> {
    impled!()
  }
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<TjCheckiteminfo>) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<TjCheckiteminfo>, Vec<TjCheckiteminfo>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    info!("需要新增的检查项目数量:{},需要修改的数量:{}", &insert_vals.len(), &update_vals.len());
    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = TjCheckiteminfo::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = TjCheckiteminfo::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }
}
