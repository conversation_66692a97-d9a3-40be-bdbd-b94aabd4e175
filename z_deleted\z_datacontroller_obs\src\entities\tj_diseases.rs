use serde::{Deserialize, Serialize};
#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_diseases")]
#[derive(<PERSON><PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct TjDiseases {
  pub id: i64,
  pub tj_discode: String,
  pub tj_disname: String,
  pub tj_disnum: String,
  pub tj_deptid: i32,
  pub tj_type: String,
  pub tj_content: String,
  pub tj_occudiseaseflag: i32,
  pub tj_startage: i32,
  pub tj_endage: i32,
  pub tj_sex: i32,
  pub tj_career: String,
  pub tj_marriage: i32,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_typeid: i32,
  pub tj_opinion: String,
}
