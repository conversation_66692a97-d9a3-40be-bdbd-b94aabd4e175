use crate::entities::{prelude::*, tj_hazarditem};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;
use tracing::*;

impl TjHazarditem {
  pub async fn query_many(ids: &Vec<i64>, testtypes: &Vec<i32>, db: &DatabaseConnection) -> Result<Vec<TjHazarditem>> {
    let mut conditions = Condition::all().add(tj_hazarditem::Column::Id.gt(0));
    if ids.len() > 0 {
      conditions = conditions.add(tj_hazarditem::Column::TjHid.is_in(ids.to_owned()))
    }
    if testtypes.len() > 0 {
      conditions = conditions.add(tj_hazarditem::Column::TjTesttype.is_in(testtypes.to_owned()))
    }
    let ret = TjHazarditemEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save(info: &TjHazarditem, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_hazarditem::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    Ok(val_id.unwrap())
  }

  pub async fn save_many(info: &Vec<TjHazarditem>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = info.len();
    let (insert_vals, update_vals): (Vec<TjHazarditem>, Vec<TjHazarditem>) = info.to_owned().into_iter().partition(|f| f.id == 0);
    // info!("insert_vals:{:?}", &insert_vals);
    // info!("update_vals:{:?}", &update_vals);

    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_hazarditem::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_hazarditem::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjHazarditemEntity::insert_many(active_insert_values).exec(db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_hazarditem::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(db).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          // return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    Ok(total as i64)
  }

  pub async fn delete_by_hid(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let conditions = Condition::all().add(tj_hazarditem::Column::TjHid.is_in(ids.to_owned()));
    let ret = TjHazarditemEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let conditions = Condition::all().add(tj_hazarditem::Column::Id.is_in(ids.to_owned()));
    let ret = TjHazarditemEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
