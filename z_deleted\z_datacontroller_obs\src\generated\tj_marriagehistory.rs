//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_marriagehistory")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub tj_testid: String,
    pub tj_date: i32,
    pub tj_spouseradiation: String,
    pub tj_spouseoccu: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
