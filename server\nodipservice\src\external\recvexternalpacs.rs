use std::sync::Arc;

use anyhow::{anyhow, Result};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tiberius::{Client, Config};
use tokio::{net::TcpStream, sync::RwLock};
use tokio_util::compat::{Compat, TokioAsyncWriteCompatExt};
use tracing::*;

// use crate::config::settings::Extpacs;
// "server=tcp:localhost,1433;IntegratedSecurity=true;TrustServerCertificate=true".to_owned()
#[derive(Default, Debug)]
pub struct RecvExternalPacs {
  pacskey: String,
  dbtype: i32,
  // userame: String,
  // password: String,
  // uri: String,
  oracle_conn: Option<oracle::Connection>,
  mssql_conn: Option<Arc<RwLock<tiberius::Client<Compat<TcpStream>>>>>,
  mysql_conn: Option<sqlx::MySqlPool>,
}

impl RecvExternalPacs {
  pub async fn new(pacskey: &str, username: &str, password: &str, uri: &str, dbtype: i32) -> Self {
    let mut externalpacs = RecvExternalPacs {
      pacskey: pacskey.to_string(),
      dbtype,
      // userame: username.to_string(),
      // password: password.to_string(),
      // uri: uri.to_string(),
      ..Default::default()
    };
    match dbtype {
      x if x == crate::common::constant::DatabaseType::MsSql as i32 => {
        info!("start to create pacs external ms sqlserver connection");
        let config = Config::from_ado_string(uri).expect("ado_string error");
        let tcp = TcpStream::connect(config.get_addr()).await.expect("connect error");
        tcp.set_nodelay(true).expect("set no delay error");
        let client = Client::connect(config, tcp.compat_write()).await.expect("connection error");
        externalpacs.mssql_conn = Some(Arc::new(RwLock::new(client)));
        info!("external pacs ms sqlserver connection created successfully");
      }
      x if x == crate::common::constant::DatabaseType::Oracle as i32 => {
        info!("start to create external pacs oracle connection");
        let oracle_conn = oracle::Connection::connect(username, password, uri).expect("init oracle connection error");
        externalpacs.oracle_conn = Some(oracle_conn);
        info!("external oracle pacs connection created successfully");
      }
      x if x == crate::common::constant::DatabaseType::MySql as i32 => {
        info!("start to create external pacs mysql connection");
        let conn = sqlx::MySqlPool::connect(uri).await.expect("connect mysql error");
        externalpacs.mysql_conn = Some(conn);
        info!("external pacs mysql connection created successfully");
      }
      _ => {
        // info!("not supported database type......");
      }
    };
    info!("current extpacs is:{:?}", &externalpacs);
    externalpacs
  }

  pub fn get_pacskey(&self) -> String {
    self.pacskey.to_string()
  }

  pub async fn query_from_external_pacs(&self, tjbh: &str, hassfyc: i32, version: i32, db: &DbConnection) -> Result<Vec<VTjPacsresult>> {
    let mut result: Vec<VTjPacsresult> = vec![];
    if self.dbtype == crate::common::constant::DatabaseType::MsSql as i32 {
      if self.mssql_conn.is_none() {
        return Err(anyhow!("mssql 无链接？？？？？？"));
      }

      let mssql_conn = self.mssql_conn.as_ref().unwrap(); //.to_owned();
      let mut mssql_conn_wr = mssql_conn.write().await;
      let ret = super::recv::recvmssql::RecvMssql::query_from_external_pacsresult(&mut mssql_conn_wr, tjbh, hassfyc, version).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      result = ret.unwrap();
    };
    if self.dbtype == crate::common::constant::DatabaseType::Oracle as i32 {
      if self.oracle_conn.is_none() {
        return Err(anyhow!("Oracle 无链接？？？？？？"));
      }
      //get from oracle
      let oracle_conn = self.oracle_conn.as_ref().unwrap();
      let ret = super::recv::recvoracle::RecvOracle::query_from_external_pacsresult(oracle_conn, tjbh, hassfyc, version).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      result = ret.unwrap();
    }
    if self.dbtype == crate::common::constant::DatabaseType::MySql as i32 {
      if self.mysql_conn.is_none() {
        return Err(anyhow!("mysql not connected"));
      }
      let mysql_conn = self.mysql_conn.as_ref().unwrap();
      let ret = super::recv::recvmysql::RecvMysql::query_from_external_pacsresult(mysql_conn, tjbh, hassfyc, version).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      result = ret.unwrap();
      //get from mysql
    }
    if result.len() <= 0 {
      return Err(anyhow!("pacs系统没有结果数据"));
    }

    let xmxhs: Vec<String> = result.iter().map(|v| v.jclx.to_string()).collect();
    let ret = VTjPacsresult::delete_many_by_jclx(&vec![tjbh.to_string()], &xmxhs, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }
    let ret = VTjPacsresult::save_many(&result, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }

    Ok(result)
  }

  pub async fn query_lis_xmxx(&self) -> Result<Vec<VLisXmxx>> {
    let mut result: Vec<VLisXmxx> = vec![];
    if self.dbtype == crate::common::constant::DatabaseType::MsSql as i32 {
      if self.mssql_conn.is_none() {
        return Err(anyhow!("mssql 无链接？？？？？？"));
      }

      let mssql_conn = self.mssql_conn.as_ref().unwrap().to_owned();
      let mut mssql_conn_wr = mssql_conn.write().await;
      let ret = super::recv::recvmssql::RecvMssql::query_lis_xmxx(&mut mssql_conn_wr).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      result = ret.unwrap();
    };
    if self.dbtype == crate::common::constant::DatabaseType::Oracle as i32 {
      if self.oracle_conn.is_none() {
        return Err(anyhow!("Oracle 无链接？？？？？？"));
      }
      //get from oracle
      let oracle_conn = self.oracle_conn.as_ref().unwrap();
      let ret = super::recv::recvoracle::RecvOracle::query_lis_xmxx(oracle_conn).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      result = ret.unwrap();
    }
    if self.dbtype == crate::common::constant::DatabaseType::MySql as i32 {
      if self.mysql_conn.is_none() {
        return Err(anyhow!("mysql not connected"));
      }
      let mysql_conn = self.mysql_conn.as_ref().unwrap();
      let ret = super::recv::recvmysql::RecvMysql::query_lis_xmxx(mysql_conn).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      result = ret.unwrap();
      //get from mysql
    }
    Ok(result)
  }

  pub async fn release(&self) {
    if self.oracle_conn.is_some() {
      let _ = self.oracle_conn.as_ref().unwrap().close();
    }
    if self.mssql_conn.is_some() {
      // let client = self.mssql_conn.as_ref().unwrap().to_owned();
      // let rd = client.as_ref().read().await;
      // let _ = rd.try_into().clone().clonse.await;
    }
    if let Some(mysql_conn) = self.mysql_conn.as_ref() {
      let _ = mysql_conn.close().await;
    }
  }
}
