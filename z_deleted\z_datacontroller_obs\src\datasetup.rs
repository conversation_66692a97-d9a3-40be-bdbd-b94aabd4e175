use rbatis::{core::db::DBPoolOptions, db::DBExecR<PERSON>ult, rbatis::<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};

#[derive(Debug)]
pub struct DbConnection {
  dbconn: Rbat<PERSON>,
}

impl DbConnection {
  pub async fn new(uri: &str) -> Self {
    let mut opt = DBPoolOptions::new();
    opt.max_connections = 200;
    opt.connect_timeout = std::time::Duration::from_secs(60);
    opt.idle_timeout = Some(std::time::Duration::from_secs(28800));

    let dbconn = Rbatis::new();
    // rbconn.set_log_plugin(arg)
    dbconn.link_opt(uri, opt).await.unwrap();

    DbConnection { dbconn }
  }

  pub fn get_connection(&self) -> &Rbatis {
    &self.dbconn
  }

  pub async fn execute_sql(&self, sqlstr: &str) -> Result<DBExecResult, Error> {
    let ret = self.dbconn.exec(&sqlstr, vec![]).await;
    info!("execute sql ret:{:?}, sql:{}", ret, sqlstr);
    ret
  }
}
