//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "ss_dictionary")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub ss_typeid: i32,
  pub ss_pid: i32,
  pub ss_short: String,
  pub ss_name: String,
  pub ss_showorder: i32,
  pub ss_memo: String,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
