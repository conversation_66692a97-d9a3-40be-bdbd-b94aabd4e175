use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok, DataBody, ResponseBody},
  auth::auth::Claims,
  common::constant::{self},
  config::settings::Settings,
};
use tracing::*;
use axum::{Extension, Json};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{basic::akasvc::AkaSvc, basic::orgsvc::OrgSvc, dto::*};
use serde_json::Value;
use std::sync::Arc;

pub async fn query_autodiagconditions(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntsDto>) -> Json<Value> {
  // let results = crate::SYSCACHE.get().unwrap().get_autodiag_conditions(&dto.keys, &db).await;
  // response_json_value_ok(results.len() as u64, results)

  let ret = AkaSvc::query_autodiagconditions(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjAutodiagcondition>::new());
  }
  let results = ret.unwrap();
  let databody = DataBody::new(results.len() as u64, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn save_autodiagconditions(
  Extension(db): Extension<Arc<DbConnection>>,
  _tx: Extension<Arc<tokio::sync::mpsc::UnboundedSender<i32>>>,
  Json(dto): Json<TjAutodiagcondition>,
) -> Json<Value> {
  let ret = AkaSvc::save_autodiagconditions(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  // let _ = tx.send(nodipservice::common::constant::CacheType::AutoDiag as i32);

  response_json_value_ok(1, results)
}

pub async fn delete_autodiagconditions(
  Extension(db): Extension<Arc<DbConnection>>,
  _tx: Extension<Arc<tokio::sync::mpsc::UnboundedSender<i32>>>,
  Json(dto): Json<TjAutodiagcondition>,
) -> Json<Value> {
  info!("start to delete auto diag conditions:{:?}", &dto);
  if dto.id <= 0 {
    return response_json_value_error("id <=0 not allowed", 0);
  }
  let ret = AkaSvc::delete_autodiagconditions(&vec![dto.id], &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  // let _ = tx.send(nodipservice::common::constant::CacheType::AutoDiag as i32);

  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn query_audiogramrevises(Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  let ret = AkaSvc::query_audiogramrevises(&db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjAudiogramrevise>::new());
  }
  let results = ret.unwrap();
  let databody = DataBody::new(results.len() as u64, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}
pub async fn query_departments(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeysDto>) -> Json<Value> {
  let ret = OrgSvc::query_departments(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjDepartinfo>::new());
  }
  let results = ret.unwrap();
  let databody = DataBody::new(results.len() as u64, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn save_departments(Extension(db): Extension<Arc<DbConnection>>, _tx: Extension<Arc<tokio::sync::mpsc::UnboundedSender<i32>>>, Json(dto): Json<TjDepartinfo>) -> Json<Value> {
  let ret = OrgSvc::save_departments(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  // let _ = tx.send(nodipservice::common::constant::CacheType::Dept as i32);
  response_json_value_ok(1, results)
}

pub async fn delete_departments(Extension(db): Extension<Arc<DbConnection>>, _tx: Extension<Arc<tokio::sync::mpsc::UnboundedSender<i32>>>, Json(dto): Json<TjDepartinfo>) -> Json<Value> {
  let ret = OrgSvc::delete_departments(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  // let _ = tx.send(nodipservice::common::constant::CacheType::Dept as i32);
  response_json_value_ok(1, results)
}

pub async fn query_deptitems(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeyDto>) -> Json<Value> {
  let ret = OrgSvc::query_deptitems(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjDeptitem>::new());
  }

  let results = ret.unwrap();
  let databody = DataBody::new(results.len() as u64, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn save_deptitems(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<Vec<TjDeptitem>>) -> Json<Value> {
  info!("save dept items dto:{:?}", &dto);
  let ret = OrgSvc::save_deptitems(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn delete_deptitem(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjDeptitem>) -> Json<Value> {
  info!("delete dept items dto:{:?}", &dto);
  let ret = OrgSvc::delete_deptitem(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn query_areainfos(Extension(db): Extension<Arc<DbConnection>>, Extension(config): Extension<Arc<Settings>>, Json(dto): Json<KeysDto>) -> Json<Value> {
  let areacodes = dto.keys.to_owned();
  let ret = AkaSvc::query_areainfos(&config.application.areaprovince, &areacodes, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<SsArea>::new());
  }

  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn query_monitorinfos(Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  let ret = AkaSvc::query_monitorinfos(&db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<SsArea>::new());
  }

  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn query_external_configs(Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  let ret = AkaSvc::query_external_configs(&db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<SsArea>::new());
  }

  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn query_diseases(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MulKeysDto>) -> Json<Value> {
  let disids = dto.keyi1.to_owned();
  let deptids: Vec<i64> = match dto.keyi2 {
    Some(v) => v.to_owned(),
    None => vec![],
  };
  let typeids: Vec<i64> = match dto.keyi3 {
    Some(v) => v.to_owned(),
    None => vec![],
  };
  let ret = AkaSvc::query_diseases(&disids, &deptids, &typeids, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjDiseases>::new());
  }

  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

//
pub async fn query_occuconditions(Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  let ret = AkaSvc::query_occuconditions(&db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjOccucondition>::new());
  }

  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_occuconditions(Extension(db): Extension<Arc<DbConnection>>, _tx: Extension<Arc<tokio::sync::mpsc::UnboundedSender<i32>>>, Json(dto): Json<TjOccucondition>) -> Json<Value> {
  let ret = AkaSvc::save_occuconditions(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  // let _ = tx.send(nodipservice::common::constant::CacheType::OccuConditions as i32);
  response_json_value_ok(1, results)
}

pub async fn save_diseases(Extension(db): Extension<Arc<DbConnection>>, _tx: Extension<Arc<tokio::sync::mpsc::UnboundedSender<i32>>>, Json(dto): Json<TjDiseases>) -> Json<Value> {
  let ret = AkaSvc::save_diseases(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  // let _ = tx.send(nodipservice::common::constant::CacheType::Diseases as i32);
  response_json_value_ok(1, results)
}

pub async fn delete_diseases(Extension(db): Extension<Arc<DbConnection>>, _tx: Extension<Arc<tokio::sync::mpsc::UnboundedSender<i32>>>, Json(dto): Json<TjDiseases>) -> Json<Value> {
  info!("delete disease dto:{dto:?}");
  let ret = AkaSvc::delete_diseases(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  // let _ = tx.send(nodipservice::common::constant::CacheType::Diseases as i32);
  response_json_value_ok(1, results)
}

pub async fn query_staffs(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeysDto>) -> Json<Value> {
  let ret = OrgSvc::query_staffs(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjStaffadmin>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_staff(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjStaffadmin>) -> Json<Value> {
  let ret = OrgSvc::save_staff(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn delete_staff(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjStaffadmin>) -> Json<Value> {
  if dto.id <= 0 {
    return response_json_value_error("id <= 0,not allowed", 0);
  }
  let ret = OrgSvc::delete_staff(dto.id, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1, dto.id)
}

pub async fn query_staff_depts(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntDto>) -> Json<Value> {
  let ret = OrgSvc::query_staff_depts(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjStaffdept>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_staff_depts(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjStaffdept>) -> Json<Value> {
  let ret = OrgSvc::save_staff_depts(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjStaffdept>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn delete_staff_dept(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjStaffdept>) -> Json<Value> {
  info!("start to delete staff dept info:{:?}", &dto);
  let ret = OrgSvc::delete_staff_dept(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  // let results = ret.unwrap();
  response_json_value_ok(1, 1)
}

pub async fn query_staff_rights(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntDto>) -> Json<Value> {
  let ret = OrgSvc::query_staffrights(dto.key, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjStaffright>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_staff_rights(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<StaffRightsDto>) -> Json<Value> {
  info!("staff rights:{dto:#?}");
  let ret = OrgSvc::save_staffrights(&dto.staff, &dto.rights, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn query_guideinfos(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntsDto>) -> Json<Value> {
  let ret = AkaSvc::query_guideinfos(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjGuideinfo>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_guideinfo(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjGuideinfo>) -> Json<Value> {
  let ret = AkaSvc::save_guideinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn delete_guideinfo(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjGuideinfo>) -> Json<Value> {
  if dto.id <= 0 {
    return response_json_value_error("id <= 0,not allowed", 0);
  }
  let ret = AkaSvc::delete_guideinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  // let results = ret.unwrap();
  response_json_value_ok(1, dto.id)
}

pub async fn query_guide_items(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntDto>) -> Json<Value> {
  // info!("query guide items dto:{:#?}", &dto);
  let ret = AkaSvc::query_guide_items(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjGuideitem>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}
pub async fn save_guide_items(Extension(db): Extension<Arc<DbConnection>>, Json(dtos): Json<Vec<TjGuideitem>>) -> Json<Value> {
  //save_guide_items
  // info!("save guide items dto:{:?}",&dtos);
  let ret = AkaSvc::save_guide_items(&dtos, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  // let results = ret.unwrap();
  response_json_value_ok(1 as u64, ret.unwrap())
}
pub async fn delete_guide_items(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeysDto>) -> Json<Value> {
  info!("delete guide items dto:{:?}", &dto);
  let ret = AkaSvc::delete_guide_items(&dto.keys_int, &dto.keys_str, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1, 1)
}

pub async fn query_barnameinfos(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntsDto>) -> Json<Value> {
  let ret = AkaSvc::query_barinfos(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjBarnameinfo>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}
pub async fn save_barnameinfo(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjBarnameinfo>) -> Json<Value> {
  let ret = AkaSvc::save_barnameinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn delete_barnameinfo(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjBarnameinfo>) -> Json<Value> {
  let ret = AkaSvc::delete_barnameinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn query_bardetails(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeysDto>) -> Json<Value> {
  let ret = AkaSvc::query_bardetails(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjBardetail>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_bardetail(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjBardetail>) -> Json<Value> {
  let ret = AkaSvc::save_bardetail(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn delete_bardetail(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjBardetail>) -> Json<Value> {
  info!("DTO:{dto:?}");
  let ret = AkaSvc::delete_bardetail(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn query_baritems(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeysDto>) -> Json<Value> {
  let ret = AkaSvc::query_baritems(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjBaritems>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_baritems(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjBaritems>) -> Json<Value> {
  let ret = AkaSvc::save_baritem(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn delete_baritems(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjBaritems>) -> Json<Value> {
  let ret = AkaSvc::delete_baritem(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn query_groupinfos(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntDto>) -> Json<Value> {
  let ret = OrgSvc::query_groupinfos(dto.key, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjBarnameinfo>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}
pub async fn save_groupinfo(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjGroupinfo>) -> Json<Value> {
  let ret = OrgSvc::save_groupinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn delete_groupinfo(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjGroupinfo>) -> Json<Value> {
  let ret = OrgSvc::delete_groupinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn query_grouprights(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntDto>) -> Json<Value> {
  let ret = OrgSvc::query_grouprights(dto.key, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjGroupright>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_grouprights(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<GroupRightsDto>) -> Json<Value> {
  info!("group rights:{dto:#?}");
  let ret = OrgSvc::save_grouprights(dto.group.id, &dto.rights, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  // let results = ret.unwrap();
  response_json_value_ok(1, 1)
}

// ----------------------- law ------------------

pub async fn query_lawinfos(Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  let ret = AkaSvc::query_lawinfos(&db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjEvallaw>::new());
  }

  let results = ret.unwrap();
  let databody = DataBody::new(results.len() as u64, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn save_lawinfos(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjEvallaw>) -> Json<Value> {
  info!("save law info dto:{:?}", &dto);
  let ret = AkaSvc::save_lawinfo(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}

pub async fn delete_lawinfos(Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<Vec<TjEvallaw>>) -> Json<Value> {
  info!("delete law info dto:{:?}", &dto);
  let ret = AkaSvc::delete_lawinfos(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
