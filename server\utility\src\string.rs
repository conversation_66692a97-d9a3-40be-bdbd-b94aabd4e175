use anyhow::{anyhow, Result};
use regex::Regex;

pub fn split_string(split: &str, src: &String) -> Vec<String> {
  src.split(|c| split.contains(c)).filter(|&f| f != "").map(|s| s.to_string()).collect()
}

pub fn split_by_regex(src: &str) -> Result<Vec<String>> {
  let reg_str = format!(r"(([^，、\s|\(\)]+(\(.*?\))*)+)");
  let ret = Regex::new(&reg_str);
  // std::regex r{ R"(([^，、\(\)]+(\(.*?\))*)+)" };
  if ret.as_ref().is_err() {
    return Err(anyhow!("split error:{:?}", ret.as_ref().unwrap_err().to_string()));
  }
  let reg = ret.unwrap();
  let result = reg
    .find_iter(src)
    .map(|v| {
      // println!("V:{:?}", &v);
      // println!("String:{:?}", &v.as_str());
      // "a".to_string()
      v.as_str().to_string()
    })
    .collect::<Vec<String>>();
  // let result = reg.split(src).map(|v| v.to_string()).collect::<Vec<String>>();

  Ok(result)
}
