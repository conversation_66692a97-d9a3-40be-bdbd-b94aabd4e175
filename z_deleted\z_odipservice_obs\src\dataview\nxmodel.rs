use datacontroller::entities::{
  ss_dictionary::SsDictionary, tj_audiogramdetail::TjAudiogramdetail, tj_checkallnew::TjCheckallnew, tj_checkiteminfo::TjCheckiteminfo, tj_corpinfo::TjCorpinfo, tj_corpoccureport::TjCorpoccureport,
  tj_corpoccureport_info::TjCorpoccureportinfo, tj_departinfo::TjDepartinfo, tj_medexaminfo::TjMedexaminfo, tj_patient::TjPatient, tj_staffadmin::TjStaffadmin, tj_testsummary::TjTestsummary,
};
use serde::{Deserialize, Serialize};
use utility::timeutil::{self, current_timestamp};

#[derive(C<PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct NxWorkerinfo {
  pub id: i64,
  pub p_wkid: i64,
  pub wk_name: String,
  pub wk_sex: i32,
  pub wk_marriage: i32,
  pub wk_birthdate: String,
  pub wk_idcard: String,
  pub wk_phone: String,
  pub wk_address: String,
  pub wk_postcode: String,
  pub wk_workdate: String, //开始工作时间
  pub create_date: i64,
  pub creator: String,
  pub wx_openid: String,
  pub wx_bind: i32, //是否已经微信绑定 0：no， 1：yes
  pub wx_photo: String,
}
pub fn convert_from_tj_patientinfo(ptinfo: &TjPatient) -> NxWorkerinfo {
  NxWorkerinfo {
    id: 0,
    p_wkid: ptinfo.p_wkid,
    wk_name: ptinfo.tj_pname.to_owned(),
    wk_sex: ptinfo.tj_psex,
    wk_marriage: ptinfo.tj_pmarriage,
    wk_birthdate: ptinfo.tj_pbirthday.to_owned(),
    wk_idcard: ptinfo.tj_pidcard.to_owned(),
    wk_phone: ptinfo.tj_pphone.to_owned(),
    wk_address: ptinfo.tj_paddress.to_owned(),
    wk_postcode: "".to_string(),
    wk_workdate: "".to_string(),
    create_date: ptinfo.tj_popdate,
    creator: ptinfo.tj_staffid.to_string(),
    wx_openid: "".to_string(),
    wx_bind: 0,
    wx_photo: ptinfo.tj_photo.to_owned(),
  }
}

#[derive(Clone, Debug, Default, Serialize, Deserialize)]
pub struct NxMedexaminfo {
  pub id: i64,
  pub p_medid: i64,
  pub p_wkid: i64,
  pub p_jobid: i64,
  pub p_testid: String,
  pub p_testtype: i32, //体检类型
  pub p_meddate: String,
  pub p_jtid: i64,
  pub p_isrecheck: i32,
  pub p_wkyears: f64,
  pub p_wkmonthes: f64,
  pub p_hdyears: f64,
  pub p_hdmonthes: f64,
  pub p_status: i32,     //状态 -1：取消 1：预约 2：登记 3:体检中 4：结束
  pub p_resultid: i32,   //结果 -1：未知 0：正常 1：复查 2:职业禁忌证 3:疑似职业病 4：其他疾病或异常
  pub p_orgid: i64,      //体检机构
  pub p_cmpid: i64,      //所属企业
  pub p_rptfile: String, //报告文件
  pub p_hcard: String,   //健康证
  pub create_date: i64,
  pub creator: String,
}
pub fn convert_from_tj_medexaminfo(orgid: i64, mdinfo: &TjMedexaminfo, ptinfo: &TjPatient, ckall: &TjCheckallnew, corpinfo: &TjCorpinfo) -> NxMedexaminfo {
  NxMedexaminfo {
    id: 0,
    p_medid: mdinfo.p_medid,
    p_wkid: ptinfo.p_wkid,
    p_jobid: 0,
    p_testid: mdinfo.tj_testid.to_owned(),
    p_testtype: mdinfo.tj_testtype,
    p_meddate: timeutil::format_timestamp_date_only(mdinfo.tj_testdate),
    p_jtid: 0,
    p_isrecheck: mdinfo.tj_isrecheck,
    p_wkyears: 0.0,
    p_wkmonthes: 0.0,
    p_hdyears: 0.0,
    p_hdmonthes: 0.0,
    p_status: mdinfo.tj_checkstatus,
    p_resultid: ckall.tj_typeid,
    p_orgid: orgid,
    p_cmpid: corpinfo.p_cmpid,
    p_rptfile: "".to_string(),
    p_hcard: "".to_string(),
    create_date: current_timestamp(),
    creator: mdinfo.tj_recorder.to_string(),
  }
}

#[derive(Clone, Debug, Default, Serialize, Deserialize)]
pub struct NxMedTestsummary {
  pub id: i64,
  pub p_medid: i64,
  pub p_testid: String,
  pub p_deptid: String,
  pub p_deptname: String,
  pub p_testsummary: String,
  pub p_suggestion: String,
  pub p_orgid: i64,
  pub p_doctor: String,
  pub p_checkdate: String,
  pub p_order: i32,
}
pub fn convert_from_tj_testsummary(summary: &TjTestsummary, medinfo: &TjMedexaminfo, depts: &Vec<TjDepartinfo>, orgid: i64) -> NxMedTestsummary {
  // let deptinfo = depts.iter().find(|&p| p.tj_deptid.eq_ignore_ascii_case(&summary.tj_deptid)).unwrap_or_default();
  NxMedTestsummary {
    id: 0,
    p_medid: medinfo.p_medid,
    p_testid: medinfo.tj_testid.to_owned(),
    p_deptid: summary.tj_deptid.to_owned(),
    p_deptname: depts
      .iter()
      .find(|&p| summary.tj_deptid.eq_ignore_ascii_case(&p.tj_deptid))
      .map_or("".to_string(), |f| f.tj_deptname.to_owned()),
    p_testsummary: summary.tj_summary.to_owned(),
    p_suggestion: summary.tj_suggestion.to_owned(),
    p_orgid: orgid,
    p_doctor: summary.tj_doctor.to_owned(),
    p_checkdate: timeutil::format_timestamp(summary.tj_checkdate),
    p_order: depts.iter().find(|&p| summary.tj_deptid.eq_ignore_ascii_case(&p.tj_deptid)).map_or(0, |f| f.tj_showorder),
  }
}
#[derive(Clone, Debug, Default, Serialize, Deserialize)]
pub struct NxMedCheckall {
  pub id: i64,
  pub p_medid: i64,
  pub p_testid: String,
  pub p_resultid: i32,
  pub p_result: String,
  pub tj_hazard: String,
  pub tj_diseasename: String,
  pub p_ocuabnormal: String,
  pub p_othabnormal: String,
  pub p_ocuconclusion: String,
  pub p_othconclusion: String,
  pub p_ocusuggestion: String,
  pub p_othsuggestion: String,
  pub p_ocuopinion: String,
  pub p_othopinion: String,
  pub p_orgid: i64,
  pub p_doctor: String,
  pub p_rptfile: String,
  pub p_checkdate: String,
  pub create_date: i64,
}

pub fn convert_from_tj_checkallnew(checkall: &TjCheckallnew, medinfo: &TjMedexaminfo, dicts: &Vec<SsDictionary>, doctors: &Vec<TjStaffadmin>, orgid: i64, rptfile: &str) -> NxMedCheckall {
  NxMedCheckall {
    id: 0,
    p_medid: medinfo.p_medid,
    p_testid: medinfo.tj_testid.to_owned(),
    p_resultid: checkall.tj_typeid,
    p_result: dicts.iter().find(|&f| f.ss_pid == checkall.tj_typeid).map_or("".to_string(), |v| v.ss_name.to_owned()),
    tj_hazard: "".to_string(),
    tj_diseasename: checkall.tj_diseasename.to_owned(),
    p_ocuabnormal: checkall.tj_ocuabnormal.to_owned(),
    p_othabnormal: checkall.tj_othabnormal.to_owned(),
    p_ocuconclusion: checkall.tj_ocuconclusion.to_owned(),
    p_othconclusion: checkall.tj_othconclusion.to_owned(),
    p_ocusuggestion: checkall.tj_ocusuggestion.to_owned(),
    p_othsuggestion: checkall.tj_othsuggestion.to_owned(),
    p_ocuopinion: checkall.tj_ocuopinion.to_owned(),
    p_othopinion: checkall.tj_othopinion.to_owned(),
    p_orgid: orgid,
    p_rptfile: rptfile.to_owned(),
    p_doctor: doctors.iter().find(|&p| p.id == checkall.tj_staffid).map_or("".to_string(), |f| f.tj_staffname.to_owned()),
    p_checkdate: timeutil::format_timestamp_date_only(checkall.tj_checkdate),
    create_date: current_timestamp(),
  }
}

#[derive(Clone, Debug, Default, Serialize, Deserialize)]
pub struct NxAudiogramdetail {
  pub id: i64,
  pub p_medid: i64,
  pub tj_testid: String,
  pub tj_itemid: String,
  pub tj_freq: i32,
  pub tj_zeqd: i32,
  pub tj_zeqdj: i32,
  pub tj_zegd: i32,
  pub tj_zegdj: i32,
  pub tj_yeqd: i32,
  pub tj_yeqdj: i32,
  pub tj_yegd: i32,
  pub tj_yegdj: i32,
  pub p_orgid: i64,
}

pub fn convert_from_tj_audiogramdetails(audiogram: &TjAudiogramdetail, medinfo: &TjMedexaminfo, orgid: i64) -> NxAudiogramdetail {
  NxAudiogramdetail {
    id: 0,
    p_medid: medinfo.p_medid,
    tj_testid: medinfo.tj_testid.to_owned(),
    tj_itemid: audiogram.tj_itemid.to_owned(),
    tj_freq: audiogram.tj_freq,
    p_orgid: orgid,
    tj_zeqd: audiogram.tj_result,
    tj_zeqdj: audiogram.tj_revise,
    tj_zegd: audiogram.tj_result,
    tj_zegdj: audiogram.tj_revise,
    tj_yeqd: audiogram.tj_result,
    tj_yeqdj: audiogram.tj_revise,
    tj_yegd: audiogram.tj_result,
    tj_yegdj: audiogram.tj_revise,
  }
}

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct NxMedCheckresult {
  pub id: i64,
  pub p_medid: i64,
  pub p_testid: String,
  pub p_itemcode: String,
  pub p_syncode: String,
  pub p_combineflag: i32,
  pub p_deptid: String,
  pub p_deptname: String,
  pub p_itemname: String,
  pub p_result: String,
  pub p_range: String,
  pub p_unit: String,
  pub p_abnormal: i32,
  pub p_abnormalshow: String,
  pub p_showorder: i32,
  pub p_orgid: i64,
  pub p_doctor: String,
  pub p_recheckdoctor: String,
  pub p_checkdate: String,
}

pub fn convert_from_tj_checkiteminfo(iteminfo: &TjCheckiteminfo, medinfo: &TjMedexaminfo, depts: &Vec<TjDepartinfo>, orgid: i64) -> NxMedCheckresult {
  NxMedCheckresult {
    id: 0,
    p_medid: medinfo.p_medid,
    p_testid: medinfo.tj_testid.to_owned(),
    p_itemcode: iteminfo.tj_itemid.to_owned(),
    p_syncode: iteminfo.tj_synid.to_owned(),
    p_combineflag: iteminfo.tj_combineflag,
    p_deptid: iteminfo.tj_deptid.to_owned(),
    p_deptname: depts
      .iter()
      .find(|&p| iteminfo.tj_deptid.eq_ignore_ascii_case(&p.tj_deptid))
      .map_or("".to_string(), |f| f.tj_deptname.to_owned()),
    p_itemname: iteminfo.tj_itemname.to_owned(),
    p_result: iteminfo.tj_result.to_owned(),
    p_range: iteminfo.tj_itemrange.to_owned(),
    p_unit: iteminfo.tj_itemunit.to_owned(),
    p_abnormal: iteminfo.tj_abnormalflag,
    p_abnormalshow: iteminfo.tj_abnormalshow.to_owned(),
    p_showorder: iteminfo.tj_showorder,
    p_orgid: orgid,
    p_recheckdoctor: iteminfo.tj_recheckdoctor.to_owned(),
    p_doctor: iteminfo.tj_checkdoctor.to_owned(),
    p_checkdate: timeutil::format_timestamp(iteminfo.tj_checkdate),
  }
}

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct NxMedreportinfo {
  pub id: i64,
  pub p_orgid: i64,
  pub p_cmpid: i64,
  pub p_rptid: i64, //唯一值
  // pub tj_corpid: String,
  pub tj_corpname: String,
  pub tj_wtcorpname: String,
  pub tj_starttime: i64,
  pub tj_endtime: i64,
  pub tj_testyear: i32,
  pub tj_testtype: i32,
  pub tj_typename: String,
  pub tj_poisions: String,
  pub tj_testdate: String,
  pub tj_testaddress: String,
  pub tj_peoplenum: i32,
  pub tj_apeoplenum: i32,
  pub tj_testitems: String,
  pub tj_evalaw: String,
  pub tj_testlaw: String,
  pub tj_result: String,
  pub tj_createdate: i64,
  pub tj_moddate: i64,
  pub tj_creator: String,
  pub tj_modifier: String,
  pub tj_reportnum: String,
  pub tj_status: i32,
  pub tj_isrecheck: i32,
  pub tj_reportnumint: String,
  pub tj_reporttype: i32,
  pub tj_syncflag: i32,
  pub tj_memo: String,
  pub tj_rptfile: String,
}

pub fn convert_from_tj_report(rpt: &TjCorpoccureport, corpinfo: Option<&TjCorpinfo>, orgid: i64, rptfile: &String) -> NxMedreportinfo {
  if corpinfo.is_none() {
    return NxMedreportinfo { ..Default::default() };
  }
  let corpinfo = corpinfo.unwrap();
  if corpinfo.p_cmpid <= 0 {
    return NxMedreportinfo { ..Default::default() };
  }
  NxMedreportinfo {
    id: 0,
    p_orgid: orgid,
    p_cmpid: corpinfo.p_cmpid,
    p_rptid: 0,
    // tj_corpid: rpt.tj_corpid.to_owned(),
    tj_corpname: rpt.tj_corpname.to_owned(),
    tj_wtcorpname: rpt.tj_wtcorpname.to_owned(),
    tj_starttime: rpt.tj_starttime.to_owned(),
    tj_endtime: rpt.tj_endtime.to_owned(),
    tj_testyear: rpt.tj_testyear,
    tj_testtype: rpt.tj_testtype,
    tj_typename: rpt.tj_typename.to_owned(),
    tj_poisions: rpt.tj_poisions.to_owned(),
    tj_testdate: rpt.tj_testdate.to_owned(),
    tj_testaddress: rpt.tj_testaddress.to_owned(),
    tj_peoplenum: rpt.tj_peoplenum,
    tj_apeoplenum: rpt.tj_apeoplenum,
    tj_testitems: rpt.tj_testitems.to_owned(),
    tj_evalaw: rpt.tj_evalaw.to_owned(),
    tj_testlaw: rpt.tj_testlaw.to_owned(),
    tj_result: rpt.tj_result.to_owned(),
    tj_createdate: rpt.tj_createdate,
    tj_moddate: rpt.tj_moddate,
    tj_creator: rpt.tj_creator.to_owned(),
    tj_modifier: rpt.tj_modifier.to_owned(),
    tj_reportnum: rpt.tj_reportnum.to_owned(),
    tj_status: rpt.tj_status,
    tj_isrecheck: rpt.tj_isrecheck,
    tj_reportnumint: rpt.tj_reportnumint.to_owned(),
    tj_reporttype: rpt.tj_reporttype,
    tj_syncflag: rpt.tj_syncflag,
    tj_memo: rpt.tj_memo.to_owned(),
    tj_rptfile: rptfile.to_owned(),
  }
}

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct NxMedReportdetails {
  pub id: i64,
  pub p_reportid: i64,
  pub p_medid: i64,
  pub p_rptnum: String,
}

pub fn convert_from_tj_corpinfo_detail(cpdt: &TjCorpoccureportinfo, medinfo: Option<&TjMedexaminfo>) -> NxMedReportdetails {
  if medinfo.is_none() {
    return NxMedReportdetails { ..Default::default() };
  }
  let medinfo = medinfo.unwrap();
  if medinfo.p_medid <= 0 {
    return NxMedReportdetails { ..Default::default() };
  }
  NxMedReportdetails {
    id: 0,
    p_reportid: cpdt.tj_report_id,
    p_medid: medinfo.p_medid,
    p_rptnum: cpdt.tj_rptnum.to_owned(),
  }
}
