use axum::{
  body::{Body, Bytes},
  http::{Request, StatusCode},
  middleware::Next,
  response::{IntoResponse, Response},
};

pub async fn print_request_response(req: Request<Body>, next: Next<Body>) -> Result<impl IntoResponse, (StatusCode, String)> {
  let path = req.uri().path().to_owned();
  // info!("Path is:{}", path);
  let method = req.method().as_str().to_owned();
  // info!("method:{}", method);
  let (parts, body) = req.into_parts();
  let bytes = buffer_and_print("request", &method, &path, body).await?;
  let req = Request::from_parts(parts, Body::from(bytes));

  let res = next.run(req).await;

  // let (parts, body) = res.into_parts();
  // let bytes = buffer_and_print("response", &method, &path, body).await?;
  // let res = Response::from_parts(parts, Body::from(bytes));

  Ok(res)
}

async fn buffer_and_print<B>(direction: &str, method: &str, uri: &str, body: B) -> Result<Bytes, (StatusCode, String)>
where
  B: axum::body::HttpBody<Data = Bytes>,
  B::Error: std::fmt::Display,
{
  let bytes = match hyper::body::to_bytes(body).await {
    Ok(bytes) => bytes,
    Err(err) => {
      return Err((StatusCode::BAD_REQUEST, format!("failed to read {} body: {}", direction, err)));
    }
  };

  if let Ok(body) = std::str::from_utf8(&bytes) {
    info!("{}: {} {}, body = {:?}", direction, method, uri, body);
  }

  Ok(bytes)
}
