use crate::entities::{prelude::*, tj_autodiagcondition};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set, TransactionTrait};
use serde_json::json;

impl TjAutodiagcondition {
  pub async fn query(id: i64, db: &DatabaseConnection) -> Result<Option<TjAutodiagcondition>> {
    let ret = TjAutodiagconditionEntity::find().filter(tj_autodiagcondition::Column::Id.eq(id)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(disids: &Vec<i64>, itemids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjAutodiagcondition>> {
    let mut conditions = Condition::all();
    if disids.len() > 0 {
      conditions = conditions.add(tj_autodiagcondition::Column::TjDisid.is_in(disids.to_owned()));
    }
    if itemids.len() > 0 {
      conditions = conditions.add(tj_autodiagcondition::Column::TjItemid.is_in(itemids.to_owned()));
    }
    let ret = TjAutodiagconditionEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save_many(infos: &Vec<TjAutodiagcondition>, db: &DatabaseConnection) -> Result<i64> {
    if infos.len() <= 0 {
      // return Err(anyhow!("empty data"));
      return Ok(0);
    }
    let total = infos.len();
    let (insert_vals, update_vals): (Vec<TjAutodiagcondition>, Vec<TjAutodiagcondition>) = infos.to_owned().into_iter().partition(|f| f.id == 0);
    let txn = db.begin().await.expect("start transaction error");
    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<tj_autodiagcondition::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = tj_autodiagcondition::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = TjAutodiagconditionEntity::insert_many(active_insert_values).exec(&txn).await;
      if ret.as_ref().is_err() {
        let _ = txn.rollback();
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_packagedetail::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = tj_autodiagcondition::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(&txn).await;
        if ret.as_ref().is_err() {
          let _ = txn.rollback();
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    let ret = txn.commit().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(total as i64)
  }
  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjAutodiagconditionEntity::delete_many()
      .filter(Condition::all().add(tj_autodiagcondition::Column::Id.is_in(ids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
  pub async fn save(info: &TjAutodiagcondition, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_autodiagcondition::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }
}
