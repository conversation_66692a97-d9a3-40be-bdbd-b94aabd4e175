//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_pacsresult")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i64,
    pub tj_testid: String,
    pub tj_patientname: String,
    pub tj_itemid: String,
    pub tj_itemname: String,
    pub tj_jclx: String,
    pub tj_jcxm: String,
    pub tj_jcmc: String,
    pub tj_jcys: String,
    pub tj_sxys: String,
    pub tj_bgys: String,
    pub tj_importer: String,
    pub tj_imagesight: String,
    pub tj_imagediagnosis: String,
    pub tj_bgrq: i64,
    pub tj_importdate: i64,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
