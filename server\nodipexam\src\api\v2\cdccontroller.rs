use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok},
  auth::auth::Claims,
};
use axum::{Extension, Json};
use tracing::*;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::dbinit::DbConnection;
use nodipservice::{basic::itemsvc::ItemSvc, dto::*, medexam::cdcsvc::*};
use serde_json::Value;
use std::sync::Arc;

////////////////
pub async fn pair_checkitem(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultipleKeyDto>) -> Json<Value> {
  // let itemid =
  info!("start to pair cdc checkitem with dto:{:?}", &dto);
  let ret = ItemSvc::pair_checkitem(&dto.keystr1, &dto.keystr2, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), "".to_string());
  }
  let result = ret.unwrap();
  response_json_value_ok(1, result)
}

// query cdc dictionary
pub async fn query_dictionaries(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntsDto>) -> Json<Value> {
  // let itemid =
  info!("start with dto:{:?}", &dto);
  let ret = CdcSvc::query_cdc_dictionaries(&dto.keys, &db).await;
  if ret.as_ref().is_err() {
    // let res: Vec<TjAutodiagcondition> = Vec::new::<Vec<TjAutodiagcondition>>();
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), "".to_string());
  }
  let result = ret.unwrap();
  response_json_value_ok(result.len() as u64, result)
}
