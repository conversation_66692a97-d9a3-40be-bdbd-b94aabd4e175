use axum::{body::Body, extract::Request};
use chrono::prelude::*;
// use axum::extract::State;
// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
use hyper::body::Incoming;
use hyper_util::{
  client::legacy::connect::HttpConnector,
  rt::{TokioExecutor, TokioIo},
};
use nodipservice::{
  basic::{akasvc::AkaSvc, hazardsvc::HazardSvc},
  common::{constant::YesOrNo, syscache::SysCache},
  external::{recvexternallis::RecvExternalLis, recvexternalpacs::RecvExternalPacs},
};
use std::{fs, sync::Arc, time::Duration};
use strum::IntoEnumIterator;
use tower::Service;
use tracing::*;
// use nodipservice::prelude::DictService;
use crate::{
  config::settings::Settings,
  external::recv::{recvlabdata::RecvLabdata, recvpacsdata::RecvPacsData},
};
use nodipservice::SYSCACHE;

use tokio::{
  signal,
  sync::{watch, RwLock},
};
use utility::uidservice::UidgenService;
mod api;
mod auth;
mod common;
mod config;
mod external;
mod nxmiddleware;
#[cfg(test)]
mod test;

type Client = hyper_util::client::legacy::Client<HttpConnector, Body>;

#[tokio::main]
// #[tokio::main(flavor = "multi_thread", worker_threads = 5)]
async fn main() {
  // let cfg = "config/nodipexam.yaml";
  // log4rs::init_file(cfg, Default::default()).unwrap();

  let prefix = "nodipexam";
  let dir = "./log";
  let _guard = utility::loggings::init_tracing(prefix, dir);
  // let local_time = time::LocalTime::new(time::macros::format_description!("%Y-%m-%d %H:%M:%S%.3f"));
  // let console_layer = fmt::layer()
  //   .with_ansi(false)
  //   .with_timer(CustomLocalTime)
  //   .with_writer(std::io::stdout)
  //   .with_target(true)
  //   .with_line_number(true);

  // // 设置每日滚动，保留 7 天
  // // 使用 AppendTimestamp，設置時間戳格式為本地時間
  // // let suffix = AppendTimestamp::default(FileLimit::MaxFiles(7)).with_format("%Y%m%d"); // 格式：YYYYMMDD
  // let suffix = AppendTimestamp::with_format("%Y%m%d", FileLimit::MaxFiles(7), file_rotate::suffix::DateFrom::Now);

  // let log_writer = FileRotate::new(
  //   &format!("{}/{}.log", dir, prefix),
  //   // AppendTimestamp::default(FileLimit::Age(chrono::Duration::days(7))),
  //   suffix,
  //   ContentLimit::Time(TimeFrequency::Daily),
  //   file_rotate::compression::Compression::None,
  //   None,
  // );
  // // let time_format = format_description!("[year]-[month]-[day] [hour]:[minute]:[second]");
  // let (non_blocking_appender, _guard) = tracing_appender::non_blocking(log_writer); // 输出非阻塞
  // let file_layer = fmt::layer()
  //   .with_ansi(false)
  //   .with_writer(non_blocking_appender)
  //   .with_line_number(true)
  //   .with_target(true)
  //   // .with_timer(tracing_subscriber::fmt::time::UtcTime::rfc_3339())
  //   .with_timer(CustomLocalTime)
  //   .boxed();

  // tracing_subscriber::registry()
  //   .with(file_layer)
  //   .with(console_layer)
  //   .with(tracing_subscriber::EnvFilter::new("INFO"))
  //   .init();

  // let cfg_file = "config/nodipexam.toml";
  let sys_config = Settings::init("config/nodipexam.toml").expect("init configuration error");
  info!("Configuration file:{:?}", &sys_config);

  init_startup(&sys_config);

  let sys_config_clone = sys_config.clone();

  let mut default_extlis: config::settings::Extlis = config::settings::Extlis { ..Default::default() };
  if sys_config_clone.extlis.len() > 0 {
    let default_lis = sys_config_clone.extlis.get("default");
    if default_lis.is_some() {
      let extlis_default = default_lis.unwrap();
      if !extlis_default.uri.is_empty() {
        default_extlis = extlis_default.clone();
      }
    }
  }

  let recv_external_lis: RecvExternalLis; // = None;
  if !default_extlis.uri.is_empty() && sys_config_clone.application.lisego == YesOrNo::No as i32 {
    //init
    let external_lis = RecvExternalLis::new("default", &default_extlis.username, &default_extlis.password, &default_extlis.uri, default_extlis.dbtype).await;
    recv_external_lis = external_lis;
  } else {
    recv_external_lis = RecvExternalLis::new("", "", "", "", 0).await;
  }
  let mut default_extpacs: config::settings::Extpacs = config::settings::Extpacs { ..Default::default() };
  if sys_config_clone.extpacs.len() > 0 {
    let default_val = sys_config_clone.extpacs.get("default");
    if default_val.is_some() {
      let ext_default = default_val.unwrap();
      if !ext_default.uri.is_empty() {
        default_extpacs = ext_default.clone();
      }
    }
  }
  let recv_external_pacs: RecvExternalPacs;
  if !default_extpacs.uri.is_empty() && sys_config_clone.application.pacsego == YesOrNo::No as i32 {
    //init
    let external_lis = RecvExternalPacs::new("default", &default_extpacs.username, &default_extpacs.password, &default_extpacs.uri, default_extpacs.dbtype).await;
    recv_external_pacs = external_lis;
  } else {
    recv_external_pacs = RecvExternalPacs::new("", "", "", "", 0).await;
  }
  // let client = Client::new();
  let client: Client = hyper_util::client::legacy::Client::<(), ()>::builder(TokioExecutor::new()).build(HttpConnector::new());

  let httpaddr = format!("{}:{}", sys_config.application.apphost, sys_config.application.appport);
  // let httpaddr: SocketAddr = http_url.as_str().parse().expect("parse url error");
  info!("start to create database connection......");
  let dbconn = DbConnection::new(&sys_config.database.uri.as_str(), 3 /*DatabaseType::MySql as i32*/).await;

  info!("start to init system cache......");
  let syscache = SysCache::new(&sys_config.application.areaprovince, &dbconn).await;
  SYSCACHE.set(syscache).expect("set global value error");

  let uidgen = UidgenService::new(1, 1);

  let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel::<i32>();
  //create database clone
  let dbconn_clone = dbconn.clone();
  // let recv_external_lis_clone = recv_external_lis.clone();
  // let recv_external_pacs_clone = recv_external_pacs.clone();
  info!("start to create routes......");
  let app = api::routers::create_route(
    Arc::new(dbconn),
    Arc::new(sys_config),
    Arc::new(RwLock::new(uidgen)),
    Arc::new(tx),
    Arc::new(recv_external_lis),
    Arc::new(recv_external_pacs),
    client,
  );
  let mut doclean = 0;
  let args: Vec<String> = std::env::args().collect();
  // info!("参数列表:{:?}", &args);
  if args.len() > 1 {
    doclean = args[1].parse::<i32>().unwrap_or_default();
  }
  if doclean == 1 {
    let _ret = HazardSvc::clean_hazardinfo_evallaw(&dbconn_clone).await;
  }

  let version = env!("CARGO_PKG_VERSION");
  info!(
    "{} server started on {}, version:{}, {}",
    env!("CARGO_PKG_NAME"),
    &httpaddr,
    &version,
    env!("CARGO_PKG_DESCRIPTION")
  );
  let mut time_interval = tokio::time::interval(Duration::from_secs(3600));
  tokio::spawn(async move {
    time_interval.tick().await;
    loop {
      tokio::select! {
        ct = rx.recv() =>{
            if let Some(cachetype) = ct{
              info!("缓存数据需要更新了:{:?}",&ct);
              SYSCACHE.get().unwrap().update_caches(cachetype,&dbconn_clone).await;
          }
        }
        _=time_interval.tick()=>{
            //触发定时任务，可以清除本地数据库中一些不用的临时数据，比如tj_labresult表
          let local: DateTime<Local> = Local::now();
            if local.hour() > 1 && local.hour() <= 2 {
              info!("1-2点，开始数据清理......");
              AkaSvc::clear_data(&dbconn_clone).await;
              HazardSvc::clean_hazardinfos(&dbconn_clone).await;
              for ct in nodipservice::common::constant::CacheType::iter(){
                SYSCACHE.get().unwrap().update_caches(ct as i32, &dbconn_clone).await;
              }
            }
            if local.hour() > 2 && local.hour() <= 3 {
              if sys_config_clone.application.autorecvlis == YesOrNo::Yes as i32{
              info!("2-3点，开始自动接收lis数据......");
              let _ = RecvLabdata::auto_recv_labresult(&sys_config_clone,  YesOrNo::Yes as i64, &dbconn_clone).await;
            }
              if sys_config_clone.application.autorecvpacs == YesOrNo::Yes as i32{
              info!("2-3点，开始自动接收pacs数据......");
              let _ = RecvPacsData::auto_recv_pacsresult(&sys_config_clone,  YesOrNo::Yes as i64, &dbconn_clone).await;
            }
          }
        }
      }
    }
  });
  // let listener = tokio::net::TcpListener::bind(&httpaddr).await.expect("bind error");
  // axum::serve(listener, app.into_make_service_with_connect_info::<SocketAddr>()).await.expect("start server error");

  let listener = tokio::net::TcpListener::bind(&httpaddr).await.expect("bind error");

  // Create a watch channel to track tasks that are handling connections and wait for them to complete.
  let (close_tx, close_rx) = watch::channel(());

  // axum::serve(listener, app.into_make_service_with_connect_info::<SocketAddr>()).await.expect("start server error");
  loop {
    let (socket, remote_addr) = tokio::select! {
        // Either accept a new connection...
        result = listener.accept() => {
            result.unwrap()
        }
        // ...or wait to receive a shutdown signal and stop the accept loop.
        _ = shutdown_signal() => {
            info!("signal received, not accepting new connections");
            break;
        }
    };

    info!("connection accepted from  {remote_addr}");
    // We don't need to call `poll_ready` because `Router` is always ready.
    let tower_service = app.clone();
    // Clone the watch receiver and move it into the task.
    let close_rx = close_rx.clone();
    // Spawn a task to handle the connection. That way we can serve multiple connections
    // concurrently.
    tokio::spawn(async move {
      // Hyper has its own `AsyncRead` and `AsyncWrite` traits and doesn't use tokio.
      // `TokioIo` converts between them.
      let socket = TokioIo::new(socket);

      // Hyper also has its own `Service` trait and doesn't use tower. We can use
      // `hyper::service::service_fn` to create a hyper `Service` that calls our app through
      // `tower::Service::call`.
      let hyper_service = hyper::service::service_fn(move |mut request: Request<Incoming>| {
        // We have to clone `tower_service` because hyper's `Service` uses `&self` whereas
        // tower's `Service` requires `&mut self`.
        // request.headers_mut().insert(HOST, remote_addr);
        request.extensions_mut().insert(remote_addr);
        // We don't need to call `poll_ready` since `Router` is always ready.
        tower_service.clone().call(request)
      });

      // `hyper_util::server::conn::auto::Builder` supports both http1 and http2 but doesn't
      // support graceful so we have to use hyper directly and unfortunately pick between
      // http1 and http2.
      let conn = hyper::server::conn::http1::Builder::new()
        .serve_connection(socket, hyper_service)
        // `with_upgrades` is required for websockets.
        .with_upgrades();

      // `graceful_shutdown` requires a pinned connection.
      let mut conn = std::pin::pin!(conn);

      loop {
        tokio::select! {
            // Poll the connection. This completes when the client has closed the
            // connection, graceful shutdown has completed, or we encounter a TCP error.
            result = conn.as_mut() => {
                if let Err(err) = result {
                    info!("failed to serve connection: {err:#}");
                }
                break;
            }
            // Start graceful shutdown when we receive a shutdown signal.
            //
            // We use a loop to continue polling the connection to allow requests to finish
            // after starting graceful shutdown. Our `Router` has `TimeoutLayer` so
            // requests will finish after at most 10 seconds.
            _ = shutdown_signal() => {
                info!("signal received, starting graceful shutdown");
                // recv_external_pacs.release().await;
                // recv_external_lis.release().await;
                conn.as_mut().graceful_shutdown();
            }
        }
      }

      info!("connection {remote_addr} closed");

      // Drop the watch receiver to signal to `main` that this task is done.
      drop(close_rx);
    });
  }
  // We only care about the watch receivers that were moved into the tasks so close the residual
  // receiver.
  drop(close_rx);

  // Close the listener to stop accepting new connections.
  drop(listener);

  // Wait for all tasks to complete.
  info!("waiting for {} tasks to finish", close_tx.receiver_count());
  close_tx.closed().await;
  info!("server exited successfully");

  //  let socket = hyper_util::rt::TokioIo::new(socket);
  // let conn = hyper::server::conn::http1::Builder::new().serve_connection(socket, app)

  // axum::Server::bind(&httpaddr)
  //   // .serve(app.into_make_service())
  //   .serve(app.into_make_service_with_connect_info::<SocketAddr>())
  //   .with_graceful_shutdown(shutdown_signal())
  //   .await
  //   .expect("start server error");
}

fn init_startup(config: &Settings) {
  //create directory???
  fs::create_dir_all(&config.application.uploaddir).expect("create uploaddir directory error");
  fs::create_dir_all(&config.application.reportdir).expect("create reportdir directory error");
  fs::create_dir_all(&config.application.photodir).expect("create photodir directory error");
  fs::create_dir_all(&config.application.signdir).expect("create signdir directory error");
  fs::create_dir_all(&config.application.filedir).expect("create filedir directory error");
  fs::create_dir_all("./audiogram").expect("create audiogram directory error"); //audiochart
  fs::create_dir_all("./audiochart").expect("create audiochart directory error");
  //audiochart
}

async fn shutdown_signal() {
  let ctrl_c = async {
    signal::ctrl_c().await.expect("failed to install Ctrl+C handler");
  };
  #[cfg(unix)]
  let terminate = async {
    signal::unix::signal(signal::unix::SignalKind::terminate())
      .expect("failed to install signal handler")
      .recv()
      .await;
  };
  #[cfg(not(unix))]
  let terminate = std::future::pending::<()>();
  tokio::select! {
      _ = ctrl_c => {},
      _ = terminate => {},
  }
  // info!("signal received, starting graceful shutdown");
}
