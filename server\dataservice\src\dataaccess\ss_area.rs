use crate::entities::{prelude::*, ss_area};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter};

use tracing::*;

impl SsArea {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<SsArea>> {
    // let ret = NxJobinfoEntity::find_by_id(id).one(db).await;
    let ret = SsAreaEntity::find().filter(ss_area::Column::AreaCode.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(codes: &str, db: &DatabaseConnection) -> Result<Vec<SsArea>> {
    info!("area code is:[{}]", &codes);
    let mut condition: Condition;

    if !codes.is_empty() {
      condition = Condition::any();
      let pre_areas: Vec<&str> = codes.split(",").collect();
      // info!("pre_areas:{:?}", &pre_areas);
      if pre_areas.len() > 0 {
        for val in pre_areas.into_iter() {
          condition = condition.add(ss_area::Column::AreaCode.starts_with(val));
        }
      }
    } else {
      condition = Condition::all();
    }
    let query = SsAreaEntity::find().filter(condition);
    // info!("query_many for ss_area: {:?}", &query.build(DbBackend::MySql).to_string());
    let ret = query.all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
