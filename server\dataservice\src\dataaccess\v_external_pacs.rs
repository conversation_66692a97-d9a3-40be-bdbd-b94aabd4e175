use crate::entities::{prelude::*, v_external_pacs};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter};
// use serde_json::json;

impl VExternalPacs {
  pub async fn query_many(testid: &str, barcode: &str, db: &DatabaseConnection) -> Result<Vec<VExternalPacs>> {
    if testid.is_empty() && barcode.is_empty() {
      return Err(anyhow!("conditions are empty, not allowed"));
    }
    let mut conditions = Condition::all();
    if !testid.is_empty() {
      conditions = conditions.add(v_external_pacs::Column::Testid.eq(testid));
    }
    if !barcode.is_empty() {
      conditions = conditions.add(v_external_pacs::Column::Tid.eq(barcode));
    }

    let ret = VExternalPacsEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
