//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_hazarddisease")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_hid: i32,
  pub tj_testtype: i32,
  pub tj_forbidden: String,
  pub tj_targetdisease: String,
  pub tj_memo: String,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
