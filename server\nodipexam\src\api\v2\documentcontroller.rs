use crate::auth::auth::Claims;
use crate::{
  // api::httpresponse::{response_json_value_error, response_json_value_ok},
  common::filesvc::FileSvc,
  config::settings::Settings,
};
// use anyhow::Result;
use axum::{response::IntoResponse, Extension, Json};
// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
// use dbopservice::dbinit::DbConnection;
use hyper::StatusCode;
use nodipservice::dto::*;
// use serde_json::Value;
use nodipservice::medexam::documentsvc::DocumentSvc;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::*;

pub async fn generate_corp_report(_claims: Claims, config: Extension<Arc<Settings>>, Extension(dbconn): Extension<Arc<DbConnection>>, Json(dto): Json<MultipleKeyDto>) -> impl IntoResponse {
  let rptid = dto.keyint1;
  let rpttype = dto.keyint2 as i32;
  let mut oritype = 0;
  if let Some(keyint3) = dto.keyint3 {
    oritype = keyint3;
  }
  let mut splitinrow = 1;
  let keystr1 = dto.keystr1.to_owned();
  if !keystr1.is_empty() {
    splitinrow = keystr1.parse().unwrap_or(0);
  }
  let reportdir = config.application.reportdir.to_owned();
  let commander = config.application.commander.to_owned();
  let egouri = config.application.proxyserver.to_owned();
  // let (tx, mut rx) = tokio::sync::mpsc::channel::<Result<String>>(10);
  let handle = tokio::spawn(async move {
    info!("spawn new task to generate report......");
    let ret = DocumentSvc::generate_corp_occu_report(rptid, rpttype, oritype, splitinrow, &reportdir, &egouri, &commander, &dbconn).await;
    // tx.send(ret).await;
    ret
  });
  let ret = handle.await.unwrap();
  if ret.as_ref().is_err() {
    error!("generate report file error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }
  let rptfile = ret.unwrap();

  let ret = FileSvc::do_download(&format!("{}/{}", config.application.reportdir, rptfile)).await;
  if ret.as_ref().is_err() {
    error!("download file error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }

  Ok(ret.unwrap())
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RecheckNoticeDto {
  pub corpid: i64,
  pub testids: Vec<String>,
  pub notice: String,
}

// {
// 	"rptnum":"2025-0011",
// 	"corpname":"杭州虹泽科技有限公司",
// 	"recheckdate":"2025年3月20日",
// 	"dptname":"体检中心",
// 	"testids":["10000875","10000874"],
// 	"notice":"这个是复查的注意事项\n2:脱离噪声岗位24小时",
// 	"outdir":"./reports"
// }
#[derive(Debug, Serialize, Deserialize)]
pub struct RecheckReportDto {
  pub rptnum: String,
  pub corpname: String,
  pub recheckdate: String,
  pub dptname: String,
  pub testids: Vec<String>,
  pub notice: String,
  pub outdir: String,
}

pub async fn generate_corp_recheck_notice(
  _claims: Claims,
  config: Extension<Arc<Settings>>,
  Extension(_dbconn): Extension<Arc<DbConnection>>,
  Json(_dto): Json<RecheckNoticeDto>,
) -> impl IntoResponse {
  let rptfile = "".to_string();

  let ret = FileSvc::do_download(&format!("{}/{}", config.application.reportdir, rptfile)).await;
  if ret.as_ref().is_err() {
    error!("download file error:{}", ret.as_ref().err().unwrap().to_string());
    return Err((StatusCode::NOT_FOUND, ret.as_ref().err().unwrap().to_string()));
  }

  Ok(ret.unwrap())
}
