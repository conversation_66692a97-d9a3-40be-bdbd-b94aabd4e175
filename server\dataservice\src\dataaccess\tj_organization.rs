use crate::entities::{prelude::*, tj_organization};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter};

impl TjOrganization {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<TjOrganization>> {

    let ret = TjOrganizationEntity::find().filter(tj_organization::Column::TjOrgcode.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }
}
