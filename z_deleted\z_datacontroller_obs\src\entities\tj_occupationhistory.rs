use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_occupationhistory")]
#[derive(<PERSON><PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct TjOccupationHistory {
    pub id: i64,
    pub tj_testid: String,
    pub tj_startdate: i32,
    pub tj_enddate: i32,
    pub tj_corpname: String,
    pub tj_workshop: String,
    pub tj_worktype: String,
    pub tj_worktypename: String,
    pub tj_harmful: String,
    pub tj_harmfulname: String,
    pub tj_protective: String,
    pub tj_raddaynum: String,
    pub tj_radtotalnum: String,
    pub tj_radoverdose: String,
    pub tj_radexposure: String,
    pub tj_radcode: String,
    pub tj_radtype: String,
    pub tj_radprotective: String,
}
