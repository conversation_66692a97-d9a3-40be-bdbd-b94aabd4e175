//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Default, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_corpinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  #[sea_orm(unique)]
  pub tj_corpid: String,
  pub tj_corpname: String,
  pub tj_contactor: String,
  pub tj_contactorjob: String,
  pub tj_principle: String,
  pub tj_phone: String,
  pub tj_fax: String,
  pub tj_areacode: String,
  pub tj_address: String,
  pub tj_postcode: String,
  pub tj_industry2: String,
  pub tj_economic2: String,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_operator: i64,
  pub tj_adddate: i64,
  pub tj_testtype: i32,
  pub tj_password: String,
  pub tj_mail: String,
  pub tj_memo: String,
  pub tj_orgcode: String,
  pub tj_gbcode: String,
  pub tj_ecotypeh: i32,
  pub tj_ecotype: i32,
  pub tj_industryh: i32,
  pub tj_industry: i32,
  pub tj_secondcode: String,
  pub tj_secondname: String,
  pub tj_corpscale: i32,
  pub tj_total: i32,
  pub tj_totalf: i32,
  pub tj_operationer: i32,
  pub tj_operationerf: i32,
  pub tj_hazarders: i32,
  pub tj_hazardersf: i32,
  pub tj_externalstaff: i32,
  pub tj_syncflag: i32,
  pub tj_status: i32,
  pub tj_monitortype: String,
  pub p_cmpid: i64,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
