// use crate::config::settings::Settings;
use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use crate::{
  client::httpclient::HttpClient,
  dto::{ExtTypeDTO, ExternalDTO},
};
use tracing::*;
use dataservice::{dbinit::DbConnection, entities::prelude::*};

// use super::laidapacs::laida::{HisJCRW, LaidaPacs};

pub struct LayyService;

impl LayyService {
  pub async fn do_upload(
    dto: &ExternalDTO,
    new_extitems: &Vec<ExtCheckiteminfo>,
    exist_items: &Vec<ExtCheckiteminfo>,
    server: &String,
    // uid: &mut UidgenService,
    _db: &DbConnection,
  ) -> Result<()> {
    info!("start to upload lis for layy with server:{server}");
    let ret = LayyService::upload_lis(&dto, &new_extitems, &exist_items, server).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    // info!("start to upload pacs......");
    // let ret = LayyService::upload_pacs(&new_extitems, &exist_items, &config, &db).await;
    // if ret.as_ref().is_err() {
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    Ok(())
  }

  async fn upload_lis(dto: &ExternalDTO, new_extitems: &Vec<ExtCheckiteminfo>, exist_items: &Vec<ExtCheckiteminfo>, server: &String) -> Result<()> {
    // let server = config.external.serverurl.to_owned();
    if server.is_empty() {
      return Ok(());
    }

    let dton = ExtTypeDTO {
      testid: dto.testid.to_owned(),
      exttype: dto.exttype,
      newinfos: Some(new_extitems.to_owned()),
      existinfos: Some(exist_items.to_owned()),
    };
    // info!("start to upload lis for layy with httpclient");
    let ret: Result<String> = HttpClient::send_http_post_request("", &server, &Some(dton)).await;
    if ret.as_ref().is_err() {
      error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }

    info!("upload lis for layy with httpclient DONE");
    Ok(())
  }

  // async fn upload_pacs(new_extitems: &Vec<ExtCheckiteminfo>, exist_items: &Vec<ExtCheckiteminfo>, config: &Settings, db: &DbConnection) -> Result<()> {
  //   let to_del_iteminfos: Vec<ExtCheckiteminfo> = exist_items
  //     .iter()
  //     .filter(|p| new_extitems.iter().find(|f| f.itemid2.eq_ignore_ascii_case(&p.itemid2)).is_none() && p.depttype == DeptType::Function as i32)
  //     .map(|v| v.to_owned())
  //     .collect();
  //   let to_add_iteminfos: Vec<ExtCheckiteminfo> = new_extitems
  //     .iter()
  //     .filter(|p| exist_items.iter().find(|f| f.itemid2.eq_ignore_ascii_case(&p.itemid2)).is_none() && p.depttype == DeptType::Function as i32)
  //     .map(|v| v.to_owned())
  //     .collect();
  //   info!(
  //     "start to upload external pacs, to add items:{},to delete items:{}",
  //     &to_add_iteminfos.len(),
  //     &to_del_iteminfos.len()
  //   );
  //   if to_add_iteminfos.len() <= 0 && to_del_iteminfos.len() <= 0 {
  //     return Ok(());
  //   }
  //   if config.extpacs.len() <= 0 {
  //     return Err(anyhow!("没有配置pacs连接信息"));
  //   }
  //   let default_pacs = config.extpacs.get("default").map_or(Extpacs { ..Default::default() }, |f| f.to_owned());
  //   if default_pacs.uri.is_empty() {
  //     return Err(anyhow!("没有默认得pacs连接信息"));
  //   }
  //   if to_add_iteminfos.len() > 0 {
  //     //insert new
  //     let ret = LayyService::insert_jcrw(&to_add_iteminfos, &default_pacs, &db).await;
  //     if ret.as_ref().is_err() {
  //       error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
  //       return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //     }
  //     // for val in to_add_iteminfos.into_iter() {}
  //   }
  //   if to_del_iteminfos.len() > 0 {
  //     //delete
  //     let ret = LayyService::delete_jcrw(&to_del_iteminfos, &default_pacs).await;
  //     if ret.as_ref().is_err() {
  //       error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
  //       return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //     }
  //   }
  //   Ok(())
  // }

  // async fn insert_jcrw(ext: &Vec<ExtCheckiteminfo>, pacs: &Extpacs, db: &DbConnection) -> Result<()> {
  //   //tiberius
  //   return LayyService::insert_with_tiberius(ext, &pacs, &db).await;
  // }
  // async fn delete_jcrw(ext: &Vec<ExtCheckiteminfo>, pacs: &Extpacs) -> Result<()> {
  //   // if pacs.dbtype == 1 {
  //   LayyService::delete_with_tiberius(ext, pacs).await
  //   // } else {
  //   //   error!("没有配置正确的pacs对接参数(dbtype=1)");
  //   //   return Err(anyhow!("没有配置正确的pacs对接参数（dbtype=1)"));
  //   // }
  //   // Ok(())
  // }
  // async fn insert_with_tiberius(ext: &Vec<ExtCheckiteminfo>, config: &Extpacs, db: &DbConnection) -> Result<()> {
  //   info!("start to connect with tiberius----------");
  //   let tconfig = tiberius::Config::from_jdbc_string(&config.uri); //.expect("init from jdbc error");
  //   if tconfig.is_err() {
  //     return Err(anyhow!("invalid config:{}", tconfig.unwrap_err().to_string()));
  //   }
  //   let tconfig = tconfig.unwrap();
  //   let tcp = TcpStream::connect(tconfig.get_addr()).await; //.expect("tcpstream connect error");
  //   if tcp.is_err() {
  //     return Err(anyhow!("invalid tcp:{}", tcp.unwrap_err().to_string()));
  //   }
  //   let tcp = tcp.unwrap();
  //   let ret = tcp.set_nodelay(true); //.expect("tcp stream set no delay error");
  //   if ret.as_ref().is_err() {
  //     return Err(anyhow!("set nodaly error:{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   let ret = tiberius::Client::connect(tconfig, tokio_util::compat::TokioAsyncWriteCompatExt::compat_write(tcp)).await; //.expect("connect with tiberium client error");
  //   if ret.as_ref().is_err() {
  //     return Err(anyhow!("connect error:{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   let mut client = ret.unwrap();
  //   info!("----------end of tiberius:{:?}", &client);
  //   info!("start to process hisJCRW......");
  //   for val in ext.iter() {
  //     let ksxx = nodipservice::SYSCACHE.get().unwrap().get_department(&val.deptid, db).await;
  //     let studyuid = val.uid.to_string();
  //     let jch = format!("{}{}", ksxx.tj_pyjm, val.testid);
  //     let djxh: i32 = 4;
  //     let jzkh = val.testid.to_string();
  //     let bah = val.testid.to_string();
  //     let brxm = val.testername.to_string();
  //     let brxb: i32 = val.psex;
  //     let csrq = val.birthdate.to_string();
  //     let brnl: i32 = val.age;
  //     let ylxh: i32 = val.itemid.parse::<i32>().unwrap_or_default();
  //     let ylmc = val.itemname.to_string();
  //     let sjysgh = val.requesterid.to_string();
  //     let sjysxm = val.requestername.to_string();
  //     let sjksdm = "1001";
  //     let sjksmc = "体检中心";
  //     let sjsj = val.requestdate2.to_string();
  //     let jclxdm = val.zdym.to_string();
  //     let jcsbdm = val.zdym.to_string();
  //     let status = "登记";
  //     let sqdh = format!("2.18.510{}{}", val.testid, val.itemid); //val.uid.to_string();
  //     let djrq = val.requestdate2.to_string();
  //     let djysgh = val.requesterid.to_string();
  //     let djysxm = val.requestername.to_string();
  //     let sfzh = val.idcard.to_string();
  //     let lxdh = val.phone.to_string();
  //     let mut lsh = Decimal::from(0);
  //     {
  //       info!("start to check examination if exist......");
  //       let sql_select_lsh = format!("select lsh from hisJCRW where sqdh = '{sqdh}' and bah='{bah}'");
  //       info!("sql string:{}", &sql_select_lsh);
  //       let ret = client.query(&sql_select_lsh, &[]).await;
  //       info!("query result:{:?}", &ret);
  //       if ret.as_ref().is_err() {
  //         error!("insert into hisjcrw error:{}", ret.as_ref().unwrap_err().to_string());
  //         return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //       }
  //       let stream = ret.unwrap();
  //       let ret = stream.into_row().await;
  //       if ret.as_ref().is_err() {
  //         error!("insert into hisjcrw error:{}", ret.as_ref().unwrap_err().to_string());
  //         return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //       }
  //       if let Ok(result) = ret {
  //         if let Some(row) = result {
  //           if let Some(some_lsh) = row.get(0) {
  //             lsh = some_lsh;
  //           }
  //         }
  //       }
  //     }
  //     info!("lsh is:{}", &lsh);
  //     //处理jclw数据
  //     if lsh == Decimal::from(0) {
  //       let sql_jcrw = format!(
  //         r#"insert into HisJCRW(studyuid, jch,djxh,jzkh,bah,brxm,brxb,csrq,brnl,ylxh,ylmc,sjysgh,sjysxm,sjksdm,sjksmc,sjsj,jclxdm,jcsbdm,status,sqdh,djrq,djysgh,djysxm,sfzh,lxdh)
  //         values('{studyuid}','{jch}','{djxh}','{jzkh}','{bah}','{brxm}','{brxb}','{csrq}','{brnl}','{ylxh}','{ylmc}','{sjysgh}','{sjysxm}','{sjksdm}','{sjksmc}','{sjsj}','{jclxdm}','{jcsbdm}','{status}','{sqdh}','{djrq}','{djysgh}','{djysxm}','{sfzh}','{lxdh}')"#
  //       );
  //       info!("sql string:{}", &sql_jcrw);
  //       let ret = client.execute(sql_jcrw, &[]).await;
  //       info!("execute result:{:?}", &ret);
  //       if ret.as_ref().is_err() {
  //         error!("insert into hisjcrw error:{}", ret.as_ref().unwrap_err().to_string());
  //         return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //       }
  //       {
  //         let sql_select_lsh = format!("select lsh from hisJCRW where sqdh = '{sqdh}' and bah='{bah}'");
  //         info!("sql string:{}", &sql_select_lsh);
  //         let ret = client.query(&sql_select_lsh, &[]).await;
  //         info!("query result:{:?}", &ret);
  //         if ret.as_ref().is_err() {
  //           error!("insert into hisjcrw error:{}", ret.as_ref().unwrap_err().to_string());
  //           return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //         }
  //         let stream = ret.unwrap();
  //         let ret = stream.into_row().await;
  //         if ret.as_ref().is_err() {
  //           error!("insert into hisjcrw error:{}", ret.as_ref().unwrap_err().to_string());
  //           return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //         }
  //         if let Ok(result) = ret {
  //           if let Some(row) = result {
  //             if let Some(some_lsh) = row.get(0) {
  //               lsh = some_lsh;
  //             }
  //           }
  //         }
  //       }
  //       info!("lsh is:{}", &lsh);
  //     }
  //     let mut zllx = 1;
  //     {
  //       let sql_str = format!("select jcrwlsh from HisRWZL where jcrwlsh = {lsh}");
  //       info!("sql string:{}", &sql_str);
  //       let ret = client.query(&sql_str, &[]).await;
  //       info!("query result:{:?}", &ret);
  //       if ret.as_ref().is_err() {
  //         error!("insert into hisjcrw error:{}", ret.as_ref().unwrap_err().to_string());
  //         return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //       }
  //       let stream = ret.unwrap();
  //       let ret = stream.into_row().await;
  //       if ret.as_ref().is_err() {
  //         error!("insert into hisjcrw error:{}", ret.as_ref().unwrap_err().to_string());
  //         return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //       }
  //       let row = ret.unwrap();
  //       if row.is_some() {
  //         info!("流水号：{}在rwzl中已经存在,采用修改的方式进行", lsh);
  //         zllx = 5;
  //       }
  //     }
  //     let sql_rwzl = format!("insert into HisRWZL(rwzllx,jcrwlsh,rwzlsj) values({zllx}, {lsh}, getdate())");
  //     info!("sql string:{}", &sql_rwzl);
  //     let ret = client.execute(sql_rwzl, &[]).await;
  //     info!("execute result:{:?}", &ret);
  //     if ret.as_ref().is_err() {
  //       error!("insert into hisjcrw error:{}", ret.as_ref().unwrap_err().to_string());
  //       return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //     }
  //   }
  //   Ok(())
  // }
  // async fn delete_with_tiberius(ext: &Vec<ExtCheckiteminfo>, config: &Extpacs) -> Result<()> {
  //   info!("start to connect with tiberius----------");
  //   let tconfig = tiberius::Config::from_jdbc_string(&config.uri); //.expect("init from jdbc error");
  //   if tconfig.is_err() {
  //     return Err(anyhow!("invalid config:{}", tconfig.unwrap_err().to_string()));
  //   }
  //   let tconfig = tconfig.unwrap();
  //   let tcp = TcpStream::connect(tconfig.get_addr()).await; //.expect("tcpstream connect error");
  //   if tcp.is_err() {
  //     return Err(anyhow!("invalid tcp:{}", tcp.unwrap_err().to_string()));
  //   }
  //   let tcp = tcp.unwrap();

  //   let ret = tcp.set_nodelay(true); //.expect("tcp stream set no delay error");
  //   if ret.as_ref().is_err() {
  //     return Err(anyhow!("set nodaly error:{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   let ret = tiberius::Client::connect(tconfig, tokio_util::compat::TokioAsyncWriteCompatExt::compat_write(tcp)).await; //.expect("connect with tiberium client error");
  //   if ret.as_ref().is_err() {
  //     return Err(anyhow!("connect error:{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   let mut client = ret.unwrap();
  //   info!("----------end of tiberius:{:?}", &client);

  //   info!("start to delete rwzl......");
  //   for val in ext.iter() {
  //     let bah = val.testid.to_string();
  //     // let sqdh = val.uid.to_string();
  //     let sqdh = format!("2.18.510{}{}", val.testid, val.itemid);
  //     let mut lsh = Decimal::from(0);
  //     {
  //       let sql_select_lsh = format!("select lsh from hisJCRW where sqdh = '{sqdh}' and bah='{bah}'");
  //       info!("sql string:{}", &sql_select_lsh);
  //       let ret = client.query(&sql_select_lsh, &[]).await;
  //       info!("query result:{:?}", &ret);
  //       if ret.as_ref().is_err() {
  //         error!("select lsh  error:{}", ret.as_ref().unwrap_err().to_string());
  //         return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //       }
  //       let stream = ret.unwrap();
  //       let ret = stream.into_row().await;
  //       if ret.as_ref().is_err() {
  //         error!("select lsh  error:{}", ret.as_ref().unwrap_err().to_string());
  //         return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //       }
  //       if let Ok(result) = ret {
  //         if let Some(row) = result {
  //           if let Some(some_lsh) = row.get(0) {
  //             lsh = some_lsh;
  //           }
  //         }
  //       }
  //     }
  //     info!("lsh is:{}", &lsh);
  //     {
  //       let sql_str = format!("select jcrwlsh from HisRWZL where jcrwlsh = {lsh}");
  //       info!("sql string:{}", &sql_str);
  //       let ret = client.query(&sql_str, &[]).await;
  //       info!("query result:{:?}", &ret);
  //       if ret.as_ref().is_err() {
  //         error!("select jcrwlsh error:{}", ret.as_ref().unwrap_err().to_string());
  //         return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //       }
  //       let stream = ret.unwrap();
  //       let ret = stream.into_row().await;
  //       if ret.as_ref().is_err() {
  //         error!("select jcrwlsh error:{}", ret.as_ref().unwrap_err().to_string());
  //         return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //       }
  //       let row = ret.unwrap();
  //       if row.is_none() {
  //         error!("流水号：{} 在rwzl中不存在", lsh);
  //         return Err(anyhow!("流水号：{} 在rwzl中不存在", lsh));
  //       }
  //     }
  //     let sql_rwzl = format!("insert into HisRWZL(rwzllx,jcrwlsh,rwzlsj) values(10, {lsh}, getdate())");
  //     info!("sql string:{}", &sql_rwzl);
  //     let ret = client.execute(sql_rwzl, &[]).await;
  //     info!("execute result:{:?}", &ret);
  //     if ret.as_ref().is_err() {
  //       error!("insert into HisRWZL error:{}", ret.as_ref().unwrap_err().to_string());
  //       return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
  //     }
  //   }
  //   Ok(())
  // }
}
