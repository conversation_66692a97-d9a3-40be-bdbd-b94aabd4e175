[workspace]
members = ["server/*"]
resolver = "2"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true

# [profile.release]
# opt-level = 3
# debug = false
# rpath = false
# lto = false
# debug-assertions = false
# codegen-units = 16
# panic = 'unwind'
# incremental = false
# overflow-checks = false

[workspace.dependencies]
utility = { path = "./utility" }
dataservice = { path = "./dataservice" }
nodipservice = { path = "./nodipservice" }

serde = { version = "1.0.218", features = ["derive"] }
serde_json = "1.0.140"
tokio = { version = "1.45.1", features = ["full"] }
tokio-util = { version = "0.7.15", features = ["full"] }

axum = { version = "0.8.4", features = ["multipart", "tokio", "original-uri"] }
axum-extra = { version = "0.10.1", features = ["typed-header"] }

tower = { version = "0.5.2", features = ["util", "filter"] }
hyper = { version = "1.6.0", features = ["full"] }
hyper-util = { version = "0.1.11", features = ["client-legacy"] }
tower-http = { version = "0.6.6", features = ["trace", "fs", "cors"] }
headers = "0.4.1"
http-body-util = "0.1.2"
futures = "0.3.31"
futures-util = { version = "0.3" }

anyhow = "1.0.93"
thiserror = "2.0.12"
config = "0.15.11"

log = "0.4.27"
log4rs = "1.3"

tracing = "0.1" # 分布式追踪库
tracing-subscriber = { version = "0.3", features = [
    "env-filter",
    "fmt",
    "time",
] } # tracing 库的订阅者实现
tracing-error = "0.2" # 用于错误跟踪的扩展
tracing-log = "0.2" # 将日志事件推送到 tracing
tracing-appender = "0.2"
file-rotate = "0.8.0"

jsonwebtoken = "9.3.0"
# chrono = "0.4"
mime_guess = "2.0.5"
itertools = "0.14.0"
reqwest = { version = "0.12.20", default-features = false, features = [
    "json",
    "stream",
    "multipart",
    "rustls-tls",
] }

moka = { version = "0.12.5", features = ["future"] }
uuid = { version = "1.17.0", features = ["v4"] }
rust_decimal = "1.37.1"

chrono = { version = "0.4.41", features = ["serde"] }
strum = { version = "0.27.1", features = ["derive"] }

rust_xlsxwriter = { version = "0.88.0" }

calamine = { version = "0.26.0" }
zip = "3.0.0"

base64 = { version = "0.22" }
dashmap = { version = "6.1" }
docx-rs = { version = "0.4" }
evalexpr = { version = "11.3.1" }

mpart-async = { version = "0.7" }                             # "0.7"
num_enum = "0.7.3"
once_cell = "1.21.3"
oracle = "0.6.3"
sqlx = "0.8.3"
strum_macros = "0.27.1"
encoding_rs = "0.8.35"
md-5 = "0.10"
quick-xml = "0.37.5"
yaserde = { version = "0.12", features = ["yaserde_derive"] }

indexmap = { version = "2.9.0" }
glob = "0.3.1"

html-escape = { version = "0.2.5" }
