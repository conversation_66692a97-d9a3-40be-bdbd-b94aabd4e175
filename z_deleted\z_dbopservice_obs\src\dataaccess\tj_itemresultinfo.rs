use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(C<PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjItemresultinfo {
  pub id: i64,
  pub tj_itemid: String,
  pub tj_itemresult: String,
  pub tj_suminfo: String,
  pub tj_sumflag: i32,
  pub tj_showorder: i32,
}
crud!(TjItemresultinfo {}, "tj_itemresultinfo");
rbatis::impl_select!(TjItemresultinfo{query(itemid:&str) => "`where tj_itemid = #{itemid} `"});
rbatis::impl_select!(TjItemresultinfo{query_many(itemids:&[&str]) => 
  "`where id > 0 `
  if !itemids.is_empty():
    ` and tj_itemid in ${itemids.sql()} `
  "});

rbatis::impl_delete!(TjItemresultinfo{delete(ids:&[i64]) => "`where id in ${ids.sql()} `"});

impl TjItemresultinfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjItemresultinfo) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjItemresultinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjItemresultinfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
}
