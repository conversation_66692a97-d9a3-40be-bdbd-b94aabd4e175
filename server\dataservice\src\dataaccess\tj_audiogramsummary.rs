use crate::entities::{prelude::*, tj_audiogramsummary};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;
impl TjAudiogramsummary {
  // pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<TjAudiogramsummary>> {
  //   // let ret = NxJobinfoEntity::find_by_id(id).one(db).await;
  //   let ret = TjAudiogramsummaryEntity::find().filter(ss_area::Column::AreaCode.eq(code)).one(db).await;
  //   if ret.as_ref().is_err() {
  //     // error!("query error:{}", ret.err().unwrap().to_string());
  //     return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
  //   }
  //   Ok(ret.unwrap())
  // }

  pub async fn query_many(db: &DatabaseConnection) -> Result<Vec<TjAudiogramsummary>> {
    let ret = TjAudiogramsummaryEntity::find().all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjAudiogramsummary, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_audiogramsummary::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    Ok(val_id.unwrap())
  }

  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let conditions = Condition::all().add(tj_audiogramsummary::Column::Id.is_in(ids.to_owned()));
    let ret = TjAudiogramsummaryEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
