use anyhow::{anyhow, Result};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;

use crate::dto::ExternalDTO;

use super::yibukeji::Yibu<PERSON>eji;
pub struct WyhsService;

impl WyhsService {
  pub async fn do_upload(
    dto: &ExternalDTO,
    medinfo: &TjMedexaminfo,
    new_extitems: &Vec<ExtCheckiteminfo>,
    exist_items: &Vec<ExtCheckiteminfo>,
    server: &String,
    // uid: &mut UidgenService,
    force: i32,
    db: &DbConnection,
  ) -> Result<String> {
    let ret = YibuKeji::upload_medexaminfo(dto, medinfo, new_extitems, exist_items, server, force, db).await;
    if ret.as_ref().is_err() {
      error!("upload_medexaminfo error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok("".to_string())
  }
  pub async fn receive_lis_result(medinfo: &TjMedexaminfo, server: &String, db: &DbConnection) -> Result<()> {
    // let ret = TjMedexaminfo::query(&testid, &db.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   error!("query exam error:{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let medinfo = ret.unwrap();
    // if medinfo.is_none() {
    //   return Err(anyhow!("不能找到体检编号为{testid}的体检信息"));
    // }
    // let medinfo = medinfo.unwrap();
    let ret = YibuKeji::receive_lis_result(&medinfo, server, db).await;
    if ret.as_ref().is_err() {
      error!("receive lis result error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }

    Ok(())
  }
  pub async fn receive_pacs_result(medinfo: &TjMedexaminfo, server: &String, db: &DbConnection) -> Result<()> {
    // let ret = TjMedexaminfo::query(&testid, &db.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   error!("query exam error:{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let medinfo = ret.unwrap();
    // if medinfo.is_none() {
    //   return Err(anyhow!("不能找到体检编号为{testid}的体检信息"));
    // }
    // let medinfo = medinfo.unwrap();
    let ret = YibuKeji::receive_pacs_result(&medinfo, server, db).await;
    if ret.as_ref().is_err() {
      error!("upload_medexaminfo error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }

    Ok(())
  }

  pub async fn query_lis_items(server: &String) -> Result<Vec<VLisXmxx>> {
    let ret = YibuKeji::query_lis_xmxx(server).await;
    if ret.as_ref().is_err() {
      error!("upload_medexaminfo error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
}
