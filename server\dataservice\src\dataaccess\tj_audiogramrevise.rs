use crate::entities::prelude::*;
use anyhow::{anyhow, Result};
use sea_orm::{DatabaseConnection, EntityTrait};

impl TjAudiogramrevise {
  pub async fn query_many(db: &DatabaseConnection) -> Result<Vec<TjAudiogramrevise>> {
    let ret = TjAudiogramreviseEntity::find().all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
