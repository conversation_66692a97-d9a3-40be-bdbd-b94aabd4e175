use anyhow::{anyhow, Result};
use itertools::Itertools;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;

use crate::{common::constant, dto::HazardDto};

pub struct HazardSvc;

impl HazardSvc {
  pub async fn query_hazardinfos(hdnames: &Vec<String>, db: &DbConnection) -> Result<Vec<TjHazardinfo>> {
    let empty_i32: Vec<i64> = Vec::new();

    let mut hdinfos = crate::SYSCACHE.get().unwrap().get_hazardinfos(&empty_i32, &empty_i32, hdnames, db).await;

    info!("找到的危害因素信息:{:?}", &hdinfos);
    let mut new_hdinfos: Vec<TjHazardinfo> = Vec::new();
    for val in hdnames.into_iter() {
      info!("根据名称：[{}]查找危害因素......", &val);
      let new_name = val.replace("（", "(").replace("）", ")");
      let is_exist = hdinfos.iter().find(|&f| f.tj_hname.eq_ignore_ascii_case(&new_name));
      if is_exist.is_none() {
        info!("危害因素:[{}]不存在，写入新的危害因素......", &new_name);
        //create new hdinfo
        let hdinfo: TjHazardinfo = TjHazardinfo {
          id: 0,
          tj_tid: 1,
          tj_hname: new_name, //.to_string(),
          tj_pyjm: "".to_string(),
          tj_showorder: 0,
          tj_forbiden: "".to_string(),
          tj_memo: "".to_string(),
          tj_extcode: "".to_string(),
          tj_status: 0,
        };
        new_hdinfos.push(hdinfo);
      }
    }
    if new_hdinfos.len() > 0 {
      info!("新的危害因素:{:?}......", &new_hdinfos);
      let ret = TjHazardinfo::save_many(&new_hdinfos, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Hazardinfo as i32, &db).await;
      hdinfos = crate::SYSCACHE.get().unwrap().get_hazardinfos(&empty_i32, &empty_i32, hdnames, db).await;
    }
    Ok(hdinfos)
  }

  pub async fn query_hazardinfos_by_ids(typeids: &Vec<i64>, hdids: &Vec<i64>, hdnames: &Vec<String>, db: &DbConnection) -> Result<Vec<HazardDto>> {
    let hdinfos = crate::SYSCACHE.get().unwrap().get_hazardinfos(&typeids, &hdids, &hdnames, &db).await;
    // info!("Hazard infos:{:?}", &hdinfos);

    let hdids: Vec<i64> = hdinfos.iter().map(|v| v.id).collect();
    let ret = TjHazardlaw::query_many_by_hdid(&hdids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let laws = ret.unwrap();

    let mut result: Vec<HazardDto> = vec![];
    for val in hdinfos.into_iter() {
      let hdlaws: Vec<TjHazardlaw> = laws.iter().filter(|&v| v.tj_hdid as i64 == val.id).map(|v| v.to_owned()).collect();
      let hddto = HazardDto { hdinfo: val, hdlaws };
      result.push(hddto);
    }
    // info!("final result are:{:?}", &result);
    Ok(result)
  }

  pub async fn save_hazardinfo(hdinfo: &TjHazardinfo, hdlaws: &Vec<TjHazardlaw>, db: &DbConnection) -> Result<i64> {
    let mut info = hdinfo.to_owned();
    let mut laws = hdlaws.to_owned();
    let ret = TjHazardinfo::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    if info.id <= 0 {
      info.id = ret.unwrap_or_default();
    }
    let mut exist_laws: Vec<TjHazardlaw> = vec![];
    if hdinfo.id > 0 {
      let ret = TjHazardlaw::query_many_by_hdid(&vec![info.id], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      exist_laws = ret.unwrap();
    }
    for val in laws.iter_mut() {
      val.tj_hdid = info.id as i32;
    }

    let to_delete_laws: Vec<TjHazardlaw> = exist_laws
      .iter()
      .filter(|&f| laws.iter().find(|&nf| nf.tj_lawid == f.tj_lawid).is_none())
      .map(|m| m.to_owned())
      .collect();
    info!("需要删除的依据信息:{:?}", &to_delete_laws);
    let ids: Vec<i64> = to_delete_laws.iter().map(|v| v.id).collect();
    let ret = TjHazardlaw::delete(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let to_insert_laws: Vec<TjHazardlaw> = laws
      .iter()
      .filter(|&f| exist_laws.iter().find(|&ef| ef.tj_lawid == f.tj_lawid).is_none())
      .map(|v| v.to_owned())
      .collect();
    info!("需要新增的依据信息:{:?}", &to_insert_laws);
    let ret = TjHazardlaw::save_many(&to_insert_laws, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    // let ret = TjHazardlaw::save_many(infos, db)
    let dbclone = db.clone();
    tokio::spawn(async move {
      // info!("开始i更新危害因素缓存");
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Hazardinfo as i32, &dbclone).await;
      // info!("更新危害因素缓存。。。。。。");
    });
    Ok(1)
  }
  pub async fn delete_hazardinfos(ids: &Vec<i64>, db: &DbConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let ret = TjHazardinfo::delete(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = TjHazardlaw::delete_by_hdids(ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Hazardinfo as i32, &dbclone).await;
    });
    Ok(1 as u64)
  }
  pub async fn connect_hazardinfos(ids: &Vec<i64>, refid: i64, conitems: i32, conlaw: i32, condis: i32, db: &DbConnection) -> Result<i64> {
    info!("开始把危害因素：{:?}关联到:[{}]", &ids, refid);
    let mut new_ids: Vec<i64> = ids.clone();
    new_ids.push(refid);
    let ret = TjHazardinfo::query_many(&vec![], &new_ids, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut hdinfos = ret.unwrap();
    let ref_hdinfo = hdinfos.iter().find(|&v| v.id == refid);
    if ref_hdinfo.is_none() {
      return Err(anyhow!("can't find ref hazardinfo by id:{refid}"));
    }
    let ref_hdinfo = ref_hdinfo.unwrap().to_owned();
    let mut itemids: Vec<i64> = vec![];
    let mut disids: Vec<i64> = vec![];
    let mut lawhdinfos: Vec<TjHazardinfo> = vec![];
    for val in hdinfos.iter_mut() {
      if val.id == refid {
        continue;
      }
      if conitems == constant::YesOrNo::Yes as i32 {
        //关联检查项目
        itemids.push(val.id);
      }
      if condis == constant::YesOrNo::Yes as i32 {
        disids.push(val.id);
      }
      if conlaw == constant::YesOrNo::Yes as i32 {
        // val.tj_forbiden = ref_hdinfo.tj_forbiden.to_owned();
        val.tj_tid = ref_hdinfo.tj_tid;
        lawhdinfos.push(val.to_owned());
      }
    }
    if lawhdinfos.len() > 0 {
      let ret = TjHazardinfo::save_many(&lawhdinfos, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let ret = TjHazardlaw::query_many_by_hdid(&vec![refid], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let hdlaws = ret.unwrap();
      let ret = TjHazardlaw::delete(&ids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      info!("参考危害因素的评价依据是：{:?}", &hdlaws);
      if hdlaws.len() > 0 {
        let mut hazard_laws: Vec<TjHazardlaw> = vec![];
        for id in ids.iter() {
          for val in hdlaws.iter() {
            let hdl = TjHazardlaw {
              id: 0,
              tj_hdid: id.to_owned() as i32,
              tj_lawid: val.tj_lawid,
            };
            hazard_laws.push(hdl);
          }
        }
        info!("关联后的评价依据：{:?}", &hazard_laws);
        if hazard_laws.len() > 0 {
          let ret = TjHazardlaw::save_many(&hazard_laws, &db.get_connection()).await;
          if ret.as_ref().is_err() {
            error!("{}", ret.as_ref().unwrap_err().to_string());
            return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
          }
        }
      }
    }
    if itemids.len() > 0 {
      //update hazard item
      //查找关联危害因素的检查项目
      let ret = TjHazarditem::query_many(&vec![refid], &vec![], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let refhditems = ret.unwrap();
      if refhditems.len() > 0 {
        //关联危害因素有检查项目信息
        //清除
        let ret = TjHazarditem::delete_by_hid(&itemids, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
        }
        let mut conhditems: Vec<TjHazarditem> = vec![];
        for hdid in itemids.into_iter() {
          conhditems.extend(refhditems.iter().map(|v| {
            let mut newv = v.to_owned();
            newv.id = 0;
            newv.tj_hid = hdid as i32;
            newv
          }));
        }
        // info!("关联的检查项目信息：{:?}", &conhditems);
        let ret = TjHazarditem::save_many(&conhditems, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
    }

    if disids.len() > 0 {
      //update hazard item
      //查找关联危害因素的检查项目
      let ret = TjHazarddisease::query_many(&vec![refid], 0, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let refdisitems = ret.unwrap();
      if refdisitems.len() > 0 {
        //关联危害因素有疾病信息项目信息
        //清除
        let ret = TjHazarddisease::delete_by_hid(&disids, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
        }
        let mut conhddis: Vec<TjHazarddisease> = vec![];
        for hdid in disids.into_iter() {
          conhddis.extend(refdisitems.iter().map(|v| {
            let mut newv = v.to_owned();
            newv.id = 0;
            newv.tj_hid = hdid as i32;
            newv
          }));
        }
        // info!("关联危害因素的疾病信息：{:?}", &conhddis);
        let ret = TjHazarddisease::save_many(&conhddis, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
    }

    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Hazardinfo as i32, &dbclone).await;
    });
    Ok(ids.len() as i64)
  }
  pub async fn query_hazardtypes(db: &DbConnection) -> Result<Vec<TjHazardtype>> {
    // let ret = TjHazardtype::select_all(&mut db.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
    Ok(crate::SYSCACHE.get().unwrap().get_hazardtypes(db).await)
  }
  pub async fn save_hazardtype(info: &TjHazardtype, db: &DbConnection) -> Result<i64> {
    let ret = TjHazardtype::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Hazardtype as i32, &dbclone).await;
    });
    Ok(ret.unwrap())
  }
  pub async fn delete_hazardtype(id: i64, db: &DbConnection) -> Result<i64> {
    if id <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let ret = TjHazardtype::delete(&vec![id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = TjHazardinfo::update_type(id, 1, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Hazardtype as i32, &dbclone).await;
    });
    Ok(ret.unwrap() as i64)
  }

  pub async fn query_hazarditems(ids: &Vec<i64>, testtypes: &Vec<i32>, db: &DbConnection) -> Result<Vec<TjHazarditem>> {
    let ret = TjHazarditem::query_many(&ids, &testtypes, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut results = ret.unwrap();
    results.sort_by(|a, b| a.tj_testtype.cmp(&b.tj_testtype).then(b.tj_oflag.cmp(&a.tj_oflag)));
    Ok(results)
  }
  pub async fn save_hazarditems(infos: &Vec<TjHazarditem>, db: &DbConnection) -> Result<i64> {
    if infos.len() <= 0 {
      return Ok(0);
    }
    info!("保存危害因素的项目信息：{:?}", &infos);
    let hid = infos[0].tj_hid;
    let ret = TjHazarditem::query_many(&vec![hid as i64], &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let exist_hitems = ret.unwrap();
    let delete_hitems: Vec<TjHazarditem> = exist_hitems
      .iter()
      .filter(|&f| {
        infos
          .iter()
          .find(|&f2| f.tj_hid == f2.tj_hid && f.tj_itemid.eq_ignore_ascii_case(&f2.tj_itemid) && f.tj_testtype == f2.tj_testtype && f.tj_oflag == f2.tj_oflag)
          .is_none()
      })
      .map(|v| v.to_owned())
      .collect();
    info!("需要删除的接害因素检查项目:{:?}", &delete_hitems);
    if delete_hitems.len() > 0 {
      let del_ids: Vec<i64> = delete_hitems.iter().map(|v| v.id).collect();
      let ret = TjHazarditem::delete(&del_ids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let insert_hitems: Vec<TjHazarditem> = infos
      .iter()
      .filter(|&f| {
        exist_hitems
          .iter()
          .find(|&f2| f.tj_hid == f2.tj_hid && f.tj_itemid.eq_ignore_ascii_case(&f2.tj_itemid) && f.tj_testtype == f2.tj_testtype && f.tj_oflag == f2.tj_oflag)
          .is_none()
      })
      .map(|v| v.to_owned())
      .collect();
    info!("需要新增的接害因素检查项目:{:?}", &insert_hitems);
    if insert_hitems.len() > 0 {
      let ret = TjHazarditem::save_many(&insert_hitems, &db.get_connection()).await;
      info!("新增危害因素检查项目:{:?}", &ret);
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        // return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    Ok(infos.len() as i64)
  }

  pub async fn delete_hazarditems(ids: &Vec<i64>, db: &DbConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    info!("删除危害因素检查项目:{:?}", &ids);
    let ret = TjHazarditem::delete(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ids.len() as u64)
  }
  pub async fn query_hazarddiseases(ids: &Vec<i64>, testtype: i32, db: &DbConnection) -> Result<Vec<TjHazarddisease>> {
    let ret = TjHazarddisease::query_many(&ids, testtype, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save_hazarddiseases(info: &TjHazarddisease, db: &DbConnection) -> Result<i64> {
    let ret = TjHazarddisease::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn delete_hazarddiseases(ids: &Vec<i64>, db: &DbConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("ids are empty, not allowed"));
    }
    let ret = TjHazarddisease::delete(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ids.len() as u64)
  }

  pub async fn clean_hazardinfos(db: &DbConnection) {
    let ret = TjHazardinfo::query_many(&vec![], &vec![], &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("select error:{}", ret.as_ref().unwrap_err().to_string());
      return;
    }
    let mut clean_hdinfos: Vec<TjHazardinfo> = Vec::new();
    let mut hdinfos = ret.unwrap();
    for val in hdinfos.iter_mut() {
      if val.tj_hname.contains("）") || val.tj_hname.contains("（") || val.tj_hname.contains("、") || val.tj_hname.contains("，") {
        let hdname = val.tj_hname.replace("）", ")").replace("（", "(").replace("、", ",").replace("，", ",");
        val.tj_hname = hdname;
        clean_hdinfos.push(val.to_owned());
      }
    }
    info!("需要调整接害因素：{}", clean_hdinfos.len());
    if clean_hdinfos.len() > 0 {
      let ret = TjHazardinfo::save_many(&clean_hdinfos, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("select error:{}", ret.as_ref().unwrap_err().to_string());
        return;
      }
    }
  }

  pub async fn query_hazard_law(hdids: &Vec<i64>, db: &DbConnection) -> Result<Vec<TjHazardlaw>> {
    let ret = TjHazardlaw::query_many_by_hdid(&hdids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_hazard_laws(hdlaws: &Vec<TjHazardlaw>, db: &DbConnection) -> Result<i64> {
    let ret = TjHazardlaw::save_many(&hdlaws, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn delete_hazard_laws(ids: &Vec<i64>, db: &DbConnection) -> Result<i32> {
    let ret = TjHazardlaw::delete(ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap() as i32)
  }

  pub async fn clean_hazardinfo_evallaw(db: &DbConnection) -> Result<()> {
    let ret = TjHazardinfo::query_many(&vec![], &vec![], &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let _ = TjEvallaw::clear(&db.get_connection()).await;
    let _ = TjHazardlaw::clear(&db.get_connection()).await;
    let mut hdinfos = ret.unwrap();
    let evallaw: Vec<String> = hdinfos.iter().map(|v| v.tj_forbiden.to_string()).collect();
    let evallaw_combine = evallaw.join("，");
    let all_evallaw: Vec<&str> = evallaw_combine.split(&['，', '、'][..]).filter(|&v| !v.is_empty()).collect();
    // info!("all laws:{:#?}", &all_evallaw);
    //去重
    let split_evallaw: Vec<&str> = all_evallaw.into_iter().unique().collect();
    info!("unique laws:{:#?}", &split_evallaw);
    let mut lawinfos: Vec<TjEvallaw> = vec![];
    for val in split_evallaw.into_iter() {
      let law = TjEvallaw {
        id: 0,
        tj_lawname: val.to_string(),
        tj_status: 0,
        tj_memo: "".to_string(),
      };
      lawinfos.push(law);
    }
    let ret = TjEvallaw::save_many(&lawinfos, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = TjEvallaw::query_many(&vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let lawinfos = ret.unwrap();
    for hdinfo in hdinfos.iter_mut() {
      if hdinfo.tj_forbiden.is_empty() {
        continue;
      }
      info!("危害因素：{:?}", &hdinfo);
      let split_evallaw: Vec<&str> = hdinfo.tj_forbiden.split(&['，', '、'][..]).filter(|&v| !v.is_empty()).collect();
      info!("评价依据信息：{:?}", &split_evallaw);
      let hd_lawinfos: Vec<TjEvallaw> = lawinfos
        .iter()
        .filter(|&f| split_evallaw.iter().find(|&lf| f.tj_lawname.eq_ignore_ascii_case(lf)).is_some())
        .map(|v| v.to_owned())
        .collect();
      info!("本危害因素的评价依据:{:?}", &hd_lawinfos);
      let mut hd_eval: Vec<TjHazardlaw> = vec![];
      for eval in hd_lawinfos.iter() {
        if hd_eval.iter().find(|&v| v.tj_lawid == eval.id as i32).is_some() {
          continue;
        }
        let elaw = TjHazardlaw {
          id: 0,
          tj_hdid: hdinfo.id as i32,
          tj_lawid: eval.id as i32,
        };
        hd_eval.push(elaw);
      }
      // info!("保存的评价依据:{:?}", &hd_eval);
      if hd_eval.len() > 0 {
        let ret = TjHazardlaw::save_many(&hd_eval, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("error:{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
        }
      }
    }

    Ok(())
  }
}
