// use std::result;

use axum::extract::Json;
use serde::{Deserialize, Serialize};
use serde_json::Value;

// use crate::common::constant::{HttpCode, EMPTY_STR};
pub static CODE_OK: &str = "200";
pub static CODE_ERROR: &str = "500";

#[derive(Debug, Serialize, Deserialize)]
pub struct DamsResponseBody {
  pub code: String,
  #[serde(rename = "msg")]
  pub message: String,
  pub data: Option<String>,
  #[serde(rename = "requestId")]
  pub requestid: Option<String>,
}

impl DamsResponseBody {
  pub fn new(code: String, message: &str) -> DamsResponseBody {
    DamsResponseBody {
      code: code,
      message: message.to_string(),
      data: None,
      requestid: None,
    }
  }
  // pub fn response_error(error: &str, data: T) -> ResponseBody<T> {
  //   let databody = DataBody::new(1, data);
  //   ResponseBody::new(HttpCode::Error as i32, &error, databody)
  // }
}

// #[derive(Debug, Serialize, Deserialize)]
// pub struct DataBody<T> {
//   pub total: u64,
//   pub data: T,
// }
// impl<T> DataBody<T> {
//   pub fn new(total: u64, data: T) -> DataBody<T> {
//     DataBody { total: total, data: data }
//   }
// }

pub fn response_json_ok_dams(msg: &str) -> Json<Value> {
  let res = DamsResponseBody::new(CODE_OK.to_string(), &msg);
  Json(serde_json::json!(res))
}
pub fn response_json_error_dams(error: &str) -> Json<Value> {
  let res = DamsResponseBody::new(CODE_ERROR.to_string(), &error);
  Json(serde_json::json!(res))
}

// pub fn response_value_ok<T: serde::Serialize>(msg: &str, data: T) -> Json<Value> {
//   let res = ResponseBody::new((HttpCode::OK as i32).to_string(), &msg, data);
//   Json(serde_json::json!(res))
// }
// pub fn response_value_error<T: serde::Serialize>(error: &str, data: T) -> Json<Value> {
//   let res = ResponseBody::new((HttpCode::Error as i32).to_string(), &error, data);
//   Json(serde_json::json!(res))
// }

// pub fn response_json_value_ok<T: serde::Serialize>(total: u64, data: T) -> Json<Value> {
//   let databody = DataBody::new(total, data);
//   let res = ResponseBody::new(HttpCode::OK as i32, EMPTY_STR, databody);
//   Json(serde_json::json!(res))
// }

// pub fn response_json_value_error<T: serde::Serialize>(error: &str, data: T) -> Json<Value> {
//   let databody = DataBody::new(0, data);
//   let res = ResponseBody::new(HttpCode::Error as i32, &error, databody);
//   Json(serde_json::json!(&res))
// }
