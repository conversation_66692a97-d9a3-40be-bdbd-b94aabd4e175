[package]
name = "datacontroller"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = {path = "../utility"}

serde = { version = "1", features = ["derive"] }
rbson = "2.0"

log = "0.4"
fast_log="1.5"

rbatis = { version = "3.1", default-features = false, features = ["mysql","runtime-tokio-rustls"] }

tokio = { version = "^1", features = ["full"] }
anyhow = "^1.0"
rust_decimal = "^1"

chrono = "0.4"
