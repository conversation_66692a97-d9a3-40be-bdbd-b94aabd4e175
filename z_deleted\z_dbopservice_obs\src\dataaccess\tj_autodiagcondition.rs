use anyhow::{anyhow, Result};
use rbatis::rbatis_codegen::IntoSql;
use rbatis::{crud, py_sql};
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjAutodiagcondition {
  pub id: i64,
  pub tj_disid: i64,
  pub tj_hazardfactor: i64,
  pub tj_metype: i32,
  pub tj_itemid: String,
  pub tj_operator: String,
  pub tj_refvalue: String,
  pub tj_conditionsymbol: String,
  pub tj_order: i32,
}
crud!(TjAutodiagcondition {}, "tj_autodiagcondition");
rbatis::impl_delete!(TjAutodiagcondition{delete(ids:&[i64]) => "`where id in ${ids.sql()} `"});

impl TjAutodiagcondition {
  #[py_sql(
    "`select * from tj_autodiagcondition where id >= 0 `
      if !disids.is_empty():
        ` and tj_disid in ${disids.sql()}`
      if !itemids.is_empty():
        ` and tj_itemid in ${itemids.sql()} `
      "
  )]
  pub async fn query_many(rb: &mut rbatis::RBatis, disids: &Vec<i64>, itemids: &Vec<String>) -> Result<Vec<TjAutodiagcondition>, rbatis::Error> {
    impled!()
    // let mut rb = db.get_connection_clone();
    // let ret = TjAutodiagcondition::select_all(&mut rb).await;
    // if ret.as_ref().is_err() {
    //   return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    // }
    // Ok(ret.unwrap())
  }
  pub async fn save(rb: &mut rbatis::RBatis, infos: &TjAutodiagcondition) -> Result<i64> {
    let mut tx = rb.acquire_begin().await.unwrap();
    let mut retid = infos.id;
    if infos.id <= 0 {
      let ret = TjAutodiagcondition::insert(&mut tx, &infos).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if infos.id > 0 {
      let ret = TjAutodiagcondition::update_by_column(&mut tx, &infos, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      retid = ret.unwrap().last_insert_id.into();
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(retid)
  }
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<TjAutodiagcondition>) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<TjAutodiagcondition>, Vec<TjAutodiagcondition>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = TjAutodiagcondition::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = TjAutodiagcondition::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }
}
