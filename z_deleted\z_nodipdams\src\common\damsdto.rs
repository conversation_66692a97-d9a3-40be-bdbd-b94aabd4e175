use serde::{Deserialize, Serialize};

#[derive(Debug, De<PERSON>ult, Serialize, Deserialize)]
pub struct Response<T> {
  #[serde(rename = "code")]
  pub code: String, //200成功  500请求错误
  #[serde(rename = "msg")]
  pub msg: String,
  #[serde(rename = "requestId")]
  pub request_id: Option<String>,
  #[serde(rename = "data")]
  pub data: Option<T>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct AppointMedinfo {
  #[serde(rename = "orderNo")]
  pub order_no: String,
  #[serde(rename = "packageNumber")]
  pub package_number: String,
  #[serde(rename = "name")]
  pub name: String,
  #[serde(rename = "idCard")]
  pub id_card: String,
  #[serde(rename = "sex")]
  pub sex: i32,
  #[serde(rename = "Age")]
  pub age: i32,
  #[serde(rename = "phone")]
  pub phone: String,
  #[serde(rename = "marry")] //1.已婚2.未婚3.未知
  pub marry: i32,
  #[serde(rename = "Job_type")] //1.普工 2.管理人员 3.放射性从业人员 4.焊工5.电工 6.架子工 7.油漆工 8.司机
  pub job_type: i32,
  #[serde(rename = "company")]
  pub company: String,
  #[serde(rename = "comnanrld")]
  pub comnanrld: String,
  #[serde(rename = "hospitalName")]
  pub hospital_name: String,
  #[serde(rename = "checkTime")] //YYYY-MM-DD
  pub check_time: String,
  #[serde(rename = "Physical_examination_package")]
  pub physical_examination_package: i32, //1.常规体检2.职业体检
  #[serde(rename = "Working_years")]
  pub working_years: String,
  #[serde(rename = "Contact_damage")]
  pub contact_damage: String,
  #[serde(rename = "Hazardous_factors")]
  pub hazardous_factors: String,
  #[serde(rename = "Physical_examination_type")] //参数：1.上岗2.在岗 3.离岗
  pub physical_examination_type: i32,
  #[serde(rename = "RESERVESTR1")]
  pub reservestr1: String,
  #[serde(rename = "RESERVESTR2")]
  pub reservestr2: String,
  #[serde(rename = "RESERVESTR3")]
  pub reservestr3: String,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct MedexamResult {
  #[serde(rename = "requestId")]
  pub request_id: String,
  #[serde(rename = "orderNo")]
  pub order_no: String,
  #[serde(rename = "Physical_examination_type")]
  pub physical_examination_type: String,
  #[serde(rename = "data")]
  pub data: Data,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct Data {
  #[serde(rename = "hospitalOrder")]
  pub hospital_order: HospitalOrder,
  #[serde(rename = "packItemLists")]
  pub pack_item_lists: Vec<PackItemLists>, //一级体检分类
  #[serde(rename = "finalInspection")]
  pub final_inspection: FinalInspection,
}
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct HospitalOrder {
  #[serde(rename = "hospitalName")]
  pub hospital_name: String,
  #[serde(rename = "name")]
  pub name: String,
  #[serde(rename = "idCard")]
  pub id_card: String,
  #[serde(rename = "sex")] //0男 1女
  pub sex: i32,
  #[serde(rename = "Age")]
  pub age: i32,
  #[serde(rename = "phone")]
  pub phone: String,
  #[serde(rename = "company")]
  pub company: String,
  #[serde(rename = "checkTime")] //体检时间YYYY-MM-DD
  pub check_time: String,
  #[serde(rename = "Job_type")] //1.普工 2.管理人员 3.放射性从业人员 4.焊工5.电工 6.架子工 7.油漆工 8.司机
  pub job_type: String,
  #[serde(rename = "Medical_history")]
  pub medical_history: String,
  #[serde(rename = "RESERVESTR1")]
  pub reservestr1: String,
  #[serde(rename = "RESERVESTR2")]
  pub reservestr2: String,
  #[serde(rename = "RESERVESTR3")]
  pub reservestr3: String,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct PackItemLists {
  #[serde(rename = "item")]
  pub item: String,
  #[serde(rename = "doctor")]
  pub doctor: String,
  #[serde(rename = "checkTime")]
  pub check_time: String,
  #[serde(rename = "packItemIndexList")]
  pub pack_item_index_list: Vec<PackItemIndexList>,
  #[serde(rename = "RESERVESTR1")]
  pub reservestr1: String,
  #[serde(rename = "RESERVESTR2")]
  pub reservestr2: String,
  #[serde(rename = "RESERVESTR3")]
  pub reservestr3: String,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct PackItemIndexList {
  #[serde(rename = "itemIndex")]
  pub item_index: String,
  #[serde(rename = "result")]
  pub result: String,
  #[serde(rename = "isNormal")] //0否 1是
  pub is_normal: i32,
  #[serde(rename = "IndexType")] //0值 1图；（Ps:检查结果不涉附件时，可默认同步0）
  pub index_type: String,
  #[serde(rename = "identification")]
  pub identification: String,
  #[serde(rename = "reference")]
  pub reference: String,
  #[serde(rename = "units")]
  pub units: String,
  #[serde(rename = "RESERVESTR1")]
  pub reservestr1: String,
  #[serde(rename = "RESERVESTR2")]
  pub reservestr2: String,
  #[serde(rename = "RESERVESTR3")]
  pub reservestr3: String,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct FinalInspection {
  #[serde(rename = "Review_or_not")] //是否需要复查 1 是   2 否
  pub review_or_not: String,
  #[serde(rename = "Review_project")] //复查项目
  //1一般检查 2内科检查3外科检查 4眼科检查 5耳鼻喉科检查 6核电生化检查 7尿常规 8血常规 9放射科项目 10心电图项目 11辐射 12弧光、烟尘 13电工作业 14电工作业 15苯 16机动车驾驶
  pub review_project: String,
  #[serde(rename = "conclusion")]
  pub conclusion: String,
  #[serde(rename = "suggest")]
  pub suggest: String,
  #[serde(rename = "doctor")]
  pub doctor: String,
  #[serde(rename = "checkTime")]
  pub check_time: String,
  #[serde(rename = "RESERVESTR1")]
  pub reservestr1: String,
  #[serde(rename = "RESERVESTR2")]
  pub reservestr2: String,
  #[serde(rename = "RESERVESTR3")]
  pub reservestr3: String,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct MedexamReport {
  #[serde(rename = "fileType")] //体检报告类型,1：个人  2：单位
  pub filetype: String,
  #[serde(rename = "comnanrld")] //当fileTye,为2的时候必填
  pub comnanrld: String,
  #[serde(rename = "idCard")] //
  pub id_card: String,
  #[serde(rename = "requestId")] //
  pub request_id: String,
  #[serde(rename = "file")]
  pub file: String,
  #[serde(rename = "checkTime")]
  pub check_time: String,
  #[serde(rename = "orderNo")] //当fileType为1的时候必填
  pub order_no: String,
}
