[package]
name = "utility"
version = "1.0.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"

anyhow = "1"

tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = [
    "env-filter",
    "fmt",
    "time",
] }
tracing-error = { workspace = true }
tracing-appender = { workspace = true }
file-rotate = { workspace = true }


idcard = "0.3"

chrono = { version = "0.4", features = ["serde"] }
regex = "1.9"
pinyin = "0.10.0"

aes = "0.8"
hex-literal = "0.4"
cbc = "0.1"
base64 = "0.22"
hex = "0.4"
rs-snowflake = "0.6"
#加密库
crypto = { version = "0.5" }
aead = { version = "0.5" }
aes-gcm = "0.10"
rand = "0.8"
# rsa库
# rsa = "0.6"
# rsa = "0.8"
# sha2 = { version = "0.10", default-features = false, features = ["oid"] }
rsa = { version = "0.8", features = ["sha2"] }
# digest = { version = "0.10.5", default-features = false, features = ["alloc", "oid"] }
# chronoutil = "0.2"
md5 = "0.7"
