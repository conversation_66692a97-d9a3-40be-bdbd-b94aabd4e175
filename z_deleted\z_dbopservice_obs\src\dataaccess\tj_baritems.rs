use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(C<PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub struct TjBaritems {
  pub id: i64,
  pub tj_binum: String,
  pub tj_biname: String,
  pub tj_barnum: String,
  pub tj_barflag: i32,
  pub tj_barorder: i32,
}
crud!(TjBaritems {}, "tj_baritems");
rbatis::impl_select!(TjBaritems{query_many_by_ids(ids:&[i64]) => "`where id in ${ids.sql()} `"});
rbatis::impl_select!(TjBaritems{query_many(ids:&[String]) =>
  "`where id > 0 `
  if !ids.is_empty():
    ` and tj_binum in ${ids.sql()} `"});

impl TjBaritems {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjBaritems) -> Result<i64> {
    if info.id > 0 {
      let ret = TjBaritems::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      return Ok(info.id);
    }
    let ret = TjBaritems::insert(rb, info).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap().last_insert_id.into())
  }
}
