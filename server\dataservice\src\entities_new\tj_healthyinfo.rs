//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_healthyinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_testid: String,
  pub tj_pid: String,
  pub tj_surveydate: i32,
  pub tj_smoke: i32,
  pub tj_smokenum: String,
  pub tj_smokeyear: String,
  pub tj_smokemonth: String,
  pub tj_drink: i32,
  pub tj_drinknum: String,
  pub tj_drinkyear: String,
  pub tj_drinkmonth: String,
  pub tj_childrennum: String,
  pub tj_abortionnum: String,
  pub tj_stillbirthnum: String,
  pub tj_prematurenum: String,
  pub tj_abnormalnum: String,
  pub tj_childrenhealthy: String,
  pub tj_menarcheage: String,
  pub tj_period: String,
  pub tj_cycle: String,
  pub tj_menopauseage: String,
  pub tj_families: String,
  pub tj_modtime: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
