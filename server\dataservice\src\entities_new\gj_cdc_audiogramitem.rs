//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "gj_cdc_audiogramitem")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  #[sea_orm(unique)]
  pub cdc_item_code: Option<String>,
  pub cdc_item_name: Option<String>,
  pub cdc_item_pcode: Option<String>,
  pub tj_itemid: Option<String>,
  pub tj_adtype: Option<i32>,
  pub tj_ear: Option<i32>,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
