//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_corpoccureport_file_med")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub report_file_id: i64,
  pub med_id: i64,
  pub create_date: i64,
  pub is_saas: i8,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
