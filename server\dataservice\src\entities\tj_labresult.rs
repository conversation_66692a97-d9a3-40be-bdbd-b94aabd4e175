//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Default, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_labresult")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_clinicid: String,
  pub tj_testid: String,
  pub tj_patientname: String,
  pub tj_sex: String,
  pub tj_origrec: String,
  pub tj_itemid: String,
  pub tj_analyte: String,
  pub tj_shortname: String,
  pub tj_units: String,
  pub tj_final: String,
  pub tj_rn10: String,
  pub tj_ckfw_l: String,
  pub tj_ckfw_h: String,
  pub tj_ckfw: String,
  pub tj_abnormalflag: i32,
  pub tj_displowhigh: String,
  pub tj_senddate: i64,
  pub tj_ordno: String,
  pub tj_testgroup: String,
  pub tj_checkdoctor: String,
  pub tj_recheckdoctor: String,
  pub tj_importer: String,
  pub tj_importdate: i64,
  pub tj_isreceived: i32,
  pub tj_receivdate: i64,
  pub tj_checkdate: String,
  pub tj_recheckdate: String,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
