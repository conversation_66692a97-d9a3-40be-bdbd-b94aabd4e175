#[allow(unused)]
use crate::rusthl7::{Field, Hl7ParseError, Message, Separators};
use anyhow::Result;
use std::fmt::Display;

#[derive(Debug, PartialEq)]
pub struct PidSegment<'a> {
  pub source: &'a str,
  //this initial layout largely stolen from the _other_ hl7 crate: https://github.com/njaremko/hl7
  pub msg_encoding_characters: Separators,
  pub pid1_set_id: Option<Field<'a>>,
  pub pid2_patient_id: Option<Field<'a>>,
  pub pid3_patient_identifier_list: Field<'a>,
  pub pid4_alternate_patient_id: Option<Field<'a>>,
  pub pid5_patient_name: Field<'a>,
  pub pid6_mothers_maiden_name: Option<Field<'a>>,
  pub pid7_date_time_of_birth: Option<Field<'a>>,
  pub pid8_administrative_sex: Option<Field<'a>>,
  pub pid9_patient_alias: Option<Field<'a>>,
  pub pid10_race: Option<Field<'a>>,
  pub pid11_patient_address: Option<Field<'a>>,
  pub pid12_county_code: Option<Field<'a>>,
  pub pid13_phone_number_home: Option<Field<'a>>,
  pub pid14_phone_number_business: Option<Field<'a>>,
  pub pid15_primary_language: Option<Field<'a>>,
  pub pid16_marital_status: Option<Field<'a>>,
  pub pid17_religion: Option<Field<'a>>,
  pub pid18_patient_account_number: Option<Field<'a>>,
  pub pid19_ssn_number_patient: Option<Field<'a>>,
  pub pid20_drivers_license_number_patient: Option<Field<'a>>,
  pub pid21_mothers_identifier: Option<Field<'a>>,
  pub pid22_ethnic_group: Option<Field<'a>>,
  pub pid23_birth_place: Option<Field<'a>>,
  pub pid24_multiple_birth_indicator: Option<Field<'a>>,
  pub pid25_birth_order: Option<Field<'a>>,
  pub pid26_citizenship: Option<Field<'a>>,
  pub pid27_veterans_military_status: Option<Field<'a>>,
  pub pid28_nationality: Option<Field<'a>>,
  pub pid29_patient_death_date_and_time: Option<Field<'a>>,
  pub pid30_patient_death_indicator: Option<Field<'a>>,
  pub pid31_identity_unknown_indicator: Option<Field<'a>>,
  pub pid32_identity_reliability_code: Option<Field<'a>>,
  pub pid33_last_update_date_time: Option<Field<'a>>,
  pub pid34_last_update_facility: Option<Field<'a>>,
  pub pid35_taxonomic_classification_code: Option<Field<'a>>,
  pub pid36_breed_code: Option<Field<'a>>,
  // pub pid37_strain: Option<Field<'a>>,
  // pub pid38_production_class_code: Option<Field<'a>>,
  // pub pid39_tribal_citizenship: Option<Field<'a>>,
  // pub pid40_patient_telecommunication_information: Option<Field<'a>>,
}

#[allow(unused)]
impl<'a> PidSegment<'a> {
  pub fn parse<S: Into<&'a str>>(input: S, delims: &Separators) -> Result<PidSegment<'a>, Hl7ParseError> {
    let input = input.into();

    let mut fields = input.split(delims.field);

    assert!(fields.next().unwrap() == "PID");

    let msh = PidSegment {
      source: input,
      msg_encoding_characters: delims.to_owned(),
      pid1_set_id: Field::parse_optional(fields.next(), delims)?,
      pid2_patient_id: Field::parse_optional(fields.next(), delims)?,
      pid3_patient_identifier_list: Field::parse_mandatory(fields.next(), delims)?,
      pid4_alternate_patient_id: Field::parse_optional(fields.next(), delims)?,
      pid5_patient_name: Field::parse_mandatory(fields.next(), delims)?,
      pid6_mothers_maiden_name: Field::parse_optional(fields.next(), delims)?,
      pid7_date_time_of_birth: Field::parse_optional(fields.next(), delims)?,
      pid8_administrative_sex: Field::parse_optional(fields.next(), delims)?,
      pid9_patient_alias: Field::parse_optional(fields.next(), delims)?,
      pid10_race: Field::parse_optional(fields.next(), delims)?,
      pid11_patient_address: Field::parse_optional(fields.next(), delims)?,
      pid12_county_code: Field::parse_optional(fields.next(), delims)?,
      pid13_phone_number_home: Field::parse_optional(fields.next(), delims)?,
      pid14_phone_number_business: Field::parse_optional(fields.next(), delims)?,
      pid15_primary_language: Field::parse_optional(fields.next(), delims)?,
      pid16_marital_status: Field::parse_optional(fields.next(), delims)?,
      pid17_religion: Field::parse_optional(fields.next(), delims)?,
      pid18_patient_account_number: Field::parse_optional(fields.next(), delims)?,
      pid19_ssn_number_patient: Field::parse_optional(fields.next(), delims)?,
      pid20_drivers_license_number_patient: Field::parse_optional(fields.next(), delims)?,
      pid21_mothers_identifier: Field::parse_optional(fields.next(), delims)?,
      pid22_ethnic_group: Field::parse_optional(fields.next(), delims)?,
      pid23_birth_place: Field::parse_optional(fields.next(), delims)?,
      pid24_multiple_birth_indicator: Field::parse_optional(fields.next(), delims)?,
      pid25_birth_order: Field::parse_optional(fields.next(), delims)?,
      pid26_citizenship: Field::parse_optional(fields.next(), delims)?,
      pid27_veterans_military_status: Field::parse_optional(fields.next(), delims)?,
      pid28_nationality: Field::parse_optional(fields.next(), delims)?,
      pid29_patient_death_date_and_time: Field::parse_optional(fields.next(), delims)?,
      pid30_patient_death_indicator: Field::parse_optional(fields.next(), delims)?,
      pid31_identity_unknown_indicator: Field::parse_optional(fields.next(), delims)?,
      pid32_identity_reliability_code: Field::parse_optional(fields.next(), delims)?,
      pid33_last_update_date_time: Field::parse_optional(fields.next(), delims)?,
      pid34_last_update_facility: Field::parse_optional(fields.next(), delims)?,
      pid35_taxonomic_classification_code: Field::parse_optional(fields.next(), delims)?,
      pid36_breed_code: Field::parse_optional(fields.next(), delims)?,
      // pid37_strain: Field::parse_optional(fields.next(), delims)?,
      // pid38_production_class_code: Field::parse_optional(fields.next(), delims)?,
      // pid39_tribal_citizenship: Field::parse_optional(fields.next(), delims)?,
      // pid40_patient_telecommunication_information: Field::parse_optional(fields.next(), delims)?,
    };

    Ok(msh)
  }
}
/// Common formatter trait implementation for the strongly-typed segment
impl<'a> Display for PidSegment<'a> {
  /// Required for to_string() and other formatter consumers
  fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
    write!(f, "{}", self.source)
  }
}
/// Common clone trait implementation for the strongly-typed segment
impl<'a> Clone for PidSegment<'a> {
  /// Creates a new Message object using _the same source_ slice as the original.
  fn clone(&self) -> Self {
    let delims = self.msg_encoding_characters;
    PidSegment::parse(self.source, &delims).unwrap()
  }
}
/// Extracts header element for external use
pub fn _pid<'a>(msg: &Message<'a>) -> Result<PidSegment<'a>, Hl7ParseError> {
  let seg = msg.segments_by_identifier("PID").unwrap()[0];
  let segment = PidSegment::parse(seg.source, &msg.get_separators()).expect("Failed to parse Pid segment");
  Ok(segment)
}
