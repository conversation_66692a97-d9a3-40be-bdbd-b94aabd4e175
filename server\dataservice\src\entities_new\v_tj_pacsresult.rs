//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "v_tj_pacsresult")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tjbh: String,
  pub brxm: String,
  pub jclx: String,
  pub jcxm: String,
  pub jcmc: String,
  pub imagesight: String,
  pub imagediagnosis: String,
  pub jcys: String,
  pub sxys: String,
  pub bgys: String,
  pub bgrq: String,
  pub sfyc: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
