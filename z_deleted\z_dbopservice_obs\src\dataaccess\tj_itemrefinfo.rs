
use rbatis::{crud, py_sql};
use serde::{Deserialize, Serialize};
use anyhow::{anyhow,Result};
use rbatis::rbatis_codegen::IntoSql;

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjItemrefinfo {
  pub id: i64,
  pub tj_struid: String,
  pub tj_strucode: String,
  pub tj_itemid: String,
  pub tj_memo: String,
}
crud!(TjItemrefinfo {}, "tj_itemrefinfo");
rbatis::impl_select!(TjItemrefinfo{query_many(struids:&[String]) => 
  "`where id > 0 `
  if !struids.is_empty():
    ` and tj_struid in ${struids.sql()} `"});

impl TjItemrefinfo{
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjItemrefinfo, ) -> Result<i64> {
      let retid; // = 0 as i64;
      if info.id > 0 {
        let ret = TjItemrefinfo::update_by_column( rb, &info, "id").await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
      } else {
        let ret = TjItemrefinfo::insert( rb, &info).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
      }
      Ok(retid)
    }
      #[py_sql("delete from tj_itemrefinfo where id = #{id} ")]
  pub async fn delete(rb: &mut rbatis::RBatis, id: i64 ) -> rbatis::Result<()> {
    impled!()
  }
}