# log4rs.yaml
# d, data 日期, 默认为 ISO 9601 格式, 可以通过 {d(%Y-%m-%d %H:%M:%S.%f)} 这种方式改变日期格
# 检查配置文件变动的时间间隔
refresh_rate: 30 seconds
# appender 负责将日志收集到控制台或文件, 可配置多个
appenders:
  stdout:
    kind: console
    encoder:
      pattern: "{d(%Y-%m-%d %H:%M:%S.%3f)} {l} {t} {L} - {m}{n}"
  dblog: #定义rooling_file的appenders
    kind: rolling_file
    encoder:
      pattern: "{d(%Y-%m-%d %H:%M:%S.%3f)} {l} {t} {L} - {m}{n}" #"{d} {l} {t}  {L} - {m}{n}"
    path: log/cdcsqlx.log
    policy:
      kind: compound
      trigger:
        kind: size
        limit: 50 mb
      roller:
        kind: fixed_window
        pattern: "log/cdcsqlx.log.{}"
        base: 1
        count: 8
  roll: #定义rooling_file的appenders
    kind: rolling_file
    encoder:
      pattern: "{d(%Y-%m-%d %H:%M:%S.%3f)} {l} {t} {L} - {m}{n}" #"{d} {l} {t}  {L} - {m}{n}"
    path: log/cdcuploader.log
    policy:
      kind: compound
      trigger:
        kind: size
        limit: 50 mb
      roller:
        kind: fixed_window
        pattern: "log/cdcuploader.log.{}"
        base: 1
        count: 8
# 对全局 log 进行配置
root:
  level: info
  appenders:
    - stdout
    - roll
loggers:
  sqlx:
    level: info
    appenders:
      - dblog
    additive: false
  rbatis:
    level: info
    appenders:
      - dblog
    additive: false
