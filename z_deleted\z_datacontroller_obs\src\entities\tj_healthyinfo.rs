use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_healthyinfo")]
#[derive(Clone, Debug, Default, Serialize, Deserialize)]
pub struct TjHealthyinfo {
    pub id: i64,
    pub tj_testid: String,
    pub tj_pid: String,
    pub tj_surveydate: i32,
    pub tj_smoke: i32,
    pub tj_smokenum: String,
    pub tj_smokeyear: String,
    pub tj_drink: i32,
    pub tj_drinknum: String,
    pub tj_drinkyear: String,
    pub tj_childrennum: String,
    pub tj_abortionnum: String,
    pub tj_stillbirthnum: String,
    pub tj_prematurenum: String,
    pub tj_abnormalnum: String,
    pub tj_childrenhealthy: String,
    pub tj_menarcheage: String,
    pub tj_period: String,
    pub tj_cycle: String,
    pub tj_menopauseage: String,
    pub tj_families: String,
    pub tj_modtime: i32,
}
