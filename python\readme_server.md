# Report Server Executable

This document explains how to build and use the standalone executable version of the report generation server.

## Building the Executable

1. Make sure you have PyInstaller installed:

```bash
pip install pyinstaller
```

2. Build the executable using the spec file:

```bash
pyinstaller reportserver.spec
```

The executable will be created in the `dist` directory.

## Directory Structure

Make sure your project directory has the following structure before building:

```
python/
├── fonts/
│   ├── simhei.ttf
│   └── simsun.ttc
├── images/
├── config/
├── reports/
├── reportserver.py
├── reportserver.spec
└── requirements.txt
```

## Running the Server

The server executable can be run directly:

```bash
reportserver.exe
```

The server will start on `http://localhost:8000`

## API Usage

Once the server is running, you can use the API endpoints:

1. Generate a report:

```bash
curl -X POST http://localhost:8000/generate-report \
  -H "Content-Type: application/json" \
  -d '{
    "rptid": 123,
    "reporttype": 0,
    "pagestyle": 1,
    "splitinrow": 1,
    "outdir": "./reports"
  }'
```

2. Check server health:

```bash
curl http://localhost:8000/health
```

## API Documentation

Once the server is running, you can access the interactive API documentation at:

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Notes

1. The executable includes all necessary dependencies and data files
2. Make sure the output directory exists before generating reports
3. The server requires the same database configuration as the Python script
4. All fonts and images are bundled with the executable
5. The server runs in console mode to show logs and errors

## Troubleshooting

If you encounter any issues:

1. Check if all required directories (fonts, images, config, reports) are present
2. Verify database connection settings
3. Ensure the output directory is writable
4. Check the console output for detailed error messages
5. Make sure port 8000 is not in use by another application

## Configuration

The server uses the same configuration as the original Python script. Make sure your database connection settings are properly configured in the config directory.
