//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, <PERSON><PERSON>ult, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "ss_dictionary")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub ss_typeid: i32,
  pub ss_pid: i32,
  pub ss_short: String,
  pub ss_name: String,
  pub ss_showorder: i32,
  pub ss_memo: String,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
