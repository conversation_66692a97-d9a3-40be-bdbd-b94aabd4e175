use crate::entities::{prelude::*, ss_infoconfig};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter};

impl SsInfoconfig {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<SsInfoconfig>> {
    let ret = SsInfoconfigEntity::find().filter(ss_infoconfig::Column::SsCode.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(/*codes: &Vec<String>,*/ stype: i32, db: &DatabaseConnection) -> Result<Vec<SsInfoconfig>> {
    let mut condition = Condition::all();
    // if codes.len() > 0 {
    //   condition = condition.add(ss_infoconfig::Column::SsCode.is_in(codes.to_owned()));
    // }
    if stype > 0 {
      condition = condition.add(ss_infoconfig::Column::SsType.eq(stype));
    }
    let ret = SsInfoconfigEntity::find().filter(condition).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
