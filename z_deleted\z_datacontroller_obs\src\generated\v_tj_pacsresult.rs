//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "v_tj_pacsresult")]
pub struct Model {
    pub tjbh: Option<String>,
    pub brxm: Option<String>,
    pub jclx: Option<String>,
    pub jcxm: Option<String>,
    pub jcmc: Option<String>,
    pub imagesight: Option<String>,
    pub imagediagnosis: Option<String>,
    pub jcys: Option<String>,
    pub sxys: Option<String>,
    pub bgys: Option<String>,
    pub bgrq: Option<String>,
    pub organizationname: Option<String>,
    pub resultstatus: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
