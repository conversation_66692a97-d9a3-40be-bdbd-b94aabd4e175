//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_diseases_copy1")]
pub struct Model {
  #[sea_orm(column_name = "ID", primary_key)]
  pub id: i32,
  pub tj_discode: String,
  pub tj_disname: String,
  pub tj_disnum: String,
  pub tj_deptid: i32,
  pub tj_type: String,
  pub tj_content: String,
  pub tj_occudiseaseflag: i32,
  pub tj_startage: i32,
  pub tj_endage: i32,
  pub tj_sex: i32,
  pub tj_career: String,
  pub tj_marriage: i32,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_typeid: i32,
  pub tj_opinion: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
