//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_hazarditem")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_hid: i32,
  pub tj_itemid: String,
  pub tj_testtype: i32,
  pub tj_oflag: Option<i8>,
  pub tj_showorder: Option<i32>,
  pub tj_memo: Option<String>,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
