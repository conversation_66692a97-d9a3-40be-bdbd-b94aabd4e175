[package]
name = "ptserver"
version = "0.1.0"
edition = "2021"
description = "build time: 2024-07-07"
resolver = "2"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { path = "../utility" }

serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1", features = ["full"] }
tokio-util = { version = "0.7", features = ["full"] }

axum = { version = "0.7", features = ["multipart", "tokio", "original-uri"] }
axum-extra = { version = "0.9", features = ["typed-header"] }

tower = { version = "0.4.13", features = ["util", "filter"] }
hyper = { version = "1", features = ["full"] }
hyper-util = { version = "0.1", features = ["client-legacy"] }
tower-http = { version = "0.5", features = ["trace", "fs", "cors"] }
headers = "0.4"
http-body-util = "0.1.0"
futures = "0.3"

anyhow = "1.0"
thiserror = "1.0"
config = "0.14"
log = "0.4"
log4rs = "1.3.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
jsonwebtoken = "9"
# chrono = "0.4"
mime_guess = "2"
itertools = "0.13"
reqwest = { version = "0.12", default-features = false, features = [
    "json",
    "stream",
    "multipart",
    "rustls-tls",
] }

uuid = "1"
rust_decimal = "1"

chrono = { version = "0.4", features = ["serde"] }
strum = { version = "0.26", features = ["derive"] }

#非windows平台，用rustls
[target.'cfg(not(target_os = "windows"))'.dependencies]
tiberius = { version = "0.12", default-features = false, features = [
    "rust_decimal",
    "rustls",
    "tds73",
    "chrono",
] }

#Windows平台，用native-tls
[target.'cfg(target_os = "windows")'.dependencies]
tiberius = { version = "0.12", features = ["rust_decimal", "tds73", "chrono"] }
