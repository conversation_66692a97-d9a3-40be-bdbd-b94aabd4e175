# Report Server REST API Documentation

This document describes the REST API endpoints for the Report Server, which provides programmatic access to the report generation functions from `report.py`.

## Base URL

```
http://localhost:8080/api
```

## Authentication

Currently, no authentication is required. This may be added in future versions.

## Content Type

All requests and responses use `application/json` content type unless otherwise specified.

## Error Handling

All endpoints return standard HTTP status codes. Error responses follow this format:

```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "status_code": 400,
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

## Endpoints

### 1. Health Check

Check if the server is running and healthy.

**Endpoint:** `GET /api/health`

**Response:**

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000000",
  "version": "1.0.0"
}
```

### 2. Get Report Types

Get available report types, formats, and page styles.

**Endpoint:** `GET /api/reports/types`

**Response:**

```json
{
  "report_types": [
    {
      "id": 0,
      "name": "职业健康报告",
      "description": "Occupational health report"
    },
    {
      "id": 1,
      "name": "普通体检报告",
      "description": "Normal health examination report"
    },
    { "id": 2, "name": "放射报告", "description": "Radiation report" }
  ],
  "report_formats": [
    { "id": 1, "name": "DOCX", "description": "Microsoft Word document" },
    { "id": 2, "name": "PDF", "description": "Portable Document Format" }
  ],
  "page_styles": [
    { "id": 0, "name": "Portrait", "description": "Portrait orientation" },
    { "id": 1, "name": "Landscape", "description": "Landscape orientation" }
  ]
}
```

### 3. Generate Report

Generate a report based on the provided parameters.

**Endpoint:** `POST /api/reports/generate`

**Request Body:**

```json
{
  "report_id": 12,
  "report_format": 2,
  "page_style": 0,
  "split_in_row": 1,
  "output_directory": "./reports"
}
```

**Parameters:**

- `report_id` (required): Integer - The ID of the report to generate
- `report_format` (required): Integer - 1 for DOCX, 2 for PDF
- `page_style` (optional): Integer - 0 for Portrait, 1 for Landscape (default: 0)
- `split_in_row` (optional): Integer - Split rows parameter (default: 1)
- `output_directory` (optional): String - Custom output directory (default: configured directory)

**Success Response:**

```json
{
  "success": true,
  "message": "Report generated successfully",
  "report_id": 12,
  "output_file": "company-职业-在岗期间-2025-0026.pdf",
  "file_size": 1048576,
  "generation_time": 2.5,
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

**Error Response:**

```json
{
  "success": false,
  "message": "Report generation failed: Database connection error",
  "report_id": 12,
  "generation_time": 0.1,
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### 4. Get Report Status

Get the status of a report generation request.

**Endpoint:** `GET /api/reports/status/{report_id}`

**Response:**

```json
{
  "report_id": 12,
  "status": "completed",
  "message": "Report generated successfully",
  "output_file": "company-职业-在岗期间-2025-0026.pdf",
  "created_at": "2024-01-01T12:00:00.000000",
  "completed_at": "2024-01-01T12:02:30.000000",
  "error_details": null
}
```

**Status Values:**

- `pending`: Request received but not started
- `processing`: Report generation in progress
- `completed`: Report generated successfully
- `failed`: Report generation failed
- `not_found`: Report ID not found

### 5. Download Report

Download a generated report file.

**Endpoint:** `GET /api/reports/download/{filename}`

**Response:** Binary file download with appropriate headers.

**Example:**

```
GET /api/reports/download/company-职业-在岗期间-2025-0026.pdf
```

### 6. List Reports

List all available report files.

**Endpoint:** `GET /api/reports/list`

**Response:**

```json
{
  "reports": [
    {
      "filename": "company-职业-在岗期间-2025-0026.pdf",
      "file_path": "/path/to/reports/company-职业-在岗期间-2025-0026.pdf",
      "file_size": 1048576,
      "created_at": "2024-01-01T12:00:00.000000",
      "report_type": "职业健康报告",
      "report_format": "PDF"
    }
  ],
  "total_count": 1,
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

## Configuration

The server can be configured using environment variables:

- `REPORT_SERVER_HOST`: Server host (default: "0.0.0.0")
- `REPORT_SERVER_PORT`: Server port (default: 8080)
- `REPORT_SERVER_DEBUG`: Debug mode (default: false)
- `REPORT_OUTPUT_DIR`: Output directory for reports (default: "./reports")
- `REPORT_LOG_LEVEL`: Logging level (default: "INFO")

## Usage Examples

### Using curl

```bash
# Health check
curl http://localhost:8080/api/health

# Get report types
curl http://localhost:8080/api/reports/types

# Generate a PDF report
curl -X POST http://localhost:8080/api/reports/generate \
  -H "Content-Type: application/json" \
  -d '{
    "report_id": 12,
    "report_format": 2,
    "page_style": 0,
    "split_in_row": 1
  }'

# Check report status
curl http://localhost:8080/api/reports/status/12

# List available reports
curl http://localhost:8080/api/reports/list

# Download a report
curl -O http://localhost:8080/api/reports/download/company-职业-在岗期间-2025-0026.pdf
```

### Using Python requests

```python
import requests

# Generate a report
response = requests.post('http://localhost:8080/api/reports/generate', json={
    'report_id': 12,
    'report_format': 2,
    'page_style': 0,
    'split_in_row': 1
})

if response.status_code == 200:
    result = response.json()
    print(f"Report generated: {result['output_file']}")
else:
    print(f"Error: {response.json()['message']}")
```

## Starting the Server

### Development Server

1. Install dependencies:

   ```bash
   pip install -r requirements.txt
   ```

2. Start the development server:

   ```bash
   python start_server.py
   ```

   **Note:** You'll see a warning about using the development server. This is normal for development/testing.

### Production Server (Recommended)

For production use, use a proper WSGI server:

#### Option 1: Automatic Production Setup

```bash
# Windows
start_production.bat

# PowerShell
.\start_production.ps1

# Python (cross-platform)
python start_production.py
```

#### Option 2: Manual Production Setup

```bash
# Install a production WSGI server
pip install gunicorn    # Linux/Mac (recommended)
pip install waitress    # Windows (recommended)

# Start with Gunicorn (Linux/Mac)
gunicorn --config gunicorn.conf.py wsgi:app

# Start with Waitress (Windows)
waitress-serve --host=0.0.0.0 --port=8080 wsgi:app

# Start with custom Gunicorn settings
gunicorn --bind 0.0.0.0:8080 --workers 4 --timeout 300 wsgi:app
```

3. The server will start on `http://localhost:8080` by default.

## Notes

- The server requires the same database configuration as the main application (`config/nodipexam.toml`)
- Font files should be available in the `./fonts` directory for proper report generation
- Generated reports are stored in the configured output directory (default: `./reports`)
- The server maintains an in-memory cache of report generation status
