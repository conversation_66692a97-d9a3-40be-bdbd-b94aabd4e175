use serde::{Deserialize, Serialize};

use crate::external::herenkj::Params;

//调用服务更新检查申请状态
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct OutboundResponseDto<T> {
  #[serde(rename = "resultCode")]
  pub result_code: String, //0表示成功，107表示安全访问令牌过期，非0表示失败
  #[serde(rename = "descMessage")]
  pub desc_message: String, //成功返回成功；失败返回失败原因。
  // #[serde(rename = "result")]
  // pub patient_id: Option<String>, //填写病人id
  pub result: Option<T>, //结果信息
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct SyncPatientResult {
  #[serde(rename = "patientID")]
  pub patient_id: String,
  #[serde(rename = "visitNo")]
  pub visit_no: String,
}

//调用服务更新检查申请状态
#[derive(Debug, Serialize, Deserialize)]
pub struct OutboundRequestDto<T> {
  // #[serde(rename = "accessToken")]
  // pub access_token: String, //0表示成功，107表示安全访问令牌过期，非0表示失败
  #[serde(rename = "serviceName")]
  pub service_name: String, //成功返回成功；失败返回失败原因。
  // #[serde(rename = "serviceVersion")]
  // pub service_version: String, //填写病人id
  // #[serde(rename = "params")]
  pub params: Params<T>,
}

// #[derive(Serialize, Default, Deserialize, Debug)]
// // #[serde(rename = "args")]
// pub struct Params<T> {
//   pub args: T,
// }

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
#[serde(rename = "args")]
pub struct PatBasicInfo {
  #[serde(rename = "patName")]
  pub patname: String, //
  pub sex: String,
  pub birthday: String, //YYYY-MM-DD
  #[serde(rename = "idCard")]
  pub idcard: String, //
  #[serde(rename = "phoneNumber")]
  pub phonenumber: String, //
  #[serde(rename = "companyName")]
  pub companyname: String, //
  #[serde(rename = "physicalType")]
  pub physicaltype: String, //1-个人 2-单位
  #[serde(rename = "regDate")]
  pub regdate: String, //申请时间
  #[serde(rename = "idType")]
  pub idtype: String, //卡类型
  pub address: String,
  #[serde(rename = "professionalCode")]
  pub professionalcode: String, //职业编码
  #[serde(rename = "professionalName")]
  pub professionalname: String, //职业名称
  #[serde(rename = "physicalExamId")]
  pub physical_exam_id: String, //职业名称
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
#[serde(rename = "args")]
pub struct ChargeInfo {
  #[serde(rename = "physicalID")]
  pub physicalid: String, //
  #[serde(rename = "physicalType")]
  pub physicaltype: String, //
  #[serde(rename = "name")]
  pub patname: String, //
  pub sex: String, //
  pub age: String, //
  #[serde(rename = "companyName")]
  pub companyname: String, //
  #[serde(rename = "enterDateTime")]
  pub enterdatetime: String, //登记日期
  pub charges: String, //金额
  #[serde(rename = "OperationID")]
  pub operationid: String, //YYYY-MM-DD
  #[serde(rename = "performedBy")]
  pub performedby: String, //
  #[serde(rename = "patientID")]
  pub patientid: String, //门诊
  #[serde(rename = "cancelledFlag")]
  pub cancelledflag: String, //0-正常 1-作废
  #[serde(rename = "vipflag")]
  pub vipflag: String, //vipflag
  #[serde(rename = "vipCharges")]
  pub vipcharges: String, //Vip自费金额
  #[serde(rename = "orderedEmpId")]
  pub ordered_emp_id: String, //Vip自费金额
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PatientInfo {
  #[serde(rename = "PatSex")]
  pub pat_sex: String, // M=男性, F=女性, O=未知
  #[serde(rename = "Name")]
  pub name: String,
  #[serde(rename = "MaritalStatus")]
  pub marital_status: String, // M=已婚, B=未婚, D=离婚, W=丧偶, O=其他
  #[serde(rename = "MaritalStatusCode")]
  pub marital_status_code: String, // M=已婚, B=未婚, D=离婚, W=丧偶, O=其他
  #[serde(rename = "BirthPlace")]
  pub birth_place: String,
  #[serde(rename = "BirthPlaceCode")]
  pub birth_place_code: String,
  #[serde(rename = "PresentAddressProvice")]
  pub present_address_province: String,
  #[serde(rename = "PresentAddressCity")]
  pub present_address_city: String,
  #[serde(rename = "PresentAddressCounty")]
  pub present_address_county: String,
  #[serde(rename = "PresentAddressOthers")]
  pub present_address_others: String,
  #[serde(rename = "PresentAddressZipcode")]
  pub present_address_zipcode: String,
  #[serde(rename = "IDNO")]
  pub id_no: String,
  #[serde(rename = "PhoneNumber")]
  pub phone_number: String,
  #[serde(rename = "PhoneNumberBusiness")]
  pub phone_number_business: String,
  #[serde(rename = "PhoneNumberHome")]
  pub phone_number_home: String,
  #[serde(rename = "BirthPlaceName")]
  pub birth_place_name: String,
  #[serde(rename = "Nation")]
  pub nation: String,
  #[serde(rename = "NationCode")]
  pub nation_code: String,
  #[serde(rename = "CityzenShipName")]
  pub citizenship_name: String,
  #[serde(rename = "CityzenShipCode")]
  pub citizenship_code: String,
  #[serde(rename = "UnitInContrName")]
  pub unit_in_contract_name: String,
  #[serde(rename = "JobCode")]
  pub job_code: String,
  #[serde(rename = "JobName")]
  pub job_name: String,
  #[serde(rename = "NextOfKin")]
  pub next_of_kin: String,
  #[serde(rename = "NextOfKinPhone")]
  pub next_of_kin_phone: String,
  #[serde(rename = "NextOfKinZipCode")]
  pub next_of_kin_zip_code: String,
  #[serde(rename = "NextOfKinAddr")]
  pub next_of_kin_address: String,
  #[serde(rename = "RelationshipCode")]
  pub relationship_code: String,
  #[serde(rename = "Relationship")]
  pub relationship: String,
  #[serde(rename = "HealthCardType")]
  pub health_card_type: String,
  #[serde(rename = "HealthCardNo")]
  pub health_card_no: String,
  #[serde(rename = "PatAge")]
  pub pat_age: String,
  #[serde(rename = "DOB")]
  pub dob: String,
  //  检查
  #[serde(rename = "Address")]
  pub address: Option<String>,
  #[serde(rename = "MailingAddress")]
  pub mailing_address: Option<String>,
  #[serde(rename = "ZipCode")]
  pub zip_code: Option<String>,
  //检验
  #[serde(rename = "HospitalCardType")]
  pub hospital_card_type: Option<String>, // 医院卡号类型
  #[serde(rename = "HospitalCardNo")]
  pub hospital_card_no: Option<String>, // 医院卡号
  #[serde(rename = "VIPIndicator")]
  pub vip_indicator: Option<String>, // VIP号
  #[serde(rename = "ChargeType")]
  pub charge_type: Option<String>, // 费用类别
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
#[serde(rename = "args")]
pub struct MedexamFunctionApplication {
  #[serde(rename = "OrderInfo")]
  pub orderinfo: FunctionalOrderInfo,
  #[serde(rename = "PatientInfo")]
  pub patientinfo: PatientInfo,
  #[serde(rename = "PatVisitInfo")]
  pub patvisitinfo: PatVisitInfo,
  #[serde(rename = "GMSpecimen")]
  pub gmspecimen: GmSpecimen,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
#[serde(rename = "args")]
pub struct MedexamLisApplication {
  #[serde(rename = "OrderInfo")]
  pub orderinfo: LabOrderInfo,
  #[serde(rename = "PatientInfo")]
  pub patientinfo: PatientInfo,
  #[serde(rename = "PatVisitInfo")]
  pub patvisitinfo: PatVisitInfo,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
#[serde(rename = "args")]
pub struct FunctionalOrderInfo {
  #[serde(rename = "physicalID")]
  pub physicalid: String, //
  #[serde(rename = "ApplyNo")]
  pub apply_no: String, //申请单号
  #[serde(rename = "PatientID")]
  pub patient_id: String, //病历号
  #[serde(rename = "ChargeType")]
  pub charge_type: String, //费别
  #[serde(rename = "Costs")]
  pub costs: String, //费用
  #[serde(rename = "VisitDate")]
  pub visit_date: String, //就诊日期以YYYYMMDD HH24:MI:SS格式传入
  #[serde(rename = "OrderNo")]
  pub order_no: String, //医嘱号
  #[serde(rename = "ClinDiag")]
  pub clin_diag: String, //临床诊断
  #[serde(rename = "PhysSign")]
  pub phys_sign: String, //体征
  #[serde(rename = "ClinSymp")]
  pub clin_symp: String, //临床症状
  #[serde(rename = "RelevantDiag")]
  pub relevant_diag: String, //其他诊断
  #[serde(rename = "ExamReason")]
  pub exam_reason: String, //检查目的
  #[serde(rename = "Notice")]
  pub notice: String, //注意事项
  #[serde(rename = "Status")]
  pub status: String, //检查状态  1、开立  2、作废 3、修改
  #[serde(rename = "ChargeIndicator")]
  pub charge_indicator: String, //收费状态 0、未收费 1、已收费  2、未知
  #[serde(rename = "PatientSource")]
  pub patient_source: String, //病人来源
  #[serde(rename = "ExamClassCode")]
  pub exam_class_code: String, //检查类别代码
  #[serde(rename = "ExamClassName")]
  pub exam_class_name: String, //检查类别名称
  #[serde(rename = "ReqDeptNo")]
  pub req_dept_no: String, //科申请室代码
  #[serde(rename = "ReqDeptName")]
  pub req_dept_name: String, //申请科室名称
  #[serde(rename = "ReqPhysicianID")]
  pub req_physician_id: String, //申请医生ID
  #[serde(rename = "ReqPhysician")]
  pub req_physician: String, //申请医生名称
  #[serde(rename = "ReqDateTime")]
  pub req_date_time: String, //申请时间
  #[serde(rename = "ReqMemo")]
  pub req_memo: String, // 申请备注
  #[serde(rename = "Priority")]
  pub priority: String, // 优先标志: 1=紧急, 0=普通
  #[serde(rename = "SchDateTime")]
  pub sch_date_time: String, // 预约安排时间
  #[serde(rename = "SchMemo")]
  pub sch_memo: String, // 预约说明
  #[serde(rename = "PerformedBy")]
  pub performed_by: String, // 执行科室代码
  #[serde(rename = "PerformedByName")]
  pub performed_by_name: String, // 执行科室名称
  #[serde(rename = "ReqAreaCode")]
  pub req_area_code: String, // 开单院区
  #[serde(rename = "PerformedAreaCode")]
  pub performed_area_code: String, // 执行院区
  #[serde(rename = "TechnicianId")]
  pub technician_id: String, // 检查技师工号
  #[serde(rename = "Technician")]
  pub technician: String, // 检查技师姓名
  #[serde(rename = "ExamDateTime")]
  pub exam_date_time: String, // 检查时间
  #[serde(rename = "VerifierDocNo")]
  pub verifier_doc_no: String, // 审核医生工号
  #[serde(rename = "VerifierDocName")]
  pub verifier_doc_name: String, // 审核医生姓名
  #[serde(rename = "VerifierDocPhoneNo")]
  pub verifier_doc_phone_no: String, // 审核医生电话
  #[serde(rename = "VerifierDateTime")]
  pub verifier_date_time: String, // 审核时间 2019-06-06 00:00:00
  #[serde(rename = "BabyNo")]
  pub baby_no: String, // 婴儿标识
  #[serde(rename = "ExamItems")]
  pub exam_items: Vec<ExamItem>, // 申请项目列表
  #[serde(rename = "PhoneNo")]
  pub phone_no: String, // 患者联系方式
  #[serde(rename = "ChiefComplaint")]
  pub chief_complaint: String, // 主述
  #[serde(rename = "PhysicalExam")]
  pub physical_exam: String, // 体征
  #[serde(rename = "DeliveryDate")]
  pub delivery_date: String, // 送检日期
  #[serde(rename = "AttendingDate")]
  pub attending_date: String, // 主诊日期
  #[serde(rename = "DiagnosisType")]
  pub diagnosis_type: String, // 诊断类型
  #[serde(rename = "SliceAmount")]
  pub slice_amount: String, // 切片张数
  #[serde(rename = "Memo")]
  pub memo: String, // 其他
  #[serde(rename = "LaboratoryExam")]
  pub laboratory_exam: String, // 实验室检查
  #[serde(rename = "ImagingExam")]
  pub imaging_exam: String, // 影像学检查
  #[serde(rename = "PreoperativeRadiotherapyDate")]
  pub preoperative_radiotherapy_date: String, // 术前放疗时间
  #[serde(rename = "PostoperativeRadiotherapy")]
  pub postoperative_radiotherapy: String, // 术后放疗
  #[serde(rename = "PostoperativeRadiotherapyDate")]
  pub postoperative_radiotherapy_date: String, // 术后放疗时间
  #[serde(rename = "ExamPurpose")]
  pub exam_purpose: String, // 检查目的
  #[serde(rename = "SpecialExamItem")]
  pub special_exam_item: String, // 特殊检查项目
  #[serde(rename = "SpecializedExam")]
  pub specialized_exam: String, // 专科检查
  #[serde(rename = "LeafageNodeId")]
  pub leafage_node_id: String, // 仪器号
  #[serde(rename = "Empty")]
  pub empty: String, // 空腹标志
  #[serde(rename = "SchTime")]
  pub sch_time: String, // 预约时间
  #[serde(rename = "HistorySummary")]
  pub history_summary: String, // 病史摘要
  #[serde(rename = "YjRemark")]
  pub yj_remark: String, // 医生备注
  #[serde(rename = "PatientType")]
  pub patient_type: String, // 病人类型: O=门诊, I=住院, E=急诊, T=体检
  #[serde(rename = "RegisterFlag")]
  pub register_flag: String, // 预约标识: 0=无需预约, 1=预约
  #[serde(rename = "WardCode")]
  pub ward_code: String, // 病区代码
  #[serde(rename = "WardName")]
  pub ward_name: String, // 病区名称
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct FunctionalPatientInfo {
  #[serde(rename = "PatSex")]
  pub pat_sex: String, // M=男性, F=女性, O=未知
  #[serde(rename = "Name")]
  pub name: String,
  #[serde(rename = "MaritalStatus")]
  pub marital_status: String, // M=已婚, B=未婚, D=离婚, W=丧偶, O=其他
  #[serde(rename = "MaritalStatusCode")]
  pub marital_status_code: String, // M=已婚, B=未婚, D=离婚, W=丧偶, O=其他
  #[serde(rename = "Address")]
  pub address: String,
  #[serde(rename = "MailingAddress")]
  pub mailing_address: String,
  #[serde(rename = "ZipCode")]
  pub zip_code: String,
  #[serde(rename = "BirthPlace")]
  pub birth_place: String,
  #[serde(rename = "BirthPlaceCode")]
  pub birth_place_code: String,
  #[serde(rename = "PresentAddressProvice")]
  pub present_address_province: String,
  #[serde(rename = "PresentAddressCity")]
  pub present_address_city: String,
  #[serde(rename = "PresentAddressCounty")]
  pub present_address_county: String,
  #[serde(rename = "PresentAddressOthers")]
  pub present_address_others: String,
  #[serde(rename = "PresentAddressZipcode")]
  pub present_address_zipcode: String,
  #[serde(rename = "IDNO")]
  pub id_no: String,
  #[serde(rename = "PhoneNumber")]
  pub phone_number: String,
  #[serde(rename = "PhoneNumberBusiness")]
  pub phone_number_business: String,
  #[serde(rename = "PhoneNumberHome")]
  pub phone_number_home: String,
  #[serde(rename = "BirthPlaceName")]
  pub birth_place_name: String,
  #[serde(rename = "Nation")]
  pub nation: String,
  #[serde(rename = "NationCode")]
  pub nation_code: String,
  #[serde(rename = "CityzenShipName")]
  pub citizenship_name: String,
  #[serde(rename = "CityzenShipCode")]
  pub citizenship_code: String,
  #[serde(rename = "UnitInContrName")]
  pub unit_in_contract_name: String,
  #[serde(rename = "JobCode")]
  pub job_code: String,
  #[serde(rename = "JobName")]
  pub job_name: String,
  #[serde(rename = "NextOfKin")]
  pub next_of_kin: String,
  #[serde(rename = "NextOfKinPhone")]
  pub next_of_kin_phone: String,
  #[serde(rename = "NextOfKinZipCode")]
  pub next_of_kin_zip_code: String,
  #[serde(rename = "NextOfKinAddr")]
  pub next_of_kin_address: String,
  #[serde(rename = "RelationshipCode")]
  pub relationship_code: String,
  #[serde(rename = "Relationship")]
  pub relationship: String,
  #[serde(rename = "HealthCardType")]
  pub health_card_type: String,
  #[serde(rename = "HealthCardNo")]
  pub health_card_no: String,
  #[serde(rename = "PatAge")]
  pub pat_age: String,
  #[serde(rename = "DOB")]
  pub dob: String,
}

// 检查项目子结构体
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ExamItem {
  #[serde(rename = "ItemNo")]
  pub item_no: String, // 项目ID
  #[serde(rename = "ExamItem")]
  pub exam_item: String, // 检查子项目名称
  #[serde(rename = "ExamItemCode")]
  pub exam_item_code: String, // 检查子项目代码
  #[serde(rename = "ExamSubClass")]
  pub exam_sub_class: String, // 检查子项类型
  #[serde(rename = "ExamThirdItemCode")]
  pub exam_third_item_code: String, // 检查孙项代码
  #[serde(rename = "RefOrderNo")]
  pub ref_order_no: String, // 关联医嘱父ID
  #[serde(rename = "RefOrderSubNo")]
  pub ref_order_sub_no: String, // 关联医嘱子ID
  #[serde(rename = "CollectionVolume")]
  pub collection_volume: String, // 标本数量
  #[serde(rename = "SpecimenActionCode")]
  pub specimen_action_code: String, // 标本处理动作代码
  #[serde(rename = "RelevantClinicalInfo")]
  pub relevant_clinical_info: String, // 相关诊断信息
  #[serde(rename = "SpecimenSourceName")]
  pub specimen_source_name: String, // 标本名称及部位信息
  #[serde(rename = "SpecimenSourcePart")]
  pub specimen_source_part: String, // 标本部位
  #[serde(rename = "NumbersOfSampleContainers")]
  pub numbers_of_sample_containers: String, // 标本容器数
  #[serde(rename = "CollectorComment")]
  pub collector_comment: String, // 送检医生备注
  #[serde(rename = "Costs")]
  pub costs: String, // 费用
}

// 检验主申请单信息结构体
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct LabOrderInfo {
  #[serde(rename = "OrderID")]
  pub order_id: String, // 医嘱号
  #[serde(rename = "physicalID")]
  pub physical_id: String, // 体检号
  #[serde(rename = "ApplyNo")]
  pub apply_no: String, // 申请单号
  #[serde(rename = "PriorityIndicator")]
  pub priority_indicator: String, // 优先标志: 1=紧急, 0=普通
  #[serde(rename = "PatientID")]
  pub patient_id: String, // 病历号
  #[serde(rename = "PatientSource")]
  pub patient_source: String, // 病人来源类型
  #[serde(rename = "SubjectCode")]
  pub subject_code: String, // 检验主题代码
  #[serde(rename = "SubjectName")]
  pub subject_name: String, // 检验主题名称
  #[serde(rename = "TestCauseCode")]
  pub test_cause_code: String, // 检验目的代码
  #[serde(rename = "TestCauseName")]
  pub test_cause_name: String, // 检验目的名称
  #[serde(rename = "RelevantClinicDiag")]
  pub relevant_clinic_diag: String, // 临床诊断
  #[serde(rename = "NotesForSpcm")]
  pub notes_for_spcm: String, // 标本说明
  #[serde(rename = "SpcmSampleDateTime")]
  pub spcm_sample_date_time: String, // 标本采样时间
  #[serde(rename = "SpcmReceviedDateTime")]
  pub spcm_received_date_time: String, // 标本收到时间
  #[serde(rename = "RequestedDateTime")]
  pub requested_date_time: String, // 申请时间
  #[serde(rename = "OrderingDeptCode")]
  pub ordering_dept_code: String, // 申请科室代码
  #[serde(rename = "OrderingDeptName")]
  pub ordering_dept_name: String, // 申请科室名称
  #[serde(rename = "OrderingProviderID")]
  pub ordering_provider_id: String, // 申请医生ID
  #[serde(rename = "OrderingProviderName")]
  pub ordering_provider_name: String, // 申请医生姓名
  #[serde(rename = "PerformedBy")]
  pub performed_by: String, // 执行科室
  #[serde(rename = "PerformedByCode")]
  pub performed_by_code: String, // 执行科室代码
  #[serde(rename = "ExecuteDoctorID")]
  pub execute_doctor_id: String, // 执行医生ID
  #[serde(rename = "ExecuteDoctorName")]
  pub execute_doctor_name: String, // 执行医生姓名
  #[serde(rename = "ReqAreaCode")]
  pub req_area_code: String, // 开单院区
  #[serde(rename = "PerformedAreaCode")]
  pub performed_area_code: String, // 执行院区
  #[serde(rename = "ChargeIndicate")]
  pub charge_indicate: String, // 收费状态: 0=未收费, 1=已收费
  #[serde(rename = "VerifiedDateTime")]
  pub verified_date_time: String, // 校对时间
  #[serde(rename = "VerifiedBy")]
  pub verified_by: String, // 校对者
  #[serde(rename = "VerifiedByID")]
  pub verified_by_id: String, // 校对者ID
  #[serde(rename = "AuditDateTime")]
  pub audit_date_time: String, // 审核时间
  #[serde(rename = "AuditDoctor")]
  pub audit_doctor: String, // 审核医生
  #[serde(rename = "AuditDoctorID")]
  pub audit_doctor_id: String, // 审核医生ID
  #[serde(rename = "Costs")]
  pub costs: String, // 费用
  #[serde(rename = "ChargeType")]
  pub charge_type: String, // 费用类别
  #[serde(rename = "specimenCode")]
  pub specimen_code: String, // 样本编号
  #[serde(rename = "specimenName")]
  pub specimen_name: String, // 样本名称
  #[serde(rename = "Charges")]
  pub charges: String, // 应收费用
  #[serde(rename = "LabOrderItems")]
  pub lab_order_items: Vec<LabOrderItem>, // 检验项目列表
  #[serde(rename = "PurposeOfInspection")]
  pub purpose_of_inspection: String, // 检验目的
  #[serde(rename = "DrugResistanceType")]
  pub drug_resistance_type: String, // 耐药类型
  #[serde(rename = "SampleCharacter")]
  pub sample_character: String, // 样本性状
  #[serde(rename = "rcptno")]
  pub rcptno: String, // 发票号
}

// 检验项目子结构体
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct LabOrderItem {
  #[serde(rename = "ItemNo")]
  pub item_no: String, // 项目序号
  #[serde(rename = "ItemName")]
  pub item_name: String, // 项目名称
  #[serde(rename = "ItemCode")]
  pub item_code: String, // 项目代码
  #[serde(rename = "SpcmSampleDateTime")]
  pub spcm_sample_date_time: String, // 标本采样日期及时间
  #[serde(rename = "SpcmReceivedDateTime")]
  pub spcm_received_date_time: String, // 标本收到日期及时间
  #[serde(rename = "ExecuteDate")]
  pub execute_date: String, // 执行日期
  #[serde(rename = "Notes")]
  pub notes: String, // 备注
}

// 病人基本信息结构体
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct LabPatientInfo {
  #[serde(rename = "PatSex")]
  pub pat_sex: String, // 性别: M=男性, F=女性, O=未知
  #[serde(rename = "Name")]
  pub name: String, // 病人姓名
  #[serde(rename = "ChargeType")]
  pub charge_type: String, // 费用类别
  #[serde(rename = "MaritalStatus")]
  pub marital_status: String, // 婚姻状况: M=已婚, B=未婚, D=离婚, W=丧偶, O=其他
  #[serde(rename = "MaritalStatusCode")]
  pub marital_status_code: String, // 婚姻状况代码
  #[serde(rename = "BirthPlace")]
  pub birth_place: String, // 出生地
  #[serde(rename = "BirthPlaceCode")]
  pub birth_place_code: String, // 出生地代码
  #[serde(rename = "PresentAddressProvice")]
  pub present_address_province: String, // 现住地址省份
  #[serde(rename = "PresentAddressCity")]
  pub present_address_city: String, // 所在城市
  #[serde(rename = "PresentAddressCounty")]
  pub present_address_county: String, // 所在县
  #[serde(rename = "PresentAddressOthers")]
  pub present_address_others: String, // 详细地址
  #[serde(rename = "PresentAddressZipcode")]
  pub present_address_zipcode: String, // 所在地邮编
  #[serde(rename = "IDNO")]
  pub id_no: String, // 身份证或军官证
  #[serde(rename = "PhoneNumber")]
  pub phone_number: String, // 电话号码
  #[serde(rename = "PhoneNumberBusiness")]
  pub phone_number_business: String, // 单位电话号码
  #[serde(rename = "PhoneNumberHome")]
  pub phone_number_home: String, // 家庭电话号码
  #[serde(rename = "BirthPlaceName")]
  pub birth_place_name: String, // 出生地名字
  #[serde(rename = "Nation")]
  pub nation: String, // 民族
  #[serde(rename = "NationCode")]
  pub nation_code: String, // 民族代码
  #[serde(rename = "CityzenShipName")]
  pub citizenship_name: String, // 国籍名称
  #[serde(rename = "CityzenShipCode")]
  pub citizenship_code: String, // 国籍代码
  #[serde(rename = "UnitInContrName")]
  pub unit_in_contr_name: String, // 合同单位名称
  #[serde(rename = "JobCode")]
  pub job_code: String, // 工作代码
  #[serde(rename = "JobName")]
  pub job_name: String, // 工作名称
  #[serde(rename = "NextOfKin")]
  pub next_of_kin: String, // 联系人姓名
  #[serde(rename = "NextOfKinPhone")]
  pub next_of_kin_phone: String, // 联系人电话号码
  #[serde(rename = "NextOfKinZipCode")]
  pub next_of_kin_zip_code: String, // 联系人邮政编码
  #[serde(rename = "NextOfKinAddr")]
  pub next_of_kin_addr: String, // 联系人地址
  #[serde(rename = "RelationshipCode")]
  pub relationship_code: String, // 与联系人关系代码
  #[serde(rename = "Relationship")]
  pub relationship: String, // 与联系人关系
  #[serde(rename = "HealthCardType")]
  pub health_card_type: String, // 医保卡类别
  #[serde(rename = "HealthCardNo")]
  pub health_card_no: String, // 医保卡号
  #[serde(rename = "HospitalCardType")]
  pub hospital_card_type: String, // 医院卡号类型
  #[serde(rename = "HospitalCardNo")]
  pub hospital_card_no: String, // 医院卡号
  #[serde(rename = "PatAge")]
  pub pat_age: String, // 年龄
  #[serde(rename = "DOB")]
  pub dob: String, // 出生日期
  #[serde(rename = "VIPIndicator")]
  pub vip_indicator: String, // VIP号
}

// 病人就诊信息结构体
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PatVisitInfo {
  #[serde(rename = "PatientClass")]
  pub patient_class: String, // 病人类型: O=门诊, I=住院, T=体检
  #[serde(rename = "AttendingDoctorID")]
  pub attending_doctor_id: String, // 主治医师ID
  #[serde(rename = "AttendingDoctorName")]
  pub attending_doctor_name: String, // 主治医师姓名
  #[serde(rename = "ReferringDoctorID")]
  pub referring_doctor_id: String, // 经治医师或转介医师ID
  #[serde(rename = "ReferringDoctorName")]
  pub referring_doctor_name: String, // 经治医师或转介医师姓名
  #[serde(rename = "VIPIndicator")]
  pub vip_indicator: String, // VIP号
  #[serde(rename = "AdmittingDoctorID")]
  pub admitting_doctor_id: String, // 接诊医生编码
  #[serde(rename = "AdmittingDoctorName")]
  pub admitting_doctor_name: String, // 接诊医生姓名
  #[serde(rename = "DischargeDateTime")]
  pub discharge_date_time: String, // 接诊结束时间
  #[serde(rename = "BloodType")]
  pub blood_type: String, // 血型
  #[serde(rename = "BloodTypeRH")]
  pub blood_type_rh: String, // RH类型
}

//2.5.1.	GetXMTC获取项目套餐
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PackageItem {
  #[serde(rename = "tcid")]
  pub tcid: String, // 套餐ID，长度10，非空
  #[serde(rename = "tcmc")]
  pub tcmc: String, // 套餐名称，长度40，非空
  #[serde(rename = "bm")]
  pub bm: String, // 套餐编码，长度6，非空
  #[serde(rename = "xmmc")]
  pub xmmc: String, // 项目名称，长度30，非空
  #[serde(rename = "dj")]
  pub dj: String, // 单价，长度7，非空
  #[serde(rename = "sl")]
  pub sl: String, // 数量，长度5，非空
  #[serde(rename = "je")]
  pub je: String, // 金额，长度8，非空
  #[serde(rename = "xmid")]
  pub xmid: String, // 收费项目ID，长度8，非空
  #[serde(rename = "bmh")]
  pub bmh: String, // 部门号，长度5，非空
}

//2.5.2.	GetSFXM获取收费项目

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ChargeItem {
  #[serde(rename = "xmmc")]
  pub xmmc: String, // 项目名称，长度30，非空
  #[serde(rename = "xmid")]
  pub xmid: String, // 收费项目ID，长度10，非空
  #[serde(rename = "bm")]
  pub bm: String, // 拼音编码，长度6，非空
  #[serde(rename = "dj")]
  pub dj: String, // 单价，长度7，非空
  #[serde(rename = "dw")]
  pub dw: String, // 单位，长度4，非空
  #[serde(rename = "fylbh")]
  pub fylbh: String, // 费用类别号，长度4，非空
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
#[serde(rename = "GMSpecimen")]
pub struct GmSpecimen {
  #[serde(rename = "contaNo")]
  pub specimen_id: String, // 标本号 -> Unique identifier for the specimen
  #[serde(rename = "gmSpecimenNo")]
  pub pathology_id: String, // 病理号 -> Pathology number
  #[serde(rename = "specSource")]
  pub collection_site: String, // 标本采集部位 -> Body part or site of collection
  #[serde(rename = "specPartCode")]
  pub site_code: String, // 标本部位代码 -> Code for collection site
  #[serde(rename = "meno")]
  pub notes: String, // 备注 -> Additional remarks or comments
  #[serde(rename = "amount")]
  pub quantity: i32, // 标本数量 -> Number of specimens
  #[serde(rename = "takeOutTime")]
  pub collection_time: String, // 标本取出时间 -> Time specimen was collected
  #[serde(rename = "fixTime")]
  pub fixation_time: String, // 标本固定时间 -> Time specimen was fixed
  #[serde(rename = "inspectionTime")]
  pub submission_time: String, // 送检时间 -> Time submitted for inspection
  #[serde(rename = "inspectionDeptCode")]
  pub dept_code: String, // 送检科室代码 -> Code for inspection department
  #[serde(rename = "inspectionDeptName")]
  pub dept_name: String, // 送检科室名称 -> Name of inspection department
  #[serde(rename = "inspectionDr")]
  pub inspector_code: String, // 送检人员编码 -> Code for inspecting personnel
  #[serde(rename = "inspectionDrName")]
  pub inspector_name: String, // 送检人员姓名 -> Name of inspecting personnel
}
