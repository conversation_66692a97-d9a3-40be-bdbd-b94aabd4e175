use std::sync::Arc;

use crate::{
  api::localresponse::{response_json_error, response_json_ok, response_json_value_error},
  common::damsdto::AppointMedinfo,
  config::settings::Settings,
  service::damsservice::DamsService,
};
use axum::{Extension, Json};
use dataservice::dbinit::DbConnection;
use nodipservice::dto::KeyDto;
use serde_json::Value;
use tokio::sync::RwLock;
use utility::uidservice::UidgenService;

pub async fn receive_medinfo(
  Extension(config): Extension<Arc<Settings>>,
  Extension(db): Extension<Arc<DbConnection>>,
  Extension(uid): Extension<Arc<RwLock<UidgenService>>>,
  Json(dto): Json<AppointMedinfo>,
) -> Json<Value> {
  info!("receive medinfo for appoint:{:?}", &dto);
  let mut uid = uid.write().await;
  let ret = DamsService::save_appoint_info(&dto, &config, &mut uid, &db).await;
  if ret.as_ref().is_err() {
    return response_json_error(&ret.as_ref().unwrap_err().to_string());
  }
  crate::api::httpresponse::response_json_ok_dams("")
}

pub async fn upload_medexam_result(
  Extension(config): Extension<Arc<Settings>>,
  Extension(uid): Extension<Arc<RwLock<UidgenService>>>,
  Extension(db): Extension<Arc<DbConnection>>,
  Json(dto): Json<KeyDto>,
) -> Json<Value> {
  info!("start to upload_medexam_result for:{:?}", &dto);
  let mut uid = uid.write().await;
  let ret1 = DamsService::upload_medexam_result(&dto.key, &config, &mut uid, &db).await;
  if ret1.as_ref().is_err() {
    return response_json_value_error(&ret1.as_ref().unwrap_err().to_string(), "");
  }
  // let ret = DamsService::upload_medexam_report(&dto.key, &config, &mut uid, &db).await;
  // if ret.as_ref().is_err() {
  //   return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), "");
  // }
  info!("upload medexam completed......");
  crate::api::localresponse::response_json_value_ok(0, "")
}

pub async fn upload_corp_report(
  Extension(config): Extension<Arc<Settings>>,
  Extension(uid): Extension<Arc<RwLock<UidgenService>>>,
  Extension(db): Extension<Arc<DbConnection>>,
  Json(dto): Json<KeyDto>,
) -> Json<Value> {
  let mut uid = uid.write().await;
  let ret = DamsService::upload_corp_report(&dto.key, &config, &mut uid, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(&ret.as_ref().unwrap_err().to_string(), "");
  }
  crate::api::localresponse::response_json_value_ok(0, "")
}
