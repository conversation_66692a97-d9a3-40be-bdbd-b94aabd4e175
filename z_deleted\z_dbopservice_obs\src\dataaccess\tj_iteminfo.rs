
use anyhow::{anyhow, Result};
use rbatis::{crud,  py_sql};
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjIteminfo {
  pub id: i64,
  pub tj_itemid: String,
  pub tj_itemtype: i32,
  pub tj_itemname: String,
  pub tj_itemunit: String,
  pub tj_showorder: i32,
  pub tj_sex: i32,
  pub tj_itemprice: f32,
  pub tj_valuetype: String,
  pub tj_defaultresult: String,
  pub tj_reftype: String,
  pub tj_uppervalue: String,
  pub tj_lowvalue: String,
  pub tj_highoffset: String,
  pub tj_lowoffset: String,
  pub tj_okflag: i32,
  pub tj_barflag: i32,
  pub tj_combineflag: i32,
  pub tj_autodiagflag: i32,
  pub tj_lisnum: String,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_memo: String,
  pub tj_operator: i32,
  pub tj_moddate: i64,
  pub sample_code: String,
  pub tj_extcode: String,
}
crud!(TjIteminfo {}, "tj_iteminfo");
rbatis::impl_select!(TjIteminfo{query_many(itemids:&[&str],okflag:i32,combineflag: i32, itemtype:i32, lisnum:&[&str]) => 
  "`where id > 0 and tj_okflag > 0`
  if !itemids.is_empty():
    ` and tj_itemid in ${itemids.sql()} `
  if !lisnum.is_empty():
    ` and tj_lisnum in ${lisnum.sql()} `
  if okflag >= 0:
    ` and tj_okflag = #{okflag} `
  if itemtype >= 0:
    ` and tj_itemtype = #{itemtype} `
  if combineflag >= 0:
    ` and tj_combineflag = #{combineflag} `"});
rbatis::impl_select!(TjIteminfo{query_by_lisnum(lisnum:&str) => "`where tj_lisnum like #{'%' + lisnum + '%'}`"});
rbatis::impl_select!(TjIteminfo{query_all_with_lisnum() => "`where tj_lisnum <> ''`"});
rbatis::impl_select!(TjIteminfo{query_combine_with_lisnum() => "`where tj_lisnum <> '' and tj_combineflag = 1`"});
rbatis::impl_select!(TjIteminfo{query_combines() => "`where tj_combineflag = 1`"});

impl TjIteminfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjIteminfo, ) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjIteminfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjIteminfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
  #[py_sql("update tj_iteminfo set tj_okflag = -1 where id in ${ids.sql()} ")]
  pub async fn delete(rb: &mut rbatis::RBatis, ids: &[i64], ) -> rbatis::Result<()> {
    impled!()
  }
}
