//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_hazarddisease")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_hid: i32,
    pub tj_testtype: i32,
    pub tj_forbidden: String,
    pub tj_targetdisease: String,
    pub tj_memo: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::tj_hazardinfo::Entity",
        from = "Column::TjHid",
        to = "super::tj_hazardinfo::Column::Id",
        on_update = "Restrict",
        on_delete = "Restrict"
    )]
    TjHazardinfo,
}

impl Related<super::tj_hazardinfo::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjHazardinfo.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
