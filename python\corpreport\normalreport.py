import os
from datetime import datetime
from PIL import Image
from reportlab.platypus import <PERSON>DocT<PERSON>plate, Spacer, PageBreak
from reportlab.platypus import Paragraph, Table, HRFlowable
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm, inch
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.lib.styles import getSampleStyleSheet
from corpreport.numberedcanvas import NumberedCanvas
import constant

from dataentities.dbconn import session
from dataentities.tj_staffadmin import TjStaffadmin


def generate_pdf_corp_normalreport(
    rptinfo,
    checkallinfos,
    medinfos,
    patients,
    corpinfo,
    dicts,
    customer,
    outdir,
):
    rpt = normal_report(
        rptinfo,
        outdir,
        "{}-普通-普通-{}.pdf".format(rptinfo.tj_corpname, rptinfo.tj_reportnumint),
    )
    rpt.add_front_page(corpinfo, dicts)
    rpt.add_page_break()
    rpt.add_table_t3(checkallinfos, medinfos, patients, dicts)
    rpt.add_spacer(0.8)
    rpt.add_sign_page(dicts, customer)
    rpt.add_page_break()
    rpt.add_back_page(dicts)
    rpt.build()


# 	a4:21.0 x 29.7 cm
class normal_report:
    def __init__(self, rptinfo, rptdir, output):
        self.rptinfo = rptinfo
        self.font_name = "SimSun"
        self.contents = []
        stylesheet = getSampleStyleSheet()
        self.title_style = stylesheet["Title"]
        self.normal_style = stylesheet["Normal"]
        self.body_style = stylesheet["BodyText"]
        self.current_section = 0
        self.doc = SimpleDocTemplate(
            os.path.join(rptdir, output),
            pagesize=A4,
            leftMargin=1.2 * cm,
            rightMargin=1.2 * cm,
            topMargin=2.0 * cm,
            bottomMargin=1.2 * cm,
        )

    def on_first_page(self, canvas, doc):
        canvas.saveState()
        # style = getSampleStyleSheet()["Heading3"]
        # style.alignment = TA_RIGHT
        # style.fontName =  self.font_name
        # style.fontSize = 15
        # style.leading = 70
        # style.wordWrap = "CJK"  # 设置自动换行
        # style.spaceBefore = 20
        # canvas.drawRightString(10 * cm, 20 * cm, "(杭发医院) 职检字第 (2023-0215) 号")
        # style2 = getSampleStyleSheet()["Heading2"]
        # style2.leading = 80
        # style2.fontName =  self.font_name
        # style2.fontSize = 32
        # style2.alignment = TA_CENTER
        # canvas.drawRightString(10 * cm, 20 * cm, "职业健康检查报告书")
        # # self.contents.append(Paragraph("职业健康检查报告书", style2))

        textobject = canvas.beginText()
        textobject.setTextOrigin(inch, 2.5 * inch)
        textobject.setFont("Helvetica-Oblique", 14)
        # for line in lyrics:
        # textobject.textLine("caseSensitive")
        textobject.setFillGray(0.4)
        textobject.textLines("")
        canvas.drawText(textobject)

        canvas.restoreState()

    def on_later_pages(self, canvas, doc):
        canvas.saveState()
        canvas.setFont(self.font_name, 13)
        canvas.drawCentredString(A4[0] // 2, 29 * cm, "健 康 检 查 报 告 书")
        canvas.setFont(self.font_name, 12)
        canvas.drawString(doc.leftMargin, 28.3 * cm, self.rptinfo.tj_reportnum)
        # canvas.drawRightString(
        #     A4[0] - doc.rightMargin,
        #     28.3 * cm,
        #     "第{:d}页 ".format(doc.page),
        # )
        canvas.setLineWidth(0.5)
        canvas.line(doc.leftMargin, 28 * cm, A4[0] - doc.rightMargin, 28 * cm)
        canvas.restoreState()

    def add_front_page(self, corpinfo, dicts):
        # self.story.append(Paragraph("", self.heading3_style))
        # self.contents.append(Spacer(1, 1 * cm))
        style = getSampleStyleSheet()["Heading3"]
        style.alignment = TA_RIGHT
        style.fontName = self.font_name
        style.fontSize = 15
        style.leading = 70
        style.wordWrap = "CJK"  # 设置自动换行
        style.spaceBefore = 20
        self.contents.append(Paragraph(self.rptinfo.tj_reportnum, style))

        style2 = getSampleStyleSheet()["Heading2"]
        style2.leading = 80
        style2.fontName = self.font_name
        style2.fontSize = 35
        style2.alignment = TA_CENTER
        self.contents.append(Paragraph("健康检查报告书", style2))

        self.contents.append(Spacer(1, 5 * cm))

        style3 = getSampleStyleSheet()["Heading3"]
        style3.alignment = TA_LEFT
        style3.leading = 18
        style3.fontName = self.font_name
        style3.fontSize = 20
        style3.wordWrap = "CJK"  # 设置自动换行
        # style3.underlineOffset = 30
        # col_width = 200
        data = [
            ("体检单位:", Paragraph(self.rptinfo.tj_corpname, style3)),
            # ("地    址:", Paragraph(corpinfo.tj_address, style3)),
            # ("联系电话:", Paragraph(corpinfo.tj_phone, style3)),
        ]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 15),  # 第二行到最后一行的字体大小
            ("ALIGN", (0, 1), (-1, -1), "LEFT"),  #
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  #
            ("LINEBELOW", (1, 0), (-1, -1), 1, colors.black),
        ]
        table = Table(
            data,
            colWidths=[3 * cm, 10 * cm],
            style=style,
            minRowHeights=[1 * cm],
        )

        self.contents.append(table)

        self.contents.append(Spacer(1, 8 * cm))
        style2 = getSampleStyleSheet()["Heading2"]
        # style2.leading = 20
        style2.fontName = self.font_name
        style2.fontSize = 15
        style2.alignment = TA_CENTER
        orginfo = next(
            filter(
                lambda dict: dict.ss_typeid == constant.DictType.DictCustomer.value
                and dict.ss_pid == constant.CustomerInfo.CustomerName.value,
                dicts,
            ),
            None,
        )
        if orginfo is None:
            orgname = ""
        else:
            orgname = orginfo.ss_name
        self.contents.append(Paragraph(orgname, style2))
        date_time_obj = datetime.fromtimestamp(self.rptinfo.tj_createdate).strftime(
            "%Y-%m-%d"
        )
        self.contents.append(Paragraph(date_time_obj, style2))

    def add_table_t3(self, t3_data, medinfos, patients, dicts):
        style = getSampleStyleSheet()["Normal"]
        style.alignment = TA_CENTER
        style.leading = 20
        style.fontName = "SimHei"
        style.fontSize = 15
        style.wordWrap = "CJK"  # 设置自动换行
        title = "<b>体检结果及建议汇总表</b>"
        self.contents.append(Paragraph(title, style))
        self.contents.append(Spacer(1, 0.2 * cm))

        style3 = getSampleStyleSheet()["Normal"]
        style3.alignment = TA_CENTER
        style3.leading = 10
        style3.fontName = self.font_name
        style3.fontSize = 10
        style3.wordWrap = "CJK"  # 设置自动换行
        style3.underlineOffset = 20

        style3_left = getSampleStyleSheet()["Normal"]
        style3_left.alignment = TA_LEFT
        style3_left.leading = 10
        style3_left.fontName = self.font_name
        style3_left.fontSize = 10
        style3_left.wordWrap = "CJK"  # 设置自动换行
        style3_left.underlineOffset = 20
        data = [
            (
                Paragraph("体检编号", style3),
                Paragraph("姓名", style3),
                Paragraph("性别", style3),
                Paragraph("年龄", style3),
                Paragraph("异常指标", style3_left),
                Paragraph("医学建议", style3_left),
            ),
        ]

        if len(t3_data) > 0:
            for ca in t3_data:
                medinfo = next(med for med in medinfos if med.tj_testid == ca.tj_testid)
                ptinfo = next(pt for pt in patients if pt.tj_pid == medinfo.tj_pid)
                x = ca.tj_othabnormal.split("\n")
                str_list = list(filter(None, x))
                othabnormals = "<br/>".join(str_list)
                x = ca.tj_othsuggestion.split("\n")
                str_list = list(filter(None, x))
                othsuggestion = "<br/>".join(str_list)
                data.append(
                    (
                        Paragraph(ca.tj_testid, style3),
                        Paragraph(ptinfo.tj_pname, style3),
                        Paragraph(constant.get_sex(ptinfo.tj_psex), style3),
                        Paragraph(str(medinfo.tj_age), style3),
                        Paragraph(othabnormals, style3_left),
                        Paragraph(othsuggestion, style3_left),
                    ),
                )
        else:
            data.append(
                (
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                    Paragraph("无", style3),
                )
            )
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 10),  # 第二行到最后一行的字体大小
            ("BACKGROUND", (0, 0), (-1, 0), "#eeeee6"),  # 设置第一行背景颜色
            # ("ALIGN", (0, 0), (-1, -1), "CENTER"),  # 第一行水平居中
            ("ALIGN", (0, 1), (-1, -1), "LEFT"),  # 第二行到最后一行左右左对齐
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
            # ("LINEBELOW", (1, 0), (-1, -1), 1, colors.black),
            # ("BOTTOMPADDING", (1, 0), (-1, -1), 5),
            # ("TEXTCOLOR", (0, 0), (-1, -1), colors.darkslategray),  # 设置表格内文字颜色
            ("GRID", (0, 0), (-1, -1), 0.5, colors.grey),  # 设置表格框线为grey色，线宽为0.5
            # ("SPAN", (1, 1), (1, 2)),
            # ("SPAN", (0, 1), (0, 2)),
            # ("SPAN", (0, 3), (0, 4)),
            # ('SPAN', (0, 3), (0, 4)),  # 合并第一列三四行
            # ('SPAN', (0, 5), (0, 6)),  # 合并第一列五六行
            # ('SPAN', (0, 7), (0, 8)),  # 合并第一列五六行
        ]
        # 合并表格
        if len(t3_data) <= 0:
            style.append(("SPAN", (0, 1), (5, 1)))
        # style.append(("SPAN", (1, 1), (1, 2)))
        # col, row = 0, 1
        # style.append(("SPAN", (col, row), (col, row + 1)))
        w1 = 2.0
        w2 = 1.0
        w3 = 6.5
        table = Table(
            data,
            colWidths=[
                w1 * cm,
                w1 * cm,
                w2 * cm,
                w2 * cm,
                w3 * cm,
                w3 * cm,
            ],
            style=style,
            splitInRow=0,
            repeatRows=1
            # minRowHeights=[0.8 * cm, 0.8 * cm, 0.8 * cm],
        )
        # col, row = 0, 1
        # tTableStyles = []

        # table.setStyle(tTableStyles)
        self.contents.append(table)

    def add_sign_page(self, dicts,customer):
        self.contents.append(
            HRFlowable(
                width="100%",
                thickness=1,
                color="#000000",
                spaceAfter=2,
                vAlign="MIDDLE",
                lineCap="square",
            )
        )
        self.add_spacer(0.5)

        style3 = getSampleStyleSheet()["Heading3"]
        style3.alignment = TA_LEFT
        style3.fontName = self.font_name
        style3.fontSize = 15
        style3.leftIndent = 0.5 * cm
        # style3.leading = 70
        style3.wordWrap = "CJK"  # 设置自动换行

        zjys = ""

        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictSysparm.value
            and dict.ss_pid == constant.SysParm.EZJeSign.value
        )
        if dict_info is None:
            pass
        else:
            if dict_info.ss_short == "1":  # 电子签名
                # 先检查该主检医生的签名
                staff = (
                    session.query(TjStaffadmin)
                    .filter(TjStaffadmin.id == self.rptinfo.tj_creator)
                    .first()
                )
                if staff is not None:
                    staff_esign = staff.tj_esign
                    if staff_esign != "":
                        esign = "./sign/{}".format(staff_esign)
                        if os.path.exists(esign):
                            zjys = '<img src={} width="90" height="40" valign="middle"/>'.format(
                                esign
                            )
                # print("人员信息配置了主检医生，但是没有签名信息:", zjys)
                if zjys == "":  # 再检查系统配置的总检医生
                    # print("开始根据系统配置的主检医生处理：", dict_info.ss_name)
                    esign = "./sign/{}".format(dict_info.ss_name)
                    if os.path.exists(esign):
                        zjys = '<img src={} width="90" height="40" valign="middle"/>'.format(
                            esign
                        )
            elif dict_info.ss_short == "2":
                staff = (
                    session.query(TjStaffadmin)
                    .filter(TjStaffadmin.id == self.rptinfo.tj_creator)
                    .first()
                )
                if staff is None:
                    zjys = dict_info.ss_name
                else:
                    zjys = staff.tj_staffname
            else:
                zjys = ""

        date_time_obj = datetime.fromtimestamp(self.rptinfo.tj_createdate).strftime(
            "%Y-%m-%d"
        )
        data = [("主检医师:", Paragraph(zjys, style3), "日期:", date_time_obj)]
        style = [
            ("FONTNAME", (0, 0), (-1, -1), self.font_name),  # 字体
            ("FONTSIZE", (0, 0), (-1, -1), 15),  # 第二行到最后一行的字体大小
            ("ALIGN", (0, 0), (0, 1), "RIGHT"),  # 第一列右对齐
            ("ALIGN", (2, 0), (2, 1), "RIGHT"),  # 第三列列右对齐
            ("ALIGN", (1, 0), (1, 1), "LEFT"),  # 第二列左对齐
            ("ALIGN", (3, 0), (3, 1), "LEFT"),  # 第四列左对齐
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 所有表格上下居中对齐
        ]
        table = Table(
            data,
            colWidths=[3 * cm, 6 * cm, 3 * cm, 6 * cm],
            style=style,
            minRowHeights=[2.5 * cm],
        )

        self.contents.append(table)
        self.add_spacer(1.0)
        customer = ""
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictCustomer.value
            and dict.ss_pid == constant.CustomerInfo.CustomerName.value
        )
        if dict_info is None:
            pass
        else:
            customer = dict_info.ss_name
        dwgz = "体检单位(盖章)：" + customer
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictSysparm.value
            and dict.ss_pid == constant.SysParm.NormalStample.value
        )
        if dict_info is None:
            pass
        else:
            if dict_info.ss_short == "1" or dict_info.ss_short == "2":
                filename = "./sign/{}".format(dict_info.ss_name)
                if os.path.exists(filename):
                    img = Image.open(filename)
                    ratio = img.width / img.height
                    width = 100*ratio
                    dwgz += (
                        '<img src={} width="{}" height="100" valign="top"/>'.format(
                            filename,width
                        )
                    )
        self.contents.append(Paragraph(dwgz, style3))
        # self.contents.append(Paragraph(zjys, style))

    def add_back_page(self, dicts):
        style3 = getSampleStyleSheet()["Heading1"]
        style3.alignment = TA_CENTER
        style3.fontName = self.font_name
        style3.fontSize = 30
        style3.leftIndent = 0.5 * cm
        style3.leading = 70
        style3.wordWrap = "CJK"  # 设置自动换行
        self.add_spacer(2)
        self.contents.append(Paragraph("健康检查报告书说明", style3))

        style1 = getSampleStyleSheet()["Heading1"]
        style1.fontName = self.font_name
        style1.leading = 25
        style1.wordWrap = "CJK"  # 设置自动换行
        style1.alignment = TA_LEFT
        style1.fontSize = 15
        style1.leftIndent = 0.5 * cm
        style1.strikeWidth = 10
        self.contents.append(Paragraph("一、过去患的疾病，因这次体检范围所限未能发现情况，仍按原诊断及治疗。", style1))
        self.contents.append(Paragraph("二、查出的疾病请及时治疗，异常项目请及时到医院复查。", style1))
        self.contents.append(Paragraph("三、对检查结果如有疑义，请于收到之日起十五日内向本单位提出。", style1))
        self.contents.append(Paragraph("四、本报告书不得部分复制，不得作广告宣传。", style1))
        self.contents.append(Paragraph("五、报告无本单位盖章无效。", style1))

        style2 = getSampleStyleSheet()["Heading1"]
        style2.fontName = self.font_name
        style2.leading = 18
        style2.wordWrap = "CJK"  # 设置自动换行
        style2.alignment = TA_LEFT
        style2.fontSize = 12
        style2.leftIndent = 0.5 * cm
        style2.strikeWidth = 8

        self.add_spacer(2)

        self.add_spacer(1)
        style4 = getSampleStyleSheet()["Heading3"]
        style4.fontName = self.font_name
        style4.leading = 25
        style4.wordWrap = "CJK"  # 设置自动换行
        style4.alignment = TA_LEFT
        style4.fontSize = 15
        style4.leftIndent = 0.5 * cm
        style4.strikeWidth = 8
        self.contents.append(Paragraph("本单位联系方式：", style4))

        style5 = getSampleStyleSheet()["BodyText"]
        style5.fontName = self.font_name
        style5.leading = 15
        style5.wordWrap = "CJK"  # 设置自动换行
        style5.alignment = TA_LEFT
        style5.fontSize = 12
        style5.leftIndent = 0.5 * cm
        style5.strikeWidth = 8
        # customer_name = ""
        customer_no = ""
        customer_add = ""
        customer_postcode = ""
        customer_phone = ""
        # dict_info = next(
        #     dict
        #     for dict in dicts
        #     if dict.ss_typeid == constant.DictType.DictCustomer.value
        #     and dict.ss_pid == constant.CustomerInfo.CustomerName.value
        # )
        # if dict_info is None:
        #     pass
        # else:
        #     customer_name = dict_info.ss_name
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictCustomer.value
            and dict.ss_pid == constant.CustomerInfo.CustomerContactor.value
        )
        if dict_info is None:
            pass
        else:
            customer_no = dict_info.ss_name
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictCustomer.value
            and dict.ss_pid == constant.CustomerInfo.CustomerAddress.value
        )
        if dict_info is None:
            pass
        else:
            customer_add = dict_info.ss_name
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictCustomer.value
            and dict.ss_pid == constant.CustomerInfo.CustomerPostcode.value
        )
        if dict_info is None:
            pass
        else:
            customer_postcode = dict_info.ss_name
        dict_info = next(
            dict
            for dict in dicts
            if dict.ss_typeid == constant.DictType.DictCustomer.value
            and dict.ss_pid == constant.CustomerInfo.CustomerPhone.value
        )
        if dict_info is None:
            pass
        else:
            customer_phone = dict_info.ss_name
        # self.contents.append(Paragraph("职业健康检查机构名称：" + customer_name, style5))
        self.contents.append(Paragraph("联系人：" + customer_no, style5))
        self.contents.append(Paragraph("地址：" + customer_add, style5))
        self.contents.append(Paragraph("邮编：" + customer_postcode, style5))
        self.contents.append(Paragraph("联系电话：" + customer_phone, style5))

    def add_page_break(self):
        self.contents.append(PageBreak())

    def add_spacer(self, size):
        self.contents.append(Spacer(1, size * cm))

    def build(self):
        self.doc.build(
            self.contents,
            onFirstPage=self.on_first_page,
            onLaterPages=self.on_later_pages,
            canvasmaker=NumberedCanvas,
        )
