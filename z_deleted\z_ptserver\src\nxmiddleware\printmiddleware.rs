// use std::net::SocketAddr;

use std::net::SocketAddr;

use axum::{
  body::{Body, Bytes},
  extract::Request,
  http::StatusCode,
  middleware::Next,
  response::IntoResponse,
};
use http_body_util::BodyExt;

pub async fn print_request_response(/*ConnectInfo(addr): ConnectInfo<SocketAddr>, */ req: Request, next: Next) -> Result<impl IntoResponse, (StatusCode, String)> {
  let path = req.uri().path().to_owned();
  let method = req.method().as_str().to_owned();
  let addr = match req.extensions().get::<SocketAddr>() {
    Some(v) => v.to_string(),
    None => "Unknown address".to_string(),
  };
  let (parts, body) = req.into_parts();
  // let bytes = buffer_and_print("request", body).await?;
  let bytes = buffer_and_print("request", &method, &path, &addr, body).await?;
  let req = Request::from_parts(parts, Body::from(bytes));

  let res = next.run(req).await;

  // let (parts, body) = res.into_parts();
  // let bytes = buffer_and_print("response", body).await?;
  // let res = Response::from_parts(parts, Body::from(bytes));

  Ok(res)
}

// pub async fn print_request_response(ConnectInfo(addr): ConnectInfo<SocketAddr>, req: Request, next: Next) -> Result<impl IntoResponse, (StatusCode, String)> {
//   // info!("print_request_response request is:{:?} from addr:{}", &req, &addr);
//   let path = req.uri().path().to_owned();
//   let method = req.method().as_str().to_owned();
//   // info!("method:{}", method);
//   let (parts, body) = req.into_parts();
//   let bytes = buffer_and_print("request", &method, &path, &addr.to_string(), body).await?;
//   let req = Request::from_parts(parts, Body::from(bytes));

//   let res = next.run(req).await;

//   // let (parts, body) = res.into_parts();
//   // let bytes = buffer_and_print("response", &method, &path, body).await?;
//   // let res = Response::from_parts(parts, Body::from(bytes));

//   Ok(res)
// }

async fn buffer_and_print<B>(direction: &str, method: &str, uri: &str, addr: &str, body: B) -> Result<Bytes, (StatusCode, String)>
where
  B: axum::body::HttpBody<Data = Bytes>,
  B::Error: std::fmt::Display,
{
  let bytes = match body.collect().await {
    Ok(collected) => collected.to_bytes(),
    Err(err) => {
      return Err((StatusCode::BAD_REQUEST, format!("failed to read {direction} body: {err}")));
    }
  };

  // let bytes = match hyper::body::to_bytes(body).await {
  //   Ok(bytes) => bytes,
  //   Err(err) => {
  //     return Err((StatusCode::BAD_REQUEST, format!("failed to read {} body: {}", direction, err)));
  //   }
  // };

  if let Ok(_body) = std::str::from_utf8(&bytes) {
    info!("from {}, {}: {} {} ", addr, direction, method, uri);
  }

  Ok(bytes)
}
