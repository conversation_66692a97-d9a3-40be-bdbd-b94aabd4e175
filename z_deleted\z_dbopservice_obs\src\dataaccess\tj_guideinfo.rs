use anyhow::{anyhow, Result};
use rbatis::{crud};
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;

#[derive(C<PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjGuideinfo {
  pub id: i64,
  pub tj_gnum: String,
  pub tj_gname: String,
  pub tj_gdesc: String,
  pub tj_gshoworder: i32,
  pub tj_status: i32,
}
crud!(TjGuideinfo {}, "tj_guideinfo");
rbatis::impl_select!(TjGuideinfo{query_many(ids:&[i64]) => 
  "`where id > 0 `
  if !ids.is_empty():
    ` and id in ${ids.sql()} `
 "});

impl TjGuideinfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjGuideinfo) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjGuideinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjGuideinfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
}
