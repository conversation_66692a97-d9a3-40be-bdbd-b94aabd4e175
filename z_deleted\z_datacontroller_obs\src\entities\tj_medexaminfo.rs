use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_medexaminfo")]
#[derive(C<PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct TjMedexaminfo {
  pub id: i64,
  pub tj_testid: String,
  pub tj_pid: String,
  pub tj_age: i32,
  pub tj_testcat: i32,
  pub tj_testsource: i32,
  pub tj_testtype: i32,
  pub tj_corpnum: i64,
  pub tj_empid: String,
  pub tj_workage: String,
  pub tj_wtcode: String,
  pub tj_worktype: String,
  pub tj_poisionfactor: String,
  pub tj_poisionage: String,
  pub tj_recorddate: i64,
  pub tj_recorder: String,
  pub tj_testdate: i64,
  pub tj_expdate: i64,
  pub tj_subdate: i64,
  pub tj_checkstatus: i32,
  pub tj_printflag: i32,
  pub tj_printtimes: i32,
  pub tj_rptnum: String,
  pub tj_peid: i32,
  pub tj_isrecheck: i32,
  pub tj_oldtestid: String,
  pub tj_rechecktimes: i32,
  pub tj_push: i32,
  pub tj_num: i64,
  pub tj_pushstatus: i32,
  pub tj_upload: i32,
  pub tj_uploadtime: i64,
  pub tj_syncstatus: i32,
  pub tj_paymethod: i32,
  pub tj_packagename: String,
  pub p_medid: i64,
  // pub tj_gjcdc_upload: i32,
}
