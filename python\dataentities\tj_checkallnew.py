from sqlalchemy import Column, Integer, String, Text
from dataentities.dbconn import Base


class TjCheckallnew(Base):
    # 数据库中存储的表名
    __tablename__ = "tj_checkallnew"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    tj_testid = Column(String(128), nullable=False)
    tj_typeid = Column(Integer, nullable=False)
    tj_discode = Column(String(512), nullable=False, unique=True)
    tj_itemcode = Column(String(512), nullable=False)
    tj_hazardcode = Column(String(512), nullable=False)
    tj_diseasename = Column(String(512), nullable=False)
    tj_ocuabnormal = Column(Text, nullable=False)
    tj_othabnormal = Column(Text, nullable=False)
    tj_ocuconclusion = Column(Text, nullable=False)
    tj_othconclusion = Column(Text, nullable=False)
    tj_ocusuggestion = Column(Text, nullable=False)
    tj_othsuggestion = Column(Text, nullable=False)
    tj_ocuopinion = Column(Text, nullable=False)
    tj_othopinion = Column(Text, nullable=False)
    tj_staffid = Column(Integer, nullable=False)
    tj_checkdate = Column(Integer, nullable=False)
    tj_castatus = Column(Integer, nullable=False)
