//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "testerinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  #[sea_orm(unique)]
  pub testid: String,
  pub tname: String,
  pub sex: String,
  pub birthdate: DateTime,
  pub phone: String,
  pub address: String,
  pub idcard: String,
  pub guominshi: String,
  pub memo: String,
  pub createdate: DateTime,
  pub completed: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
