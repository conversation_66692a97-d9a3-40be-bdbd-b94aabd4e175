// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use crate::{basic::dictsvc::DictSvc, common::constant, dto::PatientQueryDto};
use anyhow::{anyhow, Result};
use tracing::*;
use dataservice::{dbinit::DbConnection, entities::prelude::*};

pub struct PatientSvc;

impl PatientSvc {
  pub async fn query_patients(dto: &PatientQueryDto, db: &DbConnection) -> Result<Vec<TjPatient>> {
    // let testids: Vec<&str> = dto.testids.iter().map(|v| v.as_str()).collect();
    // let idcards: Vec<&str> = dto.idcards.iter().map(|v| v.as_str()).collect();
    // let pids: Vec<&str> = dto.pids.iter().map(|v| v.as_str()).collect();
    let ret = TjPatient::query_many(&dto.pids, &dto.pname, &dto.idcards, &dto.testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_patient_by_testid(testid: &str, db: &DbConnection) -> Result<Option<TjPatient>> {
    let ret = TjPatient::query_by_testid(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    // info!("query patient result:{:?}", &ret);
    Ok(ret.unwrap())
  }

  pub async fn query_patient_by_idcard(card: &str, db: &DbConnection) -> Result<Option<TjPatient>> {
    let ret = TjPatient::query_by_idcard(&card, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    info!("query patient result:{:?}", &ret);
    Ok(ret.unwrap())
  }

  pub async fn update_patient_patid(patid: &str, pid: &str, db: &DbConnection) -> Result<u64> {
    TjPatient::update_patid_by_pid(patid, pid, &db.get_connection()).await
  }

  pub async fn update_patient(ptinfo: &TjPatient, db: &DbConnection) -> Result<i64> {
    let ret = TjPatient::save(&ptinfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_patient(ptinfo: &TjPatient, db: &DbConnection) -> Result<TjPatient> {
    let mut ptinfo = ptinfo.to_owned();

    if !ptinfo.tj_pidcard.is_empty() {
      //query by id card
      info!("query patient by idcard:{}", &ptinfo.tj_pidcard);
      let ret = TjPatient::query_by_idcard(&ptinfo.tj_pidcard, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let exist_ptinfo = ret.unwrap();
      if exist_ptinfo.is_some() {
        let exist_ptinfo = exist_ptinfo.unwrap();
        ptinfo.id = exist_ptinfo.id;
        ptinfo.tj_pid = exist_ptinfo.tj_pid.to_owned();
      } else {
        ptinfo.id = 0;
        ptinfo.tj_pid = "".to_string();
      }
    }

    if ptinfo.tj_pid.is_empty() {
      let ret = DictSvc::query_sys_identity(&constant::IdentityCode::Pid.to_string(), db).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      ptinfo.tj_pid = ret.unwrap().id_cvalue.to_string();
    }
    info!("Ptinfo:{:?}", &ptinfo);

    let ret = TjPatient::save(&ptinfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    ptinfo.id = ret.unwrap();
    Ok(ptinfo)
  }
}
