use dataservice::{dbinit::DbConnection, entities::prelude::*};
use moka::future::Cache;
use tracing::*;

use super::constant;
// use std::time::Duration;

// use crate::common::constant;

#[derive(Debug, Clone)]
pub struct SysCache {
  // sysdicts: Vec<SsDictionary>,
  sysdicts: Cache<String, SsDictionary>,
  areas: Cache<String, SsArea>,
  diags: Cache<i64, TjAutodiagcondition>,
  diseases: Cache<i64, TjDiseases>,
  deptinfos: Cache<i64, TjDepartinfo>,
  infoconfigs: Cache<String, SsInfoconfig>,
  combines: Cache<String, TjCombineinfo>,
  itemtypes: Cache<i64, TjItemtype>,
  iteminfos: Cache<String, TjIteminfo>,
  staffs: Cache<i64, TjStaffadmin>,
  // staffdepts: Cache<i64, TjStaffdept>,
  samples: Cache<String, TjSampletype>,
  itemrefs: Cache<i64, TjItemresultinfo>,
  adrevises: C<PERSON><i32, TjAudiogramrevise>,
  corpinfos: Cache<i64, TjCorpinfo>,
  hazards: Cache<i64, TjHazardinfo>,
  hdtypes: Cache<i64, TjHazardtype>,
  deptitems: Cache<i64, TjDeptitem>,
  guides: Cache<i64, TjGuideinfo>,
  guideitems: Cache<i64, TjGuideitem>,
  baritems: Cache<String, TjBaritems>,
  bardetails: Cache<i64, TjBardetail>,
  barnames: Cache<i64, TjBarnameinfo>,
  monitors: Cache<String, SsMonitor>,
  rangeinfos: Cache<i64, TjItemrangeinfo>,
  lawinfos: Cache<i64, TjEvallaw>,
  occuconds: Cache<i64, TjOccucondition>,
  itemranges: Cache<i64, TjItemrangeinfo>,
  ac: String,
}

impl SysCache {
  pub async fn new(areacode: &String, db: &DbConnection) -> Self {
    let dicts = SsDictionary::query_many(0, 0, &db.get_connection()).await.expect("query dicts error");
    let sysdicts: Cache<String, SsDictionary> = Cache::builder().build();
    for v in dicts.into_iter() {
      sysdicts.insert(format!("{}-{}", v.ss_typeid, v.ss_pid), v).await;
    }

    let areadata = SsArea::query_many(areacode.as_str(), &db.get_connection()).await.expect("query areainfos error");
    let areas: Cache<String, SsArea> = Cache::builder().build();
    for v in areadata.into_iter() {
      areas.insert(v.area_code.to_string(), v).await;
    }
    info!("areas size:{}", &areas.entry_count());

    let diags: Cache<i64, TjAutodiagcondition> = Cache::builder().build();
    let diag_ret = TjAutodiagcondition::query_many(&vec![], &vec![], &db.get_connection())
      .await
      .expect("query autodiag conditions error");
    for v in diag_ret.into_iter() {
      diags.insert(v.id, v).await;
    }

    let diseases: Cache<i64, TjDiseases> = Cache::builder().build();
    let dis_ret = TjDiseases::query_many(&vec![], &db.get_connection()).await.expect("init disease error");
    // let mut dises: TimedCache<i64, TjDiseases> = TimedCache::with_lifespan(ttl * 60);
    for v in dis_ret.into_iter() {
      diseases.insert(v.id, v).await;
    }

    let deptinfos: Cache<i64, TjDepartinfo> = Cache::builder().build();
    let depts = TjDepartinfo::query_many(&vec![], &vec![], -1, &db.get_connection()).await.expect("query departments error");
    for v in depts.into_iter() {
      deptinfos.insert(v.id, v).await;
    }

    let itemtypes: Cache<i64, TjItemtype> = Cache::builder().build();
    let items = TjItemtype::query_many(&vec![], &db.get_connection()).await.expect("query itemtypes error");
    for v in items.into_iter() {
      itemtypes.insert(v.id, v).await;
    }

    let iteminfos: Cache<String, TjIteminfo> = Cache::builder().build();
    let items = TjIteminfo::query_many(&vec![], -1, -1, -1, &vec![], &db.get_connection()).await.expect("query iteminfos error");
    for v in items.into_iter() {
      iteminfos.insert(v.tj_itemid.clone(), v).await;
    }

    let combines: Cache<String, TjCombineinfo> = Cache::builder().build();
    let comb_items = TjCombineinfo::query_many(&vec![], &db.get_connection()).await.expect("query combine infos error");
    for val in comb_items.into_iter() {
      let key = format!("{}-{}", val.tj_combid, val.tj_itemid);
      combines.insert(key, val).await;
    }

    let infoconfigs: Cache<String, SsInfoconfig> = Cache::builder().build();
    let infos = SsInfoconfig::query_many(0, &db.get_connection()).await.expect("query infoconfigs error.......");
    for val in infos.into_iter() {
      infoconfigs.insert(format!("{}-{}", val.ss_type, val.ss_code), val).await;
    }

    let staffs: Cache<i64, TjStaffadmin> = Cache::builder().build();
    let infos = TjStaffadmin::query_many(&vec![], &vec![], &db.get_connection()).await.expect("query error.......");
    for val in infos.into_iter() {
      staffs.insert(val.id, val).await;
    }

    let samples: Cache<String, TjSampletype> = Cache::builder().build();
    let infos = TjSampletype::query_many(&db.get_connection()).await.expect("query error.......");
    for val in infos.into_iter() {
      samples.insert(val.sample_code.to_owned(), val).await;
    }

    let itemrefs: Cache<i64, TjItemresultinfo> = Cache::builder().build();
    let infos = TjItemresultinfo::query_many(&vec![], &db.get_connection()).await.expect("query error.......");
    for val in infos.into_iter() {
      itemrefs.insert(val.id, val).await;
    }

    let adrevises: Cache<i32, TjAudiogramrevise> = Cache::builder().build();
    let infos = TjAudiogramrevise::query_many(&db.get_connection()).await.expect("query revise error");
    for val in infos.into_iter() {
      adrevises.insert(val.id, val).await;
    }

    let corpinfos: Cache<i64, TjCorpinfo> = Cache::builder().build();
    let infos = TjCorpinfo::query_many(&vec![], "", &vec![], &db.get_connection()).await.expect("query corpinfos error");
    for val in infos.into_iter() {
      corpinfos.insert(val.id, val).await;
    }

    let hazards: Cache<i64, TjHazardinfo> = Cache::builder().build();
    let infos = TjHazardinfo::query_many(&vec![], &vec![], &vec![], &db.get_connection()).await.expect("query hazardinfos error");
    for val in infos.into_iter() {
      hazards.insert(val.id, val).await;
    }
    let hdtypes: Cache<i64, TjHazardtype> = Cache::builder().build();
    let infos = TjHazardtype::query_many(&db.get_connection()).await.expect("query hazardtype error");
    for val in infos.into_iter() {
      hdtypes.insert(val.id.into(), val).await;
    }
    let deptitems: Cache<i64, TjDeptitem> = Cache::builder().build();
    let infos = TjDeptitem::query_many(&vec![], &vec![], &db.get_connection()).await.expect("query deptitems error");
    for val in infos.into_iter() {
      deptitems.insert(val.id, val).await;
    }
    let guides: Cache<i64, TjGuideinfo> = Cache::builder().build();
    let infos = TjGuideinfo::query_many(&vec![], &db.get_connection()).await.expect("query guide infos error");
    for val in infos.into_iter() {
      guides.insert(val.id, val).await;
    }
    let guideitems: Cache<i64, TjGuideitem> = Cache::builder().build();
    let infos = TjGuideitem::query_many(&vec![], &db.get_connection()).await.expect("query guide items error");
    for val in infos.into_iter() {
      guideitems.insert(val.id.into(), val).await;
    }
    let baritems: Cache<String, TjBaritems> = Cache::builder().build();
    let infos = TjBaritems::query_many(&vec![], &db.get_connection()).await.expect("query baritems error");
    for val in infos.into_iter() {
      baritems.insert(val.tj_binum.to_string(), val).await;
    }
    let bardetails: Cache<i64, TjBardetail> = Cache::builder().build();
    let infos = TjBardetail::query_many(&vec![], &db.get_connection()).await.expect("query baritems error");
    for val in infos.into_iter() {
      bardetails.insert(val.id.into(), val).await;
    }
    let barnames: Cache<i64, TjBarnameinfo> = Cache::builder().build();
    let infos = TjBarnameinfo::query_many(&vec![], &db.get_connection()).await.expect("query baritems error");
    for val in infos.into_iter() {
      barnames.insert(val.id.into(), val).await;
    }
    let monitors: Cache<String, SsMonitor> = Cache::builder().build();
    let infos = SsMonitor::query_many("", &db.get_connection()).await.expect("query monitors error");
    for val in infos.into_iter() {
      monitors.insert(val.ss_jobcode.to_string(), val).await;
    }

    let rangeinfos: Cache<i64, TjItemrangeinfo> = Cache::builder().build();
    let infos = TjItemrangeinfo::query_many(-1, -1, &vec![], &db.get_connection()).await.expect("query revise error");
    for val in infos.into_iter() {
      rangeinfos.insert(val.id, val).await;
    }

    let lawinfos: Cache<i64, TjEvallaw> = Cache::builder().build();
    let infos = TjEvallaw::query_many(&vec![], &db.get_connection()).await.expect("query law error");
    for val in infos.into_iter() {
      lawinfos.insert(val.id, val).await;
    }

    let occuconds: Cache<i64, TjOccucondition> = Cache::builder().build();
    let infos = TjOccucondition::query_many("", &db.get_connection()).await.expect("query occuconds error");
    for val in infos.into_iter() {
      occuconds.insert(val.id, val).await;
    }

    let itemranges: Cache<i64, TjItemrangeinfo> = Cache::builder().build();
    let infos = TjItemrangeinfo::query_many(0, 0, &vec![], &db.get_connection()).await.expect("query item ranges error");
    for val in infos.into_iter() {
      itemranges.insert(val.id, val).await;
    }

    SysCache {
      sysdicts,
      areas,
      diags,
      diseases,
      deptinfos,
      infoconfigs,
      combines,
      itemtypes,
      iteminfos,
      staffs,
      samples,
      itemrefs,
      adrevises,
      corpinfos,
      hazards,
      hdtypes,
      deptitems,
      guides,
      guideitems,
      baritems,
      bardetails,
      barnames,
      monitors,
      rangeinfos,
      lawinfos,
      occuconds,
      itemranges,
      ac: areacode.to_string(),
    }
  }

  // pub async fn reset(&self, db: &DbConnection) -> Result<()> {
  //   self.iteminfos.invalidate_all();
  //   let items = TjIteminfo::query_many( &vec![], -1, -1, -1, &vec![])
  //     .await
  //     .expect("query iteminfos error");
  //   for v in items.into_iter() {
  //     self.iteminfos.insert(v.tj_itemid.clone(), v).await;
  //   }
  //   Ok(())
  // }

  pub async fn get_dict(&self, tid: i32, pid: i32, _db: &DbConnection) -> SsDictionary {
    // if self.sysdicts.entry_count() <= 0 {
    //   let dicts = SsDictionary::query_many( 0, 0).await.expect("query dicts error");
    //   for v in dicts.into_iter() {
    //     self.sysdicts.insert(format!("{}-{}", v.ss_typeid, v.ss_pid), v).await;
    //     self.sysdicts.sync();
    //   }
    // }

    let key = format!("{}-{}", tid, pid);
    // let dict_ret = self.sysdicts.get(&key).await;
    // if dict_ret.is_none() {
    //   let ret = SsDictionary::query_many( tid, pid).await;
    //   if ret.as_ref().is_err() {
    //     return SsDictionary {
    //       id: -1,
    //       ss_typeid: -1,
    //       ss_pid: -1,
    //       ..Default::default()
    //     };
    //   }
    //   let dicts = ret.unwrap();
    //   for v in dicts.into_iter() {
    //     self.sysdicts.insert(format!("{}-{}", v.ss_typeid, v.ss_pid), v).await;
    //   }
    // }

    let ret = self
      .sysdicts
      .entry(key)
      .or_insert(SsDictionary {
        id: 0,
        ss_typeid: tid,
        ss_pid: pid,
        ..Default::default()
      })
      .await;
    ret.value().to_owned()
  }

  pub async fn get_dicts(&self, tid: i32, _db: &DbConnection) -> Vec<SsDictionary> {
    // if self.sysdicts.entry_count() <= 0 {
    //   let dicts = SsDictionary::query_many( 0, 0).await.expect("query dicts error");
    //   for v in dicts.into_iter() {
    //     self.sysdicts.insert(format!("{}-{}", v.ss_typeid, v.ss_pid), v).await;
    //   }
    // }
    // if tid <= 0 {
    //   return self.sysdicts.iter().map(|v| v.1.clone()).collect();
    // }
    // let ret = self.sysdicts.iter().filter(|p| p.1.ss_typeid == tid).map(|v| v.1.clone()).collect();

    let ret = self.sysdicts.iter().filter(|f| if tid <= 0 { true } else { f.1.ss_typeid == tid }).map(|v| v.1).collect();

    ret
  }

  pub async fn get_area(&self, areacode: &String, _db: &DbConnection) -> SsArea {
    // if self.areas.entry_count() <= 0 {
    //   info!("caches are expired, need to refresh from backend");
    //   let items = SsArea::query_many( &self.ac).await.expect("query iteminfos error");
    //   for val in items.into_iter() {
    //     // cache_wr.cache_set(val.tj_itemid.to_owned(), val);
    //     self.areas.insert(val.area_code.clone(), val).await;
    //   }
    // }
    let ret = self
      .areas
      .entry(areacode.clone())
      .or_insert(SsArea {
        area_code: areacode.to_owned(),
        ..Default::default()
      })
      .await;
    ret.value().to_owned()
  }

  pub async fn get_areas_by_codes(&self, areacodes: &Vec<String>, _db: &DbConnection) -> Vec<SsArea> {
    let ret: Vec<SsArea> = self
      .areas
      .iter()
      .filter(|(_, v)| {
        if areacodes.len() <= 0 {
          true
        } else {
          if areacodes.iter().find(|vf| vf.eq_ignore_ascii_case(&v.area_code)).is_some() {
            true
          } else {
            false
          }
        }
      })
      .map(|(_, v)| v.clone())
      .collect();
    ret
  }

  pub async fn get_areas(&self, areacodes: &String, _db: &DbConnection) -> Vec<SsArea> {
    // if self.areas.entry_count() <= 0 {
    //   info!("caches are expired, need to refresh from backend");
    //   let items = SsArea::query_many( &self.ac).await.expect("query iteminfos error");
    //   for val in items.into_iter() {
    //     // cache_wr.cache_set(val.tj_itemid.to_owned(), val);
    //     self.areas.insert(val.area_code.clone(), val).await;
    //   }
    // }
    // info!("area codes:{}", &areacodes);
    let ret: Vec<SsArea> = self
      .areas
      .iter()
      .filter(|(_, v)| {
        if areacodes.is_empty() {
          true
        } else {
          let area_codes: Vec<&str> = areacodes.split(",").collect();
          if area_codes.iter().find(|&f| f.starts_with(&v.area_code)).is_some() {
            true
          } else {
            false
          }
        }
      })
      .map(|(_, v)| v.clone())
      .collect();
    // info!("areas size:{}", &ret.len());
    ret
  }

  pub async fn get_monitor_by_name(&self, jobname: &String) -> Option<SsMonitor> {
    self
      .monitors
      .iter()
      .find(|v| v.1.ss_jobname.eq_ignore_ascii_case(&jobname))
      .map_or(None, |f| Some(f.1.to_owned()))
  }

  pub async fn get_monitor(&self, wtcode: &String) -> Option<SsMonitor> {
    self.monitors.get(wtcode).await
  }

  pub async fn get_monitors(&self) -> Vec<SsMonitor> {
    self.monitors.iter().map(|v| v.1).collect()
  }

  pub async fn get_evallaws(&self) -> Vec<TjEvallaw> {
    self.lawinfos.iter().map(|v| v.1).collect()
  }
  pub async fn get_evallaw(&self, id: i64) -> TjEvallaw {
    self.lawinfos.get(&id).await.unwrap_or_default()
  }

  pub async fn get_infoconfigs(&self, infotype: i32, _db: &DbConnection) -> Vec<SsInfoconfig> {
    let ret = self
      .infoconfigs
      .iter()
      .filter(|(_, v)| if infotype <= 0 { true } else { v.ss_type == infotype })
      .map(|(_, v)| v.clone())
      .collect();
    ret
  }

  pub async fn get_infoconfig_by_name(&self, infoname: &String, monitor: &String) -> Vec<SsInfoconfig> {
    let ret = self
      .infoconfigs
      .iter()
      .filter(|(_, v)| v.ss_name.eq_ignore_ascii_case(&infoname) && v.ss_monitor.eq_ignore_ascii_case(&monitor))
      .map(|(_, v)| v.clone())
      .collect();
    ret
  }

  pub async fn get_worktype_by_name_and_monitortype(&self, infoname: &String, monitor: &String) -> Vec<SsInfoconfig> {
    let ret = self
      .infoconfigs
      .iter()
      .filter(|(_, v)| v.ss_type == super::constant::InfoConfigType::WorkType as i32 && v.ss_name.eq_ignore_ascii_case(&infoname) && v.ss_monitor.eq_ignore_ascii_case(&monitor))
      .map(|(_, v)| v.clone())
      .collect();
    ret
  }

  pub async fn get_autodiag_conditions(&self, disids: &Vec<i64>, _db: &DbConnection) -> Vec<TjAutodiagcondition> {
    // if self.diags.entry_count() <= 0 {
    //   let diag_ret = TjAutodiagcondition::query_many( &vec![], &vec![])
    //     .await
    //     .expect("query autodiag conditions error");
    //   for val in diag_ret.into_iter() {
    //     self.diags.insert(val.id, val).await;
    //   }
    // }
    // info!("current cache size:{}", &diag_wr.cache_size());
    // if disids.len() <= 0 {
    //   return self.diags.iter().map(|(_, v)| v).collect();
    // }
    let mut ret: Vec<TjAutodiagcondition> = self
      .diags
      .iter()
      .filter(|(_, v)| {
        if disids.len() <= 0 {
          true
        } else {
          if disids.iter().find(|&f| *f == v.tj_disid).is_some() {
            true
          } else {
            false
          }
        }
      })
      .map(|(_k, val)| val.to_owned())
      .collect();
    ret.sort_by(|a, b| {
      a.tj_disid
        .cmp(&b.tj_disid)
        .then(a.tj_order.cmp(&b.tj_order).then(b.tj_conditionsymbol.cmp(&a.tj_conditionsymbol)))
    });
    ret
  }

  pub async fn get_combineinfos(&self, combids: &Vec<String>, _db: &DbConnection) -> Vec<TjCombineinfo> {
    // if self.combines.entry_count() <= 0 {
    //   let comb_items = TjCombineinfo::query_many( &vec![]).await.expect("query combine infos error");
    //   for val in comb_items.into_iter() {
    //     let key = format!("{}-{}", val.tj_combid, val.tj_itemid);
    //     self.combines.insert(key, val).await;
    //   }
    // }

    // let mut ret: Vec<TjCombineinfo> = Vec::new();
    // if combids.len() <= 0 {
    //   // ret.extend(self.combines.iter().flat_map(|(_, v)| v).map(|val| val).collect::<Vec<TjCombineinfo>>());
    //   // ret.extend(self.combines.iter().flat_map(|(_k, v)| v.into_iter()).collect::<Vec<TjCombineinfo>>());
    //   ret.extend(self.combines.iter().map(|v| v.1.to_owned()).collect::<Vec<TjCombineinfo>>());
    // } else {
    //   ret.extend(
    //     self
    //       .combines
    //       .iter()
    //       .filter(|(_k, v)| combids.iter().find(|&f| f.eq(&v.tj_combid)).is_some())
    //       .map(|(_k, v)| v.to_owned())
    //       .collect::<Vec<TjCombineinfo>>(),
    //   );
    // }
    let ret: Vec<TjCombineinfo> = self
      .combines
      .iter()
      .filter(|f| {
        if combids.len() <= 0 {
          true
        } else {
          if combids.iter().find(|&f1| f1.eq_ignore_ascii_case(&f.1.tj_combid)).is_some() {
            true
          } else {
            false
          }
        }
      })
      .map(|v| v.1)
      .collect();
    ret
  }

  pub async fn get_iteminfos(&self, itemids: &Vec<String>, okflag: i32, combineflag: i32, itemtype: i32, lisnum: &Vec<String>, _db: &DbConnection) -> Vec<TjIteminfo> {
    // let current_size = self.iteminfos.entry_count(); //.cache_size();
    // if current_size <= 0 {
    //   info!("caches are expired, need to refresh from backend");
    //   let items = TjIteminfo::query_many( &vec![], -1, -1, -1, &vec![])
    //     .await
    //     .expect("query iteminfos error");
    //   for val in items.into_iter() {
    //     // cache_wr.cache_set(val.tj_itemid.to_owned(), val);
    //     self.iteminfos.insert(val.tj_itemid.clone(), val).await;
    //   }
    // }
    let ret = self
      .iteminfos
      .iter()
      .filter(|(_, v)| {
        let b1 = if itemids.len() <= 0 {
          return true;
        } else {
          if itemids.iter().find(|&f| f.eq_ignore_ascii_case(&v.tj_itemid)).is_some() {
            true
          } else {
            false
          }
        };
        let b2 = if okflag > -1 { v.tj_okflag == okflag } else { true };
        let b3 = if combineflag > -1 { v.tj_combineflag == combineflag } else { true };
        let b4 = if lisnum.len() <= 0 {
          true
        } else {
          if lisnum.iter().find(|&f2| f2.eq_ignore_ascii_case(&v.tj_lisnum)).is_some() {
            true
          } else {
            false
          }
        };
        let b5 = if itemtype > 0 { v.tj_itemtype == itemtype } else { true };
        b1 && b2 && b3 && b4 && b5
      })
      .map(|(_, v)| v.clone())
      .collect();
    ret
  }

  pub async fn get_iteminfo_by_lisnum(&self, lisnum: &String) -> Vec<TjIteminfo> {
    if lisnum.is_empty() {
      return vec![];
    }
    let ret = self
      .iteminfos
      .iter()
      .filter(|f| {
        if f.1.tj_lisnum.is_empty() {
          false
        } else {
          let split_lisnums: Vec<&str> = f.1.tj_lisnum.split(",").collect();
          if split_lisnums.len() <= 0 {
            false
          } else {
            if split_lisnums.iter().find(|&f2| f2.eq_ignore_ascii_case(&lisnum)).is_some() {
              true
            } else {
              false
            }
          }
        }
      })
      .map(|v| v.1)
      .collect();
    ret
  }

  pub async fn get_iteminfo_by_extcode(&self, extcode: &str) -> Vec<TjIteminfo> {
    if extcode.is_empty() {
      return vec![];
    }
    let ret = self.iteminfos.iter().filter(|f| f.1.tj_extcode.eq_ignore_ascii_case(extcode)).map(|v| v.1).collect();
    ret
  }

  pub async fn get_iteminfo(&self, itemid: &String, _db: &DbConnection) -> TjIteminfo {
    // let current_size = self.iteminfos.entry_count(); //.cache_size();
    // if self.iteminfos.entry_count() <= 0 {
    //   info!("caches are expired, need to refresh from backend");
    //   let items = TjIteminfo::query_many( &vec![], -1, -1, -1, &vec![])
    //     .await
    //     .expect("query iteminfos error");
    //   for val in items.into_iter() {
    //     // cache_wr.cache_set(val.tj_itemid.to_owned(), val);
    //     self.iteminfos.insert(val.tj_itemid.clone(), val).await;
    //   }
    // }
    let ret = self
      .iteminfos
      .entry(itemid.clone())
      .or_insert(TjIteminfo {
        tj_itemid: itemid.to_owned(),
        ..Default::default()
      })
      .await;

    ret.value().clone()
  }

  pub async fn get_item_rangeinfo(&self, itemid: &str, age: i32, sex: i32) -> Vec<TjItemrangeinfo> {
    let ret = self
      .itemranges
      .iter()
      .filter(|f| {
        let age_ret = match age {
          age if age > 0 => f.1.tj_startage <= age && f.1.tj_endage > age,
          _ => true,
        };
        let sex_ret = match sex {
          1 | 2 => f.1.tj_sex == sex,
          _ => true,
        };
        f.1.tj_itemid.eq_ignore_ascii_case(&itemid) && age_ret && sex_ret
      })
      .map(|v| v.1.clone())
      .collect();
    ret
  }

  pub async fn get_itemtypes(&self, itemtypes: &Vec<i64>, _db: &DbConnection) -> Vec<TjItemtype> {
    // self.itemtypes.sync();
    // if self.itemtypes.entry_count() <= 0 {
    //   info!("caches are expired, need to refresh from backend");
    //   let items = TjItemtype::query_many( &vec![]).await.expect("query iteminfos error");
    //   for val in items.into_iter() {
    //     self.itemtypes.insert(val.id, val).await;
    //   }
    // }
    let ret = self
      .itemtypes
      .iter()
      .filter(|(_, v)| {
        let b1 = if itemtypes.len() <= 0 {
          true
        } else {
          if itemtypes.iter().find(|&&f| f == v.tj_typeid as i64).is_some() {
            true
          } else {
            false
          }
        };
        b1
      })
      .map(|(_, v)| v.clone())
      .collect();
    ret
  }

  pub async fn get_audiogram_revises(&self) -> Vec<TjAudiogramrevise> {
    let ret = self.adrevises.iter().map(|v| v.1).collect();
    ret
  }

  pub async fn get_audiogram_revise(&self, freq: i32, sex: i32, age: i32) -> Option<TjAudiogramrevise> {
    let ret = self
      .adrevises
      .iter()
      .find(|p| p.1.tj_freq == freq && p.1.tj_sex == sex && age >= p.1.tj_startage && age <= p.1.tj_endage);
    if ret.is_none() {
      return None;
    }
    Some(ret.unwrap().1)
  }

  pub async fn get_itemresultinfos(&self, itemid: &String, _db: &DbConnection) -> Vec<TjItemresultinfo> {
    // if self.itemrefs.entry_count() <= 0 {
    //   let infos = TjItemresultinfo::query_many( &vec![]).await.expect("query error.......");
    //   // let items = TjIteminfo::query_many(&vec![], -1, -1, &vec![]).await.expect("query iteminfos error");
    //   for val in infos.into_iter() {
    //     // cache_wr.cache_set(val.tj_itemid.to_owned(), val);
    //     self.itemrefs.insert(val.id, val).await;
    //   }
    // }

    let ret = self.itemrefs.iter().filter(|f| f.1.tj_itemid.eq_ignore_ascii_case(&itemid)).map(|v| v.1.to_owned()).collect();
    ret
  }
  // pub async fn refresh_diseases(&self, infos: &Vec<TjDiseases>) {
  //   self.diseases.invalidate_all();
  //   for val in infos.iter() {
  //     self.diseases.insert(val.id, val.clone()).await;
  //   }
  // }
  pub async fn get_diseases(&self, disids: &Vec<i64>, deptids: &Vec<i64>, typeids: &Vec<i64>, _db: &DbConnection) -> Vec<TjDiseases> {
    // let current_size = self.diseases.entry_count(); //.cache_size();
    // if current_size <= 0 {
    //   let dis_ret = TjDiseases::query_many( &vec![]).await.expect("init disease error");
    //   for v in dis_ret.into_iter() {
    //     self.diseases.insert(v.id, v).await;
    //   }
    // }
    let mut ret: Vec<TjDiseases> = self
      .diseases
      .iter()
      .filter(|(_, v)| {
        let b1 = if disids.len() <= 0 {
          true
        } else {
          if disids.iter().find(|&&f| f == v.id).is_some() {
            true
          } else {
            false
          }
        };

        let b2 = if deptids.len() <= 0 {
          true
        } else {
          if deptids.iter().find(|&&f| f == v.tj_deptid as i64).is_some() {
            true
          } else {
            false
          }
        };

        let b3 = if typeids.len() <= 0 {
          true
        } else {
          if typeids.iter().find(|&&f| f == v.tj_typeid as i64).is_some() {
            true
          } else {
            false
          }
        };

        b1 && b2 && b3
      })
      .map(|(_, v)| v.clone())
      .collect();
    ret.sort_by(|a, b| a.id.cmp(&b.id).then(a.tj_deptid.cmp(&b.tj_deptid)));
    ret
  }

  pub async fn find_diseases_by_code(&self, discode: &str, _db: &DbConnection) -> Option<TjDiseases> {
    // let current_size = self.diseases.entry_count(); //.cache_size();
    // if current_size <= 0 {
    //   let dis_ret = TjDiseases::query_many( &vec![]).await.expect("init disease error");
    //   for v in dis_ret.into_iter() {
    //     self.diseases.insert(v.id, v).await;
    //   }
    // }
    let ret = self.diseases.iter().find(|p| p.1.tj_discode.eq_ignore_ascii_case(&discode));
    if ret.is_none() {
      return None;
    }
    Some(ret.unwrap().1)
  }

  pub async fn find_diseases_by_id(&self, disid: i64, _db: &DbConnection) -> Option<TjDiseases> {
    // let current_size = self.diseases.entry_count(); //.cache_size();
    // if current_size <= 0 {
    //   let dis_ret = TjDiseases::query_many( &vec![]).await.expect("init disease error");
    //   for v in dis_ret.into_iter() {
    //     self.diseases.insert(v.id, v).await;
    //   }
    // }
    let ret = self.diseases.get(&disid).await;
    if ret.is_none() {
      return None;
    }
    Some(ret.unwrap())
  }

  pub async fn get_departinfos(&self, ids: &Vec<i64>, deptids: &Vec<String>, depttype: i32, _db: &DbConnection) -> Vec<TjDepartinfo> {
    // self.deptinfos.sync();
    // if self.deptinfos.entry_count() <= 0 {
    //   info!("caches are expired, need to refresh from backend");
    //   let items = TjDepartinfo::query_many( &vec![], &vec![], -1).await.expect("query departments error");
    //   for val in items.into_iter() {
    //     // cache_wr.cache_set(val.tj_itemid.to_owned(), val);
    //     self.deptinfos.insert(val.id, val).await;
    //   }
    // }
    let ret = self
      .deptinfos
      .iter()
      .filter(|(_, v)| {
        let b1 = if ids.len() <= 0 {
          true
        } else {
          if ids.iter().find(|&&f| f == v.id).is_some() {
            true
          } else {
            false
          }
        };

        let b2 = if deptids.len() <= 0 {
          true
        } else {
          if deptids.iter().find(|&f| f.eq_ignore_ascii_case(&v.tj_deptid)).is_some() {
            true
          } else {
            false
          }
        };
        let b3 = if depttype <= 0 { true } else { v.tj_depttype == depttype };
        b1 && b2 && b3
      })
      .map(|(_, v)| v.clone())
      .collect();
    ret
  }

  pub async fn get_departs(&self, deptids: &Vec<String>, _db: &DbConnection) -> Vec<TjDepartinfo> {
    // let current_size = self.deptinfos.entry_count(); //.cache_size();
    // if current_size <= 0 {
    //   info!("caches are expired, need to refresh from backend");
    //   // let items = TjIteminfo::query_many(&vec![], -1, -1, &vec![]).await.expect("query iteminfos error");
    //   let items = TjDepartinfo::query_many( &vec![], &vec![], -1).await.expect("query departments error");
    //   for val in items.into_iter() {
    //     // cache_wr.cache_set(val.tj_itemid.to_owned(), val);
    //     self.deptinfos.insert(val.id, val).await;
    //   }
    // }
    let ret = self
      .deptinfos
      .iter()
      .filter(|(_, v)| {
        if deptids.len() <= 0 {
          return true;
        } else {
          if deptids.iter().find(|&f| f.eq_ignore_ascii_case(&v.tj_deptid)).is_some() {
            return true;
          } else {
            return false;
          }
        }
      })
      .map(|(_, v)| v.clone())
      .collect();
    ret
  }

  pub async fn get_department(&self, deptid: &String, _db: &DbConnection) -> TjDepartinfo {
    if deptid.is_empty() {
      return TjDepartinfo { ..Default::default() };
    }
    // let current_size = self.deptinfos.entry_count(); //.cache_size();
    // if current_size <= 0 {
    //   info!("caches are expired, need to refresh from backend");
    //   // let items = TjIteminfo::query_many(&vec![], -1, -1, &vec![]).await.expect("query iteminfos error");
    //   let items = TjDepartinfo::query_many( &vec![], &vec![], -1).await.expect("query departments error");
    //   for val in items.into_iter() {
    //     // cache_wr.cache_set(val.tj_itemid.to_owned(), val);
    //     self.deptinfos.insert(val.id, val).await;
    //   }
    // }

    let ret = self.deptinfos.iter().find(|(_, v)| v.tj_deptid.eq_ignore_ascii_case(&deptid));
    if ret.is_none() {
      TjDepartinfo { ..Default::default() };
    }
    let fret = ret.unwrap();
    fret.1
  }

  pub async fn get_staffs(&self, staffnos: &Vec<String>, staffids: &Vec<i64>, _db: &DbConnection) -> Vec<TjStaffadmin> {
    // if self.staffs.entry_count() <= 0 {
    //   let infos = TjStaffadmin::query_many( &vec![], &vec![])
    //     .await
    //     .expect("query infoconfigs error.......");
    //   for val in infos.into_iter() {
    //     self.staffs.insert(val.id, val).await;
    //   }
    // }

    let ret = self
      .staffs
      .iter()
      .filter(|(_, v)| {
        let b1 = if staffnos.len() <= 0 {
          true
        } else {
          if staffnos.iter().find(|&f| f.eq_ignore_ascii_case(&v.tj_staffno)).is_some() {
            true
          } else {
            false
          }
        };
        let b2 = if staffids.len() <= 0 {
          true
        } else {
          if staffids.iter().find(|&&f| f == v.id).is_some() {
            true
          } else {
            false
          }
        };
        b1 && b2
      })
      .map(|(_, v)| v.clone())
      .collect();
    ret
  }

  pub async fn get_staff(&self, id: i64, _db: &DbConnection) -> TjStaffadmin {
    // if self.staffs.entry_count() <= 0 {
    //   let infos = TjStaffadmin::query_many( &vec![], &vec![])
    //     .await
    //     .expect("query infoconfigs error.......");
    //   for val in infos.into_iter() {
    //     self.staffs.insert(val.id, val).await;
    //   }
    // }

    if id <= 0 {
      return TjStaffadmin { ..Default::default() };
    }

    let ret = self.staffs.entry(id).or_insert(TjStaffadmin { ..Default::default() }).await;

    ret.value().clone()
  }

  pub async fn get_sampletype(&self, scode: &String, _db: &DbConnection) -> TjSampletype {
    // if self.samples.entry_count() <= 0 {
    //   let infos = TjSampletype::query_many(&mut db.get_connection()).await.expect("query infoconfigs error.......");
    //   for val in infos.into_iter() {
    //     self.samples.insert(val.sample_code.to_owned(), val).await;
    //   }
    // }

    if scode.is_empty() {
      return TjSampletype { ..Default::default() };
    }

    let ret = self
      .samples
      .entry(scode.clone())
      .or_insert(TjSampletype {
        sample_code: scode.to_owned(),
        ..Default::default()
      })
      .await;

    ret.value().clone()
  }

  pub async fn get_corpinfo(&self, id: i64, _db: &DbConnection) -> Option<TjCorpinfo> {
    if id <= 0 {
      return None;
    }

    let ret = self.corpinfos.iter().find(|p| p.1.id == id);
    if ret.is_none() {
      return None;
    }
    Some(ret.unwrap().1.to_owned())
  }

  pub async fn get_corpinfos(&self, corpids: &Vec<i64>, corpname: &String, orgcode: &Vec<String>, _db: &DbConnection) -> Vec<TjCorpinfo> {
    // if self.corpinfos.entry_count() <= 0 {
    //   self.update_caches(constant::CacheType::Corpinfo as i32, &db).await;
    // }

    let ret = self
      .corpinfos
      .iter()
      .filter(|p| {
        let b1 = if corpids.len() <= 0 {
          true
        } else {
          if corpids.iter().find(|&&f| f == p.1.id).is_some() {
            true
          } else {
            false
          }
        };

        let b2 = if corpname.is_empty() {
          true
        } else {
          if p.1.tj_corpname.eq_ignore_ascii_case(&corpname) {
            true
          } else {
            false
          }
        };

        let b3 = if orgcode.len() <= 0 {
          true
        } else {
          if orgcode.iter().find(|&f| f.eq_ignore_ascii_case(&p.1.tj_orgcode)).is_some() {
            true
          } else {
            false
          }
        };

        b1 && b2 && b3
      })
      .map(|v| v.1.to_owned())
      .collect();

    ret
  }

  pub async fn get_corpinfo_by_name(&self, corpname: &String, _db: &DbConnection) -> Option<TjCorpinfo> {
    if corpname.is_empty() {
      return None;
    }

    let ret = self.corpinfos.iter().find(|p| p.1.tj_corpname.eq_ignore_ascii_case(&corpname));
    if ret.is_none() {
      return None;
    }
    Some(ret.unwrap().1.to_owned())
  }

  pub async fn get_hazardinfos(&self, hdtypes: &Vec<i64>, hdids: &Vec<i64>, hdnames: &Vec<String>, _db: &DbConnection) -> Vec<TjHazardinfo> {
    // if self.hazards.entry_count() <= 0 {
    //   self.update_caches(constant::CacheType::Hazardinfo as i32, &db).await;
    // }
    let ret = self
      .hazards
      .iter()
      .filter(|p| {
        let b1 = if hdtypes.len() <= 0 {
          true
        } else {
          if hdtypes.iter().find(|&&f| f == p.1.tj_tid as i64).is_some() {
            true
          } else {
            false
          }
        };
        let b2 = if hdids.len() <= 0 {
          true
        } else {
          if hdids.iter().find(|&&f| f == p.1.id).is_some() {
            true
          } else {
            false
          }
        };

        let b3 = if hdnames.len() <= 0 {
          true
        } else {
          if hdnames.iter().find(|&f| p.1.tj_hname.eq_ignore_ascii_case(&f)).is_some() {
            true
          } else {
            false
          }
        };

        b1 && b2 && b3
      })
      .map(|v| v.1.to_owned())
      .collect();

    ret
  }

  pub async fn get_hazardtypes(&self, _db: &DbConnection) -> Vec<TjHazardtype> {
    // self.hdtypes.sync();
    // if self.hdtypes.entry_count() <= 0 {
    //   let infos = TjHazardtype::query_many(&mut db.get_connection()).await.expect("query corpinfos error");
    //   for val in infos.into_iter() {
    //     self.hdtypes.insert(val.id, val).await;
    //   }
    // }
    let ret = self.hdtypes.iter().map(|v| v.1.to_owned()).collect();
    ret
  }

  pub async fn get_deptitems(&self, itemids: &Vec<String>, deptids: &Vec<String>, _db: &DbConnection) -> Vec<TjDeptitem> {
    // self.deptitems.sync();
    // if self.deptitems.entry_count() <= 0 {
    //   let infos = TjDeptitem::query_many(&mut db.get_connection()).await.expect("query corpinfos error");
    //   for val in infos.into_iter() {
    //     self.deptitems.insert(val.id, val).await;
    //   }
    // }
    let ret = self
      .deptitems
      .iter()
      .filter(|p| {
        let b1 = if itemids.len() <= 0 {
          true
        } else {
          if itemids.iter().find(|&f| p.1.tj_itemid.eq_ignore_ascii_case(&f)).is_some() {
            true
          } else {
            false
          }
        };
        let b2 = if deptids.len() <= 0 {
          true
        } else {
          if deptids.iter().find(|&f| p.1.tj_deptid.eq_ignore_ascii_case(&f)).is_some() {
            true
          } else {
            false
          }
        };

        b1 && b2
      })
      .map(|v| v.1.to_owned())
      .collect();

    ret
  }

  pub async fn get_guideinfos(&self, guideid: &Vec<i64>, _db: &DbConnection) -> Vec<TjGuideinfo> {
    // self.guides.sync();
    // if self.guides.entry_count() <= 0 {
    //   let infos = TjGuideinfo::query_many(&mut db.get_connection()).await.expect("query corpinfos error");
    //   for val in infos.into_iter() {
    //     self.guides.insert(val.id, val).await;
    //   }
    // }
    let ret = self
      .guides
      .iter()
      .filter(|f| {
        if guideid.len() <= 0 {
          true
        } else {
          if guideid.iter().find(|&&f1| f1 == *f.0).is_some() {
            true
          } else {
            false
          }
        }
      })
      .map(|v| v.1.to_owned())
      .collect();
    ret
  }

  pub async fn get_guideitems(&self, guideid: i64, _db: &DbConnection) -> Vec<TjGuideitem> {
    // self.guideitems.sync();
    // if self.guideitems.entry_count() <= 0 {
    //   let infos = TjGuideitem::query_many(&mut db.get_connection()).await.expect("query corpinfos error");
    //   for val in infos.into_iter() {
    //     self.guideitems.insert(val.id, val).await;
    //   }
    // }
    let ret = self
      .guideitems
      .iter()
      .filter(|f| if guideid <= 0 { true } else { f.1.tj_guideid == guideid })
      .map(|v| v.1.to_owned())
      .collect();
    ret
  }
  pub async fn get_baritems(&self, binums: &Vec<String>, _db: &DbConnection) -> Vec<TjBaritems> {
    // self.baritems.sync();
    // if self.baritems.entry_count() <= 0 {
    //   let infos = TjBaritems::query_many(&mut db.get_connection()).await.expect("query baritems error");
    //   for val in infos.into_iter() {
    //     self.baritems.insert(val.tj_binum.to_string(), val).await;
    //   }
    // }
    let ret = self
      .baritems
      .iter()
      .filter(|f| {
        if binums.len() <= 0 {
          true
        } else {
          if binums.iter().find(|&f1| f1.eq_ignore_ascii_case(&f.1.tj_binum)).is_some() {
            true
          } else {
            false
          }
        }
      })
      .map(|v| v.1.to_owned())
      .collect();
    ret
  }
  pub async fn get_bardetails(&self, binums: &Vec<String>, _db: &DbConnection) -> Vec<TjBardetail> {
    // self.bardetails.sync();
    // if self.bardetails.entry_count() <= 0 {
    //   let infos = TjBardetail::query_many(&mut db.get_connection()).await.expect("query baritems error");
    //   for val in infos.into_iter() {
    //     self.bardetails.insert(val.id, val).await;
    //   }
    // }
    let ret = self
      .bardetails
      .iter()
      .filter(|f| {
        if binums.len() <= 0 {
          true
        } else {
          if binums.iter().find(|&f1| f1.eq_ignore_ascii_case(&f.1.tj_binum)).is_some() {
            true
          } else {
            false
          }
        }
      })
      .map(|v| v.1.to_owned())
      .collect();
    ret
  }
  pub async fn get_barnames(&self, ids: &Vec<i64>, _db: &DbConnection) -> Vec<TjBarnameinfo> {
    // self.barnames.sync();
    // if self.barnames.entry_count() <= 0 {
    //   let infos = TjBarnameinfo::query_many(&mut db.get_connection()).await.expect("query baritems error");
    //   for val in infos.into_iter() {
    //     self.barnames.insert(val.id, val).await;
    //   }
    // }
    let ret = self
      .barnames
      .iter()
      .filter(|f| {
        if ids.len() <= 0 {
          true
        } else {
          if ids.iter().find(|&&f1| f1 == *f.0).is_some() {
            true
          } else {
            false
          }
        }
      })
      .map(|v| v.1.to_owned())
      .collect();
    ret
  }

  pub async fn query_itemrangeinfos(&self, age: i32, sex: i32, itemids: &Vec<String>) -> Vec<TjItemrangeinfo> {
    let ret = self
      .rangeinfos
      .iter()
      .filter(|p| {
        let b1 = if sex < 0 {
          true
        } else {
          if p.1.tj_sex == sex || p.1.tj_sex == 0 || p.1.tj_sex == 3 {
            true
          } else {
            false
          }
        };
        let b2 = if age < 0 { true } else { p.1.tj_startage <= age && age < p.1.tj_endage };
        let b3 = if itemids.len() == 0 {
          true
        } else {
          if itemids.iter().find(|&f| p.1.tj_itemid.eq_ignore_ascii_case(&f)).is_some() {
            true
          } else {
            false
          }
        };
        b1 && b2 && b3
      })
      .map(|v| v.1.to_owned())
      .collect();

    ret
  }

  pub async fn find_itemrangeinfo(&self, age: i32, sex: i32, itemid: &String) -> Option<TjItemrangeinfo> {
    let ret = self
      .rangeinfos
      .iter()
      .find(|p| {
        let b1 = if p.1.tj_sex == sex || p.1.tj_sex == 0 || p.1.tj_sex == 3 { true } else { false };
        let b2 = if age < 0 { true } else { p.1.tj_startage <= age && age < p.1.tj_endage };
        let b3 = p.1.tj_itemid.eq_ignore_ascii_case(&itemid);
        b1 && b2 && b3
      })
      .map(|v| v.1.to_owned());

    ret
  }

  pub async fn get_occuconditions(&self) -> Vec<TjOccucondition> {
    let ret = self
      .occuconds
      .iter()
      // .filter(|(_, v)| v.ss_type == super::constant::InfoConfigType::WorkType as i32 && v.ss_name.eq_ignore_ascii_case(&infoname) && v.ss_monitor.eq_ignore_ascii_case(&monitor))
      .map(|(_, v)| v.clone())
      .collect();
    ret
  }
  pub async fn find_occucondition(&self, hdid: i32, sex: i32, itemid: &str) -> Option<TjOccucondition> {
    for val in self.occuconds.iter() {
      if val.1.tj_poision == hdid
        && val.1.tj_itemid.eq_ignore_ascii_case(&itemid)
        && (val.1.tj_sex == constant::Sex::All as i32 || val.1.tj_sex == constant::Sex::Unknown as i32 || val.1.tj_sex == sex)
      {
        return Some(val.1.clone());
      }
    }
    return None;
    // let mut results: Vec<TjOccucondition> = vec![];
    // let itemid = itemid.trim();
    // for val in self.occuconds.iter() {
    //   if pthazards.iter().find(|&f| f.tj_hid == val.1.tj_poision as i64).is_some() && val.1.tj_itemid.eq_ignore_ascii_case(&itemid) {
    //     results.push(val.1.clone());
    //   }
    // }

    // return results;
  }

  pub async fn find_occuconditions(&self, pthazards: &Vec<TjPatienthazards>, itemid: &str) -> Vec<TjOccucondition> {
    let mut results: Vec<TjOccucondition> = vec![];
    let itemid = itemid.trim();
    for val in self.occuconds.iter() {
      if pthazards.iter().find(|&f| f.tj_hid == val.1.tj_poision as i64).is_some() && val.1.tj_itemid.eq_ignore_ascii_case(&itemid) {
        results.push(val.1.clone());
      }
    }

    return results;
  }

  pub async fn update_caches(&self, ct: i32, db: &DbConnection) {
    match ct {
      x if x == crate::common::constant::CacheType::Dict as i32 => {
        self.sysdicts.invalidate_all();
        self.sysdicts.run_pending_tasks().await;
        let dicts = SsDictionary::query_many(0, 0, &db.get_connection()).await.expect("query dicts error");
        for v in dicts.into_iter() {
          self.sysdicts.insert(format!("{}-{}", v.ss_typeid, v.ss_pid), v).await;
        }
      }
      x if x == crate::common::constant::CacheType::Infoconfig as i32 => {
        self.infoconfigs.invalidate_all();
        self.infoconfigs.run_pending_tasks().await;
        let infos = SsInfoconfig::query_many(0, &db.get_connection()).await.expect("query infoconfigs error.......");
        for val in infos.into_iter() {
          self.infoconfigs.insert(format!("{}-{}", val.ss_type, val.ss_code), val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Area as i32 => {
        self.areas.invalidate_all();
        self.areas.run_pending_tasks().await;
        let items = SsArea::query_many(&self.ac, &db.get_connection()).await.expect("query iteminfos error");
        for val in items.into_iter() {
          // cache_wr.cache_set(val.tj_itemid.to_owned(), val);
          self.areas.insert(val.area_code.clone(), val).await;
        }
      }
      x if x == crate::common::constant::CacheType::AutoDiag as i32 => {
        self.diags.invalidate_all();
        self.diags.run_pending_tasks().await;
        let diag_ret = TjAutodiagcondition::query_many(&vec![], &vec![], &db.get_connection())
          .await
          .expect("query autodiag conditions error");
        for val in diag_ret.into_iter() {
          self.diags.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Combine as i32 => {
        self.combines.invalidate_all();
        let comb_items = TjCombineinfo::query_many(&vec![], &db.get_connection()).await.expect("query combine infos error");
        for val in comb_items.into_iter() {
          let key = format!("{}-{}", val.tj_combid, val.tj_itemid);
          self.combines.insert(key, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Item as i32 => {
        self.iteminfos.invalidate_all();
        self.iteminfos.run_pending_tasks().await;
        let items = TjIteminfo::query_many(&vec![], -1, -1, -1, &vec![], &db.get_connection()).await.expect("query iteminfos error");
        for val in items.into_iter() {
          // cache_wr.cache_set(val.tj_itemid.to_owned(), val);
          self.iteminfos.insert(val.tj_itemid.clone(), val).await;
        }
        self.iteminfos.run_pending_tasks().await;
        info!("缓存的项目信息数量：{}", self.iteminfos.entry_count());
      }
      x if x == crate::common::constant::CacheType::ItemResult as i32 => {
        self.itemrefs.invalidate_all();
        self.itemrefs.run_pending_tasks().await;
        let infos = TjItemresultinfo::query_many(&vec![], &db.get_connection()).await.expect("query error.......");
        // let items = TjIteminfo::query_many(&vec![], -1, -1, &vec![]).await.expect("query iteminfos error");
        for val in infos.into_iter() {
          // cache_wr.cache_set(val.tj_itemid.to_owned(), val);
          self.itemrefs.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Diseases as i32 => {
        self.diseases.invalidate_all();
        self.diseases.run_pending_tasks().await;
        let dis_ret = TjDiseases::query_many(&vec![], &db.get_connection()).await.expect("init disease error");
        for v in dis_ret.into_iter() {
          self.diseases.insert(v.id, v).await;
        }
      }
      x if x == crate::common::constant::CacheType::Dept as i32 => {
        self.deptinfos.invalidate_all();
        self.deptinfos.run_pending_tasks().await;
        let items = TjDepartinfo::query_many(&vec![], &vec![], -1, &db.get_connection()).await.expect("query departments error");
        for val in items.into_iter() {
          // cache_wr.cache_set(val.tj_itemid.to_owned(), val);
          self.deptinfos.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Staff as i32 => {
        self.staffs.invalidate_all();
        self.staffs.run_pending_tasks().await;
        let infos = TjStaffadmin::query_many(&vec![], &vec![], &db.get_connection()).await.expect("query infoconfigs error.......");
        for val in infos.into_iter() {
          self.staffs.insert(val.id, val).await;
        }
      }
      // x if x == crate::common::constant::CacheType::OccuConditions as i32 => {
      //   self.occuconds.invalidate_all();
      //   self.occuconds.run_pending_tasks().await;
      //   let infos = TjOccucondition::query_many("", &db.get_connection()).await.expect("query occuconds error");
      //   for val in infos.into_iter() {
      //     self.occuconds.insert(val.id, val).await;
      //   }
      // }
      x if x == crate::common::constant::CacheType::Corpinfo as i32 => {
        self.corpinfos.invalidate_all();
        self.corpinfos.run_pending_tasks().await;
        let infos = TjCorpinfo::query_many(&vec![], "", &vec![], &db.get_connection()).await.expect("query corpinfos error");
        for val in infos.into_iter() {
          self.corpinfos.insert(val.id, val).await;
        }
        self.corpinfos.run_pending_tasks().await;
        info!("corpinfos are updated......,current total size is:{}", &self.corpinfos.entry_count());
      }
      x if x == crate::common::constant::CacheType::Hazardinfo as i32 => {
        self.hazards.invalidate_all();
        self.hazards.run_pending_tasks().await;
        let infos = TjHazardinfo::query_many(&vec![], &vec![], &vec![], &db.get_connection()).await.expect("query corpinfos error");
        for val in infos.into_iter() {
          self.hazards.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Hazardtype as i32 => {
        self.hdtypes.invalidate_all();
        let infos = TjHazardtype::query_many(&db.get_connection()).await.expect("query corpinfos error");
        for val in infos.into_iter() {
          self.hdtypes.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Deptitem as i32 => {
        self.deptitems.invalidate_all();
        self.deptitems.run_pending_tasks().await;
        let infos = TjDeptitem::query_many(&vec![], &vec![], &db.get_connection()).await.expect("query corpinfos error");
        for val in infos.into_iter() {
          self.deptitems.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Guideinfo as i32 => {
        self.guides.invalidate_all();
        self.guides.run_pending_tasks().await;
        let infos = TjGuideinfo::query_many(&vec![], &db.get_connection()).await.expect("query corpinfos error");
        for val in infos.into_iter() {
          self.guides.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::GuideItem as i32 => {
        self.guideitems.invalidate_all();
        self.guideitems.run_pending_tasks().await;
        let infos = TjGuideitem::query_many(&vec![], &db.get_connection()).await.expect("query corpinfos error");
        for val in infos.into_iter() {
          self.guideitems.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Baritem as i32 => {
        self.baritems.invalidate_all();
        self.baritems.run_pending_tasks().await;
        let infos = TjBaritems::query_many(&vec![], &db.get_connection()).await.expect("query corpinfos error");
        for val in infos.into_iter() {
          self.baritems.insert(val.tj_binum.to_string(), val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Bardetails as i32 => {
        self.bardetails.invalidate_all();
        self.bardetails.run_pending_tasks().await;
        let infos = TjBardetail::query_many(&vec![], &db.get_connection()).await.expect("query corpinfos error");
        for val in infos.into_iter() {
          self.bardetails.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Barname as i32 => {
        self.barnames.invalidate_all();
        self.barnames.run_pending_tasks().await;
        let infos = TjBarnameinfo::query_many(&vec![], &db.get_connection()).await.expect("query corpinfos error");
        for val in infos.into_iter() {
          self.barnames.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Itemtype as i32 => {
        self.itemtypes.invalidate_all();
        self.itemtypes.run_pending_tasks().await;
        let infos = TjItemtype::query_many(&vec![], &db.get_connection()).await.expect("query corpinfos error");
        for val in infos.into_iter() {
          self.itemtypes.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::ItemRange as i32 => {
        self.rangeinfos.invalidate_all();
        self.rangeinfos.run_pending_tasks().await;
        let infos = TjItemrangeinfo::query_many(-1, -1, &vec![], &db.get_connection()).await.expect("query rangeinfos error");
        for val in infos.into_iter() {
          self.rangeinfos.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::EvalLaw as i32 => {
        self.lawinfos.invalidate_all();
        self.lawinfos.run_pending_tasks().await;
        let infos = TjEvallaw::query_many(&vec![], &db.get_connection()).await.expect("query evallaw error");
        for val in infos.into_iter() {
          self.lawinfos.insert(val.id, val).await;
        }
      }
      x if x == crate::common::constant::CacheType::Occucond as i32 => {
        self.occuconds.invalidate_all();
        self.occuconds.run_pending_tasks().await;
        let infos = TjOccucondition::query_many("", &db.get_connection()).await.expect("query occu conds error");
        for val in infos.into_iter() {
          self.occuconds.insert(val.id, val).await;
        }
      }
      _ => {
        error!("还没有实现的缓存更新类别：{ct}");
      }
    }
  }

  // pub async fn refresh_autodiag_conditions(&self, infos: &Vec<TjAutodiagcondition>) {
  //   self.diags.invalidate_all();
  //   for val in infos.iter() {
  //     self.diags.insert(val.id, val.clone()).await;
  //   }
  // }
}

// let deptitems: Cache<i64, TjDeptitem> = Cache::builder().build();
// let infos = TjDeptitem::query_many(&mut db.get_connection()).await.expect("query corpinfos error");
// for val in infos.into_iter() {
//   deptitems.insert(val.id, val).await;
// }
