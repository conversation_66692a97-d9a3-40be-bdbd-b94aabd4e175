use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjAudiogramdetail {
  pub id: i64,
  pub tj_testid: String,
  pub tj_itemid: String,
  pub tj_freq: i32,
  pub tj_adtype: i32,
  pub tj_ear: i32,
  pub tj_result: i32,
  pub tj_revise: i32,
  pub tj_cover: i32,
}
crud!(TjAudiogramdetail {}, "tj_audiogramdetail");
rbatis::impl_select!(TjAudiogramdetail{query_many(testids:&[&str]) => "`where tj_testid in ${testids.sql()} `"});
rbatis::impl_select!(TjAudiogramdetail{query(testid:&str) => "`where tj_testid = #{testid} `"});
rbatis::impl_delete!(TjAudiogramdetail{delete(testids:&[&str]) => "`where tj_testid in ${testids.sql()} `"});

impl TjAudiogramdetail {
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<TjAudiogramdetail>) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<TjAudiogramdetail>, Vec<TjAudiogramdetail>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = TjAudiogramdetail::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = TjAudiogramdetail::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }
}
