//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "s_cdc_hazard_checkset")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub cdc_hazard_code: String,
  pub cdc_hazard_name: String,
  pub cdc_item_code: String,
  pub cdc_item_name: String,
  pub cdc_test_type: i32,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
