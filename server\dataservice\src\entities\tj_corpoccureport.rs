//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(C<PERSON>, Debug, Default, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_corpoccureport")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_corpid: String,
  pub tj_corpname: String,
  pub tj_wtcorpname: String,
  pub tj_starttime: i64,
  pub tj_endtime: i64,
  pub tj_testyear: i32,
  pub tj_testtype: i32,
  pub tj_typename: String,
  #[sea_orm(column_type = "custom(\"MEDIUMTEXT\")")]
  pub tj_poisions: String,
  pub tj_testdate: String,
  pub tj_testaddress: String,
  pub tj_peoplenum: i32,
  pub tj_apeoplenum: i32,
  pub tj_testitems: String,
  #[sea_orm(column_type = "custom(\"MEDIUMTEXT\")")]
  pub tj_evalaw: String,
  #[sea_orm(column_type = "custom(\"MEDIUMTEXT\")")]
  pub tj_testlaw: String,
  #[sea_orm(column_type = "custom(\"MEDIUMTEXT\")")]
  pub tj_result: String,
  pub tj_createdate: i64,
  pub tj_moddate: i64,
  pub tj_creator: String,
  pub tj_modifier: String,
  pub tj_reportnum: String,
  pub tj_status: i32,
  pub tj_peid: i32,
  pub tj_pages: i32,
  pub tj_isrecheck: i32,
  pub tj_orptid: i32,
  pub tj_reportnumint: String,
  pub tj_pyjm: String,
  pub tj_reporttype: i32,
  pub tj_syncflag: i32,
  pub tj_memo: String,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
