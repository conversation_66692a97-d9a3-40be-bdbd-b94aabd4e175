//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "s_cdc_hazard_checkset")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub cdc_hazard_code: String,
    pub cdc_hazard_name: String,
    pub cdc_item_code: String,
    pub cdc_item_name: String,
    pub cdc_test_type: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
