use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_testsummary")]
#[derive(<PERSON><PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct TjTestsummary {
    pub id: i64,
    pub tj_testid: String,
    pub tj_deptid: String,
    pub tj_summary: String,
    pub tj_suggestion: String,
    pub tj_isfinished: i32,
    pub tj_doctorid: String,
    pub tj_doctor: String,
    pub tj_date: i64,
    pub tj_forceend: i32,
    pub tj_checkdoctor: String,
    pub tj_checkdate: i64,
}
