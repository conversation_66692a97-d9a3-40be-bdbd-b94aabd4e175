use crate::config::settings::Settings;
use anyhow::{anyhow, Result};
use chrono::{Duration, Local};
// use dbopservice::{
//   dataaccess::{prelude::*, v_tj_lisresult::VTjLisresultV2},
//   dbinit::DbConnection,
// };
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{
  client::httpclient::HttpClient,
  common::constant::{DeptType, ExamStatus, RecvResultCode, YesOrNo},
  dto::{EgoDto, MultipleKeyDto, ResultMessage},
  external::{recvexternallis::RecvExternalLis, wyhsservice::WyhsService},
  medexam::{checkitemsvc::CheckitemSvc, medexamsvc::MedexamSvc, summarysvc::SummarySvc},
  SYSCACHE,
};
// use rbatis::RBatis;
use utility::timeutil;
use tracing::*;

pub struct RecvLabdata;

impl RecvLabdata {
  pub async fn recv_labdata(dto: &MultipleKeyDto, config: &Settings, import_type: i32, extlis: &RecvExternalLis, db: &DbConnection) -> Vec<ResultMessage> {
    let mut lisval = dto.keystr2.to_owned();
    let testid = dto.keystr1.to_string();

    // info!("extlis info:{:?}", &extlis);

    let ret = TjMedexaminfo::query(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: ret.as_ref().unwrap_err().to_string(),
      }];
      // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let medret = ret.unwrap();
    if medret.is_none() {
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 不能找到体检信息", &testid),
      }];
      // return Err(anyhow!("不能找到体检号为:{}的体检信息", &testid));
    }
    let mut medinfo = medret.unwrap();

    if medinfo.tj_checkstatus <= ExamStatus::Appoint as i32 {
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 该体检者预约状态，不能接收数据", &testid),
      }];
    }
    if import_type == 1 {
      //从武义壶山医院获取lis结果数据
      if config.external.exttype.eq_ignore_ascii_case("wyhs") {
        //武义壶山医院
        if config.external.serverurl.is_empty() {
          let retmsg = vec![ResultMessage {
            code: RecvResultCode::Failure as i32,
            message: "对接服务器未配置".to_string(),
          }];
          // return response_json_value_ok(1, retmsg);
          return retmsg;
        }

        let ret = WyhsService::receive_lis_result(&medinfo, &config.external.serverurl, db).await;
        if ret.as_ref().is_err() {
          let retmsg = vec![ResultMessage {
            code: RecvResultCode::Failure as i32,
            message: ret.unwrap_err().to_string(),
          }];
          return retmsg;
        }
      } else {
        //从nodipego获取lis数据
        if config.application.lisego == YesOrNo::Yes as i32 {
          //用ego的方式获取视图数据
          let server = config.application.proxyserver.to_string();
          if server.is_empty() {
            let retmsg = vec![ResultMessage {
              code: RecvResultCode::Failure as i32,
              message: "ego服务器信息没有配置".to_string(),
            }];
            // return response_json_value_ok(1, retmsg);
            return retmsg;
          }

          let uri = format!("{}/api/extend/lis/result", server);
          let ego_dto = EgoDto {
            testid: dto.keystr1.to_string(),
            extkey: lisval.to_string(),
          };
          let ret: Result<Vec<VTjLisresult>> = HttpClient::send_http_post_request("", &uri, &Some(ego_dto)).await;
          // info!("get from server:{},result:{:?}", &uri, &ret);
          if ret.as_ref().is_err() {
            error!("ego get error:{}", ret.as_ref().unwrap_err().to_string());
            let retmsg = vec![ResultMessage {
              code: RecvResultCode::Failure as i32,
              message: ret.as_ref().unwrap_err().to_string(),
            }];
            // return response_json_value_ok(1, retmsg);
            return retmsg;
          }
        } else {
          //用本地连接视图的方式获取数据
          // info!("用本地连接数据库视图的方式获取lis数据......");
          if lisval.is_empty() {
            lisval = "default".to_string();
          }
          let extlis_val = extlis.get_liskey();
          if extlis_val.eq_ignore_ascii_case(&lisval) {
            //default
            let ret = extlis.query_from_external_lis(&testid, db).await;
            if ret.as_ref().is_err() {
              let retmsg = vec![ResultMessage {
                code: RecvResultCode::Failure as i32,
                message: ret.as_ref().unwrap_err().to_string(),
              }];
              // return response_json_value_ok(1, retmsg);
              return retmsg;
            }
          } else {
            //需要建立新链接。。。。。。
            let lis = config.extlis.get(&lisval);
            if lis.is_none() {
              // return response_json_value_error("lis连接信息没有配置", "");
              let retmsg = vec![ResultMessage {
                code: RecvResultCode::Failure as i32,
                message: format!("{}的lis连接信息没有配置", &lisval),
              }];
              // return response_json_value_ok(1, retmsg);
              return retmsg;
            }
            let lis = lis.unwrap();
            //create connection
            let external_lis_new = RecvExternalLis::new(&lisval, &lis.username, &lis.password, &lis.uri, lis.dbtype).await;
            let ret = external_lis_new.query_from_external_lis(&testid, db).await;
            if ret.as_ref().is_err() {
              let retmsg = vec![ResultMessage {
                code: RecvResultCode::Failure as i32,
                message: ret.as_ref().unwrap_err().to_string(),
              }];
              // return response_json_value_ok(1, retmsg);
              return retmsg;
            }
            //release connection
            external_lis_new.release().await;
          }
        }
      }
    }
    //从获取的数据中获取结果，并小结
    // let testid = dto.keystr1.to_owned();
    let testids: Vec<String> = vec![testid.to_string()];
    let deptids_string = dto.keystr3.to_owned().unwrap_or_default();
    let deptids: Vec<String> = deptids_string.iter().map(|v| v.to_string()).collect();
    let auto_summary = dto.keyint1;
    // let auto_summary = dto.keyint2;

    let ret = TjCheckallnew::query(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: ret.as_ref().unwrap_err().to_string(),
      }];
    }
    let ckall = ret.unwrap();
    if ckall.is_none() {
      // return Err(anyhow!("没有{testid}的总检信息"));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 不能找到总检信息", &testid),
      }];
    }
    let ckall = ckall.unwrap();
    if ckall.tj_castatus == YesOrNo::Yes as i32 {
      // return Err(anyhow!("体检号为:{}的体检已经总检，不能再次接收数据", &testid));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 已经总检，不能再次接收数据", &testid),
      }];
    }

    let ret = TjCheckiteminfo::query_many(&testids, &deptids, -1, -1, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: ret.as_ref().unwrap_err().to_string(),
      }];
    }
    let ciinfos_all = ret.unwrap();
    if ciinfos_all.len() <= 0 {
      // return Err(anyhow!("没有{testid}在这些科室的检查数据"));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 没有这些科室的检查数据", &testid),
      }];
    }

    let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: ret.as_ref().unwrap_err().to_string(),
      }];
    }
    let medret = ret.unwrap();
    if medret.is_none() {
      // return Err(anyhow!("不能找到体检号为:{}的体检人员信息", &testid));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{} 不能找到人员信息", &testid),
      }];
    }
    let ptinfo = medret.unwrap();

    let ret = TjPatienthazards::query(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: ret.as_ref().unwrap_err().to_string(),
      }];
    }
    let pthazards = ret.unwrap();
    let ret = TjTestsummary::query_many(&testids, &deptids, -1, -1, -1, &"".to_string(), -1, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      // return Err(anyhow!("没有{testid}在这些科室的检查信息"));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{}({}) 不能找到科室检查信息", &testid, &ptinfo.tj_pname),
      }];
    }
    let mut summaries = ret.unwrap();
    if summaries.len() <= 0 {
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: format!("{}({}) 不能找到科室检查信息", &testid, &ptinfo.tj_pname),
      }];
    }
    let ret = TjIteminfo::query_all_with_lisnum(&mut db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("query iteminfo by lisnum error:{}", ret.as_ref().unwrap_err().to_string());
      // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      return vec![ResultMessage {
        code: RecvResultCode::Failure as i32,
        message: ret.as_ref().unwrap_err().to_string(),
      }];
    }
    let iteminfos = ret.unwrap();

    let mut recv_results: Vec<ResultMessage> = Vec::new();
    //从外部对接获取数据
    if import_type == 1 {
      // info!("从外部对接获取数据......");
      let ret = RecvLabdata::query_from_external_labresult(&medinfo.tj_testid, config, db).await;
      if ret.as_ref().is_err() {
        error!("query error:{}", ret.as_ref().unwrap_err().to_string());
        // return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        return vec![ResultMessage {
          code: RecvResultCode::Failure as i32,
          message: format!("{} {}", &medinfo.tj_testid, ret.as_ref().unwrap_err().to_string()),
        }];
      }
      let lisresults = ret.unwrap();
      if lisresults.len() <= 0 {
        // return Err(anyhow!("lis没有结果数据"));
        return vec![ResultMessage {
          code: RecvResultCode::Failure as i32,
          message: format!("{}({}) 对接lis系统没有该体检编号的结果数据", &medinfo.tj_testid, &ptinfo.tj_pname),
        }];
      }
      for dptid in deptids_string.iter() {
        // info!("开始接收科室：{dptid}的检查结果");
        let mut recv_ret = ResultMessage { ..Default::default() };

        let summary = summaries
          .iter_mut()
          .find(|f| f.tj_deptid.eq_ignore_ascii_case(&dptid) && f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid));
        if summary.is_none() {
          recv_ret.code = RecvResultCode::Failure as i32;
          recv_ret.message = format!(
            "{}({}) 没有{}科室的检查信息",
            medinfo.tj_testid,
            ptinfo.tj_pname,
            SYSCACHE.get().unwrap().get_department(&dptid, &db).await.tj_deptname
          );
          recv_results.push(recv_ret);
          continue;
        }
        let summary = summary.unwrap();

        //从lis获取结果数据
        // info!("开始处理lis结果数据");
        let ret = RecvLabdata::recv_external_lab_result_by_dept(&mut medinfo, summary, &pthazards, &ptinfo, &ciinfos_all, &lisresults, &iteminfos, auto_summary, db).await;
        recv_results.push(ret);
      }
    } else {
      //从本地导入获取数据
      let ret = TjLabresult::query_many(&testid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        return vec![ResultMessage {
          code: RecvResultCode::Failure as i32,
          message: format!("{} 获取结果数据失败:{}", &medinfo.tj_testid, &ret.unwrap_err().to_string()),
        }];
      }
      let tjlabrets = ret.unwrap();
      if tjlabrets.len() <= 0 {
        return vec![ResultMessage {
          code: RecvResultCode::Failure as i32,
          message: format!("{} 没有结果数据", &medinfo.tj_testid),
        }];
      }
      // let ret = TjItemrefinfo::select_all(&mut db.get_connection()).await;
      // if ret.as_ref().is_err() {
      //   return vec![ResultMessage {
      //     code: RecvResultCode::Failure as i32,
      //     message: format!("获取仪器项目信息错误:{}", &ret.unwrap_err().to_string()),
      //   }];
      // }
      // let itemrefinfos = ret.unwrap();
      for dptid in deptids_string.iter() {
        // info!("从本地导入获取数据......,导入科室:{}", &dptid);
        let mut recv_ret = ResultMessage { ..Default::default() };

        let summary = summaries
          .iter_mut()
          .find(|f| f.tj_deptid.eq_ignore_ascii_case(&dptid) && f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid));
        if summary.is_none() {
          recv_ret.code = RecvResultCode::Failure as i32;
          recv_ret.message = format!(
            "{} 没有科室{}的检查信息",
            medinfo.tj_testid,
            SYSCACHE.get().unwrap().get_department(&dptid, &db).await.tj_deptname
          );
          recv_results.push(recv_ret);
          continue;
        }
        let summary = summary.unwrap();
        //从lis获取结果数据
        // let ret = RecvLabdata::recv_lab_result_by_dept(&mut medinfo, summary, &pthazards, &ptinfo, &ciinfos_all, auto_summary, db).await;
        let ret = RecvLabdata::recv_lab_result_by_dept_v2(&mut medinfo, summary, &pthazards, &ptinfo, &ciinfos_all, auto_summary, &tjlabrets, db).await;
        recv_results.push(ret);
      }
    }
    recv_results
  }

  //从导入接收
  async fn recv_lab_result_by_dept_v2(
    medinfo: &mut TjMedexaminfo,
    summary: &mut TjTestsummary,
    pthazards: &Vec<TjPatienthazards>,
    ptinfo: &TjPatient,
    ciinfos_all: &Vec<TjCheckiteminfo>,
    auto_summary: i64,
    tjlabrets: &Vec<TjLabresult>,
    // staff: &TjStaffadmin,
    db: &DbConnection,
  ) -> ResultMessage {
    let mut recv_ret: ResultMessage = ResultMessage { ..Default::default() };

    let mut ciinfos: Vec<TjCheckiteminfo> = ciinfos_all
      .iter()
      .filter(|&f| f.tj_deptid.eq_ignore_ascii_case(&summary.tj_deptid) && f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid))
      .map(|v| v.to_owned())
      .collect::<Vec<TjCheckiteminfo>>();
    let mut not_found = 0;
    let mut checkdate = 0;
    let mut checkdoctor = "".to_string();
    let mut recheckdoctor = "".to_string();
    for ci in ciinfos.iter_mut() {
      if ci.tj_combineflag == YesOrNo::Yes as i32 {
        // ci.tj_checkdoctor = tjlabrets.iter().find(|&f| f)
        continue;
      }

      let lbrt = tjlabrets.iter().find(|&c| {
        let b1 = c.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid);
        let b2 = if c.tj_itemid.is_empty() {
          false
        } else {
          let lab_itemids: Vec<&str> = c.tj_itemid.split(",").collect();
          if lab_itemids.into_iter().find(|&f2| f2.eq_ignore_ascii_case(&ci.tj_itemid)).is_some() {
            true
          } else {
            false
          }
        };

        b1 && b2
      });
      if lbrt.is_none() {
        not_found += 1;
        continue;
      }

      let lbrt = lbrt.unwrap();

      if lbrt.tj_final.is_empty() {
        info!("体检号：{}，项目编号：{}，项目名称:{}，结果为空，不处理", &medinfo.tj_testid, &lbrt.tj_itemid, &lbrt.tj_analyte);
        not_found += 1;
        continue;
      }
      ci.tj_result = lbrt.tj_final.to_owned();
      ci.tj_checkdoctor = lbrt.tj_checkdoctor.to_owned();
      ci.tj_recheckdoctor = if lbrt.tj_recheckdoctor.is_empty() {
        ci.tj_checkdoctor.to_string()
      } else {
        lbrt.tj_recheckdoctor.to_owned()
      };
      ci.tj_checkdate = lbrt.tj_importdate;
      ci.tj_recheckdate = lbrt.tj_importdate;
      // ci.tj_abnormalflag = lbrt.tj_abnormalflag;
      if !lbrt.tj_units.is_empty() {
        ci.tj_itemunit = lbrt.tj_units.to_owned()
      };
      if !lbrt.tj_ckfw.is_empty() {
        ci.tj_itemrange = lbrt.tj_ckfw.to_owned();
      }
      if !lbrt.tj_displowhigh.is_empty() {
        ci.tj_abnormalshow = lbrt.tj_displowhigh.to_owned();
      }
      if !lbrt.tj_ckfw_l.is_empty() {
        ci.tj_lowvalue = lbrt.tj_ckfw_l.to_string();
      }
      if !lbrt.tj_ckfw_h.is_empty() {
        ci.tj_uppervalue = lbrt.tj_ckfw_h.to_string();
      }

      checkdate = ci.tj_checkdate;
      checkdoctor = ci.tj_checkdoctor.to_owned();
      recheckdoctor = ci.tj_recheckdoctor.to_owned();

      // if ci.tj_abnormalflag == YesOrNo::No as i32 {
      let mut sex = ptinfo.tj_psex;
      let mut age = 0;
      if !ptinfo.tj_pidcard.is_empty() {
        let ckret = utility::cidcard::IdCard::new(&ptinfo.tj_pidcard);
        sex = ckret.get_gender();
        age = ckret.get_age();
      }

      CheckitemSvc::update_checktiem_result_flag(ci, age, sex, &db).await;
      // }
    }
    for ci in ciinfos.iter_mut() {
      if ci.tj_combineflag == YesOrNo::Yes as i32 {
        ci.tj_checkdate = checkdate;
        ci.tj_recheckdate = checkdate;
        ci.tj_checkdoctor = checkdoctor.clone();
        ci.tj_recheckdoctor = recheckdoctor.clone();
      }
    }
    if not_found != ciinfos.len() - 1 {
      // info!("开始保存结果信息...................");
      let ret = TjCheckiteminfo::save_many(&ciinfos, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        recv_ret.code = RecvResultCode::Failure as i32;
        recv_ret.message = ret.as_ref().unwrap_err().to_string();
        return recv_ret;
      }
    }

    if not_found > 0 && not_found < ciinfos.len() - 1 {
      recv_ret.code = RecvResultCode::PartialOK as i32;
      recv_ret.message = format!(
        "{}({}) 在科室{}数据接收部分成功",
        medinfo.tj_testid,
        ptinfo.tj_pname,
        SYSCACHE.get().unwrap().get_department(&summary.tj_deptid, &db).await.tj_deptname
      );
    } else if not_found == ciinfos.len() - 1 {
      recv_ret.code = RecvResultCode::Failure as i32;
      recv_ret.message = format!(
        "{}({}) 在科室{}数据接收失败",
        medinfo.tj_testid,
        ptinfo.tj_pname,
        SYSCACHE.get().unwrap().get_department(&summary.tj_deptid, &db).await.tj_deptname
      );
    } else if not_found == 0 {
      recv_ret.code = RecvResultCode::Ok as i32;
      recv_ret.message = format!(
        "{}({}) 在科室{}数据接收成功",
        medinfo.tj_testid,
        ptinfo.tj_pname,
        SYSCACHE.get().unwrap().get_department(&summary.tj_deptid, &db).await.tj_deptname
      );
      //全部接收成功，开始自动小结
      if auto_summary == YesOrNo::Yes as i64 {
        summary.tj_checkdate = checkdate;
        summary.tj_date = checkdate;
        summary.tj_doctor = checkdoctor.to_owned();
        summary.tj_checkdoctor = recheckdoctor.to_owned();
        let ret = SummarySvc::summary(medinfo, &ptinfo, summary, &mut ciinfos, &pthazards, 0, 0, &"".to_string(), &db).await;
        let status_ret = MedexamSvc::update_medexam_status_updown(&medinfo.tj_testid, 0, 1, db).await;
        if status_ret.is_err() {
          error!("更新体检状态失败");
        }
        info!("自动小结结果：{:?}", &ret);
      }
    }

    // info!("数据接收结束：{:?}", &recv_ret);
    recv_ret
  }
  //从导入接收
  async fn _recv_lab_result_by_dept(
    medinfo: &mut TjMedexaminfo,
    summary: &mut TjTestsummary,
    pthazards: &Vec<TjPatienthazards>,
    ptinfo: &TjPatient,
    ciinfos_all: &Vec<TjCheckiteminfo>,
    auto_summary: i64,
    // staff: &TjStaffadmin,
    db: &DbConnection,
  ) -> ResultMessage {
    let mut recv_ret: ResultMessage = ResultMessage { ..Default::default() };

    let testid = medinfo.tj_testid.to_string();

    // info!("从本地接收数据，开始查找labresult信息");
    let ret = TjLabresult::query_many(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      recv_ret.code = RecvResultCode::Failure as i32;
      recv_ret.message = ret.as_ref().unwrap_err().to_string();
      return recv_ret;
    }
    let tjlabrets = ret.unwrap();
    // info!("本地的接收数据数量:{}", &tjlabrets.len());

    //从本地表中接收数据（tj_labresult)
    if tjlabrets.len() <= 0 {
      recv_ret.code = RecvResultCode::Failure as i32;
      recv_ret.message = format!("没有{testid}的结果数据");
      return recv_ret;
    }

    let mut ciinfos: Vec<TjCheckiteminfo> = ciinfos_all
      .iter()
      .filter(|&f| f.tj_deptid.eq_ignore_ascii_case(&summary.tj_deptid) && f.tj_testid.eq_ignore_ascii_case(&testid))
      .map(|v| v.to_owned())
      .collect::<Vec<TjCheckiteminfo>>();
    let mut not_found = 0;
    let mut checkdate = 0;
    let mut checkdoctor = "".to_string();
    let mut recheckdoctor = "".to_string();
    for ci in ciinfos.iter_mut() {
      if ci.tj_combineflag == YesOrNo::Yes as i32 {
        // ci.tj_checkdoctor = tjlabrets.iter().find(|&f| f)
        continue;
      }
      let lbrt = tjlabrets
        .iter()
        .find(|&c| c.tj_itemid.eq_ignore_ascii_case(&ci.tj_itemid) && c.tj_testid.eq_ignore_ascii_case(&testid));
      if lbrt.is_none() {
        not_found += 1;
        continue;
      }

      let lbrt = lbrt.unwrap();

      if lbrt.tj_final.is_empty() && lbrt.tj_checkdoctor.is_empty() {
        info!(
          "体检号：{}，项目编号：{}，项目名称:{}，结果跟检查医生都为空，不处理",
          &medinfo.tj_testid,
          &lbrt.tj_itemid,
          &lbrt.tj_analyte
        );
        not_found += 1;
        continue;
      }
      ci.tj_result = lbrt.tj_final.to_owned();
      ci.tj_checkdoctor = lbrt.tj_checkdoctor.to_owned();
      ci.tj_recheckdoctor = lbrt.tj_recheckdoctor.to_owned();
      ci.tj_checkdate = lbrt.tj_importdate;
      ci.tj_recheckdate = lbrt.tj_importdate;
      ci.tj_abnormalflag = lbrt.tj_abnormalflag;
      ci.tj_itemunit = lbrt.tj_units.to_owned();
      ci.tj_itemrange = lbrt.tj_ckfw.to_owned();
      ci.tj_abnormalshow = lbrt.tj_displowhigh.to_owned();
      ci.tj_lowvalue = lbrt.tj_ckfw_l.to_string();
      ci.tj_uppervalue = lbrt.tj_ckfw_h.to_string();
      checkdate = ci.tj_checkdate;
      checkdoctor = ci.tj_checkdoctor.to_owned();
      recheckdoctor = ci.tj_recheckdoctor.to_owned();
      if ci.tj_abnormalflag == YesOrNo::No as i32 {
        let mut sex = ptinfo.tj_psex;
        let mut age = 0;
        if !ptinfo.tj_pidcard.is_empty() {
          let ckret = utility::cidcard::IdCard::new(&ptinfo.tj_pidcard);
          sex = ckret.get_gender();
          age = ckret.get_age();
        }
        CheckitemSvc::update_checktiem_result_flag(ci, age, sex, &db).await;
      }
    }
    for ci in ciinfos.iter_mut() {
      if ci.tj_combineflag == YesOrNo::Yes as i32 {
        ci.tj_checkdate = checkdate;
        ci.tj_recheckdate = checkdate;
        ci.tj_checkdoctor = checkdoctor.clone();
        ci.tj_recheckdoctor = recheckdoctor.clone();
      }
    }
    if not_found != ciinfos.len() - 1 {
      // info!("开始保存结果信息...................");
      let ret = TjCheckiteminfo::save_many(&ciinfos, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        recv_ret.code = RecvResultCode::Failure as i32;
        recv_ret.message = ret.as_ref().unwrap_err().to_string();
        return recv_ret;
      }
    }

    if not_found > 0 && not_found < ciinfos.len() - 1 {
      recv_ret.code = RecvResultCode::PartialOK as i32;
      recv_ret.message = format!(
        "{} {} 在科室{}数据接收部分成功",
        ptinfo.tj_pname,
        medinfo.tj_testid,
        SYSCACHE.get().unwrap().get_department(&summary.tj_deptid, &db).await.tj_deptname
      );
    } else if not_found == ciinfos.len() - 1 {
      recv_ret.code = RecvResultCode::Failure as i32;
      recv_ret.message = format!(
        "{} {} 在科室{}数据接收失败",
        ptinfo.tj_pname,
        medinfo.tj_testid,
        SYSCACHE.get().unwrap().get_department(&summary.tj_deptid, &db).await.tj_deptname
      );
    } else if not_found == 0 {
      recv_ret.code = RecvResultCode::Ok as i32;
      recv_ret.message = format!(
        "{} {} 在科室{}数据接收成功",
        ptinfo.tj_pname,
        medinfo.tj_testid,
        SYSCACHE.get().unwrap().get_department(&summary.tj_deptid, &db).await.tj_deptname
      );
      //全部接收成功，开始自动小结
      if auto_summary == YesOrNo::Yes as i64 {
        summary.tj_checkdate = checkdate;
        summary.tj_date = checkdate;
        summary.tj_doctor = checkdoctor.to_owned();
        summary.tj_checkdoctor = recheckdoctor.to_owned();
        let ret = SummarySvc::summary(medinfo, &ptinfo, summary, &mut ciinfos, &pthazards, 0, 0, &"".to_string(), &db).await;
        let status_ret = MedexamSvc::update_medexam_status_updown(&medinfo.tj_testid, 0, 1, db).await;
        if status_ret.is_err() {
          error!("更新体检状态失败");
        }
        info!("自动小结结果：{:?}", &ret);
      }
    }

    // info!("数据接收结束：{:?}", &recv_ret);
    recv_ret
  }
  //从外部对接接收
  async fn recv_external_lab_result_by_dept(
    medinfo: &mut TjMedexaminfo,
    summary: &mut TjTestsummary,
    pthazards: &Vec<TjPatienthazards>,
    ptinfo: &TjPatient,
    ciinfos_all: &Vec<TjCheckiteminfo>,
    lisresults: &Vec<VTjLisresult>,
    iteminfos: &Vec<TjIteminfo>,
    auto_summary: i64,
    // staff: &TjStaffadmin,
    db: &DbConnection,
  ) -> ResultMessage {
    let mut recv_ret: ResultMessage = ResultMessage { ..Default::default() };

    let mut bgys = "".to_string();
    let mut bgrq = "".to_string();
    let mut jcys = "".to_string();

    let mut ciinfos: Vec<TjCheckiteminfo> = ciinfos_all
      .iter()
      .filter(|&f| f.tj_deptid.eq_ignore_ascii_case(&summary.tj_deptid) && f.tj_testid.eq_ignore_ascii_case(&summary.tj_testid))
      .map(|v| v.to_owned())
      .collect::<Vec<TjCheckiteminfo>>();

    let mut not_found = 0;
    for ci in ciinfos.iter_mut() {
      if ci.tj_combineflag == YesOrNo::Yes as i32 {
        continue;
      }
      let iteminfo = iteminfos.iter().find(|&v| v.tj_itemid.eq_ignore_ascii_case(&ci.tj_itemid));
      if iteminfo.is_none() {
        error!("can't find iteminfo by itemid:{}", &ci.tj_itemid);
        not_found += 1;
        continue;
      }
      let iteminfo = iteminfo.unwrap();
      if iteminfo.tj_lisnum.is_empty() {
        error!("项目:{}未配置对接项目编号，请配置", &ci.tj_itemid);
        not_found += 1;
        continue;
      }
      let lisnums: Vec<&str> = iteminfo.tj_lisnum.split(&[',', '，'][..]).filter(|&v| !v.is_empty()).collect();
      let mut lisresult: Option<&VTjLisresult> = None;
      for lisnum in lisnums.iter() {
        if let Some(lisret) = lisresults
          .iter()
          .find(|&v| v.xmxh.trim().eq_ignore_ascii_case(&lisnum) && v.tjbh.eq_ignore_ascii_case(&medinfo.tj_testid))
        {
          lisresult = Some(lisret);
          break;
        }
      }
      if lisresult.is_none() {
        error!("没有找到项目：{}的结果数据（请检查对接项目编号）", &ci.tj_itemid);
        not_found += 1;
        continue;
      }
      let lbrt = lisresult.unwrap();

      if lbrt.xmjg.trim().is_empty() {
        info!("体检号：{}，项目编号：{}，项目名称:{}，检查结果为空，不处理", &medinfo.tj_testid, &lbrt.xmxh, &lbrt.xmmc);
        not_found += 1;
        continue;
      }

      // let lbrt = lbrt.unwrap();
      ci.tj_result = lbrt.xmjg.to_owned();
      ci.tj_checkdoctor = lbrt.jyys.to_owned();
      ci.tj_recheckdoctor = lbrt.bgys.to_owned();
      ci.tj_checkdate = timeutil::convert_datetime_to_timestamp(&lbrt.bgrq, "%Y-%m-%d %H:%M:%S");
      ci.tj_recheckdate = ci.tj_checkdate;
      ci.tj_abnormalflag = lbrt.sfyc;
      ci.tj_itemunit = lbrt.xmdw.to_owned();
      ci.tj_itemrange = lbrt.ckfw.to_owned();
      ci.tj_abnormalshow = lbrt.gdbj.to_owned();
      ci.tj_lowvalue = lbrt.ckdz.to_owned();
      ci.tj_uppervalue = lbrt.ckgz.to_owned();
      
      if ci.tj_abnormalflag == YesOrNo::No as i32 {
        CheckitemSvc::update_checktiem_result_flag_without_dingliang(ci, &db).await;
      }

      bgys = lbrt.bgys.to_string();
      bgrq = lbrt.bgrq.to_string();
      jcys = lbrt.jyys.to_string();
    }
    if not_found != ciinfos.len() - 1 {
      // info!("开始保存结果信息...................");
      for v in ciinfos.iter_mut() {
        v.tj_checkdate = timeutil::convert_datetime_to_timestamp(&bgrq, "%Y-%m-%d %H:%M:%S");
        v.tj_recheckdate = v.tj_checkdate;
        v.tj_checkdoctor = jcys.to_string();
        v.tj_recheckdoctor = bgys.to_string();
      }
      let ret = TjCheckiteminfo::save_many(&ciinfos, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        recv_ret.code = RecvResultCode::Failure as i32;
        recv_ret.message = ret.as_ref().unwrap_err().to_string();
        return recv_ret;
      }
    }

    if not_found > 0 && not_found < ciinfos.len() - 1 {
      recv_ret.code = RecvResultCode::PartialOK as i32;
      recv_ret.message = format!(
        "{}({}) 在科室{}数据接收部分成功",
        medinfo.tj_testid,
        ptinfo.tj_pname,
        SYSCACHE.get().unwrap().get_department(&summary.tj_deptid, &db).await.tj_deptname
      );
    } else if not_found == ciinfos.len() - 1 {
      recv_ret.code = RecvResultCode::Failure as i32;
      recv_ret.message = format!(
        "{}({}) 在科室{}数据接收失败",
        medinfo.tj_testid,
        ptinfo.tj_pname,
        SYSCACHE.get().unwrap().get_department(&summary.tj_deptid, &db).await.tj_deptname
      );
    } else if not_found == 0 {
      recv_ret.code = RecvResultCode::Ok as i32;
      recv_ret.message = format!(
        "{}({}) 在科室{}数据接收成功",
        medinfo.tj_testid,
        ptinfo.tj_pname,
        SYSCACHE.get().unwrap().get_department(&summary.tj_deptid, &db).await.tj_deptname
      );
      //全部接收成功，开始自动小结
      if auto_summary == YesOrNo::Yes as i64 {
        summary.tj_checkdoctor = bgys.to_string();
        summary.tj_doctor = bgys.to_string();
        summary.tj_date = utility::timeutil::convert_date_to_timestamp(&bgrq, "%Y-%m-%d %H:%M:%S");
        summary.tj_checkdate = utility::timeutil::convert_date_to_timestamp(&bgrq, "%Y-%m-%d %H:%M:%S");
        info!("开始自动小结：{:?}", &summary);
        let ret = SummarySvc::summary(medinfo, &ptinfo, summary, &mut ciinfos, &pthazards, 0, 0, &"".to_string(), &db).await;
        info!("自动小结结果：{:?}", &ret);
      }
    }

    info!("科室：{}数据接收结束,结果：{:?}", &summary.tj_deptid, &recv_ret);
    recv_ret
  }

  async fn query_from_external_labresult(testid: &String, _config: &Settings, db: &DbConnection) -> Result<Vec<VTjLisresult>> {
    let ret = VTjLisresult::query_many(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let lisresults = ret.unwrap();

    // info!("从外部表获取的结果数据:{:?}", &lisresults);
    Ok(lisresults)
  }

  pub async fn auto_recv_labresult(config: &Settings, auto_summary: i64, db: &DbConnection) -> Result<Vec<ResultMessage>> {
    let local_now = Local::now();
    let checked_add = local_now.checked_add_signed(Duration::days(-21));
    if checked_add.is_none() {
      return Err(anyhow!("date time error"));
    }
    let checked_add = checked_add.unwrap();
    let two_week_before = checked_add.timestamp();
    let current_date = local_now.timestamp();

    //query from tj_medexaminfo
    let ret = TjMedexaminfo::query_many(
      two_week_before,
      current_date,
      &vec![],
      &vec![],
      0,
      -1,
      &vec![],
      &vec![],
      &vec![ExamStatus::Examining as i32],
      -1,
      &"".to_string(),
      &db.get_connection(),
    )
    .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut medinfos = ret.unwrap();
    if medinfos.len() <= 0 {
      info!("没有需要接收数据的体检人员信息");
      return Ok(vec![]);
    }
    let testids: Vec<String> = medinfos.iter().map(|v| v.tj_testid.to_string()).collect();
    info!("本次需要自动接收的体检号：{:?}", &testids);
    // let testids_str: Vec<&str> = testids.iter().map(|v| v.as_str()).collect();
    let pids: Vec<String> = medinfos.iter().map(|v| v.tj_pid.to_string()).collect();
    // let pids: Vec<&str> = pids_string.iter().map(|v| v.as_str()).collect();

    //获取需要接收的科室信息（仅接收实验室数据)
    let deptinfos = SYSCACHE.get().unwrap().get_departs(&vec![], &db).await;
    let deptids: Vec<String> = deptinfos.iter().filter(|&d| d.tj_depttype == DeptType::Lab as i32).map(|v| v.tj_deptid.to_string()).collect();
    info!("需要接收的科室信息:{:?}", &deptids);
    let deptids_str: Vec<String> = deptids.iter().map(|v| v.to_string()).collect();

    let ret = TjPatient::query_many(&pids, "", &vec![], &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ptinfos_all = ret.unwrap();

    let ret = TjPatienthazards::query_many(&testids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let pthazards_all = ret.unwrap();

    let iteminfos = SYSCACHE.get().unwrap().get_iteminfos(&vec![], -1, -1, -1, &vec![], db).await;

    let mut recv_results: Vec<ResultMessage> = Vec::new();
    for medinfo in medinfos.iter_mut() {
      info!("开始接收:{}的结果数据", &medinfo.tj_testid);
      let testids_2_str: Vec<String> = vec![medinfo.tj_testid.to_string()];
      let ret = TjTestsummary::query_many(&testids_2_str, &deptids_str, 0, 0, YesOrNo::No as i32, "", -1, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let mut summaries = ret.unwrap();
      if summaries.len() <= 0 {
        info!("{} 没有未完成科室的检查信息，不需要接收", &medinfo.tj_testid);
        // return Ok(vec![]);
        continue;
      }

      let deptids_2: Vec<String> = summaries.iter().map(|v| v.tj_deptid.to_string()).collect();
      info!("体检号:{},接收科室:{:?}", &medinfo.tj_testid, &deptids_2);
      let deptids_2_str: Vec<String> = deptids_2.iter().map(|v| v.to_string()).collect();

      let ret = TjCheckiteminfo::query_many(&testids_2_str, &deptids_2_str, -1, -1, &vec![], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let ciinfos_all = ret.unwrap();
      if ciinfos_all.len() <= 0 {
        // return Err(anyhow!("没有该体检者在这些科室的检查数据"));
        continue;
      }

      let ret = RecvLabdata::query_from_external_labresult(&medinfo.tj_testid, config, &db).await;
      if ret.as_ref().is_err() {
        error!("query error:{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let lisresults = ret.unwrap();
      if lisresults.len() <= 0 {
        info!("lis没有体检号：{}的结果数据", &medinfo.tj_testid);
        continue;
      }

      let ptinfo = ptinfos_all.iter().find(|p| p.tj_pid.eq_ignore_ascii_case(&medinfo.tj_pid));
      if ptinfo.is_none() {
        error!("不能找到编号为{}的人员信息", &medinfo.tj_pid);
        continue;
      }
      let ptinfo = ptinfo.unwrap();
      let pthazards: Vec<TjPatienthazards> = pthazards_all
        .iter()
        .filter(|f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid))
        .map(|v| v.to_owned())
        .collect();
      //循环接收科室结果数据
      for summary in summaries.iter_mut() {
        if summary.tj_isfinished == YesOrNo::Yes as i32 {
          info!("体检号:{}在科室:{}已经结束，不需要再获取数据", &medinfo.tj_testid, &summary.tj_deptid);
          continue;
        }
        info!("开始接收体检号:{}在科室:{}的数据", &medinfo.tj_testid, &summary.tj_deptid);
        let ret = RecvLabdata::recv_external_lab_result_by_dept(medinfo, summary, &pthazards, &ptinfo, &ciinfos_all, &lisresults, &iteminfos, auto_summary, db).await;
        recv_results.push(ret);
      }
    }

    Ok(recv_results)
  }
}
