from sqlalchemy import Column, Integer, String
from dataentities.dbconn import Base


class TjPatient(Base):
    # 数据库中存储的表名
    __tablename__ = "tj_patient"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    tj_pid = Column(String(128), nullable=False)
    tj_psex = Column(Integer, nullable=False)
    tj_pname = Column(String(512), nullable=False, unique=True)
    tj_pmarriage = Column(Integer, nullable=False)
    tj_ptestnum = Column(Integer, nullable=False)
    tj_paddress = Column(String(512), nullable=False)
    tj_pphone = Column(String(512), nullable=False)
    tj_pemail = Column(String(512), nullable=False)
    tj_pbirthday = Column(String(512), nullable=False)
    tj_cardtype = Column(Integer, nullable=False)
    tj_pidcard = Column(String(512), nullable=False)
    tj_pcareer = Column(String(512), nullable=False)
    tj_pmobile = Column(String(512), nullable=False)
    tj_photo = Column(String(512), nullable=False)
    tj_cryptflag = Column(Integer, nullable=False)
    tj_popdate = Column(Integer, nullable=False)
    tj_staffid = Column(Integer, nullable=False)
    tj_pmemo = Column(String(512), nullable=False)
    tj_syncflag = Column(Integer, nullable=False)
    tj_nation = Column(String(512), nullable=False)
    tj_sign = Column(String(512), nullable=False)
    p_wkid = Column(Integer, nullable=False)
    tj_patid = Column(String(512), nullable=False)
