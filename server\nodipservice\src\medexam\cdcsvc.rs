use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};

pub struct CdcSvc;

impl CdcSvc {
  pub async fn query_cdc_dictionaries(tids: &Vec<i64>, db: &DbConnection) -> Result<Vec<SCdcDictionary>> {
    let ret = SCdcDictionary::query_many(tids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
}
