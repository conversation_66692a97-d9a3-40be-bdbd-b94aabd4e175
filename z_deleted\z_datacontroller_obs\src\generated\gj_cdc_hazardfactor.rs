//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "gj_cdc_hazardfactor")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub cdc_code: String,
    pub hazard_pcode: String,
    pub tj_hazardid: i32,
    pub description: String,
    pub hazard_type: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
