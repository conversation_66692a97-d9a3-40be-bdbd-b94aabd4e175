import os
from datetime import datetime

# from datetime import datetime
from docx import Document
from docx.shared import Pt, Cm, Inches
from docx.oxml.ns import qn
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_PARAGRAPH_ALIGNMENT
from docx.enum.table import WD_TABLE_ALIGNMENT, WD_CELL_VERTICAL_ALIGNMENT
from docx.enum.section import WD_ORIENTATION, WD_SECTION_START  # 导入节方向和分解符类型

# from docx.oxml.shared import OxmlElement
# from docx.oxml import ns
from docx.oxml import OxmlElement, ns
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml
from docx.table import _Cell
from common.add_float_picture import add_float_picture

import constant
from corpreport.common import (
    get_zbr,
    get_zjys,
    get_customer_name,
    get_pzys,
    get_shys,
    get_customer_stamp,
)


def generate_docx_corp_occureport(
    rptinfo,
    checkallinfos,
    medinfos,
    patients,
    corpinfo,
    dicts,
    customer,
    pagestyle,
    splitinrow,
    outdir,
    areaname,
):
    c_dict = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictTesttype.value
        and dict.ss_pid == rptinfo.tj_testtype
    )
    # 生成docx文件 # 职业健康报告
    output = "{}-{}-{}-{}.docx".format(
        rptinfo.tj_corpname, "职业",c_dict.ss_name, rptinfo.tj_reportnumint
    )
    document = Document()
    # 在python-docx包中要使用section.page_width和section.page_height属性来实现页面大小的读取和设置
    document.add_paragraph()  # 添加一个空白段落
    section = document.sections[0]  # 获取section对象
    section.page_width = Cm(21)
    section.page_height = Cm(29.7)

    set_section_margin(section)

    section.different_first_page_header_footer = True
    header = section.header  # 获取第一个节的页眉
    paragraph = header.paragraphs[0]  # 获取页眉的第一个段落
    insertHR(paragraph)
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    run = paragraph.add_run(rptinfo.tj_reportnum)  # 添加页面内容
    run.font.name = "宋体"
    run.font.size = Pt(10)
    run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")

    generate_front_page(rptinfo, corpinfo, dicts, document,areaname)
    document.add_page_break()
    # 报告说明
    generate_desc_page(dicts, document)
    document.add_page_break()

    # 详细结果内容
    section = document.add_section(
        start_type=WD_SECTION_START.NEW_PAGE  # NEW_PAGE  CONTINUOUS
    )  # 添加连续的节

    section = document.sections[1]
    add_page_header(rptinfo, section, 0)
    # add_page_number(section.header.paragraphs[0],10)

    # 报告摘要
    generate_sum_page(rptinfo, corpinfo, dicts, document,areaname)
    document.add_page_break()

    section = document.add_section(
        start_type=WD_SECTION_START.CONTINUOUS
    )  # 添加连续的节

    section = document.sections[2]
    add_page_header(rptinfo, section, pagestyle)
    section.orientation = WD_ORIENTATION.PORTRAIT  # 设置纵向
    page_h, page_w = section.page_height, section.page_width  # 读取插入节的高和宽

    if pagestyle == constant.PageStyle.Landscape.value:
        print("设置横向")
        section.orientation = WD_ORIENTATION.LANDSCAPE  # 设置横向
        page_h, page_w = section.page_width, section.page_height  # 读取插入节的高和宽
    print("宽度：", page_w)

    section.page_width = page_w  # 设置横向纸的宽度
    section.page_height = page_h  # 设置横向纸的高度
    set_section_margin(section)

    oculike_data = []
    forbidden_data = []
    recheck_data = []
    other_data = []
    add_data = []
    for ca in checkallinfos:
        # print("ca typeid:",)
        if ca.tj_typeid == constant.CheckResultType.Oculike.value:  # 疑似
            oculike_data.append(ca)
            continue
        elif ca.tj_typeid == constant.CheckResultType.Forbidden.value:  # 禁忌
            forbidden_data.append(ca)
            continue
        elif ca.tj_typeid == constant.CheckResultType.Recheck.value:  # 需复查
            recheck_data.append(ca)
            continue
        elif ca.tj_typeid == constant.CheckResultType.Addional.value:  # 需补检
            add_data.append(ca)
            continue
        else:  # biao 3
            other_data.append(ca)
            continue

    # print("疑似的人数:", len(oculike_data))
    # print("禁忌的人数:", len(forbidden_data))
    # print("复查的人数:", len(recheck_data))
    # print("其他的人数:", len(other_data))
    oculike_data.sort(key=lambda x: x.tj_testid)
    forbidden_data.sort(key=lambda x: x.tj_testid)
    recheck_data.sort(key=lambda x: x.tj_testid)
    other_data.sort(key=lambda x: x.tj_testid)
    add_data.sort(key=lambda x: x.tj_testid)

    idx = 1
    if customer == constant.Customer.WZDAMS.value:
        if len(oculike_data) > 0:
            title = "表 {}、疑似职业病人员名单".format(idx)
            generate_detail_t1t2_page(
                title, oculike_data, medinfos, patients, dicts, document, pagestyle
            )
            idx += 1

        if len(forbidden_data) > 0:
            title = "表 {}、职业禁忌证人员名单".format(idx)
            generate_detail_t1t2_page(
                title, forbidden_data, medinfos, patients, dicts, document, pagestyle
            )
            idx += 1

        if len(recheck_data) > 0:
            title = "表 {}、需复查人员名单".format(idx)
            generate_detail_t1t2_page(
                title, recheck_data, medinfos, patients, dicts, document, pagestyle
            )
            idx += 1

        if len(other_data) > 0:
            if (
                len(oculike_data) <= 0
                and len(forbidden_data) <= 0
                and len(recheck_data) <= 0
            ):
                title = "表 {}、受检人员名单".format(idx)
            else:
                title = "表 {}、其他人员名单".format(idx)
            generate_detail_t3_page(
                title, other_data, medinfos, patients, dicts, document, pagestyle
            )
    else:
        t1_data = oculike_data + forbidden_data
        # if len(t1_data) > 0:
        title = "表 {}、疑似职业病和职业禁忌证人员名单".format(idx)
        generate_detail_t1t2_page(
            title, t1_data, medinfos, patients, dicts, document, pagestyle
        )
        idx += 1

        # if len(recheck_data) > 0:
        title = "表 {}、需复查人员名单".format(idx)
        generate_detail_t1t2_page(
            title, recheck_data, medinfos, patients, dicts, document, pagestyle
        )
        idx += 1

        # if len(other_data) > 0:
        # if len(t1_data) <= 0 and len(recheck_data) <= 0:
        #     title = "表 {}、受检人员名单".format(idx)
        # else:
        title = "表 {}、其他人员名单".format(idx)
        generate_detail_t3_page(
            title, other_data, medinfos, patients, dicts, document, pagestyle
        )
        if len(add_data) > 0:
            idx += 1
            title = "表 {}、需补检人员名单".format(idx)
            generate_detail_t4_page(
                title, add_data, medinfos, patients, dicts, document, pagestyle
            )
        # idx+=1
    generate_sign_page(rptinfo, dicts, document)
    # document.add_page_break()
    savefile = os.path.join(outdir, output)
    document.save(savefile)
    return savefile


def generate_front_page(rptinfo, corpinfo, dicts, document,areaname):
    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    paragraph.paragraph_format.space_before = Pt(10)
    run = paragraph.add_run(rptinfo.tj_reportnum)  # 报告编号
    run.font.name = "宋体"
    run.font.size = Pt(10)
    run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")

    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
    paragraph.paragraph_format.space_before = Pt(40)
    paragraph.paragraph_format.space_after = Pt(20)
    paragraph.paragraph_format.line_spacing = Pt(0)
    run = paragraph.add_run("职业健康检查报告书")
    run.font.name = "宋体"
    run.font.size = Pt(40)
    run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")

    document.add_paragraph()
    font_size = 16
    col_left = 1
    col_right = 15
    row_height = 1.1

    table = document.add_table(3, 2)  #
    # table.width = Inches(table_width)
    # table.autofit = False

    row = table.rows[0]
    row.height = Cm(row_height)
    cells = row.cells
    cells[0].text = "用人单位:"  # 为表格的（0，0）位置单元格赋值
    cells[0].width = Inches(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[1].text = rptinfo.tj_corpname
    cells[1].width = Inches(col_right)
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[1],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )

    row = table.rows[1]
    row.height = Cm(row_height)
    cells = row.cells
    cells[0].text = "地    址:"  # 为表格的（0，0）位置单元格赋值
    # cells[0].width = Cm(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[1].text = areaname+corpinfo.tj_address
    # cells[1].width = Cm(col_right)
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[1],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )

    row = table.rows[2]
    row.height = Cm(row_height)
    cells = row.cells
    cells[0].text = "联系电话:"
    # cells[0].width = Cm(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[1].text = corpinfo.tj_phone
    # cells[1].width = Cm(col_right)
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[1],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )

    paragraph = document.add_paragraph()
    paragraph.paragraph_format.space_before = Pt(20)

    table = document.add_table(2, 2)  # 为文档新增2行2列的表格
    # table.width = Inches(table_width)
    # table.autofit = False
    
    row = table.rows[0]
    row.height = Cm(row_height)
    cells = row.cells
    cells[0].text = "体检类别:"
    cells[0].width = Inches(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.TOP)

    # cells[1].text = ""
    cells[1].width = Inches(col_right)
    cells[1].vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.TOP
    paragraph = cells[1].paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.line_spacing = Pt(0)
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)

    # 上岗
    run = paragraph.add_run()
    if rptinfo.tj_testtype == constant.TestType.SG.value:
        run.add_picture("./images/yes.png")
    else:
        run.add_picture("./images/no.png")
    c_dict = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictTesttype.value
        and dict.ss_pid == constant.TestType.SG.value
    )
    if c_dict is None:
        dictname = "上岗前"
    else:
        dictname = c_dict.ss_name
    run.add_text("  {}".format(dictname))
    run.font.name = "宋体"
    run.font.size = Pt(font_size)
    run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")

    # 在岗
    c_dict = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictTesttype.value
        and dict.ss_pid == constant.TestType.ZG.value
    )
    if c_dict is None:
        dictname = "在岗期间"
    else:
        dictname = c_dict.ss_name
    paragraph = cells[1].add_paragraph()
    paragraph.paragraph_format.line_spacing = Pt(0)
    paragraph.paragraph_format.space_before = Pt(5)
    paragraph.paragraph_format.space_after = Pt(0)
    run = paragraph.add_run()
    if rptinfo.tj_testtype == constant.TestType.ZG.value:
        run.add_picture("./images/yes.png")
    else:
        run.add_picture("./images/no.png")
    run.add_text("  {}".format(dictname))
    run.font.name = "宋体"
    run.font.size = Pt(font_size)
    run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")

    # 离岗
    c_dict = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictTesttype.value
        and dict.ss_pid == constant.TestType.LG.value
    )
    if c_dict is None:
        dictname = "离岗时"
    else:
        dictname = c_dict.ss_name
    paragraph = cells[1].add_paragraph()
    paragraph.paragraph_format.line_spacing = Pt(0)
    paragraph.paragraph_format.space_before = Pt(5)
    paragraph.paragraph_format.space_after = Pt(0)
    run = paragraph.add_run()
    if rptinfo.tj_testtype == constant.TestType.LG.value:
        run.add_picture("./images/yes.png")
    else:
        run.add_picture("./images/no.png")
    run.add_text("  {}".format(dictname))
    run.font.name = "宋体"
    run.font.size = Pt(font_size)
    run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")

    # 应急
    # c_dict = next(
    #     dict
    #     for dict in dicts
    #     if dict.ss_typeid == constant.DictType.DictTesttype.value
    #     and dict.ss_pid == constant.TestType.YJ.value
    # )
    # if c_dict is None:
    #     dictname = "应急"
    # else:
    #     dictname = c_dict.ss_name
    # paragraph = cells[1].add_paragraph()
    # paragraph.paragraph_format.line_spacing = Pt(0)
    # paragraph.paragraph_format.space_before = Pt(5)
    # paragraph.paragraph_format.space_after = Pt(10)
    # run = paragraph.add_run()
    # if rptinfo.tj_testtype == constant.TestType.YJ.value:
    #     run.add_picture("./images/yes.png")
    # else:
    #     run.add_picture("./images/no.png")
    # run.add_text("  {}".format(dictname))
    # run.font.name = "宋体"
    # run.font.size = Pt(font_size)
    # run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")

    # 复查
    row = table.rows[1]
    row.height = Cm(row_height)
    cells = row.cells
    cells[0].text = "复   查:"
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[1].vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.BOTTOM
    paragraph = cells[1].paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.line_spacing = Pt(0)
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)

    run = paragraph.add_run()
    if rptinfo.tj_isrecheck == constant.YesOrNo.Yes.value:
        run.add_picture("./images/yes.png")
    else:
        run.add_picture("./images/no.png")

    dictname = "复查"

    run.add_text("  {}".format(dictname))
    run.font.name = "宋体"
    run.font.size = Pt(font_size)
    run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")

    # 医院名称
    orginfo = next(
        filter(
            lambda dict: dict.ss_typeid == constant.DictType.DictCustomer.value
            and dict.ss_pid == constant.CustomerInfo.CustomerName.value,
            dicts,
        ),
        None,
    )
    if orginfo is None:
        orgname = ""
    else:
        orgname = orginfo.ss_name
    paragraph = document.add_paragraph()
    paragraph.paragraph_format.space_before = Cm(4)
    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = paragraph.add_run()
    run.add_text(orgname)
    run.font.name = "宋体"
    run.font.size = Pt(font_size + 2)
    run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")
    date_time_obj = datetime.fromtimestamp(rptinfo.tj_createdate).strftime("%Y-%m-%d")
    run.add_break()
    run.add_text(date_time_obj)

    customer_stam = get_customer_stamp(dicts)
    if os.path.isfile(customer_stam):
        add_float_picture(paragraph, customer_stam, width=Inches(2), pos_x=Pt(220), pos_y=Pt(590))


def generate_desc_page(dicts, document):
    document.add_paragraph()
    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
    paragraph.paragraph_format.space_before = Pt(28)
    paragraph.paragraph_format.space_after = Pt(0)
    run = paragraph.add_run("职业健康检查报告书说明")  # 报告书说明
    run.font.name = "宋体"
    run.font.size = Pt(28)
    run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")

    font_size = 13
    space = 20

    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(40)
    paragraph.paragraph_format.line_spacing = Pt(0)
    run = paragraph.add_run(
        "一、对本报告书有异议的，请于收到之日起十五日内向本单位提出。"
    )
    set_run_font(run, font_size)

    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(space)
    paragraph.paragraph_format.line_spacing = Pt(0)
    run = paragraph.add_run(
        "二、本报告书无主检医师、审核人及批准人签字无效，本报告书无本单位盖章无效。"
    )
    set_run_font(run, font_size)

    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(space)
    paragraph.paragraph_format.line_spacing = Pt(0)
    run = paragraph.add_run("三、本报告书涂改无效。")
    set_run_font(run, font_size)

    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(space)
    paragraph.paragraph_format.line_spacing = Pt(0)
    run = paragraph.add_run("四、本报告书不得部分复制，不得作广告宣传。")
    set_run_font(run, font_size)

    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(space)
    paragraph.paragraph_format.line_spacing = Pt(0)
    run = paragraph.add_run(
        "五、本报告书一式三份（用人单位和用人单位所在地卫生行政部门各一份，职业健康检查机构存档一份）。"
    )
    set_run_font(run, font_size)

    space_before = 40
    addict_dict = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictSysparm.value
        and dict.ss_pid == constant.SysParm.ReportIntro.value
    )
    if addict_dict is None:
        pass
    else:
        if addict_dict.ss_short == "1":
            paragraph = document.add_paragraph()
            paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
            paragraph.paragraph_format.space_before = Pt(space)
            paragraph.paragraph_format.line_spacing = Pt(0)
            run = paragraph.add_run("    " + addict_dict.ss_name)
            set_run_font(run, font_size - 2)
            space_before = 30

    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(space_before)
    paragraph.paragraph_format.line_spacing = Pt(0)
    run = paragraph.add_run("本单位联系方式：")
    set_run_font(run, 20)

    customer_name = ""
    customer_no = ""
    customer_add = ""
    customer_postcode = ""
    customer_phone = ""
    dict_info = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictCustomer.value
        and dict.ss_pid == constant.CustomerInfo.CustomerName.value
    )
    if dict_info is None:
        pass
    else:
        customer_name = dict_info.ss_name
    dict_info = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictCustomer.value
        and dict.ss_pid == constant.CustomerInfo.CustomerDJH.value
    )
    if dict_info is None:
        pass
    else:
        customer_no = dict_info.ss_name
    dict_info = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictCustomer.value
        and dict.ss_pid == constant.CustomerInfo.CustomerAddress.value
    )
    if dict_info is None:
        pass
    else:
        customer_add = dict_info.ss_name
    dict_info = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictCustomer.value
        and dict.ss_pid == constant.CustomerInfo.CustomerPostcode.value
    )
    if dict_info is None:
        pass
    else:
        customer_postcode = dict_info.ss_name
    dict_info = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictCustomer.value
        and dict.ss_pid == constant.CustomerInfo.CustomerPhone.value
    )
    if dict_info is None:
        pass
    else:
        customer_phone = dict_info.ss_name

    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(40)
    paragraph.paragraph_format.line_spacing = Pt(0)
    run = paragraph.add_run()
    set_run_font(run, 12)
    run.add_text("职业健康检查机构名称：" + customer_name)
    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(5)
    paragraph.paragraph_format.line_spacing = Pt(0)
    run = paragraph.add_run()
    set_run_font(run, 12)
    run.add_text("职业健康检查机构备案号：" + customer_no)
    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(5)
    paragraph.paragraph_format.line_spacing = Pt(0)
    run = paragraph.add_run()
    set_run_font(run, 12)
    run.add_text("地址：" + customer_add)
    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(5)
    paragraph.paragraph_format.line_spacing = Pt(0)
    run = paragraph.add_run()
    set_run_font(run, 12)
    run.add_text("邮编：" + customer_postcode)
    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(5)
    paragraph.paragraph_format.line_spacing = Pt(0)
    run = paragraph.add_run()
    set_run_font(run, 12)
    run.add_text("联系电话：" + customer_phone)

    # 添加条形码
    qrimg = "./images/qrcode.png"
    if os.path.isfile(qrimg):
        # print("开始打印二维码......")
        paragraph = document.add_paragraph()
        paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        paragraph.paragraph_format.space_before = Pt(5)
        paragraph.paragraph_format.line_spacing = Pt(0)
        paragraph.paragraph_format.left_indent = Cm(10)
        run = paragraph.add_run()
        run.add_picture(qrimg, width=Inches(1.5), height=Inches(1.5))


def generate_sum_page(rptinfo, corpinfo, dicts, document,areaname):
    # paragraph = document.add_paragraph()
    font_size = 13
    col_left = 2.4
    col_right = 8
    row_height = 1.1

    table = document.add_table(10, 4)  #
    # table.autofit = False

    row = table.rows[0]
    row.height = Cm(row_height)
    cells = row.cells
    cells[0].text = "用人单位:"  # 为表格的（0，0）位置单元格赋值
    cells[0].width = Inches(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[1].text = rptinfo.tj_corpname
    cells[1].width = Inches(col_right+1)
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[1],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )
    cells = row.cells
    cells[2].text = "联系电话:"  # 为表格的（0，0）位置单元格赋值
    cells[2].width = Inches(col_left)
    cells[2].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[2], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[3].text = corpinfo.tj_phone
    cells[3].width = Inches(col_left + 1)
    cells[3].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[3], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[3],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )
    # row 2
    row = table.rows[1]
    row.height = Cm(row_height)
    cells = row.cells
    cells[1].merge(cells[3])
    cells[0].text = "单位地址:"  #
    cells[0].width = Inches(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[1].text = areaname+corpinfo.tj_address
    cells[1].width = Inches(col_right)
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[1],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )
    # row 3
    row = table.rows[2]
    row.height = Cm(row_height)
    cells = row.cells
    # cells[1].merge(cells[3])
    cells[0].text = "体检日期:"  #
    cells[0].width = Inches(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[1].text = rptinfo.tj_testdate
    cells[1].width = Inches(col_right+1)
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[1],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )

    # row 4
    row = table.rows[3]
    row.height = Cm(row_height)
    cells = row.cells
    cells[1].merge(cells[3])
    cells[0].text = "体检类别:"  #
    cells[0].width = Inches(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    # cells[1].text = corpinfo.tj_address
    cells[1].width = Inches(col_right)
    paragraph = cells[1].paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    run = paragraph.add_run()
    if rptinfo.tj_testtype == constant.TestType.SG.value:
        run.add_picture("./images/yes.png", width=Pt(12), height=Pt(12))
    else:
        run.add_picture("./images/no.png", width=Pt(12), height=Pt(12))
    c_dict = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictTesttype.value
        and dict.ss_pid == constant.TestType.SG.value
    )
    if c_dict is None:
        dictname = "上岗"
    else:
        dictname = c_dict.ss_name
    run.add_text("{}  ".format(dictname))
    # 在岗
    if rptinfo.tj_testtype == constant.TestType.ZG.value:
        run.add_picture("./images/yes.png", width=Pt(12), height=Pt(12))
    else:
        run.add_picture("./images/no.png", width=Pt(12), height=Pt(12))
    c_dict = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictTesttype.value
        and dict.ss_pid == constant.TestType.ZG.value
    )
    if c_dict is None:
        dictname = "在岗"
    else:
        dictname = c_dict.ss_name
    run.add_text("{}  ".format(dictname))
    # 离岗
    if rptinfo.tj_testtype == constant.TestType.LG.value:
        run.add_picture("./images/yes.png", width=Pt(12), height=Pt(12))
    else:
        run.add_picture("./images/no.png", width=Pt(12), height=Pt(12))
    c_dict = next(
        dict
        for dict in dicts
        if dict.ss_typeid == constant.DictType.DictTesttype.value
        and dict.ss_pid == constant.TestType.LG.value
    )
    if c_dict is None:
        dictname = "离岗"
    else:
        dictname = c_dict.ss_name
    run.add_text("{}  ".format(dictname))
    # 应急
    # if rptinfo.tj_testtype == constant.TestType.YJ.value:
    #     run.add_picture("./images/yes.png", width=Pt(12), height=Pt(12))
    # else:
    #     run.add_picture("./images/no.png", width=Pt(12), height=Pt(12))
    # c_dict = next(
    #     dict
    #     for dict in dicts
    #     if dict.ss_typeid == constant.DictType.DictTesttype.value
    #     and dict.ss_pid == constant.TestType.YJ.value
    # )
    # if c_dict is None:
    #     dictname = "应急"
    # else:
    #     dictname = c_dict.ss_name
    # run.add_text("{}  ".format(dictname))
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    # row 5 体检地址
    row = table.rows[4]
    row.height = Cm(row_height)
    cells = row.cells
    cells[1].merge(cells[3])
    cells[0].text = "体检地点:"  #
    cells[0].width = Inches(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[1].text = rptinfo.tj_testaddress
    cells[1].width = Inches(col_right)
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[1],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )

    # row 6
    row = table.rows[5]
    row.height = Cm(row_height)
    cells = row.cells
    cells[0].text = "应检人数:"  # 为表格的（0，0）位置单元格赋值
    cells[0].width = Inches(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[1].text = "{}人".format(rptinfo.tj_apeoplenum)
    cells[1].width = Inches(col_right+1)
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[1],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )
    cells = row.cells
    cells[2].text = "受检人数:"  # 为表格的（0，0）位置单元格赋值
    cells[2].width = Inches(col_left)
    cells[2].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[2], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[3].text = "{}人".format(rptinfo.tj_peoplenum)
    cells[3].width = Inches(col_left + 2)
    cells[3].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[3], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[3],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )
    # row 7
    row = table.rows[6]
    row.height = Cm(row_height)
    cells = row.cells
    cells[1].merge(cells[3])
    cells[0].text = "接触职业病危害因素名称:"  #
    cells[0].width = Inches(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[1].text = rptinfo.tj_poisions
    cells[1].width = Inches(col_right)
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[1],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )
    # row 8
    row = table.rows[7]
    row.height = Cm(row_height)
    cells = row.cells
    cells[1].merge(cells[3])
    cells[0].text = "体检项目:"  #
    cells[0].width = Inches(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.CENTER)

    cells[1].text = rptinfo.tj_testitems
    cells[1].width = Inches(col_right)
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[1],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )
    # row 9
    row = table.rows[8]
    row.height = Cm(row_height)
    cells = row.cells
    cells[1].merge(cells[3])
    cells[0].text = "体检和评价依据:"  #
    cells[0].width = Inches(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.CENTER)

    cells[1].text = rptinfo.tj_testlaw
    cells[1].width = Inches(col_right)
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[1],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )
    # row 10
    row = table.rows[9]
    row.height = Cm(row_height)
    cells = row.cells
    cells[1].merge(cells[3])
    cells[0].text = "备注说明:"  #
    cells[0].width = Inches(col_left)
    cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
    set_cell_attribute(cells[0], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)

    cells[1].text = rptinfo.tj_memo
    cells[1].width = Inches(col_right)
    cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
    set_cell_attribute(cells[1], font_size, WD_CELL_VERTICAL_ALIGNMENT.BOTTOM)
    set_cell_border(
        cells[1],
        bottom={"sz": 8, "color": "#000000", "val": "single"},
    )
    # 检查结果与医学建议
    paragraph = document.add_paragraph()
    paragraph.paragraph_format.space_before = Pt(40)
    paragraph.paragraph_format.space_after = Pt(15)
    run = paragraph.add_run()
    set_run_font(run, font_size + 3)
    run.add_text("体检结论与处理意见/医学建议:")
    # run.add_text("    {}".format(rptinfo.tj_result))

    paragraph = document.add_paragraph()
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    run = paragraph.add_run()
    set_run_font(run, font_size)
    run.add_text("    {}".format(rptinfo.tj_result))


def generate_detail_t1t2_page(
    title, datas, medinfos, patients, dicts, document, pagestyle
):
    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(15)
    paragraph.paragraph_format.space_after = Pt(5)
    run = paragraph.add_run()
    set_run_font(run, 16)
    run.add_text(title)
    run.bold = True
    # table content
    tbstyle = document.styles["Table Grid"]
    tbstyle.font.name = "宋体"
    tbstyle.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")
    tbstyle.font.size = Pt(10)
    table = document.add_table(rows=1, cols=12, style=tbstyle)
    table.alignment = WD_TABLE_ALIGNMENT.LEFT
    # table.autofit = False
    # header row
    row0 = table.rows[0]
    row0.height = Cm(1.5)

    for x in range(12):
        shading_elm_1 = parse_xml(r'<w:shd {} w:fill="EEEEEE"/>'.format(nsdecls("w")))
        row0.cells[x]._tc.get_or_add_tcPr().append(shading_elm_1)

    header_txt = [
        "序号",
        "体检编号",
        "姓名",
        "性别",
        "年龄",
        "接害工龄",
        "工种",
        "接触职业病危害因素名称",
        "异常指标",
        "结论",
        "处理意见",
        "医学建议",
    ]
    if pagestyle == constant.PageStyle.Landscape.value:
        # cell_width = [0.4, 1, 0.4, 0.4, 0.4, 0.5, 1, 2, 10, 1.2, 1.5, 10]
        cell_width = [0.8, 1.2, 0.8, 0.8, 0.8, 1.2, 0.8, 2, 7.4, 1.8, 1.9, 8.1]
    else:
        cell_width = [0.8, 1.2, 0.8, 0.8, 0.8, 1.2, 0.8, 2, 3, 3.0, 1.2, 3]
    # cell
    idx = 0
    hdr_cells = row0.cells
    for cell in hdr_cells:
        # print("Cell:",cell)
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = paragraph.add_run()
        set_run_font(run, 10)
        run.add_text(header_txt[idx])
        cell.width = Cm(cell_width[idx])
        idx += 1
    # Set the first row as the repeating header
    set_repeat_table_header(table.rows[0])

    idx = 1
    if len(datas) == 0:
        row_cells = table.add_row().cells
        for cell in row_cells:
            # print("Cell:",cell)
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        row_cells[0].text = "无"
        row_cells[0].width = Cm(cell_width[0])
        #合并表格
        table.cell(1, 0).merge(table.cell(1, 11)) # 合并第二行第一列到第十二列
        # 设置合并后单元格文本居中
        merged_cell = table.cell(1, 0)
        merged_cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        for paragraph in merged_cell.paragraphs:
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        return
    for ca in datas:
        medinfo = next((med for med in medinfos if med.tj_testid == ca.tj_testid),None)
        if medinfo is None:
            continue
        ptinfo = next((pt for pt in patients if pt.tj_pid == medinfo.tj_pid),None)
        if ptinfo is None:
            continue
        row_cells = table.add_row().cells
        for cell in row_cells:
            # print("Cell:",cell)
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        row_cells[0].text = "{}".format(idx)
        row_cells[0].width = Cm(cell_width[0])
        row_cells[1].text = "{}".format(ca.tj_testid)
        row_cells[1].width = Cm(cell_width[1])
        row_cells[2].text = ptinfo.tj_pname
        row_cells[2].width = Cm(cell_width[2])
        row_cells[3].text = constant.get_sex(ptinfo.tj_psex)
        row_cells[3].width = Cm(cell_width[3])
        row_cells[4].text = str(medinfo.tj_age)
        row_cells[4].width = Cm(cell_width[4])
        row_cells[5].text = medinfo.tj_poisionage
        row_cells[5].width = Cm(cell_width[5])
        row_cells[6].text = medinfo.tj_worktype
        row_cells[6].width = Cm(cell_width[6])
        row_cells[7].text = medinfo.tj_poisionfactor
        row_cells[7].width = Cm(cell_width[7])

        row_cells[8].text = ca.tj_ocuabnormal if ca.tj_ocuabnormal else "-"
        row_cells[8].width = Cm(cell_width[8])
        row_cells[9].text = constant.get_dict_name(
            dicts,
            constant.DictType.DictCheckall.value,
            ca.tj_typeid,
        )
        row_cells[9].width = Cm(cell_width[9])
        row_cells[10].text = ca.tj_ocuopinion if ca.tj_ocuopinion else "-"
        row_cells[10].width = Cm(cell_width[10])
        row_cells[11].text = ca.tj_ocusuggestion if ca.tj_ocusuggestion else "-"
        row_cells[11].width = Cm(cell_width[11])
        row_cells2 = table.add_row().cells
        for cell in row_cells2:
            # print("Cell:",cell)
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        row_cells2[8].text = ca.tj_othabnormal if ca.tj_othabnormal else "-"
        row_cells2[8].width = Cm(cell_width[8])
        row_cells2[9].text = "-"
        row_cells2[9].width = Cm(cell_width[9])
        row_cells2[10].text = ca.tj_othopinion if ca.tj_othopinion else "-"
        row_cells2[10].width = Cm(cell_width[10])
        row_cells2[11].text = ca.tj_othsuggestion if ca.tj_othsuggestion else "-"
        row_cells2[11].width = Cm(cell_width[11])
        for x in range(8):
            row_cells[x].merge(row_cells2[x])
        idx += 1

    # table.cell(1, 0).merge(table.cell(1, 1)) # 合并第二行第一列到第二列

def generate_detail_t3_page(
    title, datas, medinfos, patients, dicts, document, pagestyle
):
    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(15)
    paragraph.paragraph_format.space_after = Pt(5)
    run = paragraph.add_run()
    set_run_font(run, 16)
    run.add_text(title)
    run.bold = True
    # table content
    tbstyle = document.styles["Table Grid"]
    tbstyle.font.name = "宋体"
    tbstyle.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")
    tbstyle.font.size = Pt(10)
    table = document.add_table(rows=1, cols=11, style=tbstyle)
    table.alignment = WD_TABLE_ALIGNMENT.LEFT
    # table.autofit = False
    # header row
    row0 = table.rows[0]
    row0.height = Cm(1.5)

    for x in range(11):
        shading_elm_1 = parse_xml(r'<w:shd {} w:fill="EEEEEE"/>'.format(nsdecls("w")))
        row0.cells[x]._tc.get_or_add_tcPr().append(shading_elm_1)

    header_txt = [
        "序号",
        "体检编号",
        "姓名",
        "性别",
        "年龄",
        "接害工龄",
        "工种",
        "接触职业病危害因素名称",
        "异常指标",
        "结论",
        "医学建议",
    ]
    if pagestyle == constant.PageStyle.Landscape.value:
        cell_width = [0.8, 1.2, 0.8, 0.8, 0.8, 1.2, 0.8, 2, 8.4, 1.8, 9]
    else:
        # cell_width = [0.5, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 2, 6, 2, 7]
        cell_width = [0.8, 1.2, 0.8, 0.8, 0.8, 1.2, 0.8, 2, 4.5, 1.2, 4.5]
    # cell
    idx = 0
    hdr_cells = row0.cells
    for cell in hdr_cells:
        # print("Cell:",cell)
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = paragraph.add_run()
        set_run_font(run, 10)
        run.add_text(header_txt[idx])
        cell.width = Cm(cell_width[idx])
        idx += 1
    # tbl = table._tbl
    # tblPr = tbl.tblPr
    # tblHeader = OxmlElement('w:tblHeader')  # 创建 tblHeader 元素
    # tblHeader.set(qn('w:val'), 'true')  # 设置值为 true
    # tblPr.append(tblHeader)  # 将 tblHeader 添加到表格属性中
    # Set the first row as the repeating header
    set_repeat_table_header(table.rows[0])
    # data row
    idx = 1
    if len(datas) == 0:
        row_cells = table.add_row().cells
        for cell in row_cells:
            # print("Cell:",cell)
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        row_cells[0].text = "无"
        row_cells[0].width = Cm(cell_width[0])
        #合并表格
        table.cell(1, 0).merge(table.cell(1, 10)) # 合并第二行第一列到第十二列
        # 设置合并后单元格文本居中
        merged_cell = table.cell(1, 0)
        merged_cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        for paragraph in merged_cell.paragraphs:
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        return
    for ca in datas:
        medinfo = next((med for med in medinfos if med.tj_testid == ca.tj_testid),None)
        if medinfo is None:
            continue
        ptinfo = next((pt for pt in patients if pt.tj_pid == medinfo.tj_pid),None)
        if ptinfo is None:
            continue
        row_cells = table.add_row().cells
        for cell in row_cells:
            # print("Cell:",cell)
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        row_cells[0].text = "{}".format(idx)
        row_cells[0].width = Cm(cell_width[0])
        row_cells[1].text = "{}".format(ca.tj_testid)
        row_cells[1].width = Cm(cell_width[1])
        row_cells[2].text = ptinfo.tj_pname
        row_cells[2].width = Cm(cell_width[2])
        row_cells[3].text = constant.get_sex(ptinfo.tj_psex)
        row_cells[3].width = Cm(cell_width[3])
        row_cells[4].text = str(medinfo.tj_age)
        row_cells[4].width = Cm(cell_width[4])
        row_cells[5].text = medinfo.tj_poisionage
        row_cells[5].width = Cm(cell_width[5])
        row_cells[6].text = medinfo.tj_worktype
        row_cells[6].width = Cm(cell_width[6])
        row_cells[7].text = medinfo.tj_poisionfactor
        row_cells[7].width = Cm(cell_width[7])

        row_cells[8].text = ca.tj_othabnormal if ca.tj_othabnormal else "-"
        row_cells[8].width = Cm(cell_width[8])
        row_cells[9].text = constant.get_dict_name(
            dicts,
            constant.DictType.DictCheckall.value,
            ca.tj_typeid,
        )
        row_cells[9].width = Cm(cell_width[9])
        row_cells[10].text = ca.tj_othsuggestion if ca.tj_othsuggestion else "-"
        row_cells[10].width = Cm(cell_width[10])

        idx += 1

def generate_detail_t4_page(
    title, datas, medinfos, patients, dicts, document, pagestyle
):
    colnums=12
    paragraph = document.add_paragraph()
    paragraph.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(15)
    paragraph.paragraph_format.space_after = Pt(5)
    run = paragraph.add_run()
    set_run_font(run, 16)
    run.add_text(title)
    run.bold = True
    # table content
    tbstyle = document.styles["Table Grid"]
    tbstyle.font.name = "宋体"
    tbstyle.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")
    tbstyle.font.size = Pt(10)
    table = document.add_table(rows=1, cols=colnums, style=tbstyle)
    table.alignment = WD_TABLE_ALIGNMENT.LEFT
    # table.autofit=False
    # header row
    row0 = table.rows[0]
    row0.height = Cm(1.5)

    for x in range(colnums):
        shading_elm_1 = parse_xml(r'<w:shd {} w:fill="EEEEEE"/>'.format(nsdecls("w")))
        row0.cells[x]._tc.get_or_add_tcPr().append(shading_elm_1)

    header_txt = [
        "序号",
        "体检编号",
        "姓名",
        "性别",
        "年龄",
        "接害工龄",
        "工种",
        "接触职业病危害因素名称",
        "异常指标",
        "结论",
        "处理意见",
        "医学建议",
    ]
    if pagestyle == constant.PageStyle.Landscape.value:
        # cell_width = [0.4, 1, 0.4, 0.4, 0.4, 0.5, 1, 2, 10, 1.2, 1.5, 10]
        cell_width = [0.8, 1.2, 0.8, 0.8, 0.8, 1.2, 0.8, 2, 7.4, 1.8,1.8, 8.2]
    else:
        # cell_width = [0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 2, 6, 1, 1.6, 7]
        cell_width = [0.8, 1.2, 0.8, 0.8, 0.8, 1.2, 0.8, 2, 3, 3.0, 1.2, 3]
    # cell
    idx = 0
    hdr_cells = row0.cells
    for cell in hdr_cells:
        # print("Cell:",cell)
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = paragraph.add_run()
        set_run_font(run, 10)
        run.add_text(header_txt[idx])
        cell.width = Cm(cell_width[idx])
        idx += 1
    # Set the first row as the repeating header
    set_repeat_table_header(table.rows[0])

    idx = 1
    if len(datas) == 0:
        row_cells = table.add_row().cells
        for cell in row_cells:
            # print("Cell:",cell)
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        row_cells[0].text = "无"
        row_cells[0].width = Cm(cell_width[0])
        #合并表格
        table.cell(1, 0).merge(table.cell(1, 11)) # 合并第二行第一列到第十二列
        # 设置合并后单元格文本居中
        merged_cell = table.cell(1, 0)
        merged_cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        for paragraph in merged_cell.paragraphs:
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        return
    for ca in datas:
        medinfo = next((med for med in medinfos if med.tj_testid == ca.tj_testid),None)
        if medinfo is None:
            continue
        ptinfo = next((pt for pt in patients if pt.tj_pid == medinfo.tj_pid),None)
        if ptinfo is None:
            continue
        row_cells = table.add_row().cells
        for cell in row_cells:
            # print("Cell:",cell)
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        row_cells[0].text = "{}".format(idx)
        row_cells[0].width = Cm(cell_width[0])
        row_cells[1].text = "{}".format(ca.tj_testid)
        row_cells[1].width = Cm(cell_width[1])
        row_cells[2].text = ptinfo.tj_pname
        row_cells[2].width = Cm(cell_width[2])
        row_cells[3].text = constant.get_sex(ptinfo.tj_psex)
        row_cells[3].width = Cm(cell_width[3])
        row_cells[4].text = str(medinfo.tj_age)
        row_cells[4].width = Cm(cell_width[4])
        row_cells[5].text = medinfo.tj_poisionage
        row_cells[5].width = Cm(cell_width[5])
        row_cells[6].text = medinfo.tj_worktype
        row_cells[6].width = Cm(cell_width[6])
        row_cells[7].text = medinfo.tj_poisionfactor
        row_cells[7].width = Cm(cell_width[7])

        row_cells[8].text = ca.tj_othabnormal if ca.tj_othabnormal else "-"
        row_cells[8].width = Cm(cell_width[8])
        row_cells[9].text = constant.get_dict_name(
            dicts,
            constant.DictType.DictCheckall.value,
            ca.tj_typeid,
        )
        row_cells[9].width = Cm(cell_width[9])
        row_cells[10].text = ca.tj_ocuopinion if ca.tj_ocuopinion else "-"
        row_cells[10].width = Cm(cell_width[10])
        row_cells[11].text = ca.tj_othsuggestion if ca.tj_othsuggestion else "-"
        row_cells[11].width = Cm(cell_width[11])

        idx += 1

def generate_sign_page(rptinfo, dicts, document):
    paragraph = document.add_paragraph()
    paragraph.paragraph_format.space_before = Pt(20)
    paragraph.paragraph_format.space_before = Pt(5)
    # insertHR(paragraph)
    cell_width = [4, 6, 6, 8]
    font_size = 14
    img_width = 100
    img_height = 50
    table = document.add_table(4, 4)
    row_height = 1.2
    # row 1
    row = table.rows[0]
    row.height = Cm(row_height)
    
    cell = row.cells[0]
    cell.width = Cm(5)
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    run = paragraph.add_run()
    set_run_font(run, font_size)
    run.add_text("编制人:")

    # 电子签名
    cell = row.cells[1]
    cell.width = Cm(6)
    cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    run = paragraph.add_run()
    set_run_font(run, font_size)
    zbr = get_zbr(dicts)
    if os.path.exists(zbr):
        run.add_picture(zbr, width=Pt(img_width), height=Pt(img_height))
    elif zbr != "":
        run.add_text(zbr)
    else:
        run.add_text("")
        
    cell = row.cells[2]
    cell.width = Cm(5)
    cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    run = paragraph.add_run()
    set_run_font(run, font_size)
    run.add_text("主检医师:")

    # 电子签名
    cell = row.cells[3]
    cell.width = Cm(6)
    cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    run = paragraph.add_run()
    set_run_font(run, font_size)
    zjys = get_zjys(dicts, rptinfo.tj_creator)
    if os.path.isfile(zjys):
        run.add_picture(zjys, width=Pt(img_width), height=Pt(img_height))
    elif zjys != "":
        run.add_text(zjys)

    

    # row 2
    row = table.rows[1]
    row.height = Cm(row_height)

    cell = row.cells[0]
    cell.width = Cm(6)
    cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    # set_cell_attribute(cell, font_size, WD_CELL_VERTICAL_ALIGNMENT.CENTER)
    run = paragraph.add_run()
    set_run_font(run, font_size)
    run.add_text("审核人:")
    # 电子签名
    cell = row.cells[1]
    cell.width = Cm(6)
    cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    run = paragraph.add_run()
    set_run_font(run, font_size)
    shys = get_shys(dicts)
    if os.path.isfile(shys):
        run.add_picture(shys, width=Pt(img_width), height=Pt(img_height))
    elif shys != "":
        run.add_text(shys)
    #
    cell = row.cells[2]
    cell.width = Cm(5)
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    run = paragraph.add_run()
    set_run_font(run, font_size)
    run.add_text("批准人:")

    # 电子签名
    cell = row.cells[3]
    cell.width = Cm(6)
    cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    run = paragraph.add_run()
    set_run_font(run, font_size)
    pzys = get_pzys(dicts)
    if os.path.isfile(pzys):
        run.add_picture(pzys, width=Pt(img_width), height=Pt(img_height))
    elif pzys != "":
        run.add_text(pzys)



    # row 3
    row = table.rows[2]
    row.height = Cm(row_height)

    cell = row.cells[0]
    cell.width = Cm(6)
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    run = paragraph.add_run()
    set_run_font(run, font_size)
    run.add_text("批准日期:")
    # column 4
    cell = row.cells[1]
    cell.width = Cm(6)
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    run = paragraph.add_run()
    set_run_font(run, font_size)
    pzrq = datetime.fromtimestamp(rptinfo.tj_createdate).strftime("%Y-%m-%d")
    run.add_text(pzrq)

    cell = row.cells[2]
    cell.width = Cm(6)
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    run = paragraph.add_run()
    set_run_font(run, font_size)
    run.add_text("体检单位(盖章):")

    # 机构名称
    cell = row.cells[3]
    cell.width = Cm(6)
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    run = paragraph.add_run()
    set_run_font(run, font_size)
    customer_name = get_customer_name(dicts)
    run.add_text(customer_name)

    # row 4
    row = table.rows[3]
    row.height = Cm(row_height)

    cell = row.cells[3]
    cell.width = Cm(5)
    paragraph = cell.paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    paragraph.paragraph_format.space_before = Pt(0)
    paragraph.paragraph_format.space_after = Pt(0)
    
    customer_stam = get_customer_stamp(dicts)
    if os.path.isfile(customer_stam):
        add_float_picture(paragraph, customer_stam, width=Inches(2), pos_x=Pt(5), pos_y=Pt(-60))

    # cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.TOP
    # run = paragraph.add_run()
    # set_run_font(run, font_size)
    # customer_stam = get_customer_stamp(dicts)
    # if os.path.isfile(customer_stam):
    #     run.add_picture(customer_stam, height=Pt(150))

def set_section_margin(section):
    section.top_margin = Cm(1.27)
    section.bottom_margin = Cm(1.27)
    section.left_margin = Cm(1.27)
    section.right_margin = Cm(1.27)
    section.gutter = Cm(0.2)


def set_cell_border(cell: _Cell, **kwargs):
    """
    Set cell`s border
    Usage:

    set_cell_border(
        cell,
        top={"sz": 12, "val": "single", "color": "#FF0000", "space": "0"},
        bottom={"sz": 12, "color": "#00FF00", "val": "single"},
        start={"sz": 24, "val": "dashed", "shadow": "true"},
        end={"sz": 12, "val": "dashed"},
    )
    """
    tc = cell._tc
    tcPr = tc.get_or_add_tcPr()

    # check for tag existnace, if none found, then create one
    tcBorders = tcPr.first_child_found_in("w:tcBorders")
    if tcBorders is None:
        tcBorders = OxmlElement("w:tcBorders")
        tcPr.append(tcBorders)

    # list over all available tags
    for edge in ("start", "top", "end", "bottom", "insideH", "insideV"):
        edge_data = kwargs.get(edge)
        if edge_data:
            tag = "w:{}".format(edge)

            # check for tag existnace, if none found, then create one
            element = tcBorders.find(qn(tag))
            if element is None:
                element = OxmlElement(tag)
                tcBorders.append(element)

            # looks like order of attributes is important
            for key in ["sz", "val", "color", "space", "shadow"]:
                if key in edge_data:
                    element.set(qn("w:{}".format(key)), str(edge_data[key]))


def set_run_font(run, font_size):
    run.font.name = "宋体"
    run.font.size = Pt(font_size)
    run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")


def set_cell_attribute(cell: _Cell, size, align):
    cell.paragraphs[0].runs[0].font.name = "宋体"
    cell.paragraphs[0].runs[0].font.size = Pt(size)
    cell.paragraphs[0].paragraph_format.line_spacing = Pt(0)
    cell.paragraphs[0].paragraph_format.space_before = Pt(0)
    cell.paragraphs[0].paragraph_format.space_after = Pt(0)
    cell.paragraphs[0].runs[0].font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")
    cell.vertical_alignment = align


def create_element(name):
    return OxmlElement(name)


def create_attribute(element, name, value):
    element.set(ns.qn(name), value)


def add_page_number(paragraph, fontsize):
    paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT

    page_run = paragraph.add_run()
    page_run.font.name = "宋体"
    page_run.font.size = Pt(fontsize)
    page_run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")
    t1 = create_element("w:t")
    create_attribute(t1, "xml:space", "preserve")
    t1.text = "第 "
    page_run._r.append(t1)

    page_num_run = paragraph.add_run()
    page_num_run.font.name = "宋体"
    page_num_run.font.size = Pt(fontsize)
    page_num_run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")

    fldChar1 = create_element("w:fldChar")
    create_attribute(fldChar1, "w:fldCharType", "begin")

    instrText = create_element("w:instrText")
    create_attribute(instrText, "xml:space", "preserve")
    instrText.text = "PAGE"

    fldChar2 = create_element("w:fldChar")
    create_attribute(fldChar2, "w:fldCharType", "end")

    page_num_run._r.append(fldChar1)
    page_num_run._r.append(instrText)
    page_num_run._r.append(fldChar2)

    of_run = paragraph.add_run()
    of_run.font.name = "宋体"
    of_run.font.size = Pt(fontsize)
    of_run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")
    t2 = create_element("w:t")
    create_attribute(t2, "xml:space", "preserve")
    t2.text = " 页，共 "
    of_run._r.append(t2)

    fldChar3 = create_element("w:fldChar")
    create_attribute(fldChar3, "w:fldCharType", "begin")

    instrText2 = create_element("w:instrText")
    create_attribute(instrText2, "xml:space", "preserve")
    instrText2.text = "NUMPAGES"

    fldChar4 = create_element("w:fldChar")
    create_attribute(fldChar4, "w:fldCharType", "end")

    num_pages_run = paragraph.add_run()
    num_pages_run.font.name = "宋体"
    num_pages_run.font.size = Pt(fontsize)
    num_pages_run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")
    num_pages_run._r.append(fldChar3)
    num_pages_run._r.append(instrText2)
    num_pages_run._r.append(fldChar4)

    end_run = paragraph.add_run()
    end_run.font.name = "宋体"
    end_run.font.size = Pt(fontsize)
    end_run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")
    t3 = create_element("w:t")
    create_attribute(t3, "xml:space", "preserve")
    t3.text = " 页"
    end_run._r.append(t3)


def add_page_number_with_run(run, fontsize):
    # paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT

    # page_run = paragraph.add_run()
    run.font.name = "宋体"
    run.font.size = Pt(fontsize)
    run.font.element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")
    t1 = create_element("w:t")
    create_attribute(t1, "xml:space", "preserve")
    t1.text = "第 "
    run._r.append(t1)

    fldChar1 = create_element("w:fldChar")
    create_attribute(fldChar1, "w:fldCharType", "begin")

    instrText = create_element("w:instrText")
    create_attribute(instrText, "xml:space", "preserve")
    instrText.text = "PAGE"

    fldChar2 = create_element("w:fldChar")
    create_attribute(fldChar2, "w:fldCharType", "end")

    run._r.append(fldChar1)
    run._r.append(instrText)
    run._r.append(fldChar2)

    t2 = create_element("w:t")
    create_attribute(t2, "xml:space", "preserve")
    t2.text = " 页，共 "
    run._r.append(t2)

    fldChar3 = create_element("w:fldChar")
    create_attribute(fldChar3, "w:fldCharType", "begin")

    instrText2 = create_element("w:instrText")
    create_attribute(instrText2, "xml:space", "preserve")
    instrText2.text = "NUMPAGES"

    fldChar4 = create_element("w:fldChar")
    create_attribute(fldChar4, "w:fldCharType", "end")

    run._r.append(fldChar3)
    run._r.append(instrText2)
    run._r.append(fldChar4)

    t3 = create_element("w:t")
    create_attribute(t3, "xml:space", "preserve")
    t3.text = " 页"
    run._r.append(t3)


def add_page_header(rptinfo, section, pagestyle):
    section.different_first_page_header_footer = False
    header = section.header
    header.is_linked_to_previous = False  # 不使用上节内容和样式
    # 清空header内容
    for para in header.paragraphs:
        p = para._element
        p.getparent().remove(p)
    # 第一行：标题
    title_paragraph = header.add_paragraph()
    title_paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    run = title_paragraph.add_run()
    set_run_font(run, 12)
    run.add_text("职业健康检查报告书")
    
    # 第二行：表格
    width = Inches(7.0)#Cm(21)
    if pagestyle == constant.PageStyle.Landscape.value:
        width = Inches(10.5)#Cm(29.7)
    table = header.add_table(rows=1, cols=2, width=width)
    # table.autofit = False
    page_width = section.page_width - section.left_margin - section.right_margin
    table.columns[0].width = int(page_width * 2/3)
    table.columns[1].width = int(page_width // 3)
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    # 设置表格仅bottom有黑色边框，其它无边框
    tbl = table._tbl
    tblPr = tbl.tblPr
    borders = OxmlElement('w:tblBorders')
    for border_name in ['top', 'left', 'right', 'insideH', 'insideV']:
        border = OxmlElement(f'w:{border_name}')
        border.set(qn('w:val'), 'nil')
        borders.append(border)
    # bottom边框为黑色
    bottom = OxmlElement('w:bottom')
    bottom.set(qn('w:val'), 'single')
    bottom.set(qn('w:sz'), '6')
    bottom.set(qn('w:color'), '000000')
    borders.append(bottom)
    tblPr.append(borders)
    #set height
    row = table.rows[0]
    tr = row._tr
    trPr = tr.get_or_add_trPr()
    trHeight = OxmlElement('w:trHeight')
    trHeight.set(qn('w:val'), str(int(0.6 * 567)))  # 1cm=567 twips
    trHeight.set(qn('w:hRule'), 'exact')
    trPr.append(trHeight)
    # 左侧单元格：报告号，左对齐
    cell_left = table.cell(0, 0)
    p_left = cell_left.paragraphs[0]
    p_left.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
    run_left = p_left.add_run(rptinfo.tj_reportnum)
    set_run_font(run_left, 10)
    # 右侧单元格：页码，右对齐
    cell_right = table.cell(0, 1)
    p_right = cell_right.paragraphs[0]
    p_right.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
    run_right = p_right.add_run()
    set_run_font(run_right, 10)
    add_page_number_with_run(run_right, 10)


def insertHR(paragraph):
    p = paragraph._p  # p is the <w:p> XML element
    pPr = p.get_or_add_pPr()
    pBdr = OxmlElement("w:pBdr")
    pPr.insert_element_before(
        pBdr,
        "w:shd",
        "w:tabs",
        "w:suppressAutoHyphens",
        "w:kinsoku",
        "w:wordWrap",
        "w:overflowPunct",
        "w:topLinePunct",
        "w:autoSpaceDE",
        "w:autoSpaceDN",
        "w:bidi",
        "w:adjustRightInd",
        "w:snapToGrid",
        "w:spacing",
        "w:ind",
        "w:contextualSpacing",
        "w:mirrorIndents",
        "w:suppressOverlap",
        "w:jc",
        "w:textDirection",
        "w:textAlignment",
        "w:textboxTightWrap",
        "w:outlineLvl",
        "w:divId",
        "w:cnfStyle",
        "w:rPr",
        "w:sectPr",
        "w:pPrChange",
    )
    bottom = OxmlElement("w:bottom")
    bottom.set(qn("w:val"), "single")
    bottom.set(qn("w:sz"), "6")
    bottom.set(qn("w:space"), "1")
    bottom.set(qn("w:color"), "auto")
    pBdr.append(bottom)

def set_repeat_table_header(row):
    """Sets the table row to repeat as a header on every new page."""
    tr = row._tr
    trPr = tr.get_or_add_trPr()
    tblHeader = OxmlElement('w:tblHeader')
    tblHeader.set(qn('w:val'), "true")
    trPr.append(tblHeader)
    return row