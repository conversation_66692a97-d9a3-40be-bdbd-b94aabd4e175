use crate::{common::constant, SYSCACHE};
use anyhow::{anyhow, Result};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};

pub struct BarCodeSvc;

impl BarCodeSvc {
  pub async fn generate_bar_code(cilist: &mut Vec<TjCheckiteminfo>, db: &DbConnection) -> Result<()> {
    let bcrule = SYSCACHE
      .get()
      .unwrap()
      .get_dict(constant::DictType::DictSysparm as i32, constant::SysParm::BCRule as i32, db)
      .await;
    if bcrule.ss_name.is_empty() {
      error!("没有找到bcrule的配置信息，采用体检号做条码的方式来处理");
      cilist.iter_mut().for_each(|v| v.tj_barcode = v.tj_testid.clone());
      return Ok(());
    }
    // let bcrule = ret.unwrap();

    let itemids: Vec<String> = cilist.iter().map(|v| v.tj_itemid.to_string()).collect();
    let ret = TjIteminfo::query_many(&itemids, -1, -1, -1, &vec![], &db.get_connection()).await;
    if ret.is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let iteminfos = ret.unwrap();
    let ret = TjItemtype::query_many(&vec![], &db.get_connection()).await;
    if ret.is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let itemtypes = ret.unwrap();

    let combineitems: Vec<TjCheckiteminfo> = cilist.iter().filter(|&f| f.tj_combineflag == constant::YesOrNo::Yes as i32).map(|v| v.clone()).collect();

    for val in combineitems.iter() {
      if val.tj_combineflag == constant::YesOrNo::No as i32 || !val.tj_barcode.is_empty() {
        continue;
      }
      let mut barcode = val.tj_testid.clone();
      let iteminfo = iteminfos.iter().find(|f| f.tj_itemid.eq_ignore_ascii_case(&val.tj_itemid));
      if iteminfo.is_none() {
        error!("不能找到项目编号为:{}的项目信息", &val.tj_itemid);
        continue;
      }
      let iteminfo = iteminfo.unwrap();
      let itemtype = itemtypes.iter().find(|&f| f.tj_typeid == iteminfo.tj_itemtype);
      if itemtype.is_none() {
        error!("不能找到itemtype:{}的项目类别信息", iteminfo.tj_itemtype);
        barcode = val.tj_testid.clone();
      } else {
        let itemtype = itemtype.unwrap();
        if bcrule.ss_name.eq_ignore_ascii_case(&constant::BarcodeType::LisOnly.to_string()) {
          //lis only
          if itemtype.tj_type == constant::DeptType::Lab as i32 {
            barcode = BarCodeSvc::get_barcode(val, &bcrule, db).await;
          }
        } else if bcrule.ss_name.eq_ignore_ascii_case(&constant::BarcodeType::LisAndPacs.to_string()) {
          //lis and pacs
          if itemtype.tj_type == constant::DeptType::Lab as i32 || itemtype.tj_type == constant::DeptType::Function as i32 {
            barcode = BarCodeSvc::get_barcode(val, &bcrule, db).await;
          }
        } else if bcrule.ss_name.eq_ignore_ascii_case(&constant::BarcodeType::All.to_string()) {
          //lis and pacs and normal
          barcode = BarCodeSvc::get_barcode(val, &bcrule, db).await;
        } else {
          //none
          barcode = val.tj_testid.clone();
        }
      }

      cilist
        .iter_mut()
        .filter(|f| f.tj_synid.eq_ignore_ascii_case(&val.tj_itemid))
        .for_each(|v| v.tj_barcode = barcode.clone());

      let zh_dict = SYSCACHE
        .get()
        .unwrap()
        .get_dict(constant::DictType::DictSysparm as i32, constant::SysParm::LisCodeZh as i32, db)
        .await;
      // if zh_dict.is_none() {
      //   continue;
      // }
      // let zh_dict = zh_dict.unwrap();
      if zh_dict.ss_short.eq_ignore_ascii_case("0") || zh_dict.ss_short.is_empty() {
        continue;
      }
      let ret = TjBardetail::query_many(&vec![], &db.get_connection()).await;
      if ret.is_err() {
        error!("查找条码明细错误:{}", ret.err().unwrap().to_string());
        continue;
      }
      let bardetails = ret.unwrap();
      let bdt = bardetails.iter().find(|&f| f.tj_itemid.eq_ignore_ascii_case(&val.tj_itemid));
      if bdt.is_none() {
        continue;
      }
      let bdt = bdt.unwrap();

      let bdts: Vec<&TjBardetail> = bardetails.iter().filter(|&f| f.tj_binum.eq_ignore_ascii_case(&bdt.tj_binum)).map(|v| v).collect();
      for bd in bdts.iter() {
        for ci in cilist.iter_mut() {
          if ci.tj_synid.eq_ignore_ascii_case(&bd.tj_itemid) {
            ci.tj_barcode = barcode.clone();
          }
        }
      }
    }

    Ok(())
  }

  async fn get_barcode(ciinfo: &TjCheckiteminfo, bcrule: &SsDictionary, db: &DbConnection) -> String {
    if bcrule.ss_short == "1" {
      return BarCodeSvc::get_txsy_barcode(ciinfo, db).await;
    }
    BarCodeSvc::get_default_barcode(ciinfo, db).await
  }
  async fn get_default_barcode(ciinfo: &TjCheckiteminfo, db: &DbConnection) -> String {
    // info!("开始生成默认规则的条码信息");
    let mut barcode = ciinfo.tj_testid.clone();
    let ret = SsIdentity::query(constant::IdentityCode::Barcode.to_string().as_str(), db.get_connection()).await;
    if ret.is_err() {
      error!("在ss_identity表中查找barcode的配置信息错误:{}", ret.unwrap_err().to_string());
      return barcode;
    }
    let identity = ret.unwrap();
    if identity.is_none() {
      error!("不能在ss_identity表中找到barcode的配置信息，请检查配置信息");
      return barcode;
    }
    let identity = identity.unwrap();

    let dict = SYSCACHE
      .get()
      .unwrap()
      .get_dict(constant::DictType::DictSysparm as i32, constant::SysParm::BCPrecode as i32, db)
      .await;
    if dict.ss_short.is_empty() {
      error!("不能在identity表中找到precode的规则配置信息，采用默认规则");
      return barcode;
    }
    // let dict = ret.unwrap();
    let precode = dict.ss_short.to_owned(); //前置代码
    let bcode = identity.id_cvalue; //.to_string();
    let itotal_len = dict.ss_name.parse::<usize>().unwrap_or(12);

    //println!("{:0width$}", x, width = width); // prints 001234
    let mut width = itotal_len - precode.len();
    if width <= 0 {
      width = 0;
    }
    // info!("前置:{},条码:{},补长:{}", &precode, &bcode, width);
    barcode = format!("{}{:0width$}", precode, bcode, width = width);

    barcode
  }
  async fn get_txsy_barcode(ciinfo: &TjCheckiteminfo, db: &DbConnection) -> String {
    let mut barcode = ciinfo.tj_testid.clone();
    let ret = SsIdentity::query(constant::IdentityCode::Barcode.to_string().as_str(), db.get_connection()).await;
    if ret.is_err() {
      error!("在ss_identity表中朝朝barcode的配置信息错误:{}", ret.unwrap_err().to_string());
      return barcode;
    }
    let identity = ret.unwrap();
    if identity.is_none() {
      error!("不能在ss_identity表中找到barcode的配置信息，请检查配置信息");
      return barcode;
    }
    let identity = identity.unwrap();
    barcode = format!("{}{}00", identity.id_ovalue, identity.id_cvalue);
    barcode
  }
}
