//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_packageinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_pnum: String,
  pub tj_pname: String,
  pub tj_price: String,
  pub tj_type: i32,
  pub tj_memo: String,
  pub tj_showorder: i32,
  pub tj_fitrange: i32,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_flag: i32,
  pub tj_operator: i32,
  pub tj_moddate: i64,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
