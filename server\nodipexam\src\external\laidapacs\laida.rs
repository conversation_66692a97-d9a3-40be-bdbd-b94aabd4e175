// use anyhow::{anyhow, Result};
// use rbatis::{crud::CRUD, crud_table, rbatis::Rbatis};
// use utility::timeutil;

// #[crud_table(table_name:HisJCRW)]
#[derive(<PERSON><PERSON>, Default, Debug, serde::Serialize, serde::Deserialize)]
pub struct HisJCRW {
  pub lsh: i64,                 //流水号
  pub jch: String,              //检查号，如超声号、X线号
  pub djxh: i32,                //登记序号：1-住院、2-门诊、3-急诊、4-体检
  pub bah: String,              //病案号
  pub bqmc: Option<String>,     //病区名称
  pub bqdm: Option<String>,     //病区代码
  pub brch: Option<String>,     //病人床号
  pub jzkh: Option<String>,     //就诊卡号
  pub brxm: Option<String>,     //病人姓名
  pub brxb: Option<i32>,        //病人性别
  pub brnl: Option<i32>,        //病人年龄
  pub csrq: Option<String>,     //病人出生日期 datetime
  pub jtzz: Option<String>,     //家庭住址
  pub lxdh: Option<String>,     //联系电话
  pub ylxh: Option<i32>,        //医疗序号，即检查项目代码
  pub ylmc: Option<String>,     //医疗名称，即检查项目名称
  pub sjysgh: Option<String>,   //申检医生工号，即开单医生工号
  pub sjysxm: Option<String>,   //申检医生姓名
  pub sjksdm: Option<String>,   //申检科室代码
  pub sjksmc: Option<String>,   //申检科室名称
  pub sjsj: Option<String>,     //申检时间 datetime
  pub lczd: Option<String>,     //临床诊断
  pub djysgh: Option<String>,   //登记医生工号
  pub djysxm: Option<String>,   //登记医生姓名
  pub djrq: Option<String>,     //登记日期 datetime
  pub jclxdm: Option<String>,   //检查类型代码，即HisJCLX.jclxdm字段
  pub jcbwdm: Option<String>,   //检查部位代码，即HisJCBW.jcbwdm字段
  pub jcfsdm: Option<String>,   //检查方式代码，即HisJCFS.jcfsdm字段
  pub jcsbdm: Option<String>,   //检查设备代码，即HisJCSB.jcsbdm字段
  pub jcfy: Option<i32>,        //检查费用 numeric
  pub yyh: Option<String>,      //预约号
  pub yyrq: Option<String>,     //预约日期  datetime
  pub jcysgh: Option<String>,   //检查医生工号
  pub jcysxm: Option<String>,   //检查医生姓名
  pub jcrq: Option<String>,     //检查日期 datetime
  pub bgysgh: Option<String>,   //报告医生工号
  pub bgysxm: Option<String>,   //报告医生姓名
  pub bgrq: Option<String>,     //报告填写日期 datetime
  pub shysgh: Option<String>,   //审核医生工号
  pub shysxm: Option<String>,   //审核医生姓名
  pub shrq: Option<String>,     //审核日期 datetime
  pub dyysgh: Option<String>,   //打印医生工号
  pub dyysxm: Option<String>,   //打印医生姓名
  pub dyrq: Option<String>,     //打印日期 datetime
  pub jcjf: Option<String>,     //检查机房
  pub jcsb: Option<String>,     //检查设备
  pub see: Option<String>,      //检查所见
  pub jcjg: Option<String>,     //检查结果
  pub status: Option<String>,   //检查任务状态：预约、登记、检查、报告、审核
  pub studyuid: Option<String>, //检查全球唯一号：Study Instance UID
  pub sqdh: Option<String>,     //电子申请单号
  pub brbh: Option<String>,     //病人编号：根据用户配置的策略，由接口自动生成。
  pub jcdh: Option<String>,     //检查单号：根据用户配置的策略，由接口自动生成。
  pub sfzh: Option<String>,     //身份证号
  pub yqmc: Option<String>,     //院区名称
  pub zymb: Option<String>,     //转院目标
  pub lcxx: Option<String>,     //临床信息LCXX格式=提交日期^^临床信心^^病史摘要^^体检信息^^检查结果^^特殊说明^^|开单项目
  pub brtz: Option<i32>,        //病人体重
  pub jpfy: Option<f32>,        //胶片费用
  pub nlxx: Option<String>, //NLXX字段，支持由HIS系统输入年龄的详细信息。年龄信息数据规则为三位数字（不足三位补零）加年龄单位，年龄单位支持Y（年）、M（月）、W（周）、D（日）、H（小时）。举例说明，HIS系统填入020Y，表示20岁；填入030M，表示30个月；填入007W，表示7周；填入120D，表示120天；填入300H，表示300小时。注：如该字段为空，检查年龄计算采用原有计算方式。
  pub jcpd: Option<String>, //检查排队号
}

// #[crud_table(table_name:HisRWZL)]
#[derive(Clone, Default, Debug, serde::Serialize, serde::Deserialize)]
pub struct HisRWZL {
  pub rwzllsh: i64,     //任务指令流水号
  pub jcrwlsh: i64,     //检查任务流水号：指定操作HisJCRW的记录，对应HisJCRW.lsh字段
  pub rwzllx: i32,      //任务类型：1–登录、5–修改、 10–取消、 15–添加部位、100–检查、105–报告、110–审核、115–修改、1000-转院
  pub rwzlsj: String,   //指令生成时间
  pub rwzlczhm: String, //任务指令操作目标：仅在处理转院指令时需填写，填入转院病人的病案号
  pub rwzlczmb: String, //任务指令操作目标：仅在处理转院指令时需填写，填入转院病人转至的院区
}

// #[crud_table(table_name:HisJCLX)]
#[derive(Clone, Default, Debug, serde::Serialize, serde::Deserialize)]
pub struct HisJCLX {
  pub jclxdm: String, //检查类型代码
  pub jclxmc: String, //检查类型名称
  pub jchqz: String,  //生成检查号时使用的前缀
  pub srm1: String,   //简明输入1
  pub srm2: String,   //简明输入2
  pub sybz: String,   //使用标志（0 – 正常，1 – 取消）
}

// #[crud_table(table_name:HisJCSB)]
#[derive(Clone, Default, Debug, serde::Serialize, serde::Deserialize)]
pub struct HisJCSB {
  pub jcsbdm: String,   //检查设备代码
  pub jcsblxdm: String, //检查设备类型代码，对应HisJCLX表中的jclxdm字段
  pub jcsbmcz: String,  //检查设备名称
  pub jcsbyw: String,   //检查设备英文名称
  pub jcsbjf: String,   //检查设备所在机房
  pub jcsbwz: String,   //检查设备所在位置
}

// #[crud_table(table_name:HisJCBW)]
#[derive(Clone, Default, Debug, serde::Serialize, serde::Deserialize)]
pub struct HisJCBW {
  pub jcbwdm: String, //检查部位代码
  pub jcbwmc: String, //检查部位名称
  pub jcbwyw: String, //检查部位英文名称
  pub srm1: String,   //简明输入1
  pub srm2: String,   //简明输入2
  pub yyfw: String,   //应用范围
}

// #[crud_table(table_name:HisJCXMDM)]
#[derive(Clone, Default, Debug, serde::Serialize, serde::Deserialize)]
pub struct HisJCXMDM {
  pub ylxh: i64,      //医疗序号
  pub ylmc: String,   //检查项目名称
  pub ksdm: String,   //科室代码
  pub jclxdm: String, //检查类型代码，对应HisJCLX表中的jclxdm字段
  pub jcbwdm: String, //检查部位代码，对应HisJCBW表中的jcbwdm字段
  pub jcfsdm: String, //检查方式代码
}

// #[derive(Debug, Default)]
// pub struct LaidaPacs {
//   rb: Rbatis,
// }
// impl LaidaPacs {
//   pub async fn new(uri: &str) -> Result<Self> {
//     let rb = Rbatis::new();
//     rb.link(uri).await.expect("connect database error");
//     Ok(LaidaPacs { rb })
//   }
//   pub async fn insert(&self, jcrw: &HisJCRW) -> Result<i64> {
//     let ret = self.rb.save(&jcrw, &[]).await;
//     info!("save HisJCRW result:{:?}", &ret);
//     if ret.as_ref().is_err() {
//       error!("{}", ret.as_ref().unwrap_err().to_string());
//       return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
//     }
//     let exec_id = ret.unwrap().last_insert_id.unwrap_or_default();
//     let rwzl = HisRWZL {
//       rwzllsh: 0,
//       jcrwlsh: exec_id,
//       rwzllx: 1,
//       rwzlsj: Some(rbatis::DateTimeNative::now()), //timeutil::format_timestamp(timeutil::current_timestamp()),
//       rwzlczhm: "".to_string(),
//       rwzlczmb: "".to_string(),
//     };
//     let ret = self.rb.save(&rwzl, &[]).await;
//     if ret.as_ref().is_err() {
//       error!("{}", ret.as_ref().unwrap_err().to_string());
//       return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
//     }
//     info!("save hisRWZL result:{:?}", &ret);
//     Ok(exec_id)
//   }
//   pub async fn delete(&self, testid: &str, itemid: &str) -> Result<i64> {
//     let w = self.rb.new_wrapper().eq("bah", testid).eq("lcxx", itemid);
//     let ret = self.rb.fetch_list_by_wrapper(w).await;
//     if ret.as_ref().is_err() {
//       error!("{}", ret.as_ref().unwrap_err().to_string());
//       return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
//     }
//     let result_datas: Vec<HisJCRW> = ret.unwrap();
//     if result_datas.len() <= 0 {
//       return Ok(0);
//     }
//     let len = result_datas.len();
//     for val in result_datas.into_iter() {
//       let rwzl = HisRWZL {
//         rwzllsh: 0,
//         jcrwlsh: val.lsh,
//         rwzllx: 10,
//         rwzlsj: Some(rbatis::DateTimeNative::now()), //imeutil::format_timestamp(timeutil::current_timestamp()),
//         rwzlczhm: "".to_string(),
//         rwzlczmb: "".to_string(),
//       };
//       let ret = self.rb.save(&rwzl, &[]).await;
//       if ret.as_ref().is_err() {
//         error!("{}", ret.as_ref().unwrap_err().to_string());
//         return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
//       }
//     }
//     Ok(len as i64)
//   }
// }
