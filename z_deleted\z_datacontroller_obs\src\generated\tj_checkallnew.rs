//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_checkallnew")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    #[sea_orm(unique)]
    pub tj_testid: String,
    pub tj_typeid: i32,
    pub tj_discode: String,
    pub tj_itemcode: String,
    pub tj_hazardcode: String,
    pub tj_diseasename: String,
    #[sea_orm(column_type = "Custom(\"LONGTEXT\".to_owned())")]
    pub tj_ocuabnormal: String,
    #[sea_orm(column_type = "Custom(\"LONGTEXT\".to_owned())")]
    pub tj_othabnormal: String,
    #[sea_orm(column_type = "Custom(\"LONGTEXT\".to_owned())")]
    pub tj_ocuconclusion: String,
    #[sea_orm(column_type = "Custom(\"LONGTEXT\".to_owned())")]
    pub tj_othconclusion: String,
    #[sea_orm(column_type = "Custom(\"LONGTEXT\".to_owned())")]
    pub tj_ocusuggestion: String,
    #[sea_orm(column_type = "Custom(\"LONGTEXT\".to_owned())")]
    pub tj_othsuggestion: String,
    #[sea_orm(column_type = "Custom(\"LONGTEXT\".to_owned())")]
    pub tj_ocuopinion: String,
    #[sea_orm(column_type = "Custom(\"LONGTEXT\".to_owned())")]
    pub tj_othopinion: String,
    pub tj_staffid: i32,
    pub tj_checkdate: i64,
    pub tj_castatus: i32,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
