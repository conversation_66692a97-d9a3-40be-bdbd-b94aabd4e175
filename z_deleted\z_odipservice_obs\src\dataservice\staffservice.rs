use anyhow::{anyhow, Result};
use datacontroller::{datasetup::DbConnection, entities::prelude::*};
#[derive(Debug, Default)]
pub struct StaffService {}

impl StaffService {
    pub async fn query_many(ids: &Vec<i64>, dbconn: &DbConnection) -> Result<Vec<TjStaffadmin>> {
        let ret = TjStaffadmin::query_many(ids, dbconn).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("error:{}", ret.as_ref().err().unwrap().to_string()));
        }
        Ok(ret.unwrap())
    }

    pub fn find_staff(id: i64, infos: &Vec<TjStaffadmin>) -> TjStaffadmin {
        let ret = infos.iter().find(|&x| x.id == id).cloned();
        if ret.is_none() {
            return TjStaffadmin {
                id: 1,
                tj_staffno: "admin".to_string(),
                tj_staffname: "管理员".to_string(),
                ..Default::default()
            };
        }

        ret.unwrap()
    }
}
