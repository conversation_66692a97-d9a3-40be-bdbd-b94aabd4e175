use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub struct IteminfoV {
  pub itemid: String,
  pub itemname: String,
  pub defaultresult: String,
  pub uppervalue: String,
  pub lowvalue: String,
  pub combineflag: i32,
}
crud!(IteminfoV {}, "iteminfo_v");
rbatis::impl_select!(IteminfoV{query_many() => "`where id > 0 `"});
