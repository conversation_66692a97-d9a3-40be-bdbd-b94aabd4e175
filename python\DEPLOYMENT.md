# Report Server Deployment Guide

This guide explains how to compile, package, and deploy the Report Server REST API.

## Prerequisites

- Python 3.7+ (tested with Python 3.13)
- All dependencies from `requirements.txt`
- Access to the database configuration file (`config/nodipexam.toml`)
- Font files in the `fonts/` directory

## Installation Methods

### Method 1: Direct Python Deployment

#### 1. Install Dependencies

```bash
cd python
pip install -r requirements.txt
```

#### 2. Configure Environment

Set environment variables (optional):

```bash
# Windows
set REPORT_SERVER_HOST=0.0.0.0
set REPORT_SERVER_PORT=8080
set REPORT_OUTPUT_DIR=./reports

# Linux/Mac
export REPORT_SERVER_HOST=0.0.0.0
export REPORT_SERVER_PORT=8080
export REPORT_OUTPUT_DIR=./reports
```

#### 3. Start the Server

```bash
# From the python directory
python start_server.py

# Or directly
python reportserver.py
```

### Method 2: Standalone Executable (PyInstaller)

#### 1. Install PyInstaller

```bash
pip install pyinstaller
```

#### 2. Create Executable

```bash
cd python

# Create standalone executable
pyinstaller --onefile --name reportserver \
    --add-data "config;config" \
    --add-data "fonts;fonts" \
    --add-data "images;images" \
    --hidden-import=pymysql \
    --hidden-import=reportlab \
    --hidden-import=docx \
    start_server.py
```

#### 3. Package for Distribution

The executable will be in `dist/reportserver.exe`. Copy the following structure:

```
reportserver/
├── reportserver.exe
├── config/
│   └── nodipexam.toml
├── fonts/
│   ├── simhei.ttf
│   └── simsun.ttc
├── reports/ (will be created)
└── log/ (will be created)
```

### Method 3: Docker Deployment

#### 1. Create Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p reports log

# Expose port
EXPOSE 8080

# Set environment variables
ENV REPORT_SERVER_HOST=0.0.0.0
ENV REPORT_SERVER_PORT=8080
ENV REPORT_OUTPUT_DIR=/app/reports

# Start the server
CMD ["python", "start_server.py"]
```

#### 2. Build and Run Docker Image

```bash
# Build image
docker build -t reportserver .

# Run container
docker run -d \
    --name reportserver \
    -p 8080:8080 \
    -v $(pwd)/config:/app/config:ro \
    -v $(pwd)/fonts:/app/fonts:ro \
    -v $(pwd)/reports:/app/reports \
    reportserver
```

### Method 4: Production Deployment with Gunicorn

#### 1. Install Gunicorn

```bash
pip install gunicorn
```

#### 2. Create WSGI Entry Point

Create `wsgi.py`:

```python
from reportserver import app

if __name__ == "__main__":
    app.run()
```

#### 3. Run with Gunicorn

```bash
# Basic usage
gunicorn --bind 0.0.0.0:8080 --workers 4 wsgi:app

# With configuration file
gunicorn --config gunicorn.conf.py wsgi:app
```

#### 4. Create Gunicorn Configuration

Create `gunicorn.conf.py`:

```python
bind = "0.0.0.0:8080"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 300
keepalive = 2
preload_app = True
```

## Configuration

### Environment Variables

- `REPORT_SERVER_HOST`: Server host (default: 0.0.0.0)
- `REPORT_SERVER_PORT`: Server port (default: 8080)
- `REPORT_SERVER_DEBUG`: Debug mode (default: false)
- `REPORT_OUTPUT_DIR`: Output directory (default: ./reports)
- `REPORT_LOG_LEVEL`: Log level (default: INFO)

### Required Files

1. `config/nodipexam.toml` - Database configuration
2. `fonts/simhei.ttf` - Chinese font
3. `fonts/simsun.ttc` - Chinese font

## Service Installation

### Windows Service

#### 1. Install NSSM (Non-Sucking Service Manager)

Download from: https://nssm.cc/download

#### 2. Install Service

```cmd
nssm install ReportServer
nssm set ReportServer Application "C:\path\to\python.exe"
nssm set ReportServer AppParameters "C:\path\to\start_server.py"
nssm set ReportServer AppDirectory "C:\path\to\python"
nssm set ReportServer DisplayName "Report Server API"
nssm set ReportServer Description "REST API for Report Generation"
nssm start ReportServer
```

### Linux Systemd Service

#### 1. Create Service File

Create `/etc/systemd/system/reportserver.service`:

```ini
[Unit]
Description=Report Server API
After=network.target

[Service]
Type=simple
User=reportserver
WorkingDirectory=/opt/reportserver/python
Environment=PATH=/opt/reportserver/venv/bin
ExecStart=/opt/reportserver/venv/bin/python start_server.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 2. Enable and Start Service

```bash
sudo systemctl daemon-reload
sudo systemctl enable reportserver
sudo systemctl start reportserver
sudo systemctl status reportserver
```

## Reverse Proxy Setup

### Nginx Configuration

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /api/ {
        proxy_pass http://127.0.0.1:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
    }

    # Serve static files directly
    location /reports/ {
        alias /path/to/reports/;
        autoindex on;
    }
}
```

## Monitoring and Logging

### Log Files

- Application logs: `log/reportserver.log`
- Access logs: Handled by reverse proxy

### Health Check

```bash
curl http://localhost:8080/api/health
```

### Monitoring Script

Create `monitor.sh`:

```bash
#!/bin/bash
HEALTH_URL="http://localhost:8080/api/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "$(date): Service is healthy"
else
    echo "$(date): Service is down (HTTP $RESPONSE)"
    # Restart service or send alert
fi
```

## Troubleshooting

### Common Issues

1. **Font not found errors**

   - Ensure fonts directory is accessible
   - Check font file permissions

2. **Database connection errors**

   - Verify `config/nodipexam.toml` exists
   - Check database credentials and connectivity

3. **Permission errors**

   - Ensure write permissions for reports and log directories

4. **Port already in use**
   - Change port in configuration
   - Kill existing processes using the port

### Debug Mode

Enable debug mode for development:

```bash
export REPORT_SERVER_DEBUG=true
python start_server.py
```

## Security Considerations

1. **Network Security**

   - Use HTTPS in production
   - Restrict access with firewall rules
   - Use reverse proxy for SSL termination

2. **File Security**

   - Secure report output directory
   - Regular cleanup of old reports
   - Validate file paths to prevent directory traversal

3. **Database Security**
   - Use strong database credentials
   - Limit database user permissions
   - Use connection encryption

## Performance Tuning

1. **Worker Processes**

   - Use multiple workers for production
   - Monitor CPU and memory usage

2. **Caching**

   - Implement Redis for status caching
   - Cache database queries

3. **File Management**
   - Implement report file cleanup
   - Use external storage for large files

## Backup and Recovery

1. **Configuration Backup**

   - Backup configuration files
   - Version control deployment scripts

2. **Report Files**

   - Regular backup of report directory
   - Implement retention policies

3. **Database**
   - Regular database backups
   - Test recovery procedures
