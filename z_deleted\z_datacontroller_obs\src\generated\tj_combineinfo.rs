//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_combineinfo")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_combid: String,
    pub tj_itemid: String,
    pub tj_showorder: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::tj_iteminfo::Entity",
        from = "Column::TjItemid",
        to = "super::tj_iteminfo::Column::TjItemid",
        on_update = "Restrict",
        on_delete = "Restrict"
    )]
    TjIteminfo2,
    #[sea_orm(
        belongs_to = "super::tj_iteminfo::Entity",
        from = "Column::TjCombid",
        to = "super::tj_iteminfo::Column::TjItemid",
        on_update = "Restrict",
        on_delete = "Restrict"
    )]
    TjIteminfo1,
}

impl ActiveModelBehavior for ActiveModel {}
