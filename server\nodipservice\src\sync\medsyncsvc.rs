use std::path::Path;

use anyhow::{anyhow, Result};
use tracing::*;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use utility::timeutil::current_timestamp;

use crate::{
  client::{httpclient::HttpClient, response::ResponseBody},
  common::constant::{self, ExamStatus, PolarApi, CODE_HTTP_OK},
  dto::RegisterResponseDto,
  medexam::medexamsvc::MedexamSvc,
  sync::syncdto::MedsyncResponse,
};

use super::{
  polarmodel::{self},
  syncdto::{CorpuserDto, CorpuserSyncDto, MedexamInfo, MedsyncResultDetail},
};

pub struct MedsyncService;

impl MedsyncService {
  ///从polar平台同步体检预约的信息
  pub async fn sync_medinfo_and_corpinfos_from_polar(_userinfo: &TjStaffadmin, orgid: i64, server: &str, splitter: &String, db: &DbConnection) -> Result<Vec<RegisterResponseDto>> {
    if server.is_empty() {
      return Err(anyhow!("server is not configured, NOT able to sync"));
    }
    if orgid <= 0 {
      return Err(anyhow!("orgid is not configured, NOT able to sync"));
    }
    let url = format!("{}{}", server, PolarApi::ApiSyncAppoint.to_string());
    let ret = HttpClient::send_post_request(&orgid.to_string(), &url, "").await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let result_text = ret.unwrap();
    let ret = serde_json::from_str(result_text.as_str());
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let result_response: ResponseBody<Vec<MedexamInfo>> = ret.unwrap();
    if result_response.code != CODE_HTTP_OK {
      return Err(anyhow!("{}", result_response.message));
    }
    let results = result_response.data;
    let mut resp: Vec<RegisterResponseDto> = Vec::new();
    if results.len() <= 0 {
      return Ok(vec![]);
    }
    let package = TjPackageinfo { ..Default::default() };
    for med in results {
      let ptinfo = polarmodel::convert_to_tj_patientinfo(&med.wkinfo);
      let medinfo = polarmodel::convert_to_tj_medexaminfo(&med.medinfo);
      let hdinfos: Vec<TjHazardinfo> = vec![];
      let ret = MedexamSvc::medexam_quick_register(&ptinfo, &medinfo, &package, &hdinfos, splitter, db).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.err().unwrap().to_string()));
      }
      resp.push(ret.unwrap());
    }
    Ok(resp)
  }
  ///同步体检结果信息到polar平台
  /// 1) pdf报告
  /// 2）具体结果
  /// 3）照片信息
  pub async fn sync_medexam_to_polar(orginfo: i64, testid: &str, rptfile: &str, server: &str, db: &DbConnection) -> Result<()> {
    let ret = MedexamSvc::query_medinfo_by_testid(testid, &db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let ret = ret.unwrap();
    if ret.is_none() {
      error!("testid:{} does not exist", testid);
      return Err(anyhow!("testid:{} does not exist", testid));
    }
    let mut medinfo = ret.unwrap();
    if medinfo.tj_checkstatus < constant::ExamStatus::Allchecked as i32 {
      error!("testid:{} 未总检，不能上报", testid);
      return Err(anyhow!("testid:{} 未总检，不能上报", testid));
    }
    let ret = TjPatient::query(&medinfo.tj_pid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let ptret = ret.unwrap();
    if ptret.is_none() {
      error!("pid:{} does not exist", &medinfo.tj_pid);
      return Err(anyhow!("pid:{} does not exist", &medinfo.tj_pid));
    }
    let mut ptinfo = ptret.unwrap();
    //同步报告
    let mut uploaded_file = "".to_string();
    if !rptfile.is_empty() {
      info!("start to upload report file......:{}", &rptfile);
      let ret = MedsyncService::upload_medexam_report_file(rptfile, server, orginfo).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.err().unwrap().to_string()));
      }
      uploaded_file = ret.unwrap();
    }
    info!("uploaded file:{}", &uploaded_file);
    //如果是健康证体检，则同步照片
    let mut uploaded_photo = "".to_string();
    if medinfo.tj_testtype == constant::TestType::JKZ as i32 && !ptinfo.tj_photo.is_empty() {
      info!("start to upload photo......：{}", &ptinfo.tj_photo);
      let mut photo_file = ptinfo.tj_photo.to_owned();
      let mut path = Path::new(&photo_file);
      if !path.exists() {
        photo_file = format!("./photoes/{}", photo_file);
      }
      path = Path::new(&photo_file);
      if !path.exists() {
        error!("can't find photo file by:{}", &ptinfo.tj_photo);
      } else {
        let ret = MedsyncService::upload_medexam_photo_file(&photo_file, server, orginfo).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        uploaded_photo = ret.unwrap();
      }
    }
    info!("uploaded photo:{}", &uploaded_photo);
    //上传结果
    let ret = MedsyncService::upload_medexam_result_details(&mut medinfo, &mut ptinfo, &uploaded_file, &uploaded_photo, server, orginfo, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    Ok(())
  }
  ///同步体检状态到polar平台
  pub async fn sync_medexam_status_to_polar(_db: &DbConnection) -> Result<()> {
    Ok(())
  }

  async fn upload_medexam_result_details(medinfo: &mut TjMedexaminfo, ptinfo: &mut TjPatient, rptfile: &str, photo: &str, server: &str, orgid: i64, db: &DbConnection) -> Result<String> {
    let ret = MedsyncService::prepare_upload_medresults(medinfo, ptinfo, orgid, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let mut result = ret.unwrap();
    result.medinfo.p_rptfile = rptfile.to_string();
    result.medinfo.p_photo = photo.to_string();
    result.wkinfo.wx_photo = photo.to_string();
    // info!("final data to be uploaded:{:#?}", &result);
    //开始上传结果信息......
    let ret = serde_json::to_string(&result);
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let parm = ret.unwrap();
    let url = format!("{}{}", server, constant::PolarApi::ApiSyncResult.to_string());
    info!("start to send to saas server with api:{}", url);
    let orginfo = orgid.to_string();
    let ret = HttpClient::send_post_request(&orginfo, &url, &parm).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let resp = ret.unwrap();
    // info!("response from request:{}", &resp);
    let ret = serde_json::from_str::<ResponseBody<MedsyncResponse>>(resp.as_str());
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let result_response = ret.unwrap();
    if result_response.code != constant::CODE_HTTP_OK {
      return Err(anyhow!("{}", result_response.message));
    }

    let resp = result_response.data;
    if resp.medid > 0 {
      medinfo.p_medid = resp.medid;
      medinfo.tj_checkstatus = ExamStatus::Syncd as i32;
      medinfo.tj_uploadtime = current_timestamp();
      let ret = TjMedexaminfo::save(&medinfo, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("update medinfo error:{}", ret.as_ref().unwrap_err().to_string());
      }
    }

    if ptinfo.p_wkid != resp.wkid && resp.wkid > 0 {
      ptinfo.p_wkid = resp.wkid;
      let ret = TjPatient::save(&ptinfo, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("update medinfo error:{}", ret.as_ref().unwrap_err().to_string());
      }
    }

    Ok("".to_string())
  }

  pub async fn upload_medexam_report_file(filename: &str, server: &str, orginfo: i64) -> Result<String> {
    let url = format!("{}{}", server, constant::PolarApi::ApiSyncReport.to_string());
    let ret = HttpClient::send_upload_request(filename, "", &orginfo.to_string(), &url).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }

    let response: ResponseBody<String> = serde_json::from_str(&ret.unwrap_or_default()).unwrap_or_default();

    Ok(response.data)
  }

  pub async fn upload_medexam_photo_file(filename: &str, server: &str, orginfo: i64) -> Result<String> {
    let url = format!("{}{}", server, constant::PolarApi::ApiSyncPhoto.to_string());
    let ret = HttpClient::send_upload_request(filename, "", &orginfo.to_string(), &url).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }

    let response: ResponseBody<String> = serde_json::from_str(&ret.unwrap_or_default()).unwrap_or_default();

    Ok(response.data)
  }

  pub async fn add_polar_corp_users(dto: &CorpuserDto, server: &str, orginfo: i64, db: &DbConnection) -> Result<i64> {
    info!("add corp user dto:{:#?}", &dto);
    let ret = TjCorpinfo::query(dto.corpid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let corpret = ret.unwrap();
    if corpret.is_none() {
      error!("can't find corpinfo by:{}", &dto.corpid);
      return Err(anyhow!("Can't find corpinfo by:{}", &dto.corpid));
    }
    let mut corpinfo = corpret.unwrap();
    let mut nxcorpinfo = polarmodel::convert_from_tj_corpinfo(&corpinfo);
    nxcorpinfo.p_orgid = orginfo;

    let uploaddto = CorpuserSyncDto {
      userid: dto.userid.to_owned(),
      password: dto.password.to_owned(),
      username: dto.username.to_owned(),
      corpinfo: nxcorpinfo,
    };
    info!("upload dto:{:#?}", &uploaddto);
    let ret = serde_json::to_string(&uploaddto);
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let parm = ret.unwrap();
    let url = format!("{}{}", server, constant::PolarApi::ApiSyncCorpuser.to_string());
    let ret = HttpClient::send_post_request(&orginfo.to_string(), &url, &parm).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }

    let response: ResponseBody<i64> = serde_json::from_str(&ret.unwrap_or_default()).unwrap_or_default();
    info!("upload response:{:#?}", &response);
    if response.code != CODE_HTTP_OK {
      return Err(anyhow!("{}", response.message));
    }
    let cmpid = response.data;
    if corpinfo.p_cmpid != cmpid {
      corpinfo.p_cmpid = cmpid;
      info!("corp info to be updated:{:#?}", &corpinfo);
      let ret = TjCorpinfo::save(&corpinfo, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.err().unwrap().to_string()));
      }
    }

    Ok(cmpid)
  }

  async fn prepare_upload_medresults(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, orgid: i64, db: &DbConnection) -> Result<MedsyncResultDetail> {
    let testid = medinfo.tj_testid.to_owned();
    let ids: Vec<i64> = vec![];
    let ret = TjStaffadmin::query_many(&vec![], &ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let doctors = ret.unwrap();

    let ret = TjDepartinfo::query_many(&ids, &vec![], -1, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let deptinfos = ret.unwrap();

    let ret = SsDictionary::query_many(constant::DictType::DictCheckall as i32, 0, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let dicts = ret.unwrap();

    //查找企业信息
    let ret = TjCorpinfo::query(medinfo.tj_corpnum, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let ret = ret.unwrap();
    if ret.as_ref().is_none() {
      error!("can't find corpinfo by corpnum: {}", &medinfo.tj_corpnum);
      return Err(anyhow!("can't find corpinfo by corpnum: {}", &medinfo.tj_corpnum));
    }
    let corpinfo = ret.unwrap();
    // info!("上传时企业信息:{:#?}", &corpinfo);
    // if corpinfo.p_cmpid <= 0 {
    //   error!("根据企业id:{}查到的企业信息还没有配置", medinfo.tj_corpnum);
    //   return Err(anyhow!("该企业未配置，不能上传结果"));
    // }

    //从tj_checkiteminfo表中获取所有的检查结果信息
    let ret = TjCheckiteminfo::query_many(&vec![testid.to_string()], &vec![], -1, -1, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let checkitems = ret.unwrap();
    //从tj_testsummary表中获取所有的科室小结信息
    let ret = TjTestsummary::query(&testid, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let summaries = ret.unwrap();
    //从tj_checkallnew表中获取所有的总检结果信息
    let ret = TjCheckallnew::query(&testid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let ret = ret.unwrap();
    if ret.as_ref().is_none() {
      error!("can't find ptinfo by testid: {}", &medinfo.tj_testid);
      return Err(anyhow!("can't find ptinfo by testid: {}", &medinfo.tj_testid));
    }
    let checkalls = ret.unwrap();

    let ret = TjAudiogramdetail::query_many(&vec![testid.to_string()], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let audiogramdetails = ret.unwrap();

    let nxwkinfo = polarmodel::convert_from_tj_patientinfo(&ptinfo);
    let nxcheckalls = polarmodel::convert_from_tj_checkallnew(&checkalls, &medinfo, &dicts, &doctors, orgid, ""); //

    let nxmedinfo = polarmodel::convert_from_tj_medexaminfo(orgid, &medinfo, &ptinfo, &checkalls, &corpinfo);

    let nxsummaries = summaries
      .iter()
      .filter(|&f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid))
      .map(|x| polarmodel::convert_from_tj_testsummary(x, &medinfo, &deptinfos, orgid))
      .collect();

    let nxcheckresults = checkitems
      .iter()
      .filter(|&f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid))
      .map(|x| polarmodel::convert_from_tj_checkiteminfo(x, &medinfo, &deptinfos, orgid))
      .collect();

    let audiograms = polarmodel::convert_to_polar_audiogram_details(&audiogramdetails, &medinfo, orgid);
    // }

    // info!("medexaminfo prepared, nxwkinfo is:{:#?}", &nxwkinfo);
    // info!("medexaminfo prepared, nxmedinfo is:{:#?}", &nxmedinfo);
    // info!("medexaminfo prepared, nxcheckalls is:{:#?}", &nxcheckalls);

    let dto = MedsyncResultDetail {
      wkinfo: nxwkinfo,
      checkall: nxcheckalls,
      medinfo: nxmedinfo,
      checkresults: nxcheckresults,
      summaries: nxsummaries,
      audiograms,
    };

    Ok(dto)
  }

  // async fn sync_corpinfos(corps: &Vec<NxCorpinfo>, db: &DbConnection) -> Result<()> {
  //   //insert new corp infos
  //   let corpinfos: Vec<TjCorpinfo> = corps.iter().map(|x| polarmodel::convert_to_tj_corpinfo(x)).collect();
  //   if corpinfos.len() > 0 {
  //     let orgcodes = corpinfos.iter().map(|v| v.tj_orgcode.to_owned()).collect::<Vec<String>>();
  //     let ret = TjCorpinfo::query_many(&vec![], "", &orgcodes).await;
  //     if ret.as_ref().is_err() {
  //       error!("{}", ret.as_ref().err().unwrap().to_string());
  //       return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  //     }
  //     // //更新平台的企业状态
  //     // let corpids = corpinfos.iter().map(|x| x.p_cmpid).collect::<Vec<i64>>();
  //     // let ret = serde_json::to_string(&corpids);
  //     // if ret.as_ref().is_err() {
  //     //   error!("{}", ret.as_ref().err().unwrap().to_string());
  //     //   return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  //     // }
  //     // let parms = ret.unwrap();
  //     // info!("update corpinfo status parms:{}", &parms);
  //     // let ret = HttpClient::send_request(&parms, orgid, API_CORP_UPDATE, server).await;
  //     // if ret.as_ref().is_err() {
  //     //   error!("{}", ret.as_ref().err().unwrap().to_string());
  //     //   return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  //     // }
  //     // let resp = ret.unwrap();
  //     // info!("response from request:{}", &resp);
  //     // let ret = serde_json::from_str(resp.as_str());
  //     // if ret.as_ref().is_err() {
  //     //   error!("{}", ret.as_ref().err().unwrap().to_string());
  //     //   return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  //     // }
  //     // let result_response: ResponseBody<String> = ret.unwrap();
  //     // if result_response.code != CODE_OK {
  //     //   return Err(anyhow!("{}", result_response.message));
  //     // }
  //   }
  //   Ok(())
  // }
}
