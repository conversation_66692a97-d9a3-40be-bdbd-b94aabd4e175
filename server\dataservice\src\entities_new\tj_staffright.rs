//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_staffright")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_staffid: i32,
  pub tj_rname: String,
  pub tj_right: Option<i32>,
  pub tj_operator: Option<i32>,
  pub tj_moddate: i64,
  pub tj_menuid: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
