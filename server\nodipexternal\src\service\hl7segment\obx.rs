#[allow(unused)]
use crate::rusthl7::{Field, Hl7ParseError, Message, Separators};
use anyhow::Result;
use std::fmt::Display;

#[derive(Debug, PartialEq)]
pub struct ObxSegment<'a> {
  pub source: &'a str,
  //this initial layout largely stolen from the _other_ hl7 crate: https://github.com/njaremko/hl7
  pub msg_encoding_characters: Separators,
  pub obx_1_set_id: Option<Field<'a>>,
  pub obx_2_value_type: Field<'a>,
  pub obx_3_observation_identifier: Field<'a>,
  pub obx_4_observation_sub_id: Field<'a>,
  pub obx_5_observation_value: Option<Field<'a>>,
  pub obx_6_units: Option<Field<'a>>,
  pub obx_7_references_range: Option<Field<'a>>,
  pub obx_8_interpretation_codes: Option<Field<'a>>,
  pub obx_9_probability: Option<Field<'a>>,
  pub obx_10_nature_of_abnormal_test: Option<Field<'a>>,
  pub obx_11_observation_result_status: Field<'a>,
  pub obx_12_effective_date_of_reference_range: Option<Field<'a>>,
  pub obx_13_user_defined_access_checks: Option<Field<'a>>,
  pub obx_14_date_time_of_the_observation: Option<Field<'a>>,
  pub obx_15_producers_id: Option<Field<'a>>,
  pub obx_16_responsible_observer: Option<Field<'a>>,
  pub obx_17_observation_method: Option<Field<'a>>,
  pub obx_18_equipment_instance_identifier: Option<Field<'a>>,
  pub obx_19_date_time_of_the_analysis: Option<Field<'a>>,
  //   pub obx_20_observation_site: Option<Field<'a>>,
  //   pub obx_21_observation_instance_identifier: Option<Field<'a>>,
  //   pub obx_22_mood_code: Option<Field<'a>>,
  //   pub obx_23_performing_organization_name: Option<Field<'a>>,
  //   pub obx_24_performing_organization_address: Option<Field<'a>>,
  //   pub obx_25_performing_organization_medical_director: Option<Field<'a>>,
  //   pub obx_26_patient_results_release_category: Option<Field<'a>>,
  //   pub obx_27_root_cause: Option<Field<'a>>,
  //   pub obx_28_local_process_control: Option<Field<'a>>,
}

impl<'a> ObxSegment<'a> {
  pub fn parse<S: Into<&'a str>>(input: S, delims: &Separators) -> Result<ObxSegment<'a>, Hl7ParseError> {
    let input = input.into();

    let mut fields = input.split(delims.field);

    assert!(fields.next().unwrap() == "OBX");

    let seg = ObxSegment {
      source: input,
      msg_encoding_characters: delims.to_owned(),
      obx_1_set_id: Field::parse_optional(fields.next(), delims)?,
      obx_2_value_type: Field::parse_mandatory(fields.next(), delims)?,
      obx_3_observation_identifier: Field::parse_mandatory(fields.next(), delims)?,
      obx_4_observation_sub_id: Field::parse_mandatory(fields.next(), delims)?,
      obx_5_observation_value: Field::parse_optional(fields.next(), delims)?,
      obx_6_units: Field::parse_optional(fields.next(), delims)?,
      obx_7_references_range: Field::parse_optional(fields.next(), delims)?,
      obx_8_interpretation_codes: Field::parse_optional(fields.next(), delims)?,
      obx_9_probability: Field::parse_optional(fields.next(), delims)?,
      obx_10_nature_of_abnormal_test: Field::parse_optional(fields.next(), delims)?,
      obx_11_observation_result_status: Field::parse_mandatory(fields.next(), delims)?,
      obx_12_effective_date_of_reference_range: Field::parse_optional(fields.next(), delims)?,
      obx_13_user_defined_access_checks: Field::parse_optional(fields.next(), delims)?,
      obx_14_date_time_of_the_observation: Field::parse_optional(fields.next(), delims)?,
      obx_15_producers_id: Field::parse_optional(fields.next(), delims)?,
      obx_16_responsible_observer: Field::parse_optional(fields.next(), delims)?,
      obx_17_observation_method: Field::parse_optional(fields.next(), delims)?,
      obx_18_equipment_instance_identifier: Field::parse_optional(fields.next(), delims)?,
      obx_19_date_time_of_the_analysis: Field::parse_optional(fields.next(), delims)?,
      //   obx_20_observation_site: Field::parse_optional(fields.next(), delims)?,
      //   obx_21_observation_instance_identifier: Field::parse_optional(fields.next(), delims)?,
      //   obx_22_mood_code: Field::parse_optional(fields.next(), delims)?,
      //   obx_23_performing_organization_name: Field::parse_optional(fields.next(), delims)?,
      //   obx_24_performing_organization_address: Field::parse_optional(fields.next(), delims)?,
      //   obx_25_performing_organization_medical_director: Field::parse_optional(fields.next(), delims)?,
      //   obx_26_patient_results_release_category: Field::parse_optional(fields.next(), delims)?,
      //   obx_27_root_cause: Field::parse_optional(fields.next(), delims)?,
      //   obx_28_local_process_control: Field::parse_optional(fields.next(), delims)?,
    };

    Ok(seg)
  }
}
/// Common formatter trait implementation for the strongly-typed segment
impl<'a> Display for ObxSegment<'a> {
  /// Required for to_string() and other formatter consumers
  fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
    write!(f, "{}", self.source)
  }
}
/// Common clone trait implementation for the strongly-typed segment
impl<'a> Clone for ObxSegment<'a> {
  /// Creates a new Message object using _the same source_ slice as the original.
  fn clone(&self) -> Self {
    let delims = self.msg_encoding_characters;
    ObxSegment::parse(self.source, &delims).unwrap()
  }
}
/// Extracts header element for external use
pub fn _obr<'a>(msg: &Message<'a>) -> Result<ObxSegment<'a>, Hl7ParseError> {
  let seg = msg.segments_by_identifier("OBX").unwrap()[0];
  let segment = ObxSegment::parse(seg.source, &msg.get_separators()).expect("Failed to parse OBX segment");
  Ok(segment)
}
