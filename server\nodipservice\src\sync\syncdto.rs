use super::polarmodel::*;
use serde::{Deserialize, Serialize};

#[derive(Debug, De<PERSON>ult, Serial<PERSON>, Deserialize)]
pub struct MedexamInfo {
  pub medinfo: NxMedexaminfo,        //体检信息
  pub wkinfo: NxWorkerinfo,          //人的信息
  pub hdinfos: Vec<NxMedhazardinfo>, //毒害因素
}

#[derive(Debug, <PERSON><PERSON><PERSON>, <PERSON>ial<PERSON>, Deserialize)]
pub struct MedsyncResultDetail {
  pub wkinfo: NxWorkerinfo,
  pub medinfo: NxMedexaminfo,
  pub checkall: NxMedCheckall,
  pub checkresults: Vec<NxMedCheckresult>,
  pub summaries: Vec<NxMedTestsummary>,
  pub audiograms: Vec<NxAudiogramdetail>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct MedsyncResponse {
  pub medid: i64,
  pub wkid: i64,
}

#[derive(Debug, De<PERSON>ult, Serialize, Deserialize)]
pub struct CorpuserDto {
  pub userid: String,
  pub password: String,
  pub username: String,
  pub corpid: i64,
}
#[derive(Debug, De<PERSON>ult, Serialize, Deserialize)]
pub struct CorpuserSyncDto {
  pub userid: String,
  pub password: String,
  pub username: String,
  pub corpinfo: NxCorpinfo,
}
