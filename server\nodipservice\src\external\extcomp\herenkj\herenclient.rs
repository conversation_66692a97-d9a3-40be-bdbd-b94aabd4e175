use anyhow::{anyhow, Result};
use quick_xml::events::Event;
use quick_xml::Reader;
use reqwest::{header::CONTENT_TYPE, *};
use tracing::*;
// use yaserde::*;

pub struct HerenClient;

impl HerenClient {
  pub async fn send_request(url: &str, action: &str, dto: &str) -> Result<String> {
    if url.is_empty() {
      return Ok("".to_string());
    }
    info!(url, dto, "start to send request to server");
    // let server_uri = format!("{}{}", server, uri);
    // info!("start to build client......");
    let mut reqbuilder = Client::new()
      .request(Method::POST, url)
      // .post(url)
      .timeout(std::time::Duration::from_secs(60))
      // .header(CONTENT_TYPE, "application/soap+xml; charset=utf-8")
      .header(CONTENT_TYPE, "text/xml;charset=utf-8")
      .header("SOAPAction", action);
    // info!("client is builded ......,start to send to server:{} body:{}", url, dto);
    if !dto.is_empty() {
      reqbuilder = reqbuilder.body(dto.to_owned());
    }
    let ret = reqbuilder.send().await;
    info!("response: {:?}", &ret);
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let response = ret.unwrap();
    let status = response.status();
    let ret = response.text().await;
    info!("http status:{:?}, response text: {:?}", status, &ret);
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let response_text = ret.unwrap();
    // info!("response text is:{:?}", &response_text);
    Ok(response_text)
  }

  pub fn get_sample2_xml_wrapper(dto: &str) -> String {
    let xml_wrapper = format!(
      r#"<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org">
          <soapenv:Header/>
          <soapenv:Body>
              <tem:Sample2>
                <!--Optional:-->
                <tem:pInput>{dto}</tem:pInput>
              </tem:Sample2>
          </soapenv:Body>
        </soapenv:Envelope>
        "#,
    );
    xml_wrapper
  }

  // <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org">
  //    <soapenv:Header/>
  //    <soapenv:Body>
  //       <tem:Sample1>
  //          <!--Optional:-->
  //          <tem:method>?</tem:method>
  //          <!--Optional:-->
  //          <tem:StrJson>?</tem:StrJson>
  //       </tem:Sample1>
  //    </soapenv:Body>
  // </soapenv:Envelope>

  //sample2
  //   <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org">
  //    <soapenv:Header/>
  //    <soapenv:Body>
  //       <tem:Sample2>
  //          <!--Optional:-->
  //          <tem:pInput>?</tem:pInput>
  //       </tem:Sample2>
  //    </soapenv:Body>
  // </soapenv:Envelope>

  pub fn get_sample1_xml_wrapper(method: &str, dto: &str) -> String {
    let xml_wrapper = format!(
      r#"<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org">
          <soapenv:Header/>
          <soapenv:Body>
              <tem:Sample1>
                <!--Optional:-->
                <tem:method>{method}</tem:method>
                <!--Optional:-->
                <tem:StrJson>{dto}</tem:StrJson>
              </tem:Sample1>
          </soapenv:Body>
        </soapenv:Envelope>
        "#,
    );
    xml_wrapper
  }

  pub fn decode_response(str: &str, key: &str) -> Result<Option<String>> {
    let mut reader = Reader::from_str(str);
    reader.config_mut().trim_text(true); // Trim whitespace

    let mut buf = Vec::new();
    let mut json_string = None;

    // Parse the XML
    loop {
      match reader.read_event_into(&mut buf)? {
        Event::Start(e) => {
          // Check for Sample2Result element
          if e.name().as_ref() == key.as_bytes() {
            // Read the text content of the element
            if let Event::Text(text) = reader.read_event_into(&mut buf)? {
              json_string = Some(text.unescape()?.into_owned());
            }
          }
        }
        Event::Eof => break, // End of XML
        _ => (),             // Ignore other events
      }
      buf.clear(); // Clear buffer after each event
    }
    Ok(json_string)
  }
}
