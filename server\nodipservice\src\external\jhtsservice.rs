use crate::{
  client::httpclient::HttpClient,
  dto::{ExtTypeDTO, ExternalDTO},
};
use anyhow::{anyhow, Result};
use tracing::*;

// use crate::config::settings::Settings;

pub struct JhtsService;

impl JhtsService {
  pub async fn do_upload(dto: &ExternalDTO, server: &String) -> Result<()> {
    // let server = config.application.proxyserver.to_string();
    if server.is_empty() {
      return Ok(());
    }

    let uri = format!("{}/api/external/upload", server);

    let dto = ExtTypeDTO {
      testid: dto.testid.to_owned(),
      exttype: dto.exttype,
      newinfos: None,
      existinfos: None,
    };

    let ret: Result<String> = HttpClient::send_http_post_request("", &uri, &Some(dto)).await;
    if ret.as_ref().is_err() {
      error!("external upload error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }

    Ok(())
  }
}
