use crate::dataview::nxmodel::{NxAudiogramdetail, NxMedCheckall, NxMedCheckresult, NxMedReportdetails, NxMedTestsummary, NxMedexaminfo, NxMedreportinfo, NxWorkerinfo};
use serde::{Deserialize, Serialize};

use crate::dataview::{nxcorpinfo::NxCorpinfo, nxmedexaminfo::MedexamSyncView};

#[derive(Debug, Serialize, Deserialize)]
pub struct IdDto {
  pub id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct KeyDto {
  pub key: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct KeysDto {
  pub keys: Vec<String>,
}

#[derive(Debug, Deserialize)]
pub struct IdsDto {
  pub ids: Vec<String>,
  pub code: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MedinfoDto {
  pub dtstart: String,
  pub dtend: String,
  pub testid: Vec<String>,
  pub cpme: i32,
  pub corpid: i64,
  pub testtype: i32,
  pub statuslow: i32,
  pub statushigh: i32,
  pub pid: Vec<String>,
  pub pname: String,
  pub isrecheck: i32,
  pub rptnum: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PatientsDto {
  pub pid: Vec<String>,
  pub pname: String,
  pub idcard: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TidNumDto {
  #[serde(rename = "tid")]
  pub tid: Vec<i64>,
  pub pid: Vec<i64>,
  pub code: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TestsummaryDto {
  pub testid: Vec<String>,
  pub stdate: String,
  pub enddate: String,
  pub deptid: Vec<String>,
  pub isfinished: i32,
  pub isforceend: i32,
  pub staffno: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AudiogramDto {
  pub testid: Vec<String>,
  #[serde(rename = "ear")]
  pub transear: i32, //   int    `json:"ear"`    //耳朵 0：右耳 1：左耳
  #[serde(rename = "type")]
  pub transtype: i32, //   int    `json:"type"`   //类型 0：气导 1：骨导
  pub freq: i32, //    `json:"freq"`   //频率
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CorpinfoDto {
  pub keys: Vec<i32>,
  #[serde(rename = "type")]
  pub corptype: i32,
  pub corpid: String,
  #[serde(rename = "name")]
  pub corpname: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ReportDto {
  pub reportids: Vec<i32>, //
  pub dtstart: String,     //
  pub dtend: String,       //
  #[serde(rename = "Corp")]
  pub corp: String, //单位编号
  #[serde(rename = "type")]
  pub testtype: String, //类型
  pub pname: String,       //姓名
  pub testid: String,      //体检号
  pub reportnum: String,   //报告编号
}
#[derive(Debug, Serialize, Deserialize)]
pub struct CheckallDto {
  pub testids: Vec<String>, //  []string `json:"testids"`
  pub stdate: String,       //   string   `json:"stdate"`
  pub enddate: String,      //  string   `json:"enddate"`
  pub castatus: i32,        // int      `json:"status"`  //状态
  pub rettype: i32,         //  int      `json:"rettype"` //结果类型
}

#[derive(Debug, Serialize, Deserialize)]
//CiQueryDTO ... 体检项目查找DTO
pub struct CheckitemDto {
  pub testid: Vec<String>, //   []string `json:"testid"`
  pub deptid: Vec<String>, //   []string `json:"deptid"`
  pub barcode: String,     //  string   `json:"barcode"`  //条码
  pub flag: i32,           //     int      `json:"flag"`     //flag = 是否异常标志 - 1:全部 0：正常 1：异常 2：未检
  pub combined: i32,       // int      `json:"combined"` //combined = combined -1:全部 0：非组合项目 1：组合项目
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DiseaseinfoDto {
  pub testid: String, //
  pub deptid: String, //
  pub disid: i32,     //
  pub id: i64,        //
}
#[derive(Debug, Serialize, Deserialize)]
pub struct HazardDto {
  pub hids: Vec<i32>,      //   []int    `json:"ids"`    //毒害因素ID
  pub hnames: Vec<String>, // []string `json:"hnames"` //毒害因素名称列表
  pub htype: i32,          //  int      `json:"type"`   //毒害因素类别
}

#[derive(Debug, Serialize, Deserialize)]
pub struct IteminfoDto {
  pub lisnum: Vec<String>,  //lis编号
  pub itemids: Vec<String>, //项目编号
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SyncQueryResult {
  pub medinfos: Vec<MedexamSyncView>, //体检信息
  pub corpinfos: Vec<NxCorpinfo>,     //单位信息
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MedexamResult {
  pub pateints: Vec<NxWorkerinfo>,
  pub checkall: Vec<NxMedCheckall>,
  pub checkresults: Vec<NxMedCheckresult>,
  pub summaries: Vec<NxMedTestsummary>,
  pub audiograms: Vec<NxAudiogramdetail>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct MedexamResultDetail {
  pub wkinfo: NxWorkerinfo,
  pub medinfo: NxMedexaminfo,
  pub checkall: NxMedCheckall,
  pub checkresults: Vec<NxMedCheckresult>,
  pub summaries: Vec<NxMedTestsummary>,
  pub audiograms: Vec<NxAudiogramdetail>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ReportResult {
  pub reports: Vec<NxMedreportinfo>,
  pub reportdetails: Vec<NxMedReportdetails>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MedresultResponse {
  pub medid: i64,
  pub wkid: i64,
}
