use crate::entities::{prelude::*, v_tj_lisresult};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl VTjLisresult {
  pub async fn query_many(code: &str, db: &DatabaseConnection) -> Result<Vec<VTjLisresult>> {
    // let ret = NxJobinfoEntity::find_by_id(id).one(db).await;
    let ret = VTjLisresultEntity::find().filter(v_tj_lisresult::Column::Tjbh.eq(code)).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_many_by_xmxh(testid: &str, xmxh: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<VTjLisresult>> {
    if testid.is_empty() && xmxh.len() <= 0 {
      return Err(anyhow!("empty ids"));
    }
    let mut conditions = Condition::all().add(v_tj_lisresult::Column::Tjbh.eq(testid));
    if xmxh.len() > 0 {
      conditions = conditions.add(v_tj_lisresult::Column::Xmxh.is_in(xmxh.to_owned()));
    }
    // let ret = NxJobinfoEntity::find_by_id(id).one(db).await;
    let ret = VTjLisresultEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn insert_many(infos: &Vec<VTjLisresult>, db: &DatabaseConnection) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data to insert, not allowed"));
    }
    let mut active_values: Vec<v_tj_lisresult::ActiveModel> = Vec::new();
    for val in infos.into_iter() {
      let ret = v_tj_lisresult::ActiveModel::from_json(json!(val));
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      active_values.push(ret.unwrap());
    }
    let ret = VTjLisresultEntity::insert_many(active_values).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().last_insert_id)
  }

  pub async fn save_many(info: &Vec<VTjLisresult>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = info.len();
    let (insert_vals, update_vals): (Vec<VTjLisresult>, Vec<VTjLisresult>) = info.to_owned().into_iter().partition(|f| f.id == 0);

    if insert_vals.len() > 0 {
      let mut active_insert_values: Vec<v_tj_lisresult::ActiveModel> = Vec::new();
      for val in insert_vals {
        let mut ret = v_tj_lisresult::ActiveModel::from_json(json!(val)).unwrap_or_default();
        ret.id = NotSet;
        active_insert_values.push(ret);
      }
      let ret = VTjLisresultEntity::insert_many(active_insert_values).exec(db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      // let mut active_update_values: Vec<tj_checkiteminfo::ActiveModel> = Vec::new();
      for val in update_vals {
        let mut new_val = v_tj_lisresult::ActiveModel::from_json(json!(&val)).unwrap_or_default();
        new_val.id = Set(val.id);
        let ret = new_val.save(db).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
      }
    }
    Ok(total as i64)
  }

  pub async fn delete_many(testid: &str, db: &DatabaseConnection) -> Result<u64> {
    if testid.is_empty() {
      return Err(anyhow!("testid empty, not allowed"));
    }
    let conditions = Condition::all().add(v_tj_lisresult::Column::Tjbh.eq(testid));

    let ret = VTjLisresultEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn delete_many_by_xmxh(testids: &Vec<String>, xmxh: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    if testids.len() <= 0 || xmxh.len() <= 0 {
      return Err(anyhow!("testid empty, not allowed"));
    }
    let mut conditions = Condition::all().add(v_tj_lisresult::Column::Tjbh.is_in(testids.to_owned()));

    if xmxh.len() > 0 {
      conditions = conditions.add(v_tj_lisresult::Column::Xmxh.is_in(xmxh.to_owned()))
    }

    let ret = VTjLisresultEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn delete_many_by_ids(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    if ids.len() <= 0 {
      return Err(anyhow!("testid empty, not allowed"));
    }
    let conditions = Condition::all().add(v_tj_lisresult::Column::Id.is_in(ids.to_owned()));

    let ret = VTjLisresultEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }

  pub async fn clear(date: &str, db: &DatabaseConnection) -> Result<u64> {
    if date.is_empty() {
      return Err(anyhow!("date empty, not allowed"));
    }
    let conditions = Condition::all().add(v_tj_lisresult::Column::Bgrq.lt(date));

    let ret = VTjLisresultEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
