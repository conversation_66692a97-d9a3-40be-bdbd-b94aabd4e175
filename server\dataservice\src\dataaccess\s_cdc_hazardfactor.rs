use crate::entities::{prelude::*, s_cdc_hazardfactor};
use anyhow::{anyhow, Result};
use tracing::*;
use sea_orm::{ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, QueryTrait};

impl SCdcHazardfactor {
  pub async fn query_many(hcodes: &Vec<String>, monitor: i32, db: &DatabaseConnection) -> Result<Vec<SCdcHazardfactor>> {
    let mut conditions = Condition::all();
    if hcodes.len() > 0 {
      conditions = conditions.add(s_cdc_hazardfactor::Column::CdcCode.is_in(hcodes.to_owned()));
    }
    if monitor > 0 {
      conditions = conditions.add(s_cdc_hazardfactor::Column::CdcInitmonitor.eq(monitor));
    }
    
    let query = SCdcHazardfactorEntity::find().filter(conditions);
    info!("query string:{:?}", &query.build(sea_orm::DbBackend::MySql).to_string());
    let ret = query.all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
