//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_diseasehistory")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub tj_testid: String,
    pub tj_disname: String,
    pub tj_date: i32,
    pub tj_orgname: String,
    pub tj_curve: String,
    pub tj_isrecover: i32,
    pub tj_finalcode: String,
    pub tj_isoccu: i32,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
