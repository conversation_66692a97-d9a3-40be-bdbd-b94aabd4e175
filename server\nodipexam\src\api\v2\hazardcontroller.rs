use crate::{api::httpresponse::{response_json_value_error, response_json_value_ok}, auth::auth::Claims};
use axum::{Extension, Json};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{basic::hazardsvc::HazardSvc, dto::*};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

pub async fn query_hazardinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeysDto>) -> Json<Value> {
  let hdids = dto.keys_int.to_owned();
  let hdnames = dto.keys_str.to_owned();

  let ret = HazardSvc::query_hazardinfos_by_ids(&vec![], &hdids, &hdnames, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjHazardinfo>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_hazardinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<HazardDto>) -> Json<Value> {
  info!("save_hazardinfos dto:{dto:?}");
  let ret = HazardSvc::save_hazardinfo(&dto.hdinfo, &dto.hdlaws, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
pub async fn delete_hazardinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjHazardinfo>) -> Json<Value> {
  info!("delete hazardinfos dto:{dto:?}");
  let ret = HazardSvc::delete_hazardinfos(&vec![dto.id], &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
//关联接害因素
pub async fn connect_hazardinfos(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<HazardConnectDto>) -> Json<Value> {
  info!("connect hazardinfos:{:?}", &dto);

  if dto.refid <= 0 || dto.hdids.len() <= 0 {
    return response_json_value_ok(0, 0);
  };

  let ret = HazardSvc::connect_hazardinfos(&dto.hdids, dto.refid, dto.conitems, dto.conlaw, dto.condis, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1 as u64, 0)
}
pub async fn query_hazardtypes(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  let ret = HazardSvc::query_hazardtypes(&db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjHazardtype>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}
pub async fn save_hazardtypes(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjHazardtype>) -> Json<Value> {
  info!("save hazard types:{:?}", &dto);
  let ret = HazardSvc::save_hazardtype(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
pub async fn delete_hazardtypes(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjHazardtype>) -> Json<Value> {
  info!("dto is:{:?}", &dto);
  let ret = HazardSvc::delete_hazardtype(dto.id, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  let results = ret.unwrap();
  response_json_value_ok(1, results)
}
pub async fn query_hazarditems(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeysDto>) -> Json<Value> {
  info!("query hazard items dto:{:?}", &dto);
  let mut testtypes: Vec<i32> = Vec::new();
  if dto.keys_str.len() > 0 {
    testtypes.extend(dto.keys_str.into_iter().map(|v| v.parse::<i32>().unwrap_or_default()).collect::<Vec<i32>>());
  }
  let ret = HazardSvc::query_hazarditems(&dto.keys_int, &testtypes, &db).await;
  // info!("query hazard items result:{:?}", &ret);
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjHazarditem>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}
//
pub async fn save_hazarditems(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(infos): Json<Vec<TjHazarditem>>) -> Json<Value> {
  let ret = HazardSvc::save_hazarditems(&infos, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1 as u64, ret.unwrap())
}
pub async fn delete_hazarditems(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjHazarditem>) -> Json<Value> {
  info!("delete hazard items:{:?}", &dto);
  let ids: Vec<i64> = vec![dto.id];
  if ids.len() <= 0 {
    return response_json_value_error("empty ids, not allowed", 0);
  }
  let ret = HazardSvc::delete_hazarditems(&ids, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1, ret.unwrap())
}
pub async fn query_hazarddiseases(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<MultiKeysDto>) -> Json<Value> {
  info!("query hazard diseases dto:{:?}", &dto);
  let mut testtypes: Vec<i32> = Vec::new();
  if dto.keys_str.len() > 0 {
    testtypes.extend(dto.keys_str.into_iter().map(|v| v.parse::<i32>().unwrap_or_default()).collect::<Vec<i32>>());
  }
  let mut testtype = 0;
  if testtypes.len() > 0 {
    testtype = testtypes[0];
  }
  let ret = HazardSvc::query_hazarddiseases(&dto.keys_int, testtype, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjHazarddisease>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_hazarddiseases(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjHazarddisease>) -> Json<Value> {
  let ret = HazardSvc::save_hazarddiseases(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1 as u64, ret.unwrap())
}
pub async fn delete_hazarddiseases(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjHazarddisease>) -> Json<Value> {
  info!("dto is:{:?}", &dto);
  let ids: Vec<i64> = vec![dto.id];
  if ids.len() <= 0 {
    return response_json_value_error("empty ids", 0);
  }
  let ret = HazardSvc::delete_hazarddiseases(&ids, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1 as u64, ret.unwrap())
}
// ------------------- hazard law
pub async fn query_hazardlaws(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntDto>) -> Json<Value> {
  info!("query hazardlaws dto:{:?}", &dto);

  let ret = HazardSvc::query_hazard_law(&vec![dto.key], &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<TjHazardlaw>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}

pub async fn save_hazardlaws(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<Vec<TjHazardlaw>>) -> Json<Value> {
  let ret = HazardSvc::save_hazard_laws(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1 as u64, ret.unwrap())
}
pub async fn delete_hazardlaws(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<TjHazardlaw>) -> Json<Value> {
  info!("dto is:{:?}", &dto);
  let ids: Vec<i64> = vec![dto.id];
  if ids.len() <= 0 {
    return response_json_value_error("empty ids", 0);
  }
  let ret = HazardSvc::delete_hazard_laws(&ids, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  response_json_value_ok(1 as u64, ret.unwrap())
}
