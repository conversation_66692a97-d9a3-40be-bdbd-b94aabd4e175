use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>Eq, Serialize, Deserialize)]
pub struct TjOccucondition {
  pub id: i64,
  pub tj_testtype: i32,
  pub tj_poision: i32,
  pub tj_sex: i32,
  pub tj_itemid: String,
  pub tj_condition: String,
  pub tj_refvalue: String,
  pub tj_connector: String,
}
crud!(TjOccucondition {}, "tj_occucondition");
rbatis::impl_select!(TjOccucondition{query_many(itemid:&str) => "`where tj_itemid = #{itemid} `"});
