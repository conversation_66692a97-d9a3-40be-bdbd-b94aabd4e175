use anyhow::{anyhow, Result};
use datacontroller::{datasetup::DbConnection, entities::tj_hazardinfo::TjHazardinfo};

pub struct HazardSvc;

impl HazardSvc {
  pub async fn check_hazardinfos(hdnames: &Vec<String>, db: &DbConnection) -> Result<Vec<TjHazardinfo>> {
    let mut hazard_names: Vec<String> = Vec::new();
    hdnames.iter().for_each(|h| {
      let ret: Vec<String> = HazardSvc::split_hazards(&h);
      info!("split result:{:?}", &ret);
      hazard_names.extend(ret);
    });
    info!("split hazard names:{:?}", &hazard_names);
    let hids: Vec<i32> = Vec::new();
    let ret = TjHazardinfo::query_many_by_dto(&hids, &hazard_names, 0, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }

    //根据毒害因素名字查找的毒害因素信息
    let mut hdinfos = ret.unwrap();
    //如果不存在的毒害因素如何处理？？？
    if hdinfos.len() != hazard_names.len() {
      //两个不一致，说明有些毒害因素需要新增
      for hd in hazard_names.iter() {
        let finded = hdinfos.iter().find(|f| f.tj_hname.eq_ignore_ascii_case(&hd));
        if finded.is_none() {
          //insert new hdinfos
          let ret = HazardSvc::insert_new_hazardinfo(&hd, 1 as i64, db).await;
          if ret.as_ref().is_err() {
            error!("{}", ret.as_ref().err().unwrap().to_string());
            return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
          }
          hdinfos.push(ret.unwrap());
        }
      }
    }

    Ok(hdinfos)
  }

  pub async fn insert_new_hazardinfo(hdname: &str, hdtype: i64, db: &DbConnection) -> Result<TjHazardinfo> {
    let mut hdinfo = TjHazardinfo {
      id: 0,
      tj_tid: hdtype,
      tj_hname: hdname.to_string(),
      tj_pyjm: "".to_string(),
      tj_showorder: 0,
      tj_forbiden: "".to_string(),
      tj_memo: "".to_string(),
      tj_extcode: "".to_string(),
    };
    let ret = TjHazardinfo::insert(&hdinfo, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    hdinfo.id = ret.unwrap();
    Ok(hdinfo)
  }

  pub fn split_hazards(hdname: &str) -> Vec<String> {
    hdname.split(&[',', ' ', '，'][..]).into_iter().map(|f| f.to_string()).collect()
  }
}
