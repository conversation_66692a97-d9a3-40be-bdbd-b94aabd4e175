//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_audiogramresult")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_testid: String,
    pub tj_avgsgp: f32,
    pub tj_avgyyp: f32,
    pub tj_avgzyp: f32,
    pub tj_avgsyp: f32,
    pub tj_avgygp: f32,
    pub tj_avgzgp: f32,
    pub tj_avgyty: f32,
    pub tj_avgzty: f32,
    pub tj_avgsty: f32,
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
