#!/usr/bin/env python3
# coding=utf-8

"""
Simple Report Server Entry Point

This is a minimal entry point that avoids mypyc issues by importing
modules only when needed and using a simpler startup process.
"""

import os
import sys
import logging

def setup_environment():
    """Setup the environment and paths"""
    # Add current directory to Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # Set working directory to the script directory
    os.chdir(current_dir)

def setup_logging():
    """Setup basic logging"""
    log_dir = os.path.join('..', 'log')
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'reportserver.log')),
            logging.StreamHandler()
        ]
    )

def check_dependencies():
    """Check if required dependencies are available"""
    required_modules = [
        ('flask', 'Flask web framework'),
        ('flask_cors', 'Flask CORS extension'),
        ('reportlab', 'PDF generation library'),
        ('sqlalchemy', 'Database ORM'),
        ('docx', 'Word document library (python-docx)'),
        ('tomli', 'TOML parser'),
        ('pymysql', 'MySQL connector')
    ]
    
    missing = []
    for module, description in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing.append(f"{module} ({description})")
    
    if missing:
        print("Error: Missing required dependencies:")
        for dep in missing:
            print(f"  - {dep}")
        print("\nPlease install missing dependencies with:")
        print("  pip install -r requirements.txt")
        return False
    
    return True

def main():
    """Main entry point"""
    print("=" * 60)
    print("Report Server - Simple Startup")
    print("=" * 60)
    
    # Setup environment
    setup_environment()
    setup_logging()
    
    # Check dependencies
    if not check_dependencies():
        input("Press Enter to exit...")
        sys.exit(1)
    
    print("All dependencies found. Starting server...")
    
    try:
        # Import and configure the application
        from config import APIConfig
        
        # Load configuration
        config = APIConfig()
        if not config.validate():
            print("Configuration validation failed!")
            input("Press Enter to exit...")
            sys.exit(1)
        
        print(f"Configuration loaded:")
        print(f"  Host: {config.host}")
        print(f"  Port: {config.port}")
        print(f"  Output Directory: {config.output_directory}")
        print(f"  Debug Mode: {config.debug}")
        
        # Ensure output directory exists
        os.makedirs(config.output_directory, exist_ok=True)
        
        # Import Flask app
        from reportserver import app
        
        print(f"\nStarting Report Server on {config.host}:{config.port}")
        print("API Endpoints:")
        print(f"  Health Check: http://{config.host}:{config.port}/api/health")
        print(f"  Report Types: http://{config.host}:{config.port}/api/reports/types")
        print(f"  Generate Report: POST http://{config.host}:{config.port}/api/reports/generate")
        print("\nPress Ctrl+C to stop the server")
        print("=" * 60)
        
        # Start the Flask application
        app.run(
            host=config.host,
            port=config.port,
            debug=config.debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except ImportError as e:
        print(f"Import error: {e}")
        print("This might be due to missing dependencies or path issues.")
        input("Press Enter to exit...")
        sys.exit(1)
    except Exception as e:
        print(f"Server error: {e}")
        logging.exception("Server startup failed")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == '__main__':
    main()
