//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_groupright")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_gid: i32,
    pub tj_rname: String,
    pub tj_vright: i32,
    pub tj_operator: String,
    pub tj_moddate: i64,
    pub tj_menuid: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::tj_groupinfo::Entity",
        from = "Column::TjGid",
        to = "super::tj_groupinfo::Column::Id",
        on_update = "Restrict",
        on_delete = "Restrict"
    )]
    TjGroupinfo,
}

impl Related<super::tj_groupinfo::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjGroupinfo.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
