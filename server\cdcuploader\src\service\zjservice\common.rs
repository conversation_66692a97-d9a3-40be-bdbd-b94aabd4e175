// use crate::app;
use md5::{Digest, Md5};

// use datacontroller::entities::prelude::TjAudiogramdetail;
// use odipservice::typedefine::*;

// pub const HEALTHYEVENTID: &str = "A57";
// pub const CORPYEVENTID: &str = "E10";
// pub const FENCHEN: &str = "11";

pub fn get_body_wrapper(data: &String) -> String {
    let bodywrap: String = format!(
        r#"<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://server.zhejian.com/"><soapenv:Header/><soapenv:Body><ser:all><!--Optional:--><arg0><![CDATA[{}]]></arg0></ser:all></soapenv:Body></soapenv:Envelope>"#,
        data
    );
    bodywrap
}

//headSign=MD5（eventId＋requestTime+userId+password）；
pub fn get_header_sign(requesttime: &String, eventid: &String, userid: &String, password: &String) -> String {
    let head_sign_str = format!("{}{}{}{}", eventid, requesttime, userid, password);
    // create a Md5 hasher instance
    let mut hasher = Md5::new();
    // process input message
    hasher.update(head_sign_str.as_bytes());
    // acquire hash digest in the form of GenericArray,
    // which in this case is equivalent to [u8; 16]
    let result = hasher.finalize();

    let ret = format!("{:x}", result);
    ret.to_lowercase()
}
// pub fn get_corp_scale(scale: i32) -> String {
//     match scale {
//         1 => "10000".to_string(),
//         2 => "10001".to_string(),
//         3 => "10002".to_string(),
//         _ => "10004".to_string(),
//     }
// }

// pub fn get_operation_type(optype: app::OperationType) -> String {
//     match optype {
//         app::OperationType::ADD => "add".to_string(),
//         app::OperationType::UPDATE => "update".to_string(),
//         app::OperationType::DELETE => "delete".to_string(),
//         // _ => "add".to_string(),
//     }
// }

// pub fn get_check_result(ttype: i32) -> String {
//     match ttype {
//         0 => "1".to_string(),
//         1 => "2".to_string(),
//         2 => "4".to_string(),
//         3 => "3".to_string(), //
//         4 => "5".to_string(), //
//         5 => "1".to_string(), //
//         _ => "".to_string(),
//     }
// }

// pub fn get_check_type(sffc: i32) -> i32 {
//     match sffc {
//         0 => 11,
//         1 => 21,
//         _ => 11,
//     }
// }
// pub fn get_item_type(typestr: &str) -> i32 {
//     let itype = typestr.parse::<i32>();
//     if itype.is_err() {
//         return 3;
//     }
//     return itype.unwrap_or_default();
//     // match typestr {
//     //     "N" => 2,
//     //     _ => 1,
//     // }
// }

// // pub fn get_qualified_flag(ttype: i32) -> i32 {
// //     match ttype {
// //         0 => 1,
// //         1 => 0,
// //         _ => 0,
// //     }
// // }

// pub fn get_qualified_mark(ttype: i32, itemcode: &String, drcode: &String) -> i32 {
//     let drids: Vec<String> = drcode.split(",").map(|x| x.to_string()).collect();
//     // info!("当前项目:{},胸片项目:{:?}", &itemcode, &drids);
//     let dr_code = drids.iter().find(|&x| x.eq_ignore_ascii_case(itemcode.as_str()));
//     if dr_code.is_none() {
//         let ret = match ttype {
//             0 => 1,
//             1 => 0,
//             _ => 0,
//         };
//         return ret;
//     } else {
//         let ret = match ttype {
//             0 => 1,
//             _ => 3,
//         };
//         return ret;
//     }
// }

// pub fn get_testtype_status(ttype: i32) -> String {
//     match ttype {
//         // 2 => "".to_string(),
//         3 => "1".to_string(), //sg
//         4 => "2".to_string(), //zg
//         5 => "3".to_string(), //lg
//         6 => "5".to_string(), //yj
//         _ => "".to_string(),
//     }
// }

// //headSign=MD5（eventId＋requestTime+userId+password）；
// pub fn get_header_sign(requesttime: &String, eventid: &String, userid: &String, password: &String) -> String {
//     let head_sign_str = format!("{}{}{}{}", eventid, requesttime, userid, password);
//     // create a Md5 hasher instance
//     let mut hasher = Md5::new();
//     // process input message
//     hasher.update(head_sign_str.as_bytes());
//     // acquire hash digest in the form of GenericArray,
//     // which in this case is equivalent to [u8; 16]
//     let result = hasher.finalize();

//     let ret = format!("{:x}", result);
//     ret.to_lowercase()
// }

// pub fn get_marriage_status(marriage: i32) -> i32 {
//     match marriage {
//         1 => 1,
//         2 => 0,
//         3 => 2,
//         4 => 3,
//         _ => 4,
//     }
// }
