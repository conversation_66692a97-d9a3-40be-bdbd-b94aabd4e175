//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_labresult")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_clinicid: Option<String>,
  pub tj_testid: Option<String>,
  pub tj_patientname: Option<String>,
  pub tj_sex: Option<String>,
  pub tj_origrec: Option<String>,
  pub tj_itemid: Option<String>,
  pub tj_analyte: Option<String>,
  pub tj_shortname: Option<String>,
  pub tj_units: Option<String>,
  pub tj_final: Option<String>,
  pub tj_rn10: Option<String>,
  pub tj_ckfw_l: Option<String>,
  pub tj_ckfw_h: Option<String>,
  pub tj_ckfw: Option<String>,
  pub tj_abnormalflag: Option<i32>,
  pub tj_displowhigh: Option<String>,
  pub tj_senddate: i64,
  pub tj_ordno: Option<String>,
  pub tj_testgroup: Option<String>,
  pub tj_checkdoctor: Option<String>,
  pub tj_recheckdoctor: Option<String>,
  pub tj_importer: Option<String>,
  pub tj_importdate: i64,
  pub tj_isreceived: Option<i32>,
  pub tj_receivdate: i64,
  pub tj_checkdate: String,
  pub tj_recheckdate: String,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
