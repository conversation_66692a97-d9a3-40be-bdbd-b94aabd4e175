use crate::entities::{prelude::*, tj_checkallnew};
use anyhow::{anyhow, Result};
use sea_orm::{sea_query::Expr, ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjCheckallnew {
  pub fn new(testid: &str) -> Self {
    TjCheckallnew {
      tj_testid: testid.to_string(),
      ..Default::default()
    }
  }

  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<TjCheckallnew>> {
    if code.is_empty() {
      return Ok(None);
    }
    let ret = TjCheckallnewEntity::find().filter(tj_checkallnew::Column::TjTestid.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(testids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjCheckallnew>> {
    let ret = TjCheckallnewEntity::find().filter(tj_checkallnew::Column::TjTestid.is_in(testids.to_owned())).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn query_many_by_dto(dtstart: i64, dtend: i64, testids: &Vec<String>, typeids: &Vec<i32>, castatus: i32, db: &DatabaseConnection) -> Result<Vec<TjCheckallnew>> {
    let mut conditions = Condition::all();
    if dtstart > 0 {
      conditions = conditions.add(tj_checkallnew::Column::TjCheckdate.gt(dtstart));
    }
    if dtend > 0 {
      conditions = conditions.add(tj_checkallnew::Column::TjCheckdate.lt(dtend));
    }
    if testids.len() > 0 {
      conditions = conditions.add(tj_checkallnew::Column::TjTestid.is_in(testids.to_owned()));
    }

    if typeids.len() > 0 {
      conditions = conditions.add(tj_checkallnew::Column::TjTypeid.is_in(typeids.to_owned()));
    }

    if castatus >= 0 {
      conditions = conditions.add(tj_checkallnew::Column::TjCastatus.eq(castatus));
    }

    let query = TjCheckallnewEntity::find().filter(conditions);
    // info!("query string:{:?}", &query.build(DbBackend::MySql).to_string());
    let ret = query.all(db).await;

    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjCheckallnew, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_checkallnew::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn delete(testids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjCheckallnewEntity::update_many()
      .col_expr(tj_checkallnew::Column::TjCastatus, Expr::value(0))
      .col_expr(tj_checkallnew::Column::TjTypeid, Expr::value(0))
      .col_expr(tj_checkallnew::Column::TjOcuabnormal, Expr::value(""))
      .col_expr(tj_checkallnew::Column::TjOthabnormal, Expr::value(""))
      .col_expr(tj_checkallnew::Column::TjOcuconclusion, Expr::value(""))
      .col_expr(tj_checkallnew::Column::TjOthconclusion, Expr::value(""))
      .col_expr(tj_checkallnew::Column::TjOcusuggestion, Expr::value(""))
      .col_expr(tj_checkallnew::Column::TjOthsuggestion, Expr::value(""))
      .col_expr(tj_checkallnew::Column::TjOcuopinion, Expr::value(""))
      .col_expr(tj_checkallnew::Column::TjOcuopinion, Expr::value(""))
      .filter(Condition::all().add(tj_checkallnew::Column::TjTestid.is_in(testids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
}
