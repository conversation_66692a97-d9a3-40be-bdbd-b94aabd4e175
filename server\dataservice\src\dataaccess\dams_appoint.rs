use crate::entities::{dams_appoint, prelude::*};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, QueryOrder, Set};
use serde_json::json;

impl DamsAppoint {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<DamsAppoint>> {
    let ret = DamsAppointEntity::find().filter(dams_appoint::Column::OrderNo.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_by_company(company: &str, db: &DatabaseConnection) -> Result<Option<DamsAppoint>> {
    if company.is_empty() {
      return Err(anyhow!("Company name is empty, not allowed"));
    }
    let ret = DamsAppointEntity::find()
      .filter(dams_appoint::Column::Company.eq(company))
      .order_by_desc(dams_appoint::Column::Id)
      .one(db)
      .await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn save(info: &DamsAppoint, db: &DatabaseConnection) -> Result<i64> {
    let ret = DamsAppointEntity::delete_many().filter(dams_appoint::Column::OrderNo.eq(&info.order_no)).exec(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let ret = dams_appoint::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }
}
