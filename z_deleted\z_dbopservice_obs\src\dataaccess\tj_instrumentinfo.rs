use anyhow::{anyhow, Result};
use rbatis::{crud, sql};
use serde::{Deserialize, Serialize};
// use rbatis::rbatis_codegen::IntoSql;

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjInstrumentinfo {
  pub id: i64,
  pub tj_struid: String,
  pub tj_struname: String,
  pub tj_typename: String,
  pub tj_deptid: String,
  pub tj_memo: String,
  pub tj_com: String,
  pub tj_lowvalue: f32,
  pub tj_upvalue: f32,
  pub tj_vlimited: i32,
  pub tj_status: i32,
}
crud!(TjInstrumentinfo {}, "tj_instrumentinfo");
rbatis::impl_select!(TjInstrumentinfo{query(code:&str) ->Option => "`where tj_struid = #{code} limit 1`"});

impl TjInstrumentinfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjInstrumentinfo) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjInstrumentinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjInstrumentinfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
  // #[sql("update tj_instrumentinfo set tj_status = -1 where id = ? ")]
  #[sql("delete from tj_instrumentinfo where id = ? ")]
  pub async fn delete(rb: &mut rbatis::RBatis, id: &i64) -> rbatis::Result<()> {
    impled!()
  }
}
