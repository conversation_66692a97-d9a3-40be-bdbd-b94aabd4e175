[package]
name = "nodipsync"
version = "0.4.0"
edition = "2021"
description = "用于同saas同步的服务"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = {path = "../utility"}
datacontroller = {path = "../datacontroller"}
odipservice = {path = "../odipservice"}
nodipservice = {path="../nodipservice"}
dataservice = {path = "../dataservice"}

log = "0.4"

mime_guess  = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1", features = ["full"] }

tokio-util={version = "0.7",features = ["full"]}

chrono = "0.4"

axum = {version="0.6", features = ["headers","multipart"]}
axum-extra = "0.7.0"
tower = { version = "0.4", features = ["util", "filter"] }
hyper = { version = "0.14", features = ["full"] }
tower-http = { version = "0.4", features = ["trace","fs"] }
headers = "0.3"

anyhow = "1.0"
config = "0.13"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
jsonwebtoken = "8"

rust_decimal = "1"

thiserror = "1.0"

async-trait = "0.1"
# Investigate if wither::bson can be used instead and activate this feature.
# bson = { version = "2.4.0", features = ["serde_with", "chrono-0_4"] }
once_cell = "1.15"
bcrypt = "0.14"
# validator = { version = "0.16", features = ["derive"] }
# lazy_static = "1.4"
mime = "0.3"
# bytes = "1.2"
