//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_diseaseinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_testid: String,
  pub tj_disid: i32,
  pub tj_diseasenum: String,
  pub tj_diseasename: String,
  pub tj_suggestion: Option<String>,
  pub tj_deptid: Option<String>,
  pub tj_isdisease: Option<i32>,
  pub tj_isoccu: i32,
  pub tj_typeid: Option<i32>,
  pub tj_opinion: Option<String>,
  pub tj_showorder: Option<i32>,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
