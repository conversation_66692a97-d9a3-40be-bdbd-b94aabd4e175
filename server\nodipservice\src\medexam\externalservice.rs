use anyhow::{anyhow, Result};
use tracing::*;
use itertools::Itertools;
use std::collections::HashSet;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use utility::timeutil::current_timestamp;

use crate::dto::ExternalLisDto;

pub struct ExternalService;

impl ExternalService {
  pub async fn upload_external_lis_results(dto: &Vec<ExternalLisDto>, db: &DbConnection) -> Result<()> {
    if dto.len() <= 0 {
      return Err(anyhow!("data is empty, not allowed"));
    }
    let mut lisresults: Vec<VTjLisresult> = Vec::new();
    for val in dto.into_iter() {
      // let labret = ExternalService::convert_lisresult_to_labresult(&val);
      let labret = VTjLisresult {
        id: 0,
        tjbh: val.testid.to_string(),
        brxm: val.testername.to_string(),
        xmxh: val.itemid.to_string(),
        xmmc: val.itemname.to_string(),
        xmdw: val.itemunit.to_string(),
        xmjg: val.result.to_string(),
        sfyc: val.abnormal,
        gdbj: val.abnormalshow.to_string(),
        ckdz: val.range_l.to_string(),
        ckgz: val.range_h.to_string(),
        ckfw: val.refrange.to_string(),
        jyys: val.checkdoctor.to_string(),
        bgrq: val.checkdate.to_string(),
        bgys: val.recheckdoctor.to_string(),
      };
      lisresults.push(labret);
    }

    let testids: Vec<String> = lisresults.iter().map(|v| v.tjbh.to_string()).collect::<HashSet<_>>().into_iter().collect();
    let xmxh: Vec<String> = lisresults.iter().map(|v| v.xmxh.to_string()).collect();
    info!("start to clear local data by testid:{testids:?} and xmxh:{xmxh:?}");
    let ret = VTjLisresult::delete_many_by_xmxh(&testids, &xmxh, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("delete lis result error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("delete lisresults error:{}", ret.unwrap_err().to_string()));
    }

    // let labrets: Vec<Vec<TjLabresult>> = labresults.into_iter().chunks(1000).into_iter().map(|c| c.collect()).collect();
    // for val in labrets.into_iter() {
    //   let ret = TjLabresult::save_many(&val, &db.get_connection()).await;
    //   if ret.as_ref().is_err() {
    //     error!("{}", ret.as_ref().unwrap_err().to_string());
    //     return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    //   }
    // }
    let lisrets: Vec<Vec<VTjLisresult>> = lisresults.into_iter().chunks(100).into_iter().map(|c| c.collect()).collect();
    for val in lisrets.into_iter() {
      let ret = VTjLisresult::save_many(&val, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("save lis result error:{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("save lisresults error:{}", &ret.unwrap_err().to_string()));
      }
    }
    // let ret = VTjLisresult::save_many(&lisresults, &db.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   error!("save lis result error:{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("save lisresults error:{}", &ret.unwrap_err().to_string()));
    // }
    Ok(())
  }

  pub async fn upload_external_lis_to_labresults(dto: &Vec<ExternalLisDto>, db: &DbConnection) -> Result<()> {
    if dto.len() <= 0 {
      return Err(anyhow!("data is empty, not allowed"));
    }
    let mut labresults: Vec<TjLabresult> = Vec::new();
    for val in dto.into_iter() {
      // let labret = ExternalService::convert_lisresult_to_labresult(&val);
      let labret = TjLabresult {
        id: 0,
        tj_clinicid: val.testid.to_string(),
        tj_testid: val.testid.to_string(),
        tj_patientname: val.testername.to_string(),
        tj_sex: "".to_string(),
        tj_origrec: val.itemname.to_string(),
        tj_itemid: val.itemid.to_string(),
        tj_analyte: val.itemid.to_string(),
        tj_shortname: val.itemname.to_string(),
        tj_units: val.itemunit.to_string(),
        tj_final: val.result.to_string(),
        tj_rn10: val.result.to_string(),
        tj_ckfw_l: val.range_l.to_string(),
        tj_ckfw_h: val.range_h.to_string(),
        tj_ckfw: val.refrange.to_string(),
        tj_abnormalflag: val.abnormal,
        tj_displowhigh: val.abnormalshow.to_string(),
        tj_checkdoctor: val.checkdoctor.to_string(),
        tj_recheckdoctor: val.recheckdoctor.to_string(),
        tj_importdate: current_timestamp(),
        tj_checkdate: val.checkdate.to_string(),
        tj_recheckdate: val.recheckdate.to_string(),
        ..Default::default()
      };
      labresults.push(labret);
    }
    // let testid = labresults[0].tj_testid.to_string();
    let testids: Vec<String> = labresults.iter().map(|v| v.tj_testid.to_string()).collect::<HashSet<_>>().into_iter().collect();
    info!("start to clear local data by testid:{testids:?}");
    let itemids: Vec<String> = labresults.iter().map(|v| v.tj_itemid.to_string()).collect();
    let ret = TjLabresult::delete_by_itemids(&testids, &itemids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("delete lab result error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("delete labresults error:{}", ret.unwrap_err().to_string()));
    }
    info!("start to save lab results......:{labresults:?}");
    let ret = TjLabresult::save_many(&labresults, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("save lab result error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("save labresults error:{}", &ret.unwrap_err().to_string()));
    }
    Ok(())
  }
}
