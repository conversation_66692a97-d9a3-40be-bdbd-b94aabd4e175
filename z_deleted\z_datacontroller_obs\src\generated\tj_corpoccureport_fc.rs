//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_corpoccureport_fc")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub tj_old_testid: String,
    pub tj_old_rptnum: String,
    pub tj_new_testid: String,
    pub cdatetime: i64,
    pub tj_new_rptnum: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
