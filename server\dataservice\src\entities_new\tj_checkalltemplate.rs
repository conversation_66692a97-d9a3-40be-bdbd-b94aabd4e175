//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_checkalltemplate")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_metype: i32,
  pub tj_ocuresult: Option<String>,
  pub tj_oscsuggestion: Option<String>,
  pub tj_othresult: Option<String>,
  pub tj_othsuggestion: Option<String>,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
