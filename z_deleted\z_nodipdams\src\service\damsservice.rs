use anyhow::{anyhow, Result};
use base64::{prelude::BASE64_STANDARD, Engine};
use dataservice::{
  dbinit::DbConnection,
  entities::{dams_appoint, prelude::*},
};
use nodipservice::{
  basic::{hazardsvc::HazardSvc, itemsvc::ItemSvc, packagesvc::PackageSvc},
  dto::{CheckitemsQueryDto, ExternalDTO, MedQueryDto, MedexamResultDetail},
  medexam::{checkitemsvc::CheckitemSvc, medexamsvc::MedexamSvc, patientsvc::PatientSvc},
};
use serde_json::json;
use std::{env, path::PathBuf};
use tokio::process::Command;

use crate::{
  common::{self, damsdto::*},
  config::settings::Settings,
};

pub struct DamsService;

//1.常规体检2.职业体检

impl DamsService {
  pub async fn save_appoint_info(dto: &AppointMedinfo, setting: &Settings, _uid: &mut utility::uidservice::UidgenService, db: &DbConnection) -> Result<()> {
    if dto.physical_examination_package == common::constant::ExamPackage::Normal as i32 {
      error!("普通体检，不处理");
      return Err(anyhow!("普通体检，不处理"));
    }
    let corpname = dto.company.to_string();
    info!("Start to query corpinfo......");
    let corpinfo: TjCorpinfo;
    let ret = nodipservice::SYSCACHE.get().unwrap().get_corpinfo_by_name(&corpname, &db).await;
    if ret.is_none() {
      let ret = TjCorpinfo::query_many(&vec![], &corpname, &vec![], &db.get_connection()).await;
      if ret.is_err() {
        error!("Error:{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }

      let corpinfos = ret.unwrap();
      if corpinfos.len() <= 0 {
        error!("该企业信息不存在:{}", &corpname);
        return Err(anyhow!("该企业信息不存在:{}", &corpname));
      }
      corpinfo = corpinfos[0].to_owned();
    } else {
      corpinfo = ret.unwrap();
    }
    let info = DamsAppoint {
      id: 0,
      sex: dto.sex,
      age: dto.age,
      marry: dto.marry,
      job_type: dto.job_type,
      physical_examination_package: dto.physical_examination_package,
      physical_examination_type: dto.physical_examination_type,
      create_date: utility::timeutil::current_timestamp(),
      order_no: dto.order_no.to_owned(),
      package_number: dto.package_number.to_owned(),
      name: dto.name.to_owned(),
      id_card: dto.id_card.to_owned(),
      phone: dto.phone.to_owned(),
      company: dto.company.to_owned(),
      comnanrld: dto.comnanrld.to_owned(),
      hospital_name: dto.hospital_name.to_owned(),
      check_time: dto.check_time.to_owned(),
      working_years: dto.working_years.to_owned(),
      contact_damage: dto.contact_damage.to_owned(),
      hazardous_factors: dto.hazardous_factors.to_owned(),
      reservestr1: dto.reservestr1.to_owned(),
      reservestr2: dto.reservestr2.to_owned(),
      reservestr3: dto.reservestr3.to_owned(),
    };
    info!("Start to save appointinfo......");
    //save to database
    let ret = DamsAppoint::save(&info, &db.get_connection()).await;
    if ret.is_err() {
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let idcard = utility::cidcard::IdCard::new(&info.id_card);
    if !idcard.is_valid() {
      error!("Error:无效的身份证号码");
      return Err(anyhow!("无效的身份证号码:{}", info.id_card));
    }
    let packageid = setting.medpackages.get(&dto.job_type);
    if packageid.is_none() {
      error!("can't find package by jobtype:{}", dto.job_type);
      return Err(anyhow!("can't find package by jobtype:{}", dto.job_type));
    }
    let packageid = packageid.unwrap().packageid;
    info!("start to query packageinfo by package id: {} ......", packageid);
    let ret = PackageSvc::query_packageinfo(packageid as i64, &db).await;
    if ret.as_ref().is_err() {
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let packageinfo = ret.unwrap();
    if packageinfo.is_none() {
      error!("can't find package by packageid:{}", packageid);
      return Err(anyhow!("can't find package by packageid:{}", packageid));
    }
    let packageinfo = packageinfo.unwrap();
    let patient = TjPatient::default();
    //保存个人信息
    let ptinfo = TjPatient {
      id: patient.id,
      tj_pid: patient.tj_pid.to_string(),
      tj_pname: info.name.to_owned(),
      tj_psex: info.sex + 1, //0男，1女
      tj_pmarriage: DamsService::get_marriage(info.marry),
      tj_ptestnum: patient.tj_ptestnum + 1,
      tj_paddress: patient.tj_paddress,
      tj_pphone: info.phone.to_owned(),
      tj_pemail: patient.tj_pemail,
      tj_pbirthday: idcard.get_birthdate(),
      tj_cardtype: 1, //身份证
      tj_pidcard: info.id_card.to_owned(),
      tj_pcareer: patient.tj_pcareer,
      tj_pmobile: info.phone.to_owned(),
      tj_photo: patient.tj_photo,
      tj_cryptflag: 0,
      tj_popdate: utility::timeutil::current_timestamp(),
      tj_staffid: setting.application.staffid,
      tj_pmemo: patient.tj_pmemo,
      tj_syncflag: 0,
      tj_nation: patient.tj_nation,
      tj_sign: patient.tj_sign,
      p_wkid: 0,
      tj_patid: patient.tj_patid,
    };
    info!("Patient info:{:?}", &ptinfo);

    let ret = PatientSvc::save_patient(&ptinfo, &db).await;
    if ret.as_ref().is_err() {
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let patient = ret.unwrap();
    info!("patient after saved: {:?}", &patient);

    let worktype = DamsService::get_worktype(info.job_type);
    let wtcode = DamsService::get_wtcode(&worktype, &corpinfo).await;
    //预约体检信息
    let medinfo = TjMedexaminfo {
      id: 0,
      tj_testid: String::new(),
      tj_pid: patient.tj_pid.to_string(),
      tj_age: idcard.get_age(),
      tj_testcat: 2,
      tj_testsource: 0,
      tj_testtype: DamsService::get_testtype(info.physical_examination_type),
      tj_corpnum: corpinfo.id,
      tj_empcorp: corpinfo.id,
      tj_empid: String::new(),
      tj_workage: info.working_years.to_string(),
      tj_wtcode: wtcode, //99-9999
      tj_worktype: worktype,
      tj_workshop: String::new(),
      tj_monitortype: "01".to_string(),
      tj_poisionfactor: info.hazardous_factors.to_string(),
      tj_poisionage: info.contact_damage.to_string(),
      tj_radiationtype: String::new(),
      tj_recorddate: utility::timeutil::current_timestamp(),
      tj_recorder: setting.application.staffid.to_string(),
      tj_testdate: utility::timeutil::convert_date_to_timestamp(&info.check_time, "%Y-%m-%d"),
      tj_expdate: utility::timeutil::timestamp_add_days(utility::timeutil::current_timestamp(), 15),
      tj_subdate: utility::timeutil::current_timestamp(),
      tj_completed: 0,
      tj_total: 0,
      tj_checkstatus: nodipservice::common::constant::ExamStatus::Appoint as i32,
      tj_paymethod: 2,
      tj_printflag: 0,
      tj_printtimes: 0,
      tj_rptnum: String::new(),
      tj_peid: 0,
      tj_isrecheck: 0,
      tj_oldtestid: String::new(),
      tj_rechecktimes: 0,
      tj_push: 0,
      tj_num: 0,
      tj_pushstatus: 0,
      tj_upload: 0,
      tj_uploadtime: 0,
      tj_syncstatus: 0,
      tj_packageid: packageid.try_into().unwrap_or_default(),
      tj_packagename: packageinfo.tj_pname.to_string(),
      tj_additional: setting.application.msorder.to_string(),
      p_medid: 0,
      tj_protective: String::new(),
      tj_ordno: dto.order_no.to_owned(), //订单号
    };

    let ret = utility::string::split_by_regex(&dto.hazardous_factors);
    if ret.is_err() {
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let hdnames = ret.unwrap();
    let ret = HazardSvc::query_hazardinfos(&hdnames, &db).await;
    if ret.is_err() {
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let hdinfos = ret.unwrap();
    info!("start to quick register...........");
    //quick register
    let ret = MedexamSvc::medexam_quick_register(&ptinfo, &medinfo, &packageinfo, &hdinfos, &"".to_string(), &db).await;
    if ret.as_ref().is_err() {
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    info!("Start to upload to dian...........");
    //同步到dian
    let regresponse = ret.unwrap();
    let dto = ExternalDTO {
      testid: regresponse.medinfo.tj_testid.to_string(),
      exttype: nodipservice::common::constant::ExtOpType::ADD as i32,
      additional: setting.application.msorder.to_string(),
    };
    // // let mut idgen = uid.write().await;

    let ret = nodipservice::external::dianservice::DianService::do_upload(&dto, &setting.application.proxyserver, &setting.application.agdburi, &db).await;
    if ret.as_ref().is_err() {
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    Ok(())
  }

  pub async fn auto_upload_dams(setting: &Settings, uid: &mut utility::uidservice::UidgenService, db: &DbConnection) -> Result<String> {
    let enddate = utility::timeutil::current_timestamp();
    let stdate = utility::timeutil::timestamp_add_days(enddate, setting.application.stdays.into());
    let dto = MedQueryDto {
      dtstart: stdate,
      dtend: enddate,
      status: vec![5, 6, 7, 8, 9],
      ..Default::default()
    };
    let ret = MedexamSvc::query_medexaminfos_by_dto(&dto, db).await;
    if ret.is_err() {
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let results = ret.unwrap();
    for medresults in results.into_iter() {
      if medresults.checkall.tj_castatus == nodipservice::common::constant::YesOrNo::No as i32 {
        error!("该体检号未总检，不能同步......:{}", &medresults.medinfo.tj_testid);
        continue;
      }
      if medresults.medinfo.tj_checkstatus < nodipservice::common::constant::ExamStatus::Allchecked as i32 {
        error!("该体检号未总检，不能同步......:{}", &medresults.medinfo.tj_testid);
        continue;
      }
      if medresults.medinfo.tj_rptnum.is_empty() {
        error!("该体检号未出报告，不能同步......:{}", &medresults.medinfo.tj_testid);
        continue;
      }
      if medresults.medinfo.tj_ordno.is_empty() {
        error!("ordno is empty, no need to upload:{}", &medresults.medinfo.tj_testid);
        continue;
      }
      if medresults.medinfo.tj_syncstatus == nodipservice::common::constant::YesOrNo::Yes as i32 {
        error!("该体检号已经同步，不需要再次同步:{}", &medresults.medinfo.tj_testid);
        continue;
      }
      info!("start to upload exam info......{}", &medresults.medinfo.tj_testid);
      let ret = DamsService::do_upload_medexam_to_external(&medresults, setting, uid, db).await;
      if ret.as_ref().is_err() {
        error!("Error:{}", ret.as_ref().unwrap_err().to_string());
        // return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
        continue;
      }
      info!("start to upload exma report info......{}", &medresults.medinfo.tj_testid);
      let ret = DamsService::do_upload_medreport_to_external(&medresults, setting, uid, db).await;
      if ret.as_ref().is_err() {
        error!("Error:{}", ret.as_ref().unwrap_err().to_string());
        continue;
        // return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let mut medinfo = medresults.medinfo.to_owned();
      medinfo.tj_syncstatus = 1;
      // medinfo.tj_uploadtime = utility::timeutil::current_timestamp();
      let ret = TjMedexaminfo::save(&medinfo, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("error:{}", ret.as_ref().unwrap_err().to_string());
        continue;
        // return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
      }

      let hisinfo = DamsUploadhis {
        testid: medinfo.tj_testid.to_string(),
        ordno: medinfo.tj_ordno.to_string(),
        result: "上传成功".to_string(),
        id: 0,
      };
      let _ = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
    }
    Ok("".to_string())
  }

  pub async fn upload_medexam_result(testid: &String, setting: &Settings, uid: &mut utility::uidservice::UidgenService, db: &DbConnection) -> Result<String> {
    if testid.is_empty() {
      // hisinfo.result =
      return Err(anyhow!("没有需要传送结果的体检号......"));
    }

    let ret = MedexamSvc::query_by_testid(&testid, db).await;
    if ret.as_ref().is_err() {
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let medresults = ret.unwrap();
    if medresults.checkall.tj_castatus == nodipservice::common::constant::YesOrNo::No as i32 {
      return Err(anyhow!("该体检号未总检，不能同步......"));
    }
    if medresults.medinfo.tj_rptnum.is_empty() {
      return Err(anyhow!("该体检号未出报告，不能同步......"));
    }
    if medresults.medinfo.tj_ordno.is_empty() {
      error!("ordno is empty, no need to upload");
      return Err(anyhow!("ordno is empty, no need to upload"));
    }
    info!("start to upload exam info......{}", &medresults.medinfo.tj_testid);
    let ret = DamsService::do_upload_medexam_to_external(&medresults, setting, uid, db).await;
    if ret.as_ref().is_err() {
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    info!("start to upload exma report info......{}", &medresults.medinfo.tj_testid);
    let ret = DamsService::do_upload_medreport_to_external(&medresults, setting, uid, db).await;
    if ret.as_ref().is_err() {
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut medinfo = medresults.medinfo.to_owned();
    medinfo.tj_syncstatus = 1;
    // medinfo.tj_uploadtime = utility::timeutil::current_timestamp();
    let ret = TjMedexaminfo::save(&medinfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let hisinfo = DamsUploadhis {
      testid: medinfo.tj_testid.to_string(),
      ordno: medinfo.tj_ordno.to_string(),
      result: "上传成功".to_string(),
      id: 0,
    };
    let _ = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
    Ok("".to_string())
  }

  pub async fn upload_corp_report(rptid: &String, setting: &Settings, uid: &mut utility::uidservice::UidgenService, db: &DbConnection) -> Result<String> {
    let mut hisinfo = DamsUploadhis {
      testid: rptid.to_string(),
      ordno: "".to_string(),
      result: "".to_string(),
      id: 0,
    };
    if rptid.is_empty() {
      return Err(anyhow!("Testid is empty, not allowed"));
    }
    let rptid = rptid.parse::<i64>().unwrap_or_default();
    if setting.application.corpreportcmd.is_empty() {
      hisinfo.result = format!("{}", "corp report command doesn't configured");
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("report is not configured......");
      return Err(anyhow!("report command is not configed"));
    }
    let reportdto = nodipservice::dto::CorpQueryDto {
      reportids: vec![rptid],
      dtstart: -1,
      dtend: -1,
      corpid: -1,
      testtype: -1,
      reportnum: "".to_string(),
      pname: "".to_string(),
      testid: "".to_string(),
      reporttype: -1,
      orptid: -1,
    };
    let ret = nodipservice::medexam::reportsvc::ReportSvc::query_reports(&reportdto, &db).await;
    // let ret = MedexamSvc::query_by_testid(&testid, &db).await;
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = ret.unwrap();
    if ret.len() <= 0 {
      return Err(anyhow!("报告编号{}的报告不存在", &rptid));
    }
    let mut rptinfo = ret[0].to_owned();
    let commander = setting.application.corpreportcmd.to_owned();
    info!("commder is:{commander}");

    // let preport = format!("{} {} 2 0 1 ./reports", setting.application.reportcommand, &testid);
    let current_dir = env::current_dir();
    if current_dir.is_err() {
      return Err(anyhow::anyhow!("{}", current_dir.as_ref().unwrap_err().to_string()));
    }
    let current_dir = current_dir.unwrap();
    info!("Current directory is:{}", &current_dir.display());

    let exec_command = current_dir /*.join("./") */
      .join(commander);
    // let exec_command = PathBuf::from(commander);
    info!("exec command is:{}", &exec_command.display());
    if !exec_command.exists() {
      hisinfo.result = format!("{}", "comman doexn't exist");
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      return Err(anyhow::anyhow!("没有用于生成报告的命令"));
    }
    let rptfile = format!("{}-{}-{}.pdf", rptinfo.tj_corpname, rptinfo.tj_reporttype, rptinfo.tj_reportnumint);
    let rptdir = "./reports";
    // let rptfile = format!("{testid}-1.pdf");
    let mut command = Command::new(exec_command);
    command.arg(&rptid.to_string());
    command.arg(&"2".to_string());
    command.arg(&"0".to_string());
    command.arg(&"1".to_string());
    command.arg(rptdir);
    let output_file = current_dir.join(rptdir).join(&rptfile);
    if output_file.exists() {
      if let Err(e) = std::fs::remove_file(&output_file) {
        error!("remove file error:{:?}", e);
      }
    }
    info!("output file is:{:?}", &output_file);
    info!("command is:{:?}", &command);
    let ret = command.output().await;
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("exec command error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }
    // info!("Output is:{:#?}", &ret.unwrap());
    //等待文件生成
    let mut idx = 1;
    loop {
      if output_file.exists() {
        break;
      } else {
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
        idx += 1;
        if idx == 100 {
          hisinfo.result = format!("{}", "报告生成失败");
          // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
          let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
          if saveret.is_err() {
            error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
            return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
          }
          error!("exec command error: 生成报告失败......");
          return Err(anyhow::anyhow!("exec command error: 生成报告失败......"));
        }
        continue;
      }
    }

    let ret = tokio::fs::read(&output_file).await; //.expect("can't read file");
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", "read file error");
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("read file error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let pdf_content = ret.unwrap();
    let pdf_b64 = BASE64_STANDARD.encode(&pdf_content);

    // info!("Pdf file in base64:{:?}", &pdf_b64);
    let ret = DamsAppoint::query_by_company(&rptinfo.tj_corpname, &db.get_connection()).await;
    if ret.is_err() {
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let corpret = ret.unwrap();
    if corpret.is_none() {
      error!("企业信息不存在：{}", &rptinfo.tj_corpname);
      return Err(anyhow!("企业信息不存在：{}", &rptinfo.tj_corpname));
    }
    let appointinfo = corpret.unwrap_or_default();

    let mdreport = MedexamReport {
      filetype: "2".to_string(),
      comnanrld: appointinfo.comnanrld.to_string(), // 企业编号信息
      id_card: "".to_string(),
      request_id: uid.get_uid().to_string(),
      check_time: utility::timeutil::format_timestamp_date_only(rptinfo.tj_createdate),
      order_no: "".to_string(),
      file: pdf_b64.to_owned(),
    };
    let ret = serde_json::ser::to_string(&mdreport);
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let content = ret.unwrap();

    let uri = "/dianBusiness/pushFile";
    let client = crate::common::httpclient::HttpClient::new(&setting);
    let ret = client.upload(&content, &uri.to_string()).await;
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }
    rptinfo.tj_syncflag = 1;
    rptinfo.tj_pyjm = utility::timeutil::current_timestamp().to_string();
    let ret = TjCorpoccureport::save(&rptinfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }
    Ok("".to_string())
  }

  // pub async fn upload_medexam_report(testid: &String, setting: &Settings, uid: &mut utility::uidservice::UidgenService, db: &DbConnection) -> Result<String> {
  //   if testid.is_empty() {
  //     return Err(anyhow!("Testid is empty, not allowed"));
  //   }
  //   if setting.application.reportcommand.is_empty() {
  //     error!("report is not configured......");
  //     return Err(anyhow!("report command is not configed"));
  //   }
  //   let ret = MedexamSvc::query_by_testid(&testid, &db).await;
  //   if ret.as_ref().is_err() {
  //     error!("Error:{}", ret.as_ref().unwrap_err().to_string());
  //     return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   let medexamdetail = ret.unwrap();
  //   let ret = DamsService::do_upload_medreport_to_external(&medexamdetail, setting, uid, db).await;
  //   if ret.as_ref().is_err() {
  //     error!("Error:{}", ret.as_ref().unwrap_err().to_string());
  //     return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
  //   }
  //   Ok("".to_string())
  //   // Ok(())
  // }

  async fn do_upload_medexam_to_external(medresults: &MedexamResultDetail, setting: &Settings, uid: &mut utility::uidservice::UidgenService, db: &DbConnection) -> Result<String> {
    // info!("exam info is ok, start to check......");
    let mut hisinfo = DamsUploadhis {
      testid: medresults.medinfo.tj_testid.to_string(),
      ordno: "".to_string(),
      result: "".to_string(),
      id: 0,
    };

    info!("start to query appointinfo......");
    let ret = DamsAppoint::query(&medresults.medinfo.tj_ordno, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = ret.unwrap();
    if ret.is_none() {
      hisinfo.result = format!("ordno:{} in appoint table doesn't exist", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("order no doesnot exist:{}", &medresults.medinfo.tj_ordno);
      return Err(anyhow!("order no in appoint table doesnot exist:{}", &medresults.medinfo.tj_ordno));
    }
    let appointinfo = ret.unwrap();
    // info!("Medexam results are:{:#?}", &medresults);
    // let checktime = utility::timeutil::format_timestamp_date_only(medresults.medinfo.tj_testdate);
    // info!("checkitem is:{}", checktime);
    let ptinfo = HospitalOrder {
      hospital_name: appointinfo.hospital_name.to_string(),
      name: medresults.patient.tj_pname.to_string(),
      id_card: medresults.patient.tj_pidcard.to_string(),
      sex: medresults.patient.tj_psex - 1,
      age: medresults.medinfo.tj_age,
      phone: medresults.patient.tj_pphone.to_string(),
      company: appointinfo.company.to_string(),
      check_time: utility::timeutil::format_timestamp_date_only(medresults.medinfo.tj_testdate),
      job_type: appointinfo.job_type.to_string(),
      medical_history: "".to_string(),
      reservestr1: String::new(),
      reservestr2: String::new(),
      reservestr3: String::new(),
    };

    let itemdto = CheckitemsQueryDto {
      testids: vec![medresults.medinfo.tj_testid.to_string()],
      deptids: vec![],
      flag: -1,
      combined: -1,
    };
    let ret = CheckitemSvc::query_checkitems(&itemdto, &db).await;
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
      hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("Error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let items = ret.unwrap();
    let combininfos = items.iter().filter(|&f| f.tj_combineflag == 1).map(|v| v.to_owned()).collect::<Vec<TjCheckiteminfo>>();

    let mut itemsresults: Vec<PackItemLists> = vec![];
    for combine in combininfos {
      let citems: Vec<TjCheckiteminfo> = items.iter().filter(|&f| f.tj_combineflag == 0 && f.tj_synid == combine.tj_itemid).map(|v| v.to_owned()).collect();
      let sum = medresults.summaries.iter().find(|&f| f.tj_deptid == combine.tj_deptid);
      if sum.is_none() {
        continue;
      }
      // let suminfo = sum.unwrap();
      let mut packitems: Vec<PackItemIndexList> = Vec::new();
      for ci in citems.into_iter() {
        if ci.tj_combineflag == 1 {
          continue;
        }
        if ci.tj_synid != combine.tj_itemid {
          continue;
        }
        let packitem = PackItemIndexList {
          item_index: ci.tj_itemname.to_string(),
          result: ci.tj_result.to_string(),
          is_normal: match ci.tj_abnormalflag {
            0 => 1,
            1 => 0,
            _ => 0,
          },
          index_type: "0".to_string(),
          identification: ci.tj_abnormalshow.to_string(),
          reference: ci.tj_itemrange.to_string(),
          units: ci.tj_itemunit.to_string(),
          reservestr1: String::new(),
          reservestr2: String::new(),
          reservestr3: String::new(),
        };
        packitems.push(packitem);
      }
      let pcks = PackItemLists {
        item: combine.tj_itemname.to_string(),
        doctor: combine.tj_checkdoctor.to_string(),
        check_time: utility::timeutil::format_timestamp_date_only(combine.tj_checkdate),
        pack_item_index_list: packitems,
        reservestr1: String::new(),
        reservestr2: String::new(),
        reservestr3: String::new(),
      };
      itemsresults.push(pcks);
    }
    // let packitems = vec![packitem];
    let mut review_project = "".to_string();
    let review_items = DamsService::get_review_projects(medresults.checkall.tj_typeid, &medresults.checkall.tj_itemcode);
    if review_items.len() > 0 {
      let itemquery_dto = nodipservice::dto::IteminfoQueryDto {
        itemids: review_items,
        okflag: -1,
        combineflag: 1,
        itemtype: -1,
        lisnum: vec![],
      };
      let ret = ItemSvc::query_iteminfos(&itemquery_dto, &db).await;
      if ret.as_ref().is_err() {
        hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
        hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
        let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
        if saveret.is_err() {
          error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
        }
        error!("Error:{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      let review_combine_items = ret.unwrap();
      review_project = review_combine_items.iter().map(|v| v.tj_itemname.to_owned()).collect::<Vec<String>>().join(",");
    }
    let doctor = nodipservice::SYSCACHE.get().unwrap().get_staff(medresults.checkall.tj_staffid, &db).await;

    let finalresult = FinalInspection {
      review_or_not: match medresults.checkall.tj_typeid {
        1 => 1.to_string(),
        _ => format!("{}", 2),
      },
      review_project,
      conclusion: format!("普通：{}，职业:{}", medresults.checkall.tj_othconclusion, medresults.checkall.tj_ocuconclusion),
      suggest: format!("{}{}", medresults.checkall.tj_othsuggestion, medresults.checkall.tj_ocusuggestion),
      doctor: doctor.tj_staffname.to_string(),
      check_time: utility::timeutil::format_timestamp_date_only(medresults.checkall.tj_checkdate),
      reservestr1: String::new(),
      reservestr2: String::new(),
      reservestr3: String::new(),
    };

    let data = Data {
      hospital_order: ptinfo,
      pack_item_lists: itemsresults,
      final_inspection: finalresult,
    };

    let medresult = MedexamResult {
      request_id: uid.get_uid().to_string(),
      order_no: appointinfo.order_no.to_string(),
      physical_examination_type: match medresults.medinfo.tj_isrecheck {
        0 => "1".to_string(),
        _ => "2".to_string(),
      },
      data,
    };

    let ret = serde_json::ser::to_string(&medresult);
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
      hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let content = ret.unwrap();
    let uri = "/dianBusiness/pushData";
    info!("starto to send content to server:{},content:{}", uri, &content);
    // info!("starto to send content to server:{}", uri);
    let client = crate::common::httpclient::HttpClient::new(&setting);
    let ret = client.upload(&content, &uri.to_string()).await;
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
      hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }

    Ok("".to_string())
  }

  async fn do_upload_medreport_to_external(medexamdetail: &MedexamResultDetail, setting: &Settings, uid: &mut utility::uidservice::UidgenService, db: &DbConnection) -> Result<String> {
    let mut hisinfo = DamsUploadhis {
      testid: medexamdetail.medinfo.tj_testid.to_string(),
      ordno: "".to_string(),
      result: "".to_string(),
      id: 0,
    };
    let commander = setting.application.reportcommand.to_owned();
    info!("commder is:{commander}");

    // let preport = format!("{} {} 2 0 1 ./reports", setting.application.reportcommand, &testid);
    let current_dir = env::current_dir();
    if current_dir.is_err() {
      return Err(anyhow::anyhow!("{}", current_dir.as_ref().unwrap_err().to_string()));
    }
    let current_dir = current_dir.unwrap();
    info!("Current directory is:{}", &current_dir.display());

    let exec_command = current_dir./*join("./").*/join(commander);
    // let exec_command = PathBuf::from(commander);
    info!("exec command is:{}", &exec_command.display());
    if !exec_command.exists() {
      hisinfo.result = format!("{}", "comman doexn't exist");
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      return Err(anyhow::anyhow!("没有用于生成报告的命令"));
    }
    let rptdir = "./reports";
    let rptfile = format!("{}-1.pdf", medexamdetail.medinfo.tj_testid);
    let mut command = Command::new(exec_command);
    command.arg(&medexamdetail.medinfo.tj_testid);
    command.arg(&"2".to_string());
    command.arg(&"0".to_string());
    command.arg(&"1".to_string());
    command.arg(rptdir);
    let output_file = current_dir.join(rptdir).join(&rptfile);
    if output_file.exists() {
      if let Err(e) = std::fs::remove_file(&output_file) {
        error!("remove file error:{:?}", e);
      }
    }
    info!("output file is:{:?}", &output_file);
    info!("command is:{:?}", &command);
    let ret = command.output().await;
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("exec command error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }
    // info!("Output is:{:#?}", &ret.unwrap());
    //等待文件生成
    let mut idx = 1;
    loop {
      if output_file.exists() {
        break;
      } else {
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
        idx += 1;
        if idx == 100 {
          hisinfo.result = format!("{}", "报告生成失败");
          // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
          let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
          if saveret.is_err() {
            error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
            return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
          }
          error!("exec command error: 生成报告失败......");
          return Err(anyhow::anyhow!("exec command error: 生成报告失败......"));
        }
        continue;
      }
    }

    let ret = tokio::fs::read(&output_file).await; //.expect("can't read file");
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", "read file error");
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("read file error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let pdf_content = ret.unwrap();
    let pdf_b64 = BASE64_STANDARD.encode(&pdf_content);

    // info!("Pdf file in base64:{:?}", &pdf_b64);
    let checktime = utility::timeutil::format_timestamp_date_only(medexamdetail.medinfo.tj_testdate);
    info!("check time is:{checktime}");

    let mdreport = MedexamReport {
      filetype: "1".to_string(),
      comnanrld: "".to_string(),
      id_card: medexamdetail.patient.tj_pidcard.to_string(),
      request_id: uid.get_uid().to_string(),
      check_time: checktime.to_owned(),
      order_no: medexamdetail.medinfo.tj_ordno.to_owned(),
      file: pdf_b64.to_owned(),
    };
    // info!("report:{:?}", &mdreport);
    let ret = serde_json::ser::to_string(&mdreport);
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let content = ret.unwrap();
    // info!("Upload content is:{}", &content);

    let uri = "/dianBusiness/pushFile";
    let client = crate::common::httpclient::HttpClient::new(&setting);
    let ret = client.upload(&content, &uri.to_string()).await;
    if ret.as_ref().is_err() {
      hisinfo.result = format!("{}", ret.as_ref().unwrap_err().to_string());
      // hisinfo.ordno = format!("{}", &medresults.medinfo.tj_ordno);
      let saveret = DamsUploadhis::save(&hisinfo, &db.get_connection()).await;
      if saveret.is_err() {
        error!("Error:{}", saveret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", saveret.as_ref().unwrap_err().to_string()));
      }
      error!("error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }

    Ok("".to_string())
  }

  pub fn get_marriage(mg: i32) -> i32 {
    let marriage = match mg {
      1 => 1,
      2 => 2,
      _ => 5,
    };
    marriage
  }
  pub fn get_testtype(ttype: i32) -> i32 {
    let testtype = match ttype {
      //参数：1.上岗2.在岗 3.离岗
      1 => 3,
      2 => 4,
      3 => 5,
      _ => 0,
    };
    testtype
  }
  pub fn get_review_projects(review: i32, item: &String) -> Vec<String> {
    if review != 1 {
      return vec![];
    }
    if item.is_empty() {
      return vec![];
    }
    let items = utility::string::split_string(",", &item);
    // if items.len() <= 0 {
    //   return vec![];
    // }
    return items;

    // "".to_string()
  }

  pub fn get_worktype(wt: i32) -> String {
    //1.普工 2.管理人员 3.放射性从业人员 4.焊工5.电工 6.架子工 7.油漆工 8.司机
    let worktype = match wt {
      1 => "普工".to_string(),
      2 => "管理人员".to_string(),
      3 => "放射性从业人员".to_string(),
      4 => "焊工".to_string(),
      5 => "电工".to_string(),
      6 => "架子工".to_string(),
      7 => "油漆工".to_string(),
      8 => "司机".to_string(),
      _ => "其他".to_string(),
    };
    worktype
  }

  pub async fn get_wtcode(wt: &String, corpinfo: &TjCorpinfo) -> String {
    let ret = nodipservice::SYSCACHE.get().unwrap().get_worktype_by_name_and_monitortype(&wt, &corpinfo.tj_monitortype).await;
    if ret.len() <= 0 {
      return "99-9999".to_string();
    }
    ret[0].ss_name.to_string()
  }
}
