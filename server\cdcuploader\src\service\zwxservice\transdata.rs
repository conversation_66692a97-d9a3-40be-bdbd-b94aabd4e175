use super::common::AudioDetail;
use super::datastructs::*;
use super::{
  common,
  datastructs::{Employer, EmployerData},
};
// use log::*;
use std::collections::HashSet;
use std::fs::File;
use std::io::Write;
// use encoding_rs::*;
use tracing::*;

use crate::app::ParmDto;
use crate::common::utils;

use crate::{
  app::{self, DataType},
  config::settings::Settings,
};
use anyhow::{anyhow, Result};
// use dbopservice::dataaccess::prelude::*;
// use dbopservice::dbinit::DbConnection;
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::common::constant::{self, CheckResultType, ExamStatus, TestType, YesOrNo};
use nodipservice::medexam::audiogramsvc::AudiogramSvc;
// use nodipservice::common::syscache::SysCache;
use utility::timeutil;
use yaserde::ser::to_string_with_config;

#[derive(Debug)]
pub struct ZwxService {}

impl ZwxService {
  pub async fn trans_data(dto: &ParmDto, setting: &Settings, dbconn: &DbConnection) -> Result<String> {
    let seconfig = yaserde::ser::Config {
      perform_indent: false,
      write_document_declaration: true,
      indent_string: None,
    };
    let upload_content: String;
    if dto.datatype == DataType::CORPINFO as i32 {
      //处理datatype
      // info!("start to upload corp info");

      let corpids: Vec<i64> = dto.tid.iter().map(|x| x.parse::<i64>().unwrap_or_default()).collect();
      let ret = TjCorpinfo::query_many(&corpids, "", &vec![], &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let corpinfo = ret.unwrap();

      // info!("uplaod data is:{:?}", &corpinfo);
      // updata.corpinfos = corpinfo;

      let ret = ZwxService::trans_employer_list(&corpinfo /*, &dictdata, &setting*/).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let ret_data = ret.unwrap();

      let ret = serde_json::to_string(&ret_data);
      if ret.as_ref().is_ok() && corpinfo.len() > 0 {
        let result_str = ret.unwrap();

        let filename = format!("{}/{}.json", &setting.system.file_path, corpinfo.get(0).unwrap().tj_corpname);
        let mut file = File::create(filename).expect("create file error");
        let _ = file.write_all(result_str.as_bytes());
        //format!("{}/{}", &setting.system.file_path,
      }

      let ret = to_string_with_config(&ret_data, &seconfig);
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }

      upload_content = ret.unwrap();
    } else if dto.datatype == DataType::HEALTHY as i32 {
      //处理healthy data
      info!("start to upload healthy data info");
      let ret = ZwxService::trans_person_list(dto, &setting, &dbconn).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let ret_data = ret.unwrap();

      let ret = serde_json::to_string(&ret_data);
      if ret.as_ref().is_ok() && dto.tid.len() > 0 {
        let result_str = ret.unwrap();

        let filename = format!("{}/{}.json", &setting.system.file_path, dto.tid.get(0).unwrap());
        let mut file = File::create(filename).expect("create file error");
        let _ = file.write_all(result_str.as_bytes());
        //format!("{}/{}", &setting.system.file_path,
      }

      // SxService::write_to_file(&ret_data);
      let ret = to_string_with_config(&ret_data, &seconfig);
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let ret_data = ret.unwrap().replace("\r", "");
      upload_content = ret_data.replace("\n", "");
    } else {
      //handle all
      // let ret = ZwxService::trans_all(data, setting);
      // if ret.as_ref().is_err() {
      //     return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      // }
      // let ret_data = ret.unwrap();
      // let ret = to_string(&ret_data);
      // if ret.as_ref().is_err() {
      //     return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      // }
      // upload_content = ret.unwrap();
      upload_content = "".to_string();
    }
    // info!("UPLOAD CONTENT:{}", &upload_content);

    let gbk_content = upload_content.as_bytes();

    let ret = ZwxService::zip_to_file(&gbk_content, &dto.tid[0].as_str(), &setting.system.file_path);
    ret
    // Ok(upload_content)
  }

  async fn trans_employer_list(data: &Vec<TjCorpinfo> /*, dictdata: &DictData, setting: &Settings*/) -> Result<EmployerData> {
    if data.len() <= 0 {
      return Err(anyhow!("corpinfo length 0"));
    }
    let mut corpinfos_vec: Vec<Employer> = Vec::new();
    for val in data.iter() {
      let corpinfo = Employer {
        rid: common::get_base64(&val.id.to_string()),
        crpt_name: common::get_base64(&val.tj_corpname.to_owned()),
        institution_code: common::get_base64(&val.tj_orgcode.to_owned()),
        zone_code: common::get_base64(&val.tj_areacode.to_owned()),
        economy_code: common::get_base64(&val.tj_economic2.to_owned()),
        indus_type_code: common::get_base64(&val.tj_industry2.to_owned()),
        crpt_size_code: common::get_base64(&common::get_corp_scale(val.tj_corpscale)),
        work_force: common::get_base64(&val.tj_total.to_string()),
        hold_card_man: common::get_base64(&val.tj_hazarders.to_string()),
        address: common::get_base64(&val.tj_address.to_owned()),
        phone: common::get_base64(&"".to_string()),
        corporate_deputy: common::get_base64(&"".to_string()),
        workman_num: common::get_base64(&val.tj_operationer.to_string()),
        workmistress_num: common::get_base64(&val.tj_hazardersf.to_string()),
        postalcode: common::get_base64(&val.tj_postcode.to_owned()),
        work_area: common::get_base64(&"".to_string()),
        register_fund: common::get_base64(&"".to_string()),
        safety_principal: common::get_base64(&"".to_string()),
        filing_date: common::get_base64(&"".to_string()),
        build_date: common::get_base64(&"".to_string()),
        linkman1: common::get_base64(&"".to_string()),
        position1: common::get_base64(&"".to_string()),
        linkphone1: common::get_base64(&"".to_string()),
        linkman2: common::get_base64(&val.tj_contactor.to_owned()),
        position2: common::get_base64(&"".to_string()),
        linkphone2: common::get_base64(&val.tj_phone.to_owned()),
        safeposition: common::get_base64(&"".to_string()),
        safephone: common::get_base64(&"".to_string()),
        subje_conn: common::get_base64(&"".to_owned()),
        enrol_address: common::get_base64(&"".to_owned()),
        enrol_postalcode: common::get_base64(&"".to_owned()),
        occ_mana_office: common::get_base64(&"".to_owned()),
        if_sub_org: common::get_base64(&"0".to_owned()),
        upper_institution_code: common::get_base64(&"".to_owned()),
      };

      corpinfos_vec.push(corpinfo);
    }
    let employerlist = EmployerList { employer: corpinfos_vec };
    let employer_list = EmployerData { employerlist };

    Ok(employer_list)
  }

  async fn trans_person_list(dto: &ParmDto, setting: &Settings, dbconn: &DbConnection) -> Result<HealthyData> {
    // info!("trans_person_list .........");
    let testids: Vec<String> = dto.tid.iter().map(|v| v.to_string()).collect();
    let ret = TjMedexaminfo::query_many(0, 0, &testids, &vec![], 0, -1, &vec![], &vec![], &vec![], 0, &"".to_string(), &dbconn.get_connection()).await;
    if ret.is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let mut medinfos = ret.unwrap();
    if medinfos.len() <= 0 {
      return Err(anyhow!("不能找到id为:{:?}的体检信息", dto.tid));
    }

    let mut persons: Vec<Person> = Vec::new();

    for val in medinfos.iter_mut() {
      if val.tj_checkstatus < ExamStatus::Allchecked as i32 {
        return Err(anyhow!("体检者:{} 未出报告，不能上报", &val.tj_testid));
      }
      if val.tj_upload == YesOrNo::Yes as i32 && dto.force == YesOrNo::No as i32 {
        return Err(anyhow!("该体检者已经上报..."));
      }
      if val.tj_testtype <= TestType::PT as i32 {
        return Err(anyhow!("体检者:{} 为普通体检，不需要上报", &val.tj_testid));
      }
      //query check all
      let ret = TjCheckallnew::query(&val.tj_testid, &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let ret = ret.unwrap();
      if ret.is_none() {
        return Err(anyhow!("不能找到体检号{}的总检信息", &val.tj_testid));
      }
      let mut checkall = ret.unwrap();

      let ret = TjPatient::query(&val.tj_pid, &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let ret = ret.unwrap();
      if ret.is_none() {
        return Err(anyhow!("不能找到体检号{}的个人信息", &val.tj_testid));
      }
      let patient = ret.unwrap();

      let ret = TjCorpinfo::query(val.tj_corpnum, &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let corp_ret = ret.unwrap();
      if corp_ret.is_none() {
        return Err(anyhow!("不能找到{}的企业信息", &val.tj_corpnum));
      }
      let corpinfo = corp_ret.unwrap();

      let ret = TjPatienthazards::query(&val.tj_testid, &dbconn.get_connection()).await;
      if ret.is_err() {
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let mut pthazards = ret.unwrap();
      if pthazards.len() <= 0 {
        return Err(anyhow!("该体检者没有毒害因素"));
      }
      // let hdids = pthazards.iter().map(|x| x.tj_hid).collect();
      let hdids: Vec<i64> = pthazards.iter().map(|x| x.tj_hid).collect();
      let ret = TjHazardinfo::query_many(&vec![], &hdids, &vec![], &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("query hazard infos {}", ret.as_ref().err().unwrap().to_string()));
      }
      let hdinfos = ret.unwrap();

      if val.tj_isrecheck == YesOrNo::Yes as i32 {
        if val.tj_oldtestid.is_empty() {
          // for _i in 1..10 {
          let ret = TjCorpoccureportFc::query(&val.tj_testid, &dbconn.get_connection()).await;
          if ret.as_ref().is_err() {
            return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
          }
          let ret = ret.unwrap();
          if ret.is_none() {
            return Err(anyhow!("该体检者是复查，但是找不到原来的体检信息"));
          }
          val.tj_oldtestid = ret.unwrap().tj_old_testid;
        }
        if val.tj_oldtestid.is_empty() {
          return Err(anyhow!("不能找到复查的原体检号信息"));
        }
        for _i in 1..100 {
          //多次复查
          let ret = TjMedexaminfo::query(&val.tj_oldtestid, &dbconn.get_connection()).await;
          if ret.as_ref().is_err() {
            return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
          }
          let ret = ret.unwrap();
          if ret.is_none() {
            return Err(anyhow!("不能找到初检号为{}的体检信息", &val.tj_oldtestid));
          }
          let mi = ret.unwrap();
          info!("查找的体检信息：{:?}", &mi);
          if mi.tj_isrecheck == YesOrNo::Yes as i32 {
            val.tj_oldtestid = mi.tj_oldtestid;
            continue;
          } else {
            val.tj_oldtestid = mi.tj_testid;
            break;
          }
        }
      }

      //trans medinfos
      let ret = ZwxService::trans_medexam_info(&val, &checkall, &patient, &corpinfo, setting, dbconn).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("trans {}", ret.as_ref().err().unwrap().to_string()));
      }
      let medinfo = ret.unwrap();
      //end of trans medinfos

      //*trans touch hazards */
      let ret = ZwxService::trans_touch_hazard_factor_list(&val, &pthazards, setting, &hdinfos).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("trans {}", ret.as_ref().err().unwrap().to_string()));
      }
      let touchhazardfactors = ret.unwrap();
      //*end of touch hazards */
      //*trans hazard factors */
      let ret = ZwxService::trans_contact_hazard_factor_list(&val, &mut checkall, &mut pthazards, &hdinfos, setting, dbconn).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("trans {}", ret.as_ref().err().unwrap().to_string()));
      }
      let hazardfactors = ret.unwrap();
      /* end of factors */

      //==trans item list

      let ret = TjTestsummary::query(&val.tj_testid, &vec![], &dbconn.get_connection()).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let summaries = ret.unwrap();

      let ret = TjCheckiteminfo::query(&val.tj_testid, &dbconn.get_connection()).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let checkitems: Vec<TjCheckiteminfo> = ret.unwrap();
      // let itemids: Vec<&str> = checkitems.iter().map(|x| x.tj_itemid.as_str()).collect();
      let ret = TjIteminfo::query_many(&vec![], -1, -1, -1, &vec![], &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("trans {}", ret.as_ref().err().unwrap().to_string()));
      }
      let iteminfos = ret.unwrap();
      // let iteminfos = nodipservice::SYSCACHE.get().unwrap().get_iteminfos(&vec![], -1, -1, &vec![], &dbconn).await;
      let ret = ZwxService::trans_item_list(&val, &checkall, &checkitems, &summaries, &iteminfos, setting, dbconn).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("trans {}", ret.as_ref().err().unwrap().to_string()));
      }
      let items = ret.unwrap();
      //==end of item list

      //---trans diagnosis
      let ret = ZwxService::trans_diagnosis(&checkall).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("trans diagnosis {:?}", ret.as_ref().err().unwrap().to_string()));
      }
      let checkresults = ret.unwrap();
      //---end of diagnosis

      //===trans examsdata
      let ret = ZwxService::trans_healthy_survey(&val, dbconn).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let questionnair = ret.unwrap();
      //====end of examsdata
      //-----trans ocuhis
      let ret = ZwxService::trans_occupation_histories(&val, dbconn).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let ocuhis = ret.unwrap();
      //-----trans ocuhis end
      //===trans examsdata
      let ret = ZwxService::trans_symptom_list(&val, dbconn).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let symptoms = ret.unwrap();
      //====end of examsdata
      //-----trans ocuhis
      let ret = ZwxService::trans_disease_history_list(&val, dbconn).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let diseases = ret.unwrap();
      //-----trans ocuhis end
      let person = Person {
        optype: common::get_base64(&dto.optype.to_string()),
        medinfo,
        touchhazardfactors,
        hazardfactors,
        items,
        checkresults,
        questionnair,
        ocuhis,
        symptoms,
        diseases,
      };
      persons.push(person);
    }
    let personlist = PersonList { persons };
    let infos = HealthyData { personlist };
    Ok(infos)
  }

  async fn trans_medexam_info(medinfo: &TjMedexaminfo, checkall: &TjCheckallnew, person: &TjPatient, corpinfo: &TjCorpinfo, setting: &Settings, dbconn: &DbConnection) -> Result<MedInfo> {
    let ret = utils::get_year_and_month(medinfo.tj_testdate, medinfo.tj_workage.as_str());
    if ret.as_ref().is_err() {
      return Err(anyhow!("工龄信息错误，请检查工龄是否准确"));
    }
    let total_work_time = ret.unwrap();
    let ret = utils::get_year_and_month(medinfo.tj_testdate, medinfo.tj_poisionage.as_str());
    if ret.as_ref().is_err() {
      return Err(anyhow!("接害工龄信息错误，请检查工接害龄是否准确"));
    }
    let hazard_work_time = ret.unwrap();

    let hazards = medinfo.tj_poisionfactor.replace("、", ",");

    let mut old_testid: String = String::from("");
    if medinfo.tj_isrecheck == 1 {
      //get last checkid
      old_testid = medinfo.tj_oldtestid.to_owned();
      // let fc_info = TjCorpoccureportFc::query(&medinfo.tj_testid, &dbconn).await;
      // if fc_info.is_some() {
      //   old_testid = fc_info.unwrap().tj_old_testid;
      // }
    }
    let bhktype = 2;
    // for val in hdinfos.iter() {
    //   if val.tj_extcode.starts_with("16") {
    //     bhktype = 3;
    //     break;
    //   }
    // }
    //报告打印日期
    // let corp_report: TjCorpoccureport;
    // let mut report_ret: Option<TjCorpoccureport> = None;
    // if !medinfo.tj_rptnum.trim().is_empty() {
    //   info!("query report info by {}", &medinfo.tj_rptnum);
    //   report_ret = TjCorpoccureport::query(&medinfo.tj_rptnum, dbconn).await;
    // }
    // if report_ret.is_none() {
    let ret = TjCorpoccureportInfo::query(&medinfo.tj_testid, &dbconn.get_connection()).await;
    if ret.is_err() {
      return Err(anyhow!("体检编号:{}的报告不存在,错误信息：{}", &medinfo.tj_testid, ret.as_ref().err().unwrap().to_string()));
    }
    let rptinfo = ret.unwrap();
    if rptinfo.is_none() {
      return Err(anyhow!("该体检编号{}不在任何报告中，请确认是否已出报告", &medinfo.tj_testid));
    }
    let rptinfo = rptinfo.unwrap();
    let ret = TjCorpoccureport::query(rptinfo.tj_report_id, &dbconn.get_connection()).await;
    if ret.is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let report_info = ret.unwrap();
    if report_info.is_none() {
      return Err(anyhow!("体检编号:{}的报告不存在,id:{}", &medinfo.tj_testid, &rptinfo.tj_report_id));
    }
    let corp_report: TjCorpoccureport = report_info.unwrap();
    // } else {
    //   corp_report = report_ret.unwrap();
    // }

    let mut worktypecode = medinfo.tj_wtcode.to_owned();
    if worktypecode.eq_ignore_ascii_case("99-9999") {
      worktypecode = "999999".to_string();
    }

    let mut workname = "".to_string();
    if worktypecode.eq_ignore_ascii_case("0014") || worktypecode.eq_ignore_ascii_case("0033") || worktypecode.eq_ignore_ascii_case("999999") || worktypecode.ends_with("-99") {
      workname = medinfo.tj_worktype.to_string();
    }
    let bhkrst = common::get_check_result_string(checkall.tj_typeid);

    let mut mhkadv: String; // = "".to_string();
    let mut verdict: String; // = "".to_string();
    if checkall.tj_typeid > CheckResultType::Normal as i32 && checkall.tj_typeid < CheckResultType::Other as i32 {
      mhkadv = checkall.tj_ocusuggestion.to_owned();
      verdict = checkall.tj_ocuconclusion.to_owned();
    } else {
      mhkadv = checkall.tj_othsuggestion.to_owned();
      verdict = checkall.tj_othconclusion.to_owned();
    }
    if mhkadv.is_empty() {
      mhkadv = "无".to_string();
    }
    if verdict.is_empty() {
      verdict = "无".to_string();
    }

    let emp_sub = "".to_string();
    let emp_upper = "".to_string();
    let mut emp_credit_code = "".to_string();
    let mut emp_crpt_name = "".to_string();
    let mut emp_industry_type = "".to_string();
    let mut emp_economy_code = "".to_string();
    let mut emp_area_code = "".to_string();
    let mut emp_corp_scale = "".to_string();

    if corpinfo.tj_industry2.eq_ignore_ascii_case("201558") || corpinfo.tj_industry2.eq_ignore_ascii_case("201563") {
      emp_credit_code = corpinfo.tj_orgcode.to_owned();
      emp_crpt_name = corpinfo.tj_corpname.to_owned();
      emp_industry_type = corpinfo.tj_industry2.to_owned();
      emp_economy_code = corpinfo.tj_economic2.to_owned();
      emp_area_code = corpinfo.tj_areacode.to_owned();
      emp_corp_scale = corpinfo.tj_corpscale.to_string();
    }

    let jctype = match corpinfo.tj_monitortype.as_str() {
      "01" | "1" => "1".to_string(),
      "02" | "2" => "2".to_string(),
      _ => "1".to_string(),
    };

    let idcardtype = "01";
    let mut protective = "".to_string();
    if medinfo.tj_monitortype.eq_ignore_ascii_case("02") {
      protective = medinfo.tj_protective.to_string();
    }

    let info: MedInfo = MedInfo {
      rid: common::get_base64(&medinfo.id.to_string()),
      bhkorgancode: common::get_base64(&setting.organization.org_code.to_owned()),
      bhkcode: common::get_base64(&medinfo.tj_testid.to_owned()),
      institutioncode: common::get_base64(&corpinfo.tj_orgcode.to_owned()),
      crptname: common::get_base64(&corpinfo.tj_corpname.to_owned()),
      crptaddr: common::get_base64(&corpinfo.tj_address.to_owned()),
      personname: common::get_base64(&person.tj_pname.to_owned()),
      sex: common::get_base64(&person.tj_psex.to_string()),
      idc: common::get_base64(&person.tj_pidcard.to_uppercase().to_owned()),
      brth: common::get_base64(&person.tj_pbirthday.to_owned()),
      age: common::get_base64(&medinfo.tj_age.to_string()),
      isxmrd: common::get_base64(&common::get_marriage_status(person.tj_pmarriage).to_string()),
      lnktel: common::get_base64(&person.tj_pmobile.to_owned()),
      dpt: common::get_base64(&"".to_string()),
      wrknum: common::get_base64(&medinfo.tj_empid.to_owned()),
      wrklnt: common::get_base64(&total_work_time.0.to_string()),
      wrklntmonth: common::get_base64(&total_work_time.1.to_string()),
      tchbadrsntim: common::get_base64(&hazard_work_time.0.to_string()),
      tchbadrsnmonth: common::get_base64(&hazard_work_time.1.to_string()),
      workname: common::get_base64(&workname),
      onguardstate: common::get_base64(&common::get_testtype_status(medinfo.tj_testtype)),
      bhkdate: common::get_base64(&utils::timestamp_to_local_date(medinfo.tj_testdate)),
      bhkrst: common::get_base64(&bhkrst),
      mhkadv: common::get_base64(&mhkadv),
      verdict: common::get_base64(&verdict),
      mhkdct: common::get_base64(&nodipservice::SYSCACHE.get().unwrap().get_staff(checkall.tj_staffid, &dbconn).await.tj_staffname), //主检医师 ??????&StaffService::find_staff(checkall.tj_staffid as i64, &dictdata.staffs).tj_staffname
      bhktype: common::get_base64(&bhktype.to_string()),
      jdgdat: common::get_base64(&utils::timestamp_to_local_date(checkall.tj_checkdate)),
      badrsn: common::get_base64(&hazards),
      ifrhk: common::get_base64(&medinfo.tj_isrecheck.to_string()),
      lastbhkcode: common::get_base64(&old_testid), //如果是复查,则用上一次的体检号
      idcardtypecode: common::get_base64(&idcardtype.to_string()),
      worktypecode: common::get_base64(&worktypecode),
      harmstartdate: common::get_base64(&hazard_work_time.2), //接触毒害因素开始时间
      jctype: common::get_base64(&jctype),
      rptprintdate: common::get_base64(&utils::timestamp_to_local_date(corp_report.tj_createdate)),
      ifsuborg: common::get_base64(&"0".to_string()),           //用人单位是否分支机构
      ifsuborgemp: common::get_base64(&emp_sub),                //用工单位是否分支机构
      uppercreditcodeemp: common::get_base64(&emp_upper),       //用工单位上级单位社会信用代码
      creditcodeemp: common::get_base64(&emp_credit_code),      //用工单位社会信用代码
      cprtnameemp: common::get_base64(&emp_crpt_name),          //用工单位名称
      industypecodeemp: common::get_base64(&emp_industry_type), //用工单位行业类别编码
      economycodeemp: common::get_base64(&emp_economy_code),    //用工单位经济类型编码
      cprtsizecodeemp: common::get_base64(&emp_corp_scale),     //用工单位企业规模编码
      zonecodeemp: common::get_base64(&emp_area_code),          //用工单位所属地区编码
      protectequcode: common::get_base64(&protective),
    };
    Ok(info)
  }

  async fn trans_touch_hazard_factor_list(medinfo: &TjMedexaminfo, pthds: &Vec<TjPatienthazards>, setting: &Settings, hdinfos: &Vec<TjHazardinfo>) -> Result<TouchHazardFactorList> {
    let otherhazards: Vec<String> = setting.system.other_hdcode.split(",").map(|x| x.to_string()).collect();
    let mut otherhds: Vec<String> = Vec::new();
    let mut touch_hazards: Vec<TouchHazardFactor> = Vec::new();

    let mut idx = 1;
    for val in pthds {
      let hdinfo = hdinfos.iter().find(|&x| x.id == val.tj_hid);
      if hdinfo.is_none() {
        continue;
        //  return Err(anyhow!("不存在id为:{}的毒害因素", val.tj_hid));
      }
      let hdinfo = hdinfo.unwrap();
      if hdinfo.tj_extcode.is_empty() {
        return Err(anyhow!("毒害因素:{}的代码未匹配,请先匹配", &hdinfo.tj_hname));
      }
      let is_other = otherhazards.iter().find(|&x| x.eq_ignore_ascii_case(&hdinfo.tj_extcode));
      //其他毒害因素
      if is_other.is_some() {
        otherhds.push(hdinfo.tj_hname.to_owned());
        // continue;
      }
      touch_hazards.push(TouchHazardFactor {
        rid: common::get_base64(&format!("{}{}", medinfo.tj_testid, idx)),
        badrsncode: common::get_base64(&hdinfo.tj_extcode),
      });
      idx += 1;
    }
    Ok(TouchHazardFactorList {
      touchhazards: touch_hazards,
      otherhazards: common::get_base64(&otherhds.join(",")),
    })
  }

  async fn trans_contact_hazard_factor_list(
    medinfo: &TjMedexaminfo,
    checkall: &mut TjCheckallnew,
    pthds: &Vec<TjPatienthazards>,
    hdinfos: &Vec<TjHazardinfo>,
    setting: &Settings,
    dbconn: &DbConnection,
  ) -> Result<ContactHazardFactorList> {
    let mut pthazards = pthds.clone();
    // let mut forbidden_hazards: Vec<String> = Vec::new();
    // let mut oculike_hazards: Vec<String> = Vec::new();
    // let mut recheck_hazards: Vec<String> = Vec::new();
    // let mut other_hazards: Vec<String> = Vec::new();
    // let mut abnormal_diseases: Vec<String> = Vec::new();
    let otherhazards: Vec<String> = setting.system.other_hdcode.split(",").map(|x| x.to_string()).collect();
    let mut otherhds: Vec<String> = Vec::new();

    let ret = TjDiseaseinfo::query(&checkall.tj_testid, &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("query disease {}", ret.as_ref().err().unwrap().to_string()));
    }
    let disease_infos_ret = ret.unwrap();
    let disease_infos: Vec<TjDiseaseinfo> = disease_infos_ret.into_iter().filter(|f| f.tj_disid > 0).map(|v| v).collect();
    let mut disids: Vec<i64> = disease_infos.iter().map(|v| v.tj_disid).collect();

    if checkall.tj_typeid == CheckResultType::Forbidden as i32 {
      if checkall.tj_hazardcode.is_empty() {
        return Err(anyhow!("职业禁忌证或者疑似职业病，需要选择相应的毒害因素"));
      }
      // forbidden_hazards = checkall.tj_hazardcode.split(",").map(|x| x.to_string()).collect();
      if checkall.tj_discode.is_empty() {
        return Err(anyhow!("职业禁忌证或者疑似职业病，需要选择相应的疾病信息"));
      }
    } else if checkall.tj_typeid == CheckResultType::Oculike as i32 {
      if checkall.tj_hazardcode.is_empty() {
        return Err(anyhow!("职业禁忌证或者疑似职业病，需要选择相应的毒害因素"));
      }
      // oculike_hazards = checkall.tj_hazardcode.split(",").map(|x| x.to_string()).collect();
      if checkall.tj_discode.is_empty() {
        return Err(anyhow!("职业禁忌证或者疑似职业病，需要选择相应的疾病信息"));
      }
      // abnormal_diseases = checkall.tj_discode.split(",").map(|x| x.to_string()).collect();
    } else if checkall.tj_typeid == CheckResultType::Recheck as i32 {
      if checkall.tj_hazardcode.is_empty() {
        return Err(anyhow!("复查结论，需要选择相应的毒害因素"));
      }
      // recheck_hazards = checkall.tj_hazardcode.split(",").map(|x| x.to_string()).collect();
      if checkall.tj_itemcode.is_empty() {
        return Err(anyhow!("复查结论，需要选择相应的项目信息"));
      }
    } else if checkall.tj_typeid == CheckResultType::Other as i32 {
      if disease_infos.len() <= 0 && checkall.tj_discode.is_empty() {
        return Err(anyhow!("该体检者其他疾病或异常，但是没有疾病信息，请添加疾病信息"));
      }
      if !checkall.tj_discode.is_empty() {
        let discodes: Vec<i64> = checkall.tj_discode.split(",").map(|x| x.parse::<i64>().unwrap_or_default()).collect();
        if discodes.len() > 0 {
          disids.extend(discodes);
          disids = disids.into_iter().map(|v| v).collect::<HashSet<_>>().into_iter().collect();
        }
      }
      for v in pthazards.iter_mut() {
        if v.tj_typeid == CheckResultType::Normal as i32 || v.tj_typeid == CheckResultType::Other as i32 {
          v.tj_typeid = CheckResultType::Other as i32;
          v.tj_diseases = disids.iter().map(|v| v.to_string()).collect::<Vec<String>>().join(",");
          v.tj_recheckitems = "".to_string();
        }
      }
    }

    let ismulti = pthazards.iter().find(|&p| p.tj_typeid == checkall.tj_typeid).is_some();
    if !ismulti {
      info!("该体检者没有采用多因素结论，需要进行调整......");
      //set to multi
      if checkall.tj_typeid == CheckResultType::Forbidden as i32 || checkall.tj_typeid == CheckResultType::Oculike as i32 {
        if checkall.tj_hazardcode.is_empty() && pthazards.len() == 1 {
          //只有一个毒害因素
          checkall.tj_hazardcode = pthazards[0].tj_hid.to_string();
        }
        if checkall.tj_hazardcode.is_empty() {
          return Err(anyhow!("该体检者禁忌或者疑似，但没有选择相应的毒害因素"));
        }
        let hdids: Vec<i64> = checkall.tj_hazardcode.split(",").map(|x| x.parse::<i64>().unwrap_or_default()).collect();

        let discodes: Vec<i64> = checkall.tj_discode.split(",").map(|x| x.parse::<i64>().unwrap_or_default()).collect();
        let mut disids_n: Vec<i64> = Vec::new();
        for val in discodes.into_iter() {
          if val <= 0 {
            continue;
          }
          if val <= 3000 {
            //认为采用的是ID
            disids_n.push(val);
          } else {
            //用的是代码，需要调整为ID
            let disinfo = nodipservice::SYSCACHE.get().unwrap().find_diseases_by_code(&val.to_string(), &dbconn).await;
            if disinfo.is_none() {
              return Err(anyhow!("不能根据疾病代码：{}找到疾病信息", val));
            }
            disids_n.push(disinfo.unwrap().id);
          }
        }

        for v in pthazards.iter_mut() {
          if hdids.iter().find(|&&f| f == v.tj_hid).is_some() {
            v.tj_typeid = checkall.tj_typeid;
            v.tj_diseases = disids_n.iter().map(|f| f.to_string()).collect::<Vec<String>>().join(","); //checkall.tj_discode.to_string();
            v.tj_recheckitems = checkall.tj_itemcode.to_string();
          }
        }
      }
      if checkall.tj_typeid == CheckResultType::Recheck as i32 {
        //需复查
        if checkall.tj_itemcode.is_empty() {
          return Err(anyhow!("该体检者需复查，但没有选择相应的复查项目"));
        }
        // let itemids: Vec<&str> = checkall.tj_itemcode.split(",").map(|x| x).collect();
        if checkall.tj_hazardcode.is_empty() && pthazards.len() == 1 {
          //只有一个毒害因素
          checkall.tj_hazardcode = pthazards[0].tj_hid.to_string();
        }
        if checkall.tj_hazardcode.is_empty() {
          return Err(anyhow!("该体检者需复查，但没有选择相应的毒害因素"));
        }
        let hdids: Vec<i64> = checkall.tj_hazardcode.split(",").map(|x| x.parse::<i64>().unwrap_or_default()).collect();
        let discodes: Vec<i64> = checkall.tj_discode.split(",").map(|x| x.parse::<i64>().unwrap_or_default()).collect();
        let mut disids_n: Vec<i64> = Vec::new();
        for val in discodes.into_iter() {
          //
          if val <= 3000 {
            //认为采用的是discode
            disids_n.push(val);
          } else {
            //用的是代码，需要调整为ID
            let disinfo = nodipservice::SYSCACHE.get().unwrap().find_diseases_by_code(&val.to_string(), &dbconn).await;
            if disinfo.is_none() {
              return Err(anyhow!("不能根据疾病代码：{}找到疾病信息", val));
            }
            disids_n.push(disinfo.unwrap().id);
          }
        }
        for v in pthazards.iter_mut() {
          if hdids.iter().find(|&&f| f == v.tj_hid).is_some() {
            v.tj_typeid = checkall.tj_typeid;
            v.tj_diseases = disids_n.iter().map(|f| f.to_string()).collect::<Vec<String>>().join(","); //checkall.tj_discode.to_string();
            v.tj_recheckitems = checkall.tj_itemcode.to_string();
          }
        }
      }

      if disids.len() > 0 {
        //其他疾病或异常
        for v in pthazards.iter_mut() {
          if v.tj_typeid == CheckResultType::Normal as i32 || v.tj_typeid == CheckResultType::Other as i32 {
            v.tj_typeid = CheckResultType::Other as i32;
            v.tj_diseases = disids.iter().map(|v| v.to_string()).collect::<Vec<String>>().join(",");
            v.tj_recheckitems = "".to_string();
          }
        }
      }
      // info!("调整后的接害因素信息：{:?}", &pthazards);
      let ret = TjPatienthazards::save_many(&pthazards, &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("update patient hazards error:{:?}", ret.as_ref().unwrap_err().to_string());
      }
    } else {
      info!("不需要调整多因素结果......");
    }
    info!("调整后的毒害因素信息：{:?}", &pthds);

    let mut contacthazards: Vec<ContactHazardFactor> = Vec::new();

    for pthd in pthazards.iter() {
      match pthd.tj_typeid {
        x if x == CheckResultType::Recheck as i32 => {
          if pthd.tj_diseases.is_empty() {
            return Err(anyhow!("需复查，需要选择相应的疾病信息(详细结果里处理)"));
          };
          if pthd.tj_recheckitems.is_empty() {
            return Err(anyhow!("需复查，需要选择相应的复查项目(详细结果里处理)"));
          }
        }
        x if x == CheckResultType::Forbidden as i32 => {
          if pthd.tj_diseases.is_empty() {
            return Err(anyhow!("职业禁忌证，需要选择相应的疾病信息(详细结果里处理)"));
          }
        }
        x if x == CheckResultType::Oculike as i32 => {
          if pthd.tj_diseases.is_empty() {
            return Err(anyhow!("疑似职业病，需要选择相应的疾病信息(详细结果里处理)"));
          }
        }
        _ => {}
      }
    }

    let mut idx = 1;

    for val in pthazards.into_iter() {
      let hdinfo = hdinfos.iter().find(|&p| p.id == val.tj_hid);
      if hdinfo.is_none() {
        continue;
      }

      let hdinfo = hdinfo.unwrap();
      if hdinfo.tj_extcode.is_empty() {
        return Err(anyhow!("毒害因素:{}代码未匹配，请先匹配", hdinfo.tj_hname));
      }
      // if hdinfo.tj_extcode.ends_with("999") {
      //   otherhds.push(hdinfo.tj_hname.to_string());
      // }
      let is_other = otherhazards.iter().find(|&x| x.eq_ignore_ascii_case(&hdinfo.tj_extcode));
      //其他毒害因素
      if is_other.is_some() {
        otherhds.push(hdinfo.tj_hname.to_owned());
        // continue;
      }
      //是主要粉尘，并且已经存在，则忽略
      let is_main_dust = common::check_main_dust(&hdinfo.tj_extcode);
      if is_main_dust
        && contacthazards
          .iter()
          .find(|&x| x.badrsncode.eq_ignore_ascii_case(&common::get_base64(&hdinfo.tj_extcode)))
          .is_some()
      {
        continue;
      }

      match val.tj_typeid {
        x if x == CheckResultType::Forbidden as i32 => {
          let discodes: Vec<String> = val.tj_diseases.split(',').map(|x| x.to_string()).collect();
          for dis in discodes {
            let mut discode = dis.clone();
            if !discode.starts_with("0") {
              let did = discode.parse::<i64>().unwrap_or_default();
              if did <= 3000 {
                // let ret = dictservice.find_diseases_by_id(did).await;
                let ret = nodipservice::SYSCACHE.get().unwrap().find_diseases_by_id(did, &dbconn).await;
                if ret.is_some() {
                  discode = ret.unwrap().tj_discode.to_owned();
                }
              }
            }
            if discode.is_empty() {
              return Err(anyhow!("不是有效的禁忌证，请在总检界面重新选择"));
            }

            let cthdinfo = ContactHazardFactor {
              rid: common::get_base64(&format!("ct{}{}", idx, medinfo.tj_testid.clone())),
              badrsncode: common::get_base64(&hdinfo.tj_extcode.to_owned()),
              examconclusioncode: common::get_base64(&common::get_check_result(checkall.tj_typeid)),
              yszybcode: common::get_base64(&"".to_string()),
              zyjjzcode: common::get_base64(&discode),
              qtjbname: common::get_base64(&"".to_string()),
              enterprisename: common::get_base64(&"".to_string()),
            };
            idx += 1;
            contacthazards.push(cthdinfo);
          }
        }
        x if x == CheckResultType::Oculike as i32 => {
          let discodes: Vec<String> = val.tj_diseases.split(',').map(|x| x.to_string()).collect();
          for dis in discodes {
            let mut discode = dis.clone();
            if !discode.starts_with("0") {
              let did = discode.parse::<i64>().unwrap_or_default();
              if did <= 2000 {
                // let ret = dictservice.find_diseases_by_id(did).await;
                let ret = nodipservice::SYSCACHE.get().unwrap().find_diseases_by_id(did, &dbconn).await;
                if ret.is_some() {
                  discode = ret.unwrap().tj_discode.to_owned();
                }
              }
            }
            if discode.is_empty() {
              return Err(anyhow!("不是有效的疑似职业病，请在总检界面重新选择"));
            }
            let cthdinfo = ContactHazardFactor {
              rid: common::get_base64(&format!("ol{}{}", idx, medinfo.tj_testid.clone())),
              badrsncode: common::get_base64(&hdinfo.tj_extcode.to_owned()),
              examconclusioncode: common::get_base64(&common::get_check_result(checkall.tj_typeid)),
              yszybcode: common::get_base64(&discode),
              zyjjzcode: common::get_base64(&"".to_string()),
              qtjbname: common::get_base64(&"".to_string()),
              enterprisename: common::get_base64(&val.tj_olcorpname.to_string()),
            };
            idx += 1;
            contacthazards.push(cthdinfo);
          }
        }
        x if x == CheckResultType::Recheck as i32 => {
          // let discodes: Vec<String> = val.tj_diseases.split(',').map(|x| x.to_string()).collect();
          let cthdinfo = ContactHazardFactor {
            rid: common::get_base64(&format!("re{}{}", idx, medinfo.tj_testid.clone())),
            badrsncode: common::get_base64(&hdinfo.tj_extcode.to_owned()),
            examconclusioncode: common::get_base64(&common::get_check_result(checkall.tj_typeid)),
            yszybcode: common::get_base64(&"".to_string()),
            zyjjzcode: common::get_base64(&"".to_string()),
            qtjbname: common::get_base64(&"".to_string()),
            enterprisename: common::get_base64(&"".to_string()),
          };
          idx += 1;
          contacthazards.push(cthdinfo);
        }
        x if x == CheckResultType::Other as i32 => {
          let mut otherdiseases: Vec<String> = Vec::new();
          let disids: Vec<i64> = val.tj_diseases.split(",").map(|v| v.parse::<i64>().unwrap_or_default()).collect();
          for did in disids.into_iter() {
            let disinfo = disease_infos.iter().find(|&f| f.tj_disid == did);
            if disinfo.is_none() {
              error!("不能根据疾病ID:{did}找到疾病信息(没有把该疾病添加到体检者的疾病列表里)");
              let ret = nodipservice::SYSCACHE.get().unwrap().get_diseases(&vec![did], &vec![], &vec![], &dbconn).await;
              if ret.len() <= 0 {
                error!("疾病id为:{did}的疾病信息已经被删除");
                continue;
              }
              if ret[0].tj_disname.is_empty() {
                continue;
              }
              otherdiseases.push(ret[0].tj_disname.to_owned());
            } else {
              let disinfo = disinfo.unwrap();
              // if disinfo.tj_isoccu == YesOrNo::Yes as i32 {
              //   continue;
              // }
              if disinfo.tj_diseasename.is_empty() {
                continue;
              }
              otherdiseases.push(disinfo.tj_diseasename.to_owned());
            }
          }
          let mut qtjb_name = "".to_string();
          if otherdiseases.len() > 0 {
            qtjb_name = otherdiseases.join(",");
          }
          // let discodes: Vec<String> = val.tj_diseases.split(',').map(|x| x.to_string()).collect();
          let cthdinfo = ContactHazardFactor {
            rid: common::get_base64(&format!("re{}{}", idx, medinfo.tj_testid.clone())),
            badrsncode: common::get_base64(&hdinfo.tj_extcode.to_owned()),
            examconclusioncode: common::get_base64(&common::get_check_result(val.tj_typeid)),
            yszybcode: common::get_base64(&"".to_string()),
            zyjjzcode: common::get_base64(&"".to_string()),
            qtjbname: common::get_base64(&qtjb_name),
            enterprisename: common::get_base64(&"".to_string()),
          };
          idx += 1;
          contacthazards.push(cthdinfo);
        }
        _ => {
          info!("处理正常的毒害因素：{:#?}", &val);
          let cthdinfo = ContactHazardFactor {
            rid: common::get_base64(&format!("normal{}{}", idx, medinfo.tj_testid.clone())),
            badrsncode: common::get_base64(&hdinfo.tj_extcode.to_owned()),
            examconclusioncode: common::get_base64(&common::get_check_result(val.tj_typeid)),
            yszybcode: common::get_base64(&"".to_string()),
            zyjjzcode: common::get_base64(&"".to_string()),
            qtjbname: common::get_base64(&"".to_string()),
            enterprisename: common::get_base64(&"".to_string()),
          };
          idx += 1;
          contacthazards.push(cthdinfo);
        }
      }
    }

    // //禁忌
    // let mut idx = 1;
    // for val in forbidden_hazards {
    //   let hdinfo = hdinfos.iter().find(|&x| x.id == val.parse::<i64>().unwrap_or_default());
    //   if hdinfo.is_none() {
    //     continue;
    //     // return Err(anyhow!("不存在id为:{}的毒害因素", val));
    //   }
    //   let hdinfo = hdinfo.unwrap();
    //   if hdinfo.tj_extcode.is_empty() {
    //     return Err(anyhow!("毒害因素:{}的代码未匹配,请先匹配", &hdinfo.tj_hname));
    //   }
    //   pthazards.retain(|x| x.tj_hid != val.parse::<i64>().unwrap_or_default());
    //   let otherhd = otherhazards.iter().find(|&x| x == &hdinfo.tj_extcode);
    //   if otherhd.is_some() {
    //     otherhds.push(hdinfo.tj_hname.to_owned());
    //     // continue;
    //   }
    //   //是主要粉尘，并且已经存在，则忽略
    //   let is_main_dust = common::check_main_dust(&hdinfo.tj_extcode);
    //   if is_main_dust
    //     && contacthazards
    //       .iter()
    //       .find(|&x| x.badrsncode.eq_ignore_ascii_case(&common::get_base64(&hdinfo.tj_extcode)))
    //       .is_some()
    //   {
    //     continue;
    //   }
    //   let discodes: Vec<String> = checkall.tj_discode.split(',').map(|x| x.to_string()).collect();
    //   for dis in discodes {
    //     let mut discode = dis.clone();
    //     if !discode.starts_with("0") {
    //       let did = discode.parse::<i64>().unwrap_or_default();
    //       if did <= 3000 {
    //         // let ret = dictservice.find_diseases_by_id(did).await;
    //         let ret = nodipservice::SYSCACHE.get().unwrap().find_diseases_by_id(did, &dbconn).await;
    //         if ret.is_some() {
    //           discode = ret.unwrap().tj_discode.to_owned();
    //         }
    //       }
    //     }
    //     if discode.is_empty() {
    //       return Err(anyhow!("不是有效的禁忌证，请在总检界面重新选择"));
    //     }

    //     let cthdinfo = ContactHazardFactor {
    //       rid: common::get_base64(&format!("ct{}{}", idx, medinfo.tj_testid.clone())),
    //       badrsncode: common::get_base64(&hdinfo.tj_extcode.to_owned()),
    //       examconclusioncode: common::get_base64(&common::get_check_result(checkall.tj_typeid)),
    //       yszybcode: common::get_base64(&"".to_string()),
    //       zyjjzcode: common::get_base64(&discode),
    //       qtjbname: common::get_base64(&"".to_string()),
    //       enterprisename: common::get_base64(&"".to_string()),
    //     };
    //     idx += 1;
    //     contacthazards.push(cthdinfo);
    //   }
    // }
    // //疑似职业病
    // for val in oculike_hazards {
    //   let hdinfo = hdinfos.iter().find(|&x| x.id == val.parse::<i64>().unwrap_or_default());
    //   if hdinfo.is_none() {
    //     continue;
    //   }

    //   pthazards.retain(|x| x.tj_hid != val.parse::<i64>().unwrap_or_default());
    //   let hdinfo = hdinfo.unwrap();
    //   if hdinfo.tj_extcode.is_empty() {
    //     return Err(anyhow!("毒害因素:{}的代码未匹配,请先匹配", &hdinfo.tj_hname));
    //   }
    //   let otherhd = otherhazards.iter().find(|&x| x == &hdinfo.tj_extcode);
    //   if otherhd.is_some() {
    //     otherhds.push(hdinfo.tj_hname.to_owned());
    //     // continue;
    //   }
    //   //是主要粉尘，并且已经存在，则忽略
    //   let is_main_dust = common::check_main_dust(&hdinfo.tj_extcode);
    //   if is_main_dust
    //     && contacthazards
    //       .iter()
    //       .find(|&x| x.badrsncode.eq_ignore_ascii_case(&common::get_base64(&hdinfo.tj_extcode)))
    //       .is_some()
    //   {
    //     continue;
    //   }
    //   let discodes: Vec<String> = checkall.tj_discode.split(',').map(|x| x.to_string()).collect();
    //   for dis in discodes {
    //     let mut discode = dis.clone();
    //     if !discode.starts_with("0") {
    //       let did = discode.parse::<i64>().unwrap_or_default();
    //       if did <= 2000 {
    //         // let ret = dictservice.find_diseases_by_id(did).await;
    //         let ret = nodipservice::SYSCACHE.get().unwrap().find_diseases_by_id(did, &dbconn).await;
    //         if ret.is_some() {
    //           discode = ret.unwrap().tj_discode.to_owned();
    //         }
    //       }
    //     }
    //     if discode.is_empty() {
    //       return Err(anyhow!("不是有效的禁忌证，请在总检界面重新选择"));
    //     }
    //     let cthdinfo = ContactHazardFactor {
    //       rid: common::get_base64(&format!("ol{}{}", idx, medinfo.tj_testid.clone())),
    //       badrsncode: common::get_base64(&hdinfo.tj_extcode.to_owned()),
    //       examconclusioncode: common::get_base64(&common::get_check_result(checkall.tj_typeid)),
    //       yszybcode: common::get_base64(&discode),
    //       zyjjzcode: common::get_base64(&"".to_string()),
    //       qtjbname: common::get_base64(&"".to_string()),
    //       enterprisename: common::get_base64(&"".to_string()),
    //     };
    //     idx += 1;
    //     contacthazards.push(cthdinfo);
    //   }
    // }
    // //需复查
    // for val in recheck_hazards {
    //   let hdinfo = hdinfos.iter().find(|&x| x.id == val.parse::<i64>().unwrap_or_default());
    //   if hdinfo.is_none() {
    //     continue;
    //     // return Err(anyhow!("不存在id为:{}的毒害因素", val));
    //   }

    //   pthazards.retain(|x| x.tj_hid != val.parse::<i64>().unwrap_or_default());
    //   let hdinfo = hdinfo.unwrap();
    //   if hdinfo.tj_extcode.is_empty() {
    //     return Err(anyhow!("毒害因素:{}的代码未匹配,请先匹配", &hdinfo.tj_hname));
    //   }
    //   let otherhd = otherhazards.iter().find(|&x| x == &hdinfo.tj_extcode);
    //   if otherhd.is_some() {
    //     otherhds.push(hdinfo.tj_hname.to_owned());
    //     // continue;
    //   }
    //   //是主要粉尘，并且已经存在，则忽略
    //   let is_main_dust = common::check_main_dust(&hdinfo.tj_extcode);
    //   if is_main_dust
    //     && contacthazards
    //       .iter()
    //       .find(|&x| x.badrsncode.eq_ignore_ascii_case(&common::get_base64(&hdinfo.tj_extcode)))
    //       .is_some()
    //   {
    //     continue;
    //   }
    //   let cthdinfo = ContactHazardFactor {
    //     rid: common::get_base64(&format!("re{}{}", idx, medinfo.tj_testid.clone())),
    //     badrsncode: common::get_base64(&hdinfo.tj_extcode.to_owned()),
    //     examconclusioncode: common::get_base64(&common::get_check_result(checkall.tj_typeid)),
    //     yszybcode: common::get_base64(&"".to_string()),
    //     zyjjzcode: common::get_base64(&"".to_string()),
    //     qtjbname: common::get_base64(&"".to_string()),
    //     enterprisename: common::get_base64(&"".to_string()),
    //   };
    //   idx += 1;
    //   contacthazards.push(cthdinfo);
    // }

    // //其他毒害因素
    // for val in other_hazards {
    //   let hdinfo = hdinfos.iter().find(|&x| x.id == val.parse::<i64>().unwrap_or_default());
    //   if hdinfo.is_none() {
    //     continue;
    //   }

    //   let hdinfo = hdinfo.unwrap();
    //   let extcode = hdinfo.tj_extcode.to_owned();
    //   if extcode.is_empty() {
    //     return Err(anyhow!("毒害因素:{}代码未匹配，请先匹配", hdinfo.tj_hname));
    //   }

    //   pthazards.retain(|x| x.tj_hid != val.parse::<i64>().unwrap_or_default());

    //   let ret = TjDiseaseinfo::query(&checkall.tj_testid, &dbconn.get_connection()).await;
    //   if ret.as_ref().is_err() {
    //     return Err(anyhow!("query disease {}", ret.as_ref().err().unwrap().to_string()));
    //   }
    //   let disease_infos = ret.unwrap();
    //   let mut otherdiseases: Vec<String> = Vec::new();
    //   for disinfo in disease_infos.iter() {
    //     if disinfo.tj_isoccu == YesOrNo::Yes as i32 {
    //       continue;
    //     }
    //     otherdiseases.push(disinfo.tj_diseasename.clone());
    //   }
    //   if checkall.tj_typeid == CheckResultType::Other as i32 && checkall.tj_diseasename.len() > 0 {
    //     let others_d: Vec<String> = checkall.tj_diseasename.split(',').map(|x| x.to_string()).collect();
    //     otherdiseases.extend(others_d);
    //   }

    //   let qtjb_name = otherdiseases.join(",");

    //   let otherhd = otherhazards.iter().find(|&x| x == &hdinfo.tj_extcode);
    //   if otherhd.is_some() {
    //     otherhds.push(hdinfo.tj_hname.to_owned());
    //     // continue;
    //   }
    //   //是主要粉尘，并且已经存在，则忽略
    //   let is_main_dust = common::check_main_dust(&hdinfo.tj_extcode);
    //   if is_main_dust
    //     && contacthazards
    //       .iter()
    //       .find(|&x| x.badrsncode.eq_ignore_ascii_case(&common::get_base64(&hdinfo.tj_extcode)))
    //       .is_some()
    //   {
    //     continue;
    //   }
    //   let cthdinfo = ContactHazardFactor {
    //     rid: common::get_base64(&format!("re{}{}", idx, medinfo.tj_testid.clone())),
    //     badrsncode: common::get_base64(&hdinfo.tj_extcode.to_owned()),
    //     examconclusioncode: common::get_base64(&common::get_check_result(checkall.tj_typeid)),
    //     yszybcode: common::get_base64(&"".to_string()),
    //     zyjjzcode: common::get_base64(&"".to_string()),
    //     qtjbname: common::get_base64(&qtjb_name),
    //     enterprisename: common::get_base64(&"".to_string()),
    //   };
    //   idx += 1;
    //   contacthazards.push(cthdinfo);
    // }

    // let conclusion = CheckResultType::Normal as i32;

    // info!("剩余毒害因素:{:?}", &pthazards);
    // for val in pthazards.iter() {
    //   let hdinfo = hdinfos.iter().find(|&x| x.id == val.tj_hid);
    //   if hdinfo.is_none() {
    //     continue;
    //     //  return Err(anyhow!("不存在id为:{}的毒害因素", val.tj_hid));
    //   }
    //   let hdinfo = hdinfo.unwrap();
    //   if hdinfo.tj_extcode.is_empty() {
    //     return Err(anyhow!("毒害因素:{}的代码未匹配,请先匹配", &hdinfo.tj_hname));
    //   }
    //   let otherhd = otherhazards.iter().find(|&x| x == &hdinfo.tj_extcode);
    //   if otherhd.is_some() {
    //     otherhds.push(hdinfo.tj_hname.to_owned());
    //     // continue;
    //   }
    //   //是主要粉尘，并且已经存在，则忽略
    //   let is_main_dust = common::check_main_dust(&hdinfo.tj_extcode);
    //   if is_main_dust
    //     && contacthazards
    //       .iter()
    //       .find(|&x| x.badrsncode.eq_ignore_ascii_case(&common::get_base64(&hdinfo.tj_extcode)))
    //       .is_some()
    //   {
    //     continue;
    //   }
    //   let cthdinfo = ContactHazardFactor {
    //     rid: common::get_base64(&val.id.to_string()),
    //     badrsncode: common::get_base64(&hdinfo.tj_extcode.to_owned()),
    //     examconclusioncode: common::get_base64(&common::get_check_result(conclusion)),
    //     yszybcode: common::get_base64(&"".to_string()),
    //     zyjjzcode: common::get_base64(&"".to_string()),
    //     qtjbname: common::get_base64(&"".to_string()),
    //     enterprisename: common::get_base64(&"".to_string()),
    //   };

    //   contacthazards.push(cthdinfo);
    // }

    let info = ContactHazardFactorList {
      contacthazards,
      otherhazards: common::get_base64(&otherhds.join(",")),
    };
    Ok(info)
  }

  async fn trans_item_list(
    medinfo: &TjMedexaminfo,
    checkall: &TjCheckallnew,
    checkitems: &Vec<TjCheckiteminfo>,
    summaries: &Vec<TjTestsummary>,
    iteminfos: &Vec<TjIteminfo>,
    setting: &Settings,
    dbconn: &DbConnection,
  ) -> Result<ItemList> {
    let mut itemlist: Vec<Item> = Vec::new();
    let mut has_audiogram = false;
    let ret = SCdcItemunit::query_many("", &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let itemunits = ret.unwrap();

    for val in checkitems.iter() {
      if val.tj_combineflag == YesOrNo::Yes as i32 {
        //ignore combine item
        continue;
      }

      if val.tj_result.eq_ignore_ascii_case("未检") {
        info!("项目:{}未检", val.tj_itemname);
        continue;
      }

      if val.tj_deptid.as_str() == app::AUDIOGRAM_DEPT_ID {
        has_audiogram = true;
        continue;
      }
      if val.tj_result.is_empty() {
        continue;
      }
      let iteminfo = iteminfos.iter().find(|&x| x.tj_itemid == val.tj_itemid);
      if iteminfo.is_none() {
        continue;
      }
      let iteminfo = iteminfo.unwrap();
      if iteminfo.tj_extcode.is_empty() {
        continue;
      }
      let summary = summaries.iter().find(|&x| x.tj_testid == medinfo.tj_testid && x.tj_deptid == val.tj_deptid);
      if summary.is_none() {
        return Err(anyhow!("没有体检者{}在科室:{}的小结信息", &medinfo.tj_testid, &val.tj_deptid));
      }

      let jdgmode = common::get_judge_mode(iteminfo);
      let mut minval = "".to_string();
      let mut maxval = "".to_string();
      let mut itemunit = "".to_string();
      let mut rstdesc = "".to_string();

      if jdgmode == common::JudgeMode::DingLiang as i32 {
        // let unitinfo = dictservice.find_item_units(&iteminfo.tj_extcode).await;
        let unitinfo = itemunits
          .iter()
          .find(|&p| p.item_code.eq_ignore_ascii_case(&iteminfo.tj_extcode) && p.item_unitcode.eq_ignore_ascii_case(&iteminfo.tj_itemunit));
        if unitinfo.is_some() {
          itemunit = unitinfo.unwrap().item_unit.clone();
        } else {
          return Err(anyhow!("项目单位:{}的代码未配置", &iteminfo.tj_itemunit));
        }
        if !iteminfo.tj_lowvalue.trim().is_empty() {
          minval = iteminfo.tj_lowvalue.trim().to_string();
        } else {
          minval = "-99999".to_string();
        }
        if !iteminfo.tj_uppervalue.trim().is_empty() {
          maxval = iteminfo.tj_uppervalue.trim().to_string();
        } else {
          maxval = "99999".to_string();
        }
        // minval = iteminfo.tj_lowvalue.to_owned();
        // maxval = iteminfo.tj_uppervalue.to_owned();
        rstdesc = common::get_up_low_flag(val, iteminfo);
      }
      //get rst flag
      let ext_item_code = iteminfo.tj_extcode.to_owned();
      let flag = common::get_rst_flag(val.tj_abnormalflag, checkall.tj_drret, &ext_item_code, &setting.system.dr_deptid);

      let summary = summary.unwrap();
      let mut diag_rest = "".to_string();
      if jdgmode == common::JudgeMode::DingXing as i32 {
        diag_rest = summary.tj_summary.to_owned();
      }

      let dataversion = common::get_data_version(iteminfo, &val.tj_result);
      let mut checkdoctor = val.tj_checkdoctor.trim().to_owned();
      // info!("检查医生是:[{}]", &checkdoctor);
      // if checkdoctor.trim().is_empty() {
      //   checkdoctor = val.tj_recheckdoctor.trim().to_owned();
      // }
      if checkdoctor.trim().is_empty() {
        // info!("检查医生为空，查找小结里的医生信息");
        let sum = summaries.iter().find(|&x| x.tj_deptid.trim().eq_ignore_ascii_case(&val.tj_deptid.trim()));
        if sum.is_some() {
          // info!("找到科室小结信息：[{:?}]", &sum);
          let sum = sum.unwrap();
          checkdoctor = sum.tj_checkdoctor.trim().to_owned();
        }
        // info!("找不到科室小结信息");
      }
      if checkdoctor.is_empty() {
        return Err(anyhow!("项目:{}的检查医生为空，请检查项目输入", val.tj_itemname));
      }
      let mut checkdate = val.tj_checkdate;
      if checkdate <= 0 {
        checkdate = medinfo.tj_testdate;
        let sum = summaries.iter().find(|&x| x.tj_deptid.eq_ignore_ascii_case(&val.tj_deptid));
        if sum.is_some() {
          let sum = sum.unwrap();
          if sum.tj_checkdate < medinfo.tj_testdate {
            checkdate = medinfo.tj_testdate;
          } else {
            checkdate = sum.tj_checkdate;
          }
        }
      }
      if checkdate <= 0 {
        return Err(anyhow!("项目:{}的检查日期不对，请检查项目输入", val.tj_itemname));
      }
      let item = Item {
        rid: common::get_base64(&val.id.to_string()),
        itmcod: common::get_base64(&ext_item_code),
        msrunt: common::get_base64(&itemunit),
        itemstdvalue: common::get_base64(&common::get_reference_value(iteminfo)),
        result: common::get_base64(&val.tj_result.to_owned()),
        rgltag: common::get_base64(&common::get_qualified_flag(val.tj_abnormalflag).to_string()),
        rstdesc: common::get_base64(&rstdesc),
        iflack: common::get_base64(&"0".to_string()),
        chkdat: common::get_base64(&utils::timestamp_to_local_date(checkdate)),
        chkdoct: common::get_base64(&checkdoctor.to_owned()),
        jdgptn: common::get_base64(&jdgmode.to_string()),
        minval: common::get_base64(&minval),
        maxval: common::get_base64(&maxval),
        diagrest: common::get_base64(&diag_rest),
        rstflag: common::get_base64(&flag),
        dataversion: common::get_base64(&dataversion), //??????????????
      };
      itemlist.push(item);
    }

    if has_audiogram {
      let summary = summaries.iter().find(|&x| x.tj_testid == medinfo.tj_testid && x.tj_deptid == app::AUDIOGRAM_DEPT_ID.to_string());
      if summary.is_none() {
        return Err(anyhow!("不能找到电测听得科室小结"));
      }
      let aditems: Vec<TjCheckiteminfo> = checkitems
        .iter()
        .filter(|&x| x.tj_testid == medinfo.tj_testid && x.tj_deptid == app::AUDIOGRAM_DEPT_ID.to_string())
        .map(|x| x.clone())
        .collect();
      if aditems.len() <= 0 {
        return Err(anyhow!("不能找到电测听得科室检查项目"));
      }
      let ret = ZwxService::trans_audiogram_result(medinfo, &aditems, &summary.unwrap(), dbconn).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("上报电测听错误:{}", ret.as_ref().err().unwrap().to_string()));
      }
      let audiograms = ret.unwrap();
      for val in audiograms.itemlist.iter() {
        itemlist.push(val.clone());
      }
    }

    let info = ItemList { itemlist };
    Ok(info)
  }

  async fn trans_diagnosis(checkall: &TjCheckallnew) -> Result<CheckResultList> {
    let mut checkresults: Vec<CheckResult> = Vec::new();
    let result_type = checkall.tj_typeid;
    // if checkall.tj_othconclusion == "其他疾病或异常".to_string() && (checkall.tj_typeid == CheckResultType::Normal as i32 || checkall.tj_typeid == CheckResultType::Addional as i32)
    // {
    //     result_type = CheckResultType::Other as i32;
    // }
    let checkresult = CheckResult {
      rid: common::get_base64(&checkall.tj_testid.to_string()),
      bhkrstcode: common::get_base64(&common::get_check_result(result_type)),
    };
    checkresults.push(checkresult);
    let info = CheckResultList { checkresults };
    Ok(info)
  }

  async fn trans_audiogram_result(medinfo: &TjMedexaminfo, checkitems: &Vec<TjCheckiteminfo>, summary: &TjTestsummary, dbconn: &DbConnection) -> Result<ItemList> {
    let ret = TjAudiogramdetail::query(&medinfo.tj_testid, &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("获取电测听结果错我：{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut audio_detail = ret.unwrap();
    if audio_detail.len() <= 0 {
      if medinfo.tj_isrecheck == constant::YesOrNo::Yes as i32 {
        if medinfo.tj_oldtestid.is_empty() {
          return Err(anyhow!("整理电测听时，复查，原体检号为空"));
        }
        let ret = TjAudiogramdetail::query(&medinfo.tj_oldtestid, &dbconn.get_connection()).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("电测听结果为空"));
        }
        audio_detail = ret.unwrap();
      }
    }
    if audio_detail.len() <= 0 {
      return Err(anyhow!("电测听结果为空"));
    }

    let audio_result = AudiogramSvc::compute_audiogram_result(&medinfo.tj_testid, &audio_detail);
    // let audio_result: TjAudiogramresult; // = TjAudiogramresult { ..Default::default() };
    // let ret = TjAudiogramresult::query( &medinfo.tj_testid,&dbconn.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let ret = ret.unwrap();
    // if ret.as_ref().is_none() {
    //   // return Err(anyhow!("没有电测听结果"));
    //   if medinfo.tj_isrecheck == constant::YesOrNo::Yes as i32 {
    //     if medinfo.tj_oldtestid.is_empty() {
    //       return Err(anyhow!("整理电测听时，复查，原体检号为空"));
    //     }
    //     let ret = TjAudiogramresult::query( &medinfo.tj_oldtestid,&dbconn.get_connection()).await;
    //     if ret.as_ref().is_err() {
    //       return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    //     }
    //     let ret = ret.unwrap();
    //     if ret.as_ref().is_none() {
    //       return Err(anyhow!("电测听结果为空"));
    //     }
    //     audio_result = ret.unwrap();
    //   } else {
    //     return Err(anyhow!("电测听结果为空"));
    //   }
    // } else {
    //   audio_result = ret.unwrap();
    // }
    // let audio_result = audio_result.unwrap();

    let mut doctor = checkitems[0].tj_checkdoctor.trim().to_string(); //.to_owned();
    if doctor.is_empty() {
      // doctor = &summary.tj_doctor.trim().to_owned();
      doctor = summary.tj_doctor.trim().to_string();
    }

    if doctor.is_empty() {
      return Err(anyhow!("项目:电测听的检查医生为空，请检查项目输入"));
    }

    let mut chkdate = checkitems[0].tj_checkdate;
    if chkdate <= 0 {
      chkdate = summary.tj_checkdate;
    }
    if chkdate <= 0 {
      return Err(anyhow!("项目:电测听的检查检查日期有误，请检查项目输入"));
    }
    let mut itemlist: Vec<Item> = Vec::new();
    for val in audio_detail.into_iter() {
      if val.tj_result <= -100 {
        continue;
      }
      let addetail = common::get_audio_detail(&val);
      if addetail.is_none() {
        error!("不能找到频率:{},耳朵:{},传导方式:{}的电测听配置结果", val.tj_freq, val.tj_ear, val.tj_adtype);
        continue;
      }
      let addetail = addetail.unwrap();

      let item = ZwxService::get_audio_gram_item(medinfo, val.tj_result as f32, &doctor, chkdate, &summary.tj_suggestion, &addetail);
      itemlist.push(item);
    }

    {
      let syp = audio_result.tj_avgsyp;
      if syp > -100 as f32 {
        //do
        let retinfo = common::get_audio_result(common::AdResultType::SYP as i32);
        if retinfo.is_some() {
          let item = ZwxService::get_audio_gram_item(medinfo, syp, &doctor, chkdate, &summary.tj_suggestion, &retinfo.unwrap());
          itemlist.push(item);
        }
      }
      let zyp = audio_result.tj_avgzyp;
      if zyp > -100 as f32 {
        //do
        let retinfo = common::get_audio_result(common::AdResultType::ZYP as i32);
        if retinfo.is_some() {
          let item = ZwxService::get_audio_gram_item(medinfo, zyp, &doctor, chkdate, &summary.tj_suggestion, &retinfo.unwrap());
          itemlist.push(item);
        }
      }
      let yyp = audio_result.tj_avgyyp;
      if yyp > -100 as f32 {
        //do
        let retinfo = common::get_audio_result(common::AdResultType::YYP as i32);
        if retinfo.is_some() {
          let item = ZwxService::get_audio_gram_item(medinfo, yyp, &doctor, chkdate, &summary.tj_suggestion, &retinfo.unwrap());
          itemlist.push(item);
        }
      }
      let sgp = audio_result.tj_avgsgp;
      if sgp > -100 as f32 {
        //do
        let retinfo = common::get_audio_result(common::AdResultType::SGP as i32);
        if retinfo.is_some() {
          let item = ZwxService::get_audio_gram_item(medinfo, sgp, &doctor, chkdate, &summary.tj_suggestion, &retinfo.unwrap());
          itemlist.push(item);
        }
      }

      let ret = audio_result.tj_avgyty;
      if ret > -100 as f32 {
        //do
        let retinfo = common::get_audio_result(common::AdResultType::YJQ as i32);
        if retinfo.is_some() {
          let item = ZwxService::get_audio_gram_item(medinfo, ret, &doctor, chkdate, &summary.tj_suggestion, &retinfo.unwrap());
          itemlist.push(item);
        }
      }

      let ret = audio_result.tj_avgzty;
      if ret > -100 as f32 {
        //do
        let retinfo = common::get_audio_result(common::AdResultType::ZJQ as i32);
        if retinfo.is_some() {
          let item = ZwxService::get_audio_gram_item(medinfo, ret, &doctor, chkdate, &summary.tj_suggestion, &retinfo.unwrap());
          itemlist.push(item);
        }
      }
    }

    let info = ItemList { itemlist };
    Ok(info)
  }
  fn get_audio_gram_item(medinfo: &TjMedexaminfo, chkresult: f32, doctor: &String, chkdate: i64, _suggestion: &String, addetail: &AudioDetail) -> Item {
    let mut abnormalflag = 0;
    let mut uplow = 0;
    if chkresult >= addetail.highvalue as f32 {
      uplow = 2;
      abnormalflag = 1;
    } else if chkresult <= addetail.lowvalue as f32 {
      uplow = 1;
      abnormalflag = 1;
    }
    let mut item_unit = "1002";
    if addetail.extcode.eq_ignore_ascii_case("13150") || addetail.extcode.eq_ignore_ascii_case("13149") {
      item_unit = "1046"
    }
    let dataversion = "1001".to_string();
    Item {
      rid: common::get_base64(&format!("{}{}", medinfo.id, &addetail.extcode)),
      itmcod: common::get_base64(&addetail.extcode.to_owned()),
      msrunt: common::get_base64(&item_unit.to_string()),
      itemstdvalue: common::get_base64(&format!("{}-{}", addetail.lowvalue, addetail.highvalue)),
      result: common::get_base64(&format!("{}", chkresult)),
      rgltag: common::get_base64(&common::get_qualified_flag(abnormalflag).to_string()),
      rstdesc: common::get_base64(&format!("{}", uplow)),
      iflack: common::get_base64(&"0".to_string()),
      chkdat: common::get_base64(&utils::timestamp_to_local_date(chkdate)),
      chkdoct: common::get_base64(&doctor.clone()),
      jdgptn: common::get_base64(&"2".to_string()),
      minval: common::get_base64(&format!("{}", addetail.lowvalue)),
      maxval: common::get_base64(&format!("{}", addetail.highvalue)),
      diagrest: common::get_base64(&"".to_string()),
      rstflag: common::get_base64(&"".to_string()),
      dataversion: common::get_base64(&dataversion),
    }
  }

  async fn trans_healthy_survey(medinfo: &TjMedexaminfo, dbconn: &DbConnection) -> Result<Option<QuestionNaire>> {
    let mut testid = medinfo.tj_testid.to_string();
    let mut medinfo = medinfo.to_owned();
    if medinfo.tj_isrecheck == constant::YesOrNo::Yes as i32 {
      loop {
        //是复查
        if medinfo.tj_oldtestid.is_empty() {
          return Err(anyhow!("该体检信息是复查体检，但是没有原体检号"));
        }
        let ret = TjMedexaminfo::query(&medinfo.tj_oldtestid, &dbconn.get_connection()).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        let ret = ret.unwrap();
        if ret.is_none() {
          return Err(anyhow!("不能找到原体检号为:{}的体检信息", &medinfo.tj_oldtestid));
        }
        medinfo = ret.unwrap();
        if medinfo.tj_isrecheck == constant::YesOrNo::Yes as i32 {
          continue;
        } else {
          testid = medinfo.tj_testid.to_owned();
          break;
        }
      }
    }
    let ret = TjHealthyinfo::query(&testid, &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let ret = ret.unwrap();
    if ret.as_ref().is_none() {
      return Ok(None);
    }
    let healthyinfo = ret.unwrap();
    info!("问卷信息：{:#?}", &healthyinfo);
    let mut info = QuestionNaire {
      rid: common::get_base64(&medinfo.id.to_string()),
      mnrage: common::get_base64(&healthyinfo.tj_menarcheage.clone()),
      mns: common::get_base64(&healthyinfo.tj_period.clone()),
      cyc: common::get_base64(&healthyinfo.tj_cycle.clone()),
      mnlage: common::get_base64(&healthyinfo.tj_menopauseage.clone()),
      isxmns: common::get_base64(&"".to_string()),
      chldqty: common::get_base64(&healthyinfo.tj_childrennum.clone()),
      abrqty: common::get_base64(&healthyinfo.tj_abortionnum.clone()),
      slnkqty: common::get_base64(&healthyinfo.tj_prematurenum.clone()),
      stlqty: common::get_base64(&healthyinfo.tj_stillbirthnum.clone()),
      trsqty: common::get_base64(&healthyinfo.tj_abnormalnum.clone()),
      chldhthcnd: common::get_base64(&healthyinfo.tj_childrenhealthy.clone()),
      mrydat: common::get_base64(&"".to_string()),
      cplrdtcnd: common::get_base64(&"".to_string()),
      cplprfhthcnd: common::get_base64(&"".to_string()),
      smksta: common::get_base64(&(healthyinfo.tj_smoke - 3).to_string()),
      smkdayble: common::get_base64(&healthyinfo.tj_smokenum.clone()),
      smkyerqty: common::get_base64(&healthyinfo.tj_smokeyear.clone()),
      smkmthqty: common::get_base64(&healthyinfo.tj_smokemonth.to_string()),
      winsta: common::get_base64(&healthyinfo.tj_drink.to_string()),
      windaymlx: common::get_base64(&healthyinfo.tj_drinknum.clone()),
      winyerqty: common::get_base64(&healthyinfo.tj_drinkyear.clone()),
      jzs: common::get_base64(&healthyinfo.tj_families.clone()),
      grs: common::get_base64(&"".to_string()),
      oth: common::get_base64(&"".to_string()),
    };
    let ret = TjMarriagehistory::query_many(&testid, &dbconn.get_connection()).await;
    if ret.is_ok() {
      let marriage_info = ret.unwrap();
      if marriage_info.len() > 0 {
        info.mrydat = common::get_base64(&timeutil::get_iso_date(marriage_info[0].tj_date));
        info.cplrdtcnd = common::get_base64(&marriage_info[0].tj_spouseradiation);
        info.cplprfhthcnd = common::get_base64(&marriage_info[0].tj_spouseoccu);
      }
    }
    Ok(Some(info))
  }

  async fn trans_occupation_histories(medinfo: &TjMedexaminfo, dbconn: &DbConnection) -> Result<Option<OccupationHistoryList>> {
    let mut ocuhistory: Vec<OccupationHistory> = Vec::new();
    let ret = TjOccupationhistory::query_many(&medinfo.tj_testid, &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let ocuhises = ret.unwrap();
    if ocuhises.len() <= 0 {
      return Ok(None);
    }
    // let ret = TjHealthyinfo::query(&medinfo.tj_testid, &dbconn.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    // }
    // let ret = ret.unwrap();
    // if ret.is_none() {
    //   return Ok(None);
    // }
    // let healthyinfo = ret.unwrap();
    let ret = TjStaffadmin::query_by_id(medinfo.tj_recorder.parse::<i64>().unwrap_or_default(), &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let ret = ret.unwrap();
    if ret.is_none() {
      return Ok(None);
    }
    let staffinfo = ret.unwrap();
    for val in ocuhises.iter() {
      let mut ocustdate = "".to_string();
      let mut ocuenddate = "".to_string();
      if val.tj_startdate > 0 {
        let startdate = val.tj_startdate.to_string();
        if startdate.len() >= 8 {
          ocustdate = format!("{}.{}", &startdate[..4], &startdate[4..6]);
        }
      }
      if val.tj_enddate > 0 {
        let enddate = val.tj_enddate.to_string();
        if enddate.len() >= 8 {
          ocuenddate = format!("{}.{}", &enddate[0..4], &enddate[4..6]);
        }
      }
      let mut statstdate = "".to_string();
      if !ocustdate.is_empty() {
        statstdate = format!("{}", ocustdate)
      }
      if !ocuenddate.is_empty() {
        statstdate = format!("{}-{}", statstdate, ocuenddate);
      }

      let ocuhis = OccupationHistory {
        rid: common::get_base64(&medinfo.id.to_string()),
        histype: common::get_base64(&"2".to_string()),
        num: common::get_base64(&"".to_string()),
        stastpdate: common::get_base64(&statstdate),
        unitname: common::get_base64(&val.tj_corpname.clone()),
        department: common::get_base64(&val.tj_workshop.clone()),
        worktype: common::get_base64(&val.tj_worktype.clone()),
        prfraysrt: common::get_base64(&val.tj_harmfulname.clone()),
        prfwrklod: common::get_base64(&val.tj_raddaynum.clone()),
        prfshnvlu: common::get_base64(&val.tj_radtotalnum.clone()),
        prfexcshn: common::get_base64(&val.tj_radoverdose.clone()),
        prfraysrt2: common::get_base64(&val.tj_radtype.clone()),
        prfraysrtcods: common::get_base64(&val.tj_radexposure.clone()),
        fsszl: common::get_base64(&val.tj_radtype.clone()),
        defendstep: common::get_base64(&val.tj_protective.clone()),
        chkdat: common::get_base64(&utility::timeutil::format_timestamp(medinfo.tj_testdate)),
        chkdoct: common::get_base64(&staffinfo.tj_staffname),
      };
      ocuhistory.push(ocuhis);
    }
    let info = OccupationHistoryList { ocuhistory };
    Ok(Some(info))
  }

  async fn trans_symptom_list(_medinfo: &TjMedexaminfo, _dbconn: &DbConnection) -> Result<Option<SymptomList>> {
    return Ok(None);
    // let info = SymptomList { ..Default::default() };
    // Ok(info)
  }

  async fn trans_disease_history_list(medinfo: &TjMedexaminfo, dbconn: &DbConnection) -> Result<Option<DiseaseHistoryList>> {
    let mut diseasehistories: Vec<DiseaseHistory> = Vec::new();
    let ret = TjDiseasehistory::query_many(&medinfo.tj_testid, &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let dishistories = ret.unwrap();
    if dishistories.len() <= 0 {
      return Ok(None);
    }
    for val in dishistories.iter() {
      let dishisoty = DiseaseHistory {
        rid: common::get_base64(&val.id.to_string()),
        hstnam: common::get_base64(&val.tj_disname.clone()),
        hstdat: common::get_base64(&timeutil::get_iso_date(val.tj_date)),
        hstunt: common::get_base64(&val.tj_orgname.clone()),
        hstcruprc: common::get_base64(&val.tj_curve.clone()),
        hstlps: common::get_base64(&val.tj_finalcode.clone()),
      };
      diseasehistories.push(dishisoty);
    }
    let info = DiseaseHistoryList { diseasehistories };
    Ok(Some(info))
  }

  fn zip_to_file(content: &[u8], testid: &str, filedir: &str) -> Result<String> {
    let filename = format!("{}/{}.zip", filedir, &testid); //format!("{}/{}", &setting.system.file_path,

    let path = std::path::Path::new(&filename);
    let file = std::fs::File::create(&path).unwrap();

    let mut zip = zip::ZipWriter::new(file);

    let xml_file = format!("{}.xml", &testid);

    // match cfg!(target_os = "windows") {
    //   true => {
    //     let options = zip::write::SimpleFileOptions::default();
    //     // let options = zip::write::FileOptions::default(); //Default::default();
    //     let ret = zip.start_file(xml_file.as_str(), options);
    //     if ret.as_ref().is_err() {
    //       return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    //     }
    //   }
    //   false => {
    //     // let options = zip::write::SimpleFileOptions::default();
    //     // let options = Default::default();
    //     let ret = zip.start_file(xml_file.as_str(), Default::default());
    //     if ret.as_ref().is_err() {
    //       return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    //     }
    //   }
    // }

    let options = zip::write::SimpleFileOptions::default(); //Default::default();
    let ret = zip.start_file(xml_file.as_str(), options);
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let ret = zip.write_all(content);
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }

    let ret = zip.finish();
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    // info!("FINAL:{}", &filename);
    Ok(filename.to_string())
  }

  // fn write_to_file(content: &HealthyData, filedir: &str) {
  //   let seconfig = yaserde::ser::Config {
  //     perform_indent: false,
  //     write_document_declaration: false,
  //     indent_string: None,
  //   };
  //   let persons = content.personlist.persons.clone();
  //   // let datas  = new_content.personlist;
  //   let mut items: Vec<Item> = Vec::new();
  //   for val in persons.iter() {
  //     let person_item = val.items.itemlist.clone();
  //     for pitem in person_item.iter() {
  //       let decode_item = Item {
  //         rid: common::get_base64_decode(&pitem.rid),
  //         itmcod: common::get_base64_decode(&pitem.itmcod),
  //         msrunt: common::get_base64_decode(&pitem.msrunt),
  //         itemstdvalue: common::get_base64_decode(&pitem.itemstdvalue),
  //         result: common::get_base64_decode(&pitem.result),
  //         rgltag: common::get_base64_decode(&pitem.rgltag),
  //         rstdesc: common::get_base64_decode(&pitem.rstdesc),
  //         iflack: common::get_base64_decode(&pitem.iflack),
  //         chkdat: common::get_base64_decode(&pitem.chkdat),
  //         chkdoct: common::get_base64_decode(&pitem.chkdoct),
  //         jdgptn: common::get_base64_decode(&pitem.jdgptn),
  //         minval: common::get_base64_decode(&pitem.minval),
  //         maxval: common::get_base64_decode(&pitem.maxval),
  //         diagrest: common::get_base64_decode(&pitem.diagrest),
  //         rstflag: common::get_base64_decode(&pitem.rstflag),
  //         dataversion: common::get_base64_decode(&pitem.dataversion),
  //       };
  //       items.push(decode_item);
  //     }
  //   }
  //   let itemlist = ItemList { itemlist: items };
  //   let ret = to_string_with_config(&itemlist, &seconfig);
  //   if ret.as_ref().is_err() {
  //     return;
  //   }
  //   let item_content = ret.unwrap();
  //   let path = format!("{}/{}.xml", filedir, utility::timeutil::current_timestamp());
  //   let ret = std::fs::write(path, &item_content);
  //   if ret.as_ref().is_err() {
  //     return;
  //     //     return Err(anyhow!("write to file error:{}", ret.as_ref().err().unwrap().to_string()));
  //   }
  // }
}
