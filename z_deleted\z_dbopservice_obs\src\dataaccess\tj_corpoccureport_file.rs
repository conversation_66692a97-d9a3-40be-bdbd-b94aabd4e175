use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub struct TjCorpoccureportFile {
  pub id: i64,
  pub report_id: i32,
  pub create_date: i64,
  pub report_type: i8,
  pub file_url: String,
  pub down_load: i8,
  pub file_type: i8,
  pub report_no: String,
  pub pub_file_url: String,
  pub modify_date: i64,
  pub is_push: i8,
}
crud!(TjCorpoccureportFile {}, "tj_corpoccureport_file");
