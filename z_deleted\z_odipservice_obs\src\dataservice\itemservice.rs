use anyhow::{anyhow, Result};
use datacontroller::entities::{tj_checkiteminfo::TjCheckiteminfo, tj_iteminfo::TjIteminfo};

use crate::constant::ValueType;

pub struct ItemService;

impl ItemService {
  pub fn get_item_check_result(checkitem: &TjCheckiteminfo, iteminfo: &TjIteminfo) -> Result<String> {
    if iteminfo.tj_valuetype.eq_ignore_ascii_case(ValueType::DingXing.to_string().as_str()) {
      //定性
      if checkitem.tj_abnormalflag == 1 {
        return Ok("异常".to_string());
      } else {
        return Ok("正常".to_string());
      }
    } else if iteminfo.tj_valuetype.eq_ignore_ascii_case(ValueType::DingLiang.to_string().as_str()) {
      let ret = checkitem.tj_result.to_owned().parse::<f64>(); //.unwrap_or_default();
      if ret.as_ref().is_err() {
        return Err(anyhow!(
          "项目:{},名称:{}的结果值{}有误，请修正（定量类型结果需要是数值)",
          iteminfo.tj_itemid,
          iteminfo.tj_itemname,
          checkitem.tj_result
        ));
      }
      let val_ret = ret.unwrap_or_default();

      //定量
      if checkitem.tj_abnormalflag == 1 {
        if iteminfo.tj_reftype.eq_ignore_ascii_case("x_y") || iteminfo.tj_reftype.eq_ignore_ascii_case("x-y") {
          let min = iteminfo.tj_lowvalue.to_owned().parse::<f64>();
          if min.as_ref().is_err() {
            return Err(anyhow!(
              "项目:{},名称:{}的最小值{}有误，请修正",
              iteminfo.tj_itemid,
              iteminfo.tj_itemname,
              iteminfo.tj_lowvalue
            ));
          }
          let min_d = min.unwrap_or_default();
          let max = iteminfo.tj_uppervalue.to_owned().parse::<f64>();
          if max.as_ref().is_err() {
            return Err(anyhow!(
              "项目:{},名称:{}的最大值{}有误，请修正",
              iteminfo.tj_itemid,
              iteminfo.tj_itemname,
              iteminfo.tj_uppervalue
            ));
          }
          let max_d = max.unwrap_or_default();

          if val_ret < min_d {
            return Ok("偏低".to_string());
          } else if val_ret > max_d {
            return Ok("偏高".to_string());
          } else {
            return Ok("正常".to_string());
          }
        } else if iteminfo.tj_reftype.starts_with(">") {
          let max = iteminfo.tj_uppervalue.to_owned().parse::<f64>();
          if max.as_ref().is_err() {
            return Err(anyhow!(
              "项目:{},名称:{}的最大值{}有误，请修正",
              iteminfo.tj_itemid,
              iteminfo.tj_itemname,
              iteminfo.tj_uppervalue
            ));
          }
          let max_d = max.unwrap_or_default();
          if val_ret < max_d {
            return Ok("偏低".to_string());
          } else {
            return Ok("正常".to_string());
          }
        } else if iteminfo.tj_reftype.starts_with("<") {
          let min = iteminfo.tj_lowvalue.to_owned().parse::<f64>();
          if min.as_ref().is_err() {
            return Err(anyhow!(
              "项目:{},名称:{}的最小值{}有误，请修正",
              iteminfo.tj_itemid,
              iteminfo.tj_itemname,
              iteminfo.tj_lowvalue
            ));
          }
          let min_d = min.unwrap_or_default();
          if val_ret > min_d {
            return Ok("偏高".to_string());
          } else {
            return Ok("正常".to_string());
          }
        } else {
          return Err(anyhow!("项目:{}-{}的异常判断方式未知", iteminfo.tj_itemid, iteminfo.tj_itemname));
        }
      } else {
        return Ok("正常".to_string());
      }
    }
    Ok(checkitem.tj_result.to_owned())
  }
}
