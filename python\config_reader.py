# coding=utf-8

"""
Configuration Reader for Report Server

This module provides a robust configuration reader that can handle TOML files
with fallback options for PyInstaller compatibility.
"""

import os
import sys
import json
import configparser
from pathlib import Path
from typing import Dict, Any, Optional


class ConfigReader:
    """Configuration reader with multiple format support"""
    
    def __init__(self):
        self.config_data = {}
        self.config_loaded = False
    
    def load_config(self, config_paths: list = None) -> Dict[str, Any]:
        """Load configuration from various sources"""
        if config_paths is None:
            config_paths = [
                "./config/nodipexam.toml",
                "../config/nodipexam.toml", 
                "../../config/nodipexam.toml"
            ]
        
        # Try TOML first
        for config_path in config_paths:
            if self._try_load_toml(config_path):
                return self.config_data
        
        # Try JSON fallback
        for config_path in config_paths:
            json_path = config_path.replace('.toml', '.json')
            if self._try_load_json(json_path):
                return self.config_data
        
        # Try INI fallback
        for config_path in config_paths:
            ini_path = config_path.replace('.toml', '.ini')
            if self._try_load_ini(ini_path):
                return self.config_data
        
        # If no config found, try to create a template
        self._create_config_template(config_paths[0])
        
        raise FileNotFoundError(f"Could not find configuration file in any of: {config_paths}")
    
    def _try_load_toml(self, config_path: str) -> bool:
        """Try to load TOML configuration"""
        if not os.path.exists(config_path):
            return False
        
        try:
            # Try tomli first
            import tomli
            with open(config_path, "rb") as f:
                self.config_data = tomli.load(f)
                self.config_loaded = True
                print(f"✓ Loaded TOML config from: {config_path}")
                return True
        except ImportError:
            # tomli not available, try tomllib (Python 3.11+)
            try:
                import tomllib
                with open(config_path, "rb") as f:
                    self.config_data = tomllib.load(f)
                    self.config_loaded = True
                    print(f"✓ Loaded TOML config from: {config_path} (using tomllib)")
                    return True
            except ImportError:
                # No TOML support available
                print(f"⚠ TOML support not available, trying other formats...")
                return False
        except Exception as e:
            print(f"⚠ Failed to load TOML from {config_path}: {e}")
            return False
    
    def _try_load_json(self, config_path: str) -> bool:
        """Try to load JSON configuration"""
        if not os.path.exists(config_path):
            return False
        
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                self.config_data = json.load(f)
                self.config_loaded = True
                print(f"✓ Loaded JSON config from: {config_path}")
                return True
        except Exception as e:
            print(f"⚠ Failed to load JSON from {config_path}: {e}")
            return False
    
    def _try_load_ini(self, config_path: str) -> bool:
        """Try to load INI configuration"""
        if not os.path.exists(config_path):
            return False
        
        try:
            config = configparser.ConfigParser()
            config.read(config_path, encoding="utf-8")
            
            # Convert INI to dict structure
            self.config_data = {}
            for section_name in config.sections():
                self.config_data[section_name] = dict(config[section_name])
            
            self.config_loaded = True
            print(f"✓ Loaded INI config from: {config_path}")
            return True
        except Exception as e:
            print(f"⚠ Failed to load INI from {config_path}: {e}")
            return False
    
    def _create_config_template(self, config_path: str):
        """Create a configuration template"""
        template_data = {
            "database": {
                "uri": "mysql+pymysql://username:password@localhost:3306/database_name"
            }
        }
        
        # Create directory if it doesn't exist
        config_dir = os.path.dirname(config_path)
        if config_dir:
            os.makedirs(config_dir, exist_ok=True)
        
        # Try to create JSON template (most compatible)
        json_path = config_path.replace('.toml', '.json')
        try:
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(template_data, f, indent=2)
            print(f"✓ Created config template: {json_path}")
            print("Please edit this file with your database configuration.")
        except Exception as e:
            print(f"⚠ Failed to create config template: {e}")
    
    def get_database_uri(self) -> Optional[str]:
        """Get database URI from configuration"""
        if not self.config_loaded:
            return None
        
        try:
            return self.config_data.get("database", {}).get("uri")
        except Exception:
            return None
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key"""
        if not self.config_loaded:
            return default
        
        try:
            keys = key.split('.')
            value = self.config_data
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default


# Global config reader instance
config_reader = ConfigReader()


def load_database_config() -> Optional[str]:
    """Load database configuration with fallback support"""
    try:
        config_data = config_reader.load_config()
        return config_reader.get_database_uri()
    except Exception as e:
        print(f"Warning: Could not load database config: {e}")
        return None


def get_config_value(key: str, default: Any = None) -> Any:
    """Get configuration value with fallback support"""
    try:
        if not config_reader.config_loaded:
            config_reader.load_config()
        return config_reader.get(key, default)
    except Exception:
        return default
