#!/usr/bin/env python3
# coding=utf-8

"""
Build script for Report Server

This script automates the building and packaging of the Report Server
for different deployment scenarios.
"""

import os
import sys
import shutil
import subprocess
import argparse
from pathlib import Path


class ReportServerBuilder:
    """Builder for Report Server deployment packages"""

    def __init__(self, build_dir="build", dist_dir="dist"):
        self.build_dir = Path(build_dir)
        self.dist_dir = Path(dist_dir)
        self.project_root = Path(__file__).parent.parent
        self.python_dir = Path(__file__).parent

    def clean(self):
        """Clean build and dist directories"""
        print("Cleaning build directories...")

        for directory in [self.build_dir, self.dist_dir]:
            if directory.exists():
                shutil.rmtree(directory)
                print(f"Removed {directory}")

        # Clean PyInstaller cache
        pycache_dirs = list(self.python_dir.rglob("__pycache__"))
        for cache_dir in pycache_dirs:
            shutil.rmtree(cache_dir)
            print(f"Removed {cache_dir}")

        print("Clean completed.")

    def check_dependencies(self):
        """Check if all required dependencies are installed"""
        print("Checking dependencies...")

        # Map package names to their import names
        required_packages = {
            'flask': 'flask',
            'flask-cors': 'flask_cors',
            'reportlab': 'reportlab',
            'sqlalchemy': 'sqlalchemy',
            'python-docx': 'docx',  # python-docx package imports as 'docx'
            'tomli': 'tomli',
            'pymysql': 'pymysql'
        }

        missing_packages = []
        for package_name, import_name in required_packages.items():
            try:
                __import__(import_name)
                print(f"✓ {package_name} (imports as {import_name})")
            except ImportError:
                missing_packages.append(package_name)
                print(f"✗ {package_name} (should import as {import_name})")

        if missing_packages:
            print(f"\nMissing packages: {', '.join(missing_packages)}")
            print("Install with: pip install -r requirements.txt")
            return False

        print("All dependencies satisfied.")
        return True

    def build_executable(self):
        """Build standalone executable using PyInstaller"""
        print("Building standalone executable...")

        # Check if PyInstaller is available
        try:
            import PyInstaller
        except ImportError:
            print("PyInstaller not found. Installing...")
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"])

        # Try simple approach first
        print("Attempting simple build approach...")
        if self._build_simple_executable():
            return True

        print("Simple build failed, trying advanced approach with custom spec...")
        return self._build_advanced_executable()

    def _build_simple_executable(self):
        """Try building with a simple approach using minimal dependencies"""
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--name", "reportserver",
            "--distpath", str(self.dist_dir),
            "--workpath", str(self.build_dir),
            "--specpath", str(self.build_dir),
            # NO data files - fonts and config must be provided separately
            # Essential hidden imports only
            "--hidden-import", "pymysql",
            "--hidden-import", "docx",
            "--hidden-import", "docx.shared",
            "--hidden-import", "docx.enum",
            "--hidden-import", "tomli",
            "--hidden-import", "flask_cors",
            "--hidden-import", "reportlab",
            "--hidden-import", "reportlab.pdfbase",
            "--hidden-import", "reportlab.pdfbase.ttfonts",
            "--hidden-import", "reportlab.lib",
            "--hidden-import", "reportlab.platypus",
            "--hidden-import", "sqlalchemy",
            "--hidden-import", "sqlalchemy.dialects",
            "--hidden-import", "sqlalchemy.dialects.mysql",
            "--hidden-import", "sqlalchemy.dialects.mysql.pymysql",
            "--hidden-import", "sqlalchemy.orm",
            "--hidden-import", "werkzeug",
            "--hidden-import", "jinja2",
            "--hidden-import", "click",
            "--hidden-import", "itsdangerous",
            "--hidden-import", "markupsafe",
            # Exclude problematic modules
            "--exclude-module", "mypy",
            "--exclude-module", "mypyc",
            "--exclude-module", "typing_extensions._mypyc",
            "--exclude-module", "tkinter",
            "--exclude-module", "matplotlib",
            "--exclude-module", "numpy",
            "--exclude-module", "scipy",
            "--exclude-module", "pandas",
            # Use simple entry point
            "simple_server.py"
        ]

        print(f"Running simple build: {' '.join(cmd[:10])}...")
        result = subprocess.run(cmd, cwd=self.python_dir, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"✓ Simple executable built successfully: {self.dist_dir}/reportserver.exe")
            print("Note: Config and font files must be provided in the same directory structure")
            return True
        else:
            print("✗ Simple build failed")
            if result.stderr:
                print(f"Error: {result.stderr[:500]}...")
            return False

    def _build_advanced_executable(self):
        """Build using advanced custom spec file approach"""
        # Create a custom spec file to handle mypyc issues
        spec_content = self._create_custom_spec()
        spec_file = self.python_dir / "reportserver_custom.spec"

        with open(spec_file, "w") as f:
            f.write(spec_content)

        print("Created custom spec file to handle mypyc issues")

        # Build using the custom spec file
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--distpath", str(self.dist_dir),
            "--workpath", str(self.build_dir),
            str(spec_file)
        ]

        print(f"Running advanced build: {' '.join(cmd)}")
        result = subprocess.run(cmd, cwd=self.python_dir)

        if result.returncode == 0:
            print(f"✓ Advanced executable built successfully: {self.dist_dir}/reportserver.exe")
            return True
        else:
            print("✗ Advanced build failed")
            return False

    def _create_custom_spec(self):
        """Create a custom PyInstaller spec file that handles mypyc issues"""
        return f'''# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# Get paths
project_root = Path(r"{self.project_root}")
python_dir = Path(r"{self.python_dir}")

# Data files to include
datas = [
    (str(project_root / 'config'), 'config'),
    (str(project_root / 'fonts'), 'fonts'),
    (str(project_root / 'images'), 'images'),
]

# Hidden imports - modules that PyInstaller might miss
hiddenimports = [
    'pymysql',
    'reportlab',
    'reportlab.pdfbase',
    'reportlab.pdfbase.ttfonts',
    'reportlab.lib',
    'reportlab.lib.utils',
    'reportlab.platypus',
    'docx',
    'docx.shared',
    'docx.enum',
    'tomli',
    'flask_cors',
    'werkzeug',
    'werkzeug.security',
    'werkzeug.serving',
    'jinja2',
    'click',
    'itsdangerous',
    'markupsafe',
    'blinker',
    'sqlalchemy',
    'sqlalchemy.dialects',
    'sqlalchemy.dialects.mysql',
    'sqlalchemy.dialects.mysql.pymysql',
    'sqlalchemy.orm',
    'sqlalchemy.ext',
    'sqlalchemy.ext.declarative',
]

# Modules to exclude (problematic or unnecessary)
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'scipy',
    'pandas',
    'IPython',
    'jupyter',
    'mypy',
    'mypyc',
    'pytest',
    'setuptools',
    'distutils',
    'test',
    'tests',
    'unittest',
]

a = Analysis(
    ['simple_server.py'],
    pathex=[str(python_dir)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# Remove problematic modules that cause mypyc issues
def remove_mypyc_modules(analysis):
    """Remove modules that cause mypyc issues"""
    problematic_patterns = [
        '_mypyc',
        'mypy',
        'typing_extensions._mypyc',
        '__mypyc',
    ]

    # Filter out problematic modules from pure Python modules
    analysis.pure = [
        (name, path, typecode) for name, path, typecode in analysis.pure
        if not any(pattern in name for pattern in problematic_patterns)
    ]

    # Filter out problematic binaries
    analysis.binaries = [
        (name, path, typecode) for name, path, typecode in analysis.binaries
        if not any(pattern in name for pattern in problematic_patterns)
    ]

    print(f"Filtered out problematic modules. Remaining pure modules: {{len(analysis.pure)}}")

remove_mypyc_modules(a)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='reportserver',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''

    def create_package(self, package_type="zip"):
        """Create deployment package"""
        print(f"Creating {package_type} package...")

        package_dir = self.dist_dir / "reportserver_package"
        package_dir.mkdir(parents=True, exist_ok=True)

        # Copy executable
        exe_file = self.dist_dir / "reportserver.exe"
        if exe_file.exists():
            shutil.copy2(exe_file, package_dir)
        else:
            print("Warning: Executable not found, creating Python package instead")
            # Copy Python files
            python_files = [
                "reportserver.py", "start_server.py", "api_models.py",
                "api_handlers.py", "config.py", "requirements.txt",
                "API_DOCUMENTATION.md", "test_api.py"
            ]

            for file in python_files:
                src = self.python_dir / file
                if src.exists():
                    shutil.copy2(src, package_dir)

            # Copy Python modules
            for module_dir in ["corpreport", "dataentities", "personalreport"]:
                src_dir = self.python_dir / module_dir
                if src_dir.exists():
                    shutil.copytree(src_dir, package_dir / module_dir, dirs_exist_ok=True)

        # Copy configuration files
        config_src = self.project_root / "config"
        config_dst = package_dir / "config"
        if config_src.exists():
            shutil.copytree(config_src, config_dst, dirs_exist_ok=True)

        # Copy fonts
        fonts_src = self.project_root / "fonts"
        fonts_dst = package_dir / "fonts"
        if fonts_src.exists():
            shutil.copytree(fonts_src, fonts_dst, dirs_exist_ok=True)

        # Copy images
        images_src = self.project_root / "images"
        images_dst = package_dir / "images"
        if images_src.exists():
            shutil.copytree(images_src, images_dst, dirs_exist_ok=True)

        # Create directories
        (package_dir / "reports").mkdir(exist_ok=True)
        (package_dir / "log").mkdir(exist_ok=True)

        # Create startup scripts
        self._create_startup_scripts(package_dir)

        # Create README
        self._create_package_readme(package_dir)

        # Create archive
        if package_type == "zip":
            archive_path = self.dist_dir / "reportserver_package.zip"
            shutil.make_archive(str(archive_path.with_suffix("")), "zip", package_dir)
            print(f"✓ Package created: {archive_path}")
        elif package_type == "tar":
            archive_path = self.dist_dir / "reportserver_package.tar.gz"
            shutil.make_archive(str(archive_path.with_suffix("").with_suffix("")), "gztar", package_dir)
            print(f"✓ Package created: {archive_path}")

        return True

    def _create_startup_scripts(self, package_dir):
        """Create startup scripts for different platforms"""

        # Windows batch script
        windows_script = package_dir / "start_server.bat"
        with open(windows_script, "w") as f:
            f.write("""@echo off
echo Starting Report Server...
if exist reportserver.exe (
    reportserver.exe
) else (
    python start_server.py
)
pause
""")

        # Linux/Mac shell script
        unix_script = package_dir / "start_server.sh"
        with open(unix_script, "w") as f:
            f.write("""#!/bin/bash
echo "Starting Report Server..."
if [ -f "./reportserver" ]; then
    ./reportserver
else
    python3 start_server.py
fi
""")

        # Make shell script executable
        os.chmod(unix_script, 0o755)

        print("✓ Startup scripts created")

    def _create_package_readme(self, package_dir):
        """Create README for the package"""
        readme_path = package_dir / "README.txt"
        with open(readme_path, "w", encoding="utf-8") as f:
            f.write("""Report Server Package
====================

This package contains the Report Server REST API for generating reports.

Quick Start:
-----------
1. Ensure config/nodipexam.toml is properly configured
2. Run start_server.bat (Windows) or start_server.sh (Linux/Mac)
3. Access the API at http://localhost:8080/api

Files:
------
- reportserver.exe (or Python files) - Main application
- config/ - Configuration files
- fonts/ - Required font files
- reports/ - Generated reports will be saved here
- log/ - Log files will be created here
- start_server.* - Startup scripts

API Endpoints:
--------------
- GET /api/health - Health check
- GET /api/reports/types - Available report types
- POST /api/reports/generate - Generate a report
- GET /api/reports/status/{id} - Check report status
- GET /api/reports/download/{filename} - Download report
- GET /api/reports/list - List available reports

For detailed documentation, see API_DOCUMENTATION.md

Requirements:
-------------
- Database connection configured in config/nodipexam.toml
- Font files in fonts/ directory
- Write permissions for reports/ and log/ directories

Support:
--------
For issues and questions, check the logs in log/reportserver.log
""")

        print("✓ Package README created")

    def build_docker_image(self, tag="reportserver:latest"):
        """Build Docker image"""
        print(f"Building Docker image: {tag}")

        dockerfile_content = """FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p reports log

# Expose port
EXPOSE 8080

# Set environment variables
ENV REPORT_SERVER_HOST=0.0.0.0
ENV REPORT_SERVER_PORT=8080
ENV REPORT_OUTPUT_DIR=/app/reports

# Start the server
CMD ["python", "start_server.py"]
"""

        dockerfile_path = self.python_dir / "Dockerfile"
        with open(dockerfile_path, "w") as f:
            f.write(dockerfile_content)

        # Build Docker image
        cmd = ["docker", "build", "-t", tag, "."]
        result = subprocess.run(cmd, cwd=self.python_dir)

        if result.returncode == 0:
            print(f"✓ Docker image built: {tag}")
            return True
        else:
            print("✗ Docker build failed")
            return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Build Report Server deployment packages")
    parser.add_argument("--clean", action="store_true", help="Clean build directories")
    parser.add_argument("--check", action="store_true", help="Check dependencies only")
    parser.add_argument("--executable", action="store_true", help="Build standalone executable")
    parser.add_argument("--package", choices=["zip", "tar"], help="Create deployment package")
    parser.add_argument("--docker", metavar="TAG", help="Build Docker image")
    parser.add_argument("--all", action="store_true", help="Build everything")

    args = parser.parse_args()

    builder = ReportServerBuilder()

    if args.clean:
        builder.clean()
        return

    if not builder.check_dependencies():
        sys.exit(1)

    if args.check:
        return

    success = True

    if args.executable or args.all:
        success &= builder.build_executable()

    if args.package or args.all:
        package_type = args.package or "zip"
        success &= builder.create_package(package_type)

    if args.docker or args.all:
        tag = args.docker or "reportserver:latest"
        success &= builder.build_docker_image(tag)

    if not any([args.executable, args.package, args.docker, args.all]):
        print("No build target specified. Use --help for options.")
        print("Quick start: python build.py --all")

    if success:
        print("\n✓ Build completed successfully!")
    else:
        print("\n✗ Build failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
