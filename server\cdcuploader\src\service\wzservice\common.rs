// use crate::app;
// use md5::{Digest, Md5};

// use datacontroller::entities::prelude::TjAudiogramdetail;
// use odipservice::typedefine::*;

// pub const HEALTHYEVENTID: &str = "A57";
// pub const CORPYEVENTID: &str = "E10";
// pub const FENCHEN: &str = "11";

// pub fn get_body_wrapper(data: &String) -> String {
//     let bodywrap: String = format!(
//         r#"<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://server.zhejian.com/"><soapenv:Header/><soapenv:Body><ser:all><!--Optional:--><arg0><![CDATA[{}]]></arg0></ser:all></soapenv:Body></soapenv:Envelope>"#,
//         data
//     );
//     bodywrap
//     // bodywrap.to_string()
// }

pub fn get_body_wrapper_wz(data: &String) -> String {
  let bodywrap: String = format!(
    r#"<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://server.occupational.com"><soapenv:Header/><soapenv:Body><ser:all><!--Optional:--><arg0><![CDATA[{}]]></arg0></ser:all></soapenv:Body></soapenv:Envelope>"#,
    data
  );
  bodywrap
  // bodywrap.to_string()
}
//headSign=MD5（eventId＋requestTime+userId+password）；
// pub fn get_header_sign(requesttime: &String, eventid: &String, userid: &String, password: &String) -> String {
//   let head_sign_str = format!("{}{}{}{}", eventid, requesttime, userid, password);
//   let mut hasher = Md5::new();
//   hasher.update(head_sign_str.as_bytes());
//   let result = hasher.finalize();

//   let ret = format!("{:x}", result);
//   ret.to_lowercase()
// }
