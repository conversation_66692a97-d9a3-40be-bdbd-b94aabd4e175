//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default,PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_checkalltemplate")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_metype: i32,
  pub tj_ocuresult: String,
  pub tj_oscsuggestion: String,
  pub tj_othresult: String,
  pub tj_othsuggestion: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
