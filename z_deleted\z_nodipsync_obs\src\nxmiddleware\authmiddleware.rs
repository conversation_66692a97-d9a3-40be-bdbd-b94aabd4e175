use super::token::{AuthError, Clai<PERSON>};
use crate::nxmiddleware::token;
use axum::{
  async_trait,
  extract::{FromRequestParts, TypedHeader},
  headers::{authorization::Bearer, Authorization},
  http::{request::Parts, StatusCode},
  response::{IntoResponse, Response},
  Json,
};
use serde_json::json;

#[async_trait]
impl<S> FromRequestParts<S> for Claims
where
  S: Send + Sync,
{
  type Rejection = AuthError;

  async fn from_request_parts(parts: &mut Parts, state: &S) -> Result<Self, Self::Rejection> {
    let method = parts.method.to_string();
    let uri = parts.uri.path().to_string();

    info!("from request parts, do token authorization......");
    // Extract the token from the authorization header
    let TypedHeader(Authorization(bearer)) = TypedHeader::<Authorization<Bearer>>::from_request_parts(parts, state)
      .await
      .map_err(|_| AuthError::InvalidToken)?;
    // Decode the user data
    // let token_data = decode::<Claims>(bearer.token(), &DecodingKey::from_secret(&KEY), &Validation::default()).map_err(|_| AuthError::InvalidToken)?;
    let token_data = token::decode(bearer.token()).map_err(|_| AuthError::InvalidToken)?;

    info!("user:{} {} {}", &token_data.claims.user.id, &method, &uri);

    Ok(token_data.claims)
  }
}

impl IntoResponse for AuthError {
  fn into_response(self) -> Response {
    let (status, error_message) = match self {
      AuthError::WrongCredentials => (StatusCode::UNAUTHORIZED, "Wrong credentials"),
      AuthError::MissingCredentials => (StatusCode::BAD_REQUEST, "Missing credentials"),
      AuthError::TokenCreation => (StatusCode::INTERNAL_SERVER_ERROR, "Token creation error"),
      AuthError::InvalidToken => (StatusCode::BAD_REQUEST, "Invalid token"),
    };
    let body = Json(json!({
        "error": error_message,
    }));
    (status, body).into_response()
  }
}
