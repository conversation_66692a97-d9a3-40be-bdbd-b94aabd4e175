use serde::{Deserialize, Serialize, Serializer};
// use yaserde_derive::YaSerialize;
use yaserde::*;

use super::common;

#[derive(Debug, <PERSON><PERSON>, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "DATAS")]
pub struct EmployerData {
  #[yaserde(rename = "TB_TJ_CRPTS")]
  pub employerlist: EmployerList,
  // pub employerlist: Vec<Employer>, //root
}

#[derive(Debug, <PERSON><PERSON>, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TB_TJ_CRPTS")]
pub struct EmployerList {
  #[yaserde(rename = "TB_TJ_CRPT")]
  pub employer: Vec<Employer>, //企业人员信息
}

#[derive(Debug, <PERSON><PERSON>, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "TB_TJ_CRPT")]
pub struct Employer {
  #[yaserde(rename = "RID")]
  #[serde(serialize_with = "serialize_to_string")]
  pub rid: String,
  #[yaserde(rename = "CRPT_NAME")] //
  #[serde(serialize_with = "serialize_to_string")]
  pub crpt_name: String,
  #[yaserde(rename = "IF_SUB_ORG")]
  #[serde(serialize_with = "serialize_to_string")]
  pub if_sub_org: String,
  #[yaserde(rename = "UPPER_INSTITUTION_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub upper_institution_code: String,
  #[yaserde(rename = "INSTITUTION_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub institution_code: String,
  #[yaserde(rename = "ZONE_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub zone_code: String, //        :String, //`xml:"ZONE_CODE"`        //所属地区
  #[yaserde(rename = "INDUS_TYPE_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub indus_type_code: String, //  :String, //`xml:"INDUS_TYPE_CODE"`  //行业类别编码,必须选择到第四级类别。（级别参照编码库的层级编码）
  #[yaserde(rename = "ECONOMY_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub economy_code: String, //    :String, //`xml:"ECONOMY_CODE"`     //经济类型编码,且必须选择到最下一级。（级别参照编码库的层级编码）
  #[yaserde(rename = "CRPT_SIZE_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub crpt_size_code: String, //   :String, //`xml:"CRPT_SIZE_CODE"`   //企业规模编码
  #[yaserde(rename = "WORK_FORCE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub work_force: String, //    :String, //`xml:"WORK_FORCE"`       //职工人数
  #[yaserde(rename = "HOLD_CARD_MAN")]
  #[serde(serialize_with = "serialize_to_string")]
  pub hold_card_man: String, //    :String, //`xml:"HOLD_CARD_MAN"`    //接触职业病危害因素人数
  #[yaserde(rename = "ADDRESS")]
  #[serde(serialize_with = "serialize_to_string")]
  pub address: String, //    :String, //`xml:"ADDRESS"`          //单位注册地址
  #[yaserde(rename = "PHONE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub phone: String, //      :String, //`xml:"PHONE"`            //法人联系电话
  #[yaserde(rename = "CORPORATE_DEPUTY")]
  #[serde(serialize_with = "serialize_to_string")]
  pub corporate_deputy: String, //:String, //`xml:"CORPORATE_DEPUTY"` //法人代表
  #[yaserde(rename = "WORKMAN_NUM")]
  #[serde(serialize_with = "serialize_to_string")]
  pub workman_num: String, //    :String, //`xml:"WORKMAN_NUM"`      //生产工人数
  #[yaserde(rename = "WORKMISTRESS_NUM")]
  #[serde(serialize_with = "serialize_to_string")]
  pub workmistress_num: String, //:String, //`xml:"WORKMISTRESS_NUM"` //接触职业病危害因素女工人数
  #[yaserde(rename = "POSTALCODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub postalcode: String, //    :String, //`xml:"POSTALCODE"`       //单位注册邮编
  #[yaserde(rename = "WORK_AREA")]
  #[serde(serialize_with = "serialize_to_string")]
  pub work_area: String, //  :String, //`xml:"WORK_AREA"`        //经营面积
  #[yaserde(rename = "REGISTER_FUND")]
  #[serde(serialize_with = "serialize_to_string")]
  pub register_fund: String, //  :String, //`xml:"REGISTER_FUND"`    //注册资金
  #[yaserde(rename = "SAFETY_PRINCIPAL")]
  #[serde(serialize_with = "serialize_to_string")]
  pub safety_principal: String, //:String, //`xml:"SAFETY_PRINCIPAL"` //职业卫生安全负责人
  #[yaserde(rename = "FILING_DATE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub filing_date: String, //:String, //`xml:"FILING_DATE"`      //建档日期,D10(YYYY-MM-DD)
  #[yaserde(rename = "BUILD_DATE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub build_date: String, // :String, //`xml:"BUILD_DATE"`       //建厂日期,D10(YYYY-MM-DD)
  #[yaserde(rename = "LINKMAN1")]
  #[serde(serialize_with = "serialize_to_string")]
  pub linkman1: String, //  :String, //`xml:"LINKMAN1"`         //检测联系人
  #[yaserde(rename = "POSITION1")]
  #[serde(serialize_with = "serialize_to_string")]
  pub position1: String, //  :String, //`xml:"POSITION1"`        //检测联系人职务
  #[yaserde(rename = "LINKPHONE1")]
  #[serde(serialize_with = "serialize_to_string")]
  pub linkphone1: String, //  :String, //`xml:"LINKPHONE1"`       //检测联系人电话
  #[yaserde(rename = "LINKMAN2")]
  #[serde(serialize_with = "serialize_to_string")]
  pub linkman2: String, //  :String, //`xml:"LINKMAN2"`         //体检联系人
  #[yaserde(rename = "POSITION2")]
  #[serde(serialize_with = "serialize_to_string")]
  pub position2: String, // :String, //`xml:"POSITION2"`        //体检联系人职务
  #[yaserde(rename = "LINKPHONE2")]
  #[serde(serialize_with = "serialize_to_string")]
  pub linkphone2: String, // :String, //`xml:"LINKPHONE2"`       //体检联系人电话
  #[yaserde(rename = "SAFEPOSITION")]
  #[serde(serialize_with = "serialize_to_string")]
  pub safeposition: String, //  :String, //`xml:"SAFEPOSITION"`     //安全联系人职务
  #[yaserde(rename = "SAFEPHONE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub safephone: String, //  :String, //`xml:"SAFEPHONE"`        //职业卫生安全联系人电话
  #[yaserde(rename = "SUBJE_CONN")]
  #[serde(serialize_with = "serialize_to_string")]
  pub subje_conn: String, //  :String, //`xml:"SUBJE_CONN"`       //隶属关系
  #[yaserde(rename = "ENROL_ADDRESS")]
  #[serde(serialize_with = "serialize_to_string")]
  pub enrol_address: String, //  :String, //`xml:"ENROL_ADDRESS"`    //作业场所地址
  #[yaserde(rename = "ENROL_POSTALCODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub enrol_postalcode: String, // :String, //`xml:"ENROL_POSTALCODE"` //作业场所邮政编码
  #[yaserde(rename = "OCC_MANA_OFFICE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub occ_mana_office: String, // :String, //`xml:"OCC_MANA_OFFICE"`  //职业卫生管理机构
}

// ==============================================================
//                          体检信息
// ==============================================================

#[derive(Debug, Clone, Default, Deserialize, YaDeserialize, YaSerialize, Serialize, PartialEq)]
#[yaserde(rename = "DATAS")]
pub struct HealthyData {
  #[yaserde(rename = "PERSONS")]
  pub personlist: PersonList, //root
}

#[derive(Debug, Clone, Default, Deserialize, YaDeserialize, YaSerialize, Serialize, PartialEq)]
#[yaserde(rename = "PERSONS")]
pub struct PersonList {
  #[yaserde(rename = "PERSON")]
  pub persons: Vec<Person>, //root
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "PERSON")]
pub struct Person {
  #[yaserde(rename = "OPTYPE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub optype: String, //          `xml:"OPTYPE"`          //1：新增  2：修改 3：删除 数据操作编码; 若为新增状态，则不能重复上传相同的数据
  #[yaserde(rename = "TD_TJ_BHK")]
  pub medinfo: MedInfo, //   `xml:"TD_TJ_BHK"`       //体检主表信息
  #[yaserde(rename = "TD_TJ_TCH_BADRSNSES")]
  pub touchhazardfactors: TouchHazardFactorList, // `xml:"TD_TJ_TCH_BADRSNSES"` //接触危害因素集合
  #[yaserde(rename = "TD_TJ_BADRSNSES")]
  pub hazardfactors: ContactHazardFactorList, // `xml:"TD_TJ_BADRSNSES"` //接触危害因素集合
  #[yaserde(rename = "TD_TJ_BHKSUBS")]
  pub items: ItemList, //   `xml:"TD_TJ_BHKSUBS"`   //体检结果表集合
  #[yaserde(rename = "TD_TJ_MHKRSTS")]
  pub checkresults: CheckResultList, //  `xml:"TD_TJ_MHKRSTS"`   //主检判断主检结论集合
  #[yaserde(rename = "TD_TJ_EXMSDATA")]
  pub questionnair: Option<QuestionNaire>, //  TD_TJ_EXMSDATA  `xml:"TD_TJ_EXMSDATA"`  //一般问诊项目
  #[yaserde(rename = "TD_TJ_EMHISTORYS")]
  pub ocuhis: Option<OccupationHistoryList>, //`xml:"TD_TJ_EMHISTORYS"`   //职业史信息集合
  #[yaserde(rename = "TD_TJ_SYMPTOMS")]
  pub symptoms: Option<SymptomList>, // `xml:"TD_TJ_SYMPTOMS"`     //症状信息集合
  #[yaserde(rename = "TD_BHK_ANAMNESISES")]
  pub diseases: Option<DiseaseHistoryList>, //   `xml:"TD_BHK_ANAMNESISES"` //既往病史集合
}

//体检主表信息
#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_BHK")]
pub struct MedInfo {
  #[yaserde(rename = "RID")]
  #[serde(serialize_with = "serialize_to_string")]
  pub rid: String, //`xml:"RID"`               //业务系统主键
  #[yaserde(rename = "BHKORGAN_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub bhkorgancode: String, //`xml:"BHKORGAN_CODE"`     //体检机构编号
  #[yaserde(rename = "BHK_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub bhkcode: String, //`xml:"BHK_CODE"`          //体检编号
  #[yaserde(rename = "IF_SUB_ORG")]
  #[serde(serialize_with = "serialize_to_string")]
  pub ifsuborg: String, //`xml:"IF_SUB_ORG"`          //用人单位是否分支机构
  #[yaserde(rename = "INSTITUTION_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub institutioncode: String, //`xml:"INSTITUTION_CODE"`  //社会信用代码,字母必须全部大写
  #[yaserde(rename = "CRPT_NAME")]
  #[serde(serialize_with = "serialize_to_string")]
  pub crptname: String, //`xml:"CRPT_NAME"`         //用人单位名称
  #[yaserde(rename = "CRPT_ADDR")]
  #[serde(serialize_with = "serialize_to_string")]
  pub crptaddr: String, //`xml:"CRPT_ADDR"`         //用人单位注册地址
  #[yaserde(rename = "PERSON_NAME")]
  #[serde(serialize_with = "serialize_to_string")]
  pub personname: String, //`xml:"PERSON_NAME"`       //人员姓名
  #[yaserde(rename = "SEX")]
  #[serde(serialize_with = "serialize_to_string")]
  pub sex: String, //`xml:"SEX"`               //性别
  #[yaserde(rename = "IDC")]
  #[serde(serialize_with = "serialize_to_string")]
  pub idc: String, //`xml:"IDC"`               //证件号码,证件号码必须全部大写
  #[yaserde(rename = "BRTH")]
  #[serde(serialize_with = "serialize_to_string")]
  pub brth: String, //`xml:"BRTH"`              //出生日期,D10(YYYY-MM-DD)
  #[yaserde(rename = "AGE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub age: String, //`xml:"AGE"`               //年龄,校验低于16周岁反馈异常，按天数计算周岁。
  #[yaserde(rename = "ISXMRD")]
  #[serde(serialize_with = "serialize_to_string")]
  pub isxmrd: String, //`xml:"ISXMRD"`            //婚否
  #[yaserde(rename = "LNKTEL")]
  #[serde(serialize_with = "serialize_to_string")]
  pub lnktel: String, //`xml:"LNKTEL"`            //人员联系电话
  #[yaserde(rename = "DPT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub dpt: String, //`xml:"DPT"`               //体检人员工作部门
  #[yaserde(rename = "WRKNUM")]
  #[serde(serialize_with = "serialize_to_string")]
  pub wrknum: String, //`xml:"WRKNUM"`            //人员工号
  #[yaserde(rename = "WRKLNT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub wrklnt: String, //`xml:"WRKLNT"`            //总工龄年数
  #[yaserde(rename = "WRKLNTMONTH")]
  #[serde(serialize_with = "serialize_to_string")]
  pub wrklntmonth: String, //`xml:"WRKLNTMONTH"`       //总工龄月数
  #[yaserde(rename = "TCHBADRSNTIM")]
  #[serde(serialize_with = "serialize_to_string")]
  pub tchbadrsntim: String, //`xml:"TCHBADRSNTIM"`      //接害工龄年数
  #[yaserde(rename = "TCHBADRSNMONTH")]
  #[serde(serialize_with = "serialize_to_string")]
  pub tchbadrsnmonth: String, //`xml:"TCHBADRSNMONTH"`    //接害工龄月数
  #[yaserde(rename = "WORK_NAME")]
  #[serde(serialize_with = "serialize_to_string")]
  pub workname: String, //`xml:"WORK_NAME"`         //工种其他名称
  #[yaserde(rename = "ONGUARD_STATE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub onguardstate: String, //`xml:"ONGUARD_STATE"`     //在岗状态编码
  #[yaserde(rename = "BHK_DATE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub bhkdate: String, //`xml:"BHK_DATE"`          //体检日期,D10(YYYY-MM-DD)
  #[yaserde(rename = "BHKRST")]
  #[serde(serialize_with = "serialize_to_string")]
  pub bhkrst: String, //`xml:"BHKRST"`            //体检结果
  #[yaserde(rename = "MHKADV")]
  #[serde(serialize_with = "serialize_to_string")]
  pub mhkadv: String, //`xml:"MHKADV"`            //主检建议
  #[yaserde(rename = "VERDICT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub verdict: String, //`xml:"VERDICT"`           //体检结论
  #[yaserde(rename = "MHKDCT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub mhkdct: String, //`xml:"MHKDCT"`            //主检医师
  #[yaserde(rename = "BHK_TYPE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub bhktype: String, //`xml:"BHK_TYPE"`          //体检类型编码
  #[yaserde(rename = "JDGDAT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub jdgdat: String, //`xml:"JDGDAT"`            //主检判定日期,D10(YYYY-MM-DD)
  #[yaserde(rename = "BADRSN")]
  #[serde(serialize_with = "serialize_to_string")]
  pub badrsn: String, //`xml:"BADRSN"`            //接害因素
  #[yaserde(rename = "IF_RHK")]
  #[serde(serialize_with = "serialize_to_string")]
  pub ifrhk: String, //`xml:"IF_RHK"`            //是否为复检
  #[yaserde(rename = "LAST_BHK_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub lastbhkcode: String, //`xml:"LAST_BHK_CODE"`     //复检对应的上次体检编号
  #[yaserde(rename = "ID_CARD_TYPE_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub idcardtypecode: String, //`xml:"ID_CARD_TYPE_CODE"` //身份证件类型代码
  #[yaserde(rename = "WORK_TYPE_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub worktypecode: String, //`xml:"WORK_TYPE_CODE"`    //工种代码
  #[yaserde(rename = "HARM_START_DATE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub harmstartdate: String, //`xml:"HARM_START_DATE"`   //开始接害日期,D10(YYYY-MM-DD)
  #[yaserde(rename = "JC_TYPE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub jctype: String, //`xml:"JC_TYPE"`           //监测类型
  #[yaserde(rename = "RPT_PRINT_DATE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub rptprintdate: String, //`xml:"RPT_PRINT_DATE"`    //报告打印日期,D10(YYYY-MM-DD)
  #[yaserde(rename = "IF_SUB_ORG_EMP")]
  #[serde(serialize_with = "serialize_to_string")]
  pub ifsuborgemp: String, //`xml:"RPT_PRINT_DATE"`    //用工单位是否分支机构
  #[yaserde(rename = "UPPER_CREDIT_CODE_EMP")]
  #[serde(serialize_with = "serialize_to_string")]
  pub uppercreditcodeemp: String, //`xml:"RPT_PRINT_DATE"`    //用工单位上级单位社会信用代码
  #[yaserde(rename = "CREDIT_CODE_EMP")]
  #[serde(serialize_with = "serialize_to_string")]
  pub creditcodeemp: String, //`xml:"RPT_PRINT_DATE"`    //用工单位社会信用代码
  #[yaserde(rename = "CRPT_NAME_EMP")]
  #[serde(serialize_with = "serialize_to_string")]
  pub cprtnameemp: String, //`xml:"RPT_PRINT_DATE"`    //用工单位名称
  #[yaserde(rename = "INDUS_TYPE_CODE_EMP")]
  #[serde(serialize_with = "serialize_to_string")]
  pub industypecodeemp: String, //`xml:"RPT_PRINT_DATE"`    //用工单位行业类别编码
  #[yaserde(rename = "ECONOMY_CODE_EMP")]
  #[serde(serialize_with = "serialize_to_string")]
  pub economycodeemp: String, //`xml:"RPT_PRINT_DATE"`    //用工单位经济类型编码
  #[yaserde(rename = "CRPT_SIZE_CODE_EMP")]
  #[serde(serialize_with = "serialize_to_string")]
  pub cprtsizecodeemp: String, //`xml:"RPT_PRINT_DATE"`    //用工单位企业规模编码
  #[yaserde(rename = "ZONE_CODE_EMP")]
  #[serde(serialize_with = "serialize_to_string")]
  pub zonecodeemp: String, //`xml:"RPT_PRINT_DATE"`    //用工单位所属地区编码
  #[yaserde(rename = "PROTECT_EQU_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub protectequcode: String, //`xml:"PROTECT_EQU_CODE"`    //监测类型为主动监测、初检时必填；复检时必须与初检保持一致,对照字典3.25
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_TCH_BADRSNSES")]
pub struct TouchHazardFactorList {
  #[yaserde(rename = "TD_TJ_TCH_BADRSNS")]
  pub touchhazards: Vec<TouchHazardFactor>, // `xml:"TD_TJ_BADRSNS"`,
  #[yaserde(rename = "OTHER_TCH_BADRSN")]
  #[serde(serialize_with = "serialize_to_string")]
  pub otherhazards: String, //other hazards//OTHER_BADRSN
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_TCH_BADRSNS")]
pub struct TouchHazardFactor {
  #[yaserde(rename = "RID")]
  #[serde(serialize_with = "serialize_to_string")]
  pub rid: String, //`xml:"RID"`                  //主键
  #[yaserde(rename = "BADRSN_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub badrsncode: String, //`xml:"BADRSN_CODE"`          //有害因素编码
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_BADRSNSES")]
pub struct ContactHazardFactorList {
  #[yaserde(rename = "TD_TJ_BADRSNS")]
  pub contacthazards: Vec<ContactHazardFactor>, // `xml:"TD_TJ_BADRSNS"`,
  #[yaserde(rename = "OTHER_BADRSN")]
  #[serde(serialize_with = "serialize_to_string")]
  pub otherhazards: String, //other hazards//OTHER_BADRSN
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_BADRSNS")]
pub struct ContactHazardFactor {
  #[yaserde(rename = "RID")]
  #[serde(serialize_with = "serialize_to_string")]
  pub rid: String, //`xml:"RID"`                  //主键
  #[yaserde(rename = "BADRSN_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub badrsncode: String, //`xml:"BADRSN_CODE"`          //有害因素编码
  #[yaserde(rename = "EXAM_CONCLUSION_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub examconclusioncode: String, //`xml:"EXAM_CONCLUSION_CODE"` //体检结论代码
  #[yaserde(rename = "YSZYB_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub yszybcode: String, //`xml:"YSZYB_CODE"`           //疑似职业病代码
  #[yaserde(rename = "ZYJJZ_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub zyjjzcode: String, //`xml:"ZYJJZ_CODE"`           //职业禁忌证代码
  #[yaserde(rename = "QTJB_NAME")]
  #[serde(serialize_with = "serialize_to_string")]
  pub qtjbname: String, //`xml:"QTJB_NAME"`            //其他疾病或异常描述
  #[yaserde(rename = "ENTERPRISE_NAME")]
  #[serde(serialize_with = "serialize_to_string")]
  pub enterprisename: String, //`xml:"ENTERPRISE_NAME"` //接触相应职业病危害因素的用人单位名称,当体检类型是上岗前，体检结论是疑似职业病时，该项必填
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_BHKSUBS")]
pub struct ItemList {
  #[yaserde(rename = "TD_TJ_BHKSUB")]
  pub itemlist: Vec<Item>, //TD_TJ_BHKSUB `xml:"TD_TJ_BHKSUB"`
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_BHKSUB")]
pub struct Item {
  #[yaserde(rename = "RID")]
  #[serde(serialize_with = "serialize_to_string")]
  pub rid: String, //`xml:"RID"`           //主键
  #[yaserde(rename = "ITMCOD")]
  #[serde(serialize_with = "serialize_to_string")]
  pub itmcod: String, //`xml:"ITMCOD"`        //体检项目编码
  #[yaserde(rename = "MSRUNT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub msrunt: String, //`xml:"MSRUNT"`        //计量单位编码
  #[yaserde(rename = "ITEM_STDVALUE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub itemstdvalue: String, //`xml:"ITEM_STDVALUE"` //参考值
  #[yaserde(rename = "RESULT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub result: String, //`xml:"RESULT"`        //体检结果,若为定量项目且未缺项，则体检结果必须数值型结果
  #[yaserde(rename = "RGLTAG")]
  #[serde(serialize_with = "serialize_to_string")]
  pub rgltag: String, //`xml:"RGLTAG"`        //合格标记
  #[yaserde(rename = "RST_DESC")]
  #[serde(serialize_with = "serialize_to_string")]
  pub rstdesc: String, //`xml:"RST_DESC"`      //结果偏高偏低标记
  #[yaserde(rename = "IF_LACK")]
  #[serde(serialize_with = "serialize_to_string")]
  pub iflack: String, //`xml:"IF_LACK"`       //是否缺项
  #[yaserde(rename = "CHKDAT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub chkdat: String, //`xml:"CHKDAT"`        //检查日期,D10(YYYY-MM-DD)
  #[yaserde(rename = "CHKDOCT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub chkdoct: String, //`xml:"CHKDOCT"`       //检查医生
  #[yaserde(rename = "JDGPTN")]
  #[serde(serialize_with = "serialize_to_string")]
  pub jdgptn: String, //`xml:"JDGPTN"`        //判断模式
  #[yaserde(rename = "MINVAL")]
  #[serde(serialize_with = "serialize_to_string")]
  pub minval: String, //`xml:"MINVAL"`        //最小值
  #[yaserde(rename = "MAXVAL")]
  #[serde(serialize_with = "serialize_to_string")]
  pub maxval: String, //`xml:"MAXVAL"`        //最大值
  #[yaserde(rename = "DIAG_REST")]
  #[serde(serialize_with = "serialize_to_string")]
  pub diagrest: String, //`xml:"DIAG_REST"`     //诊断结论
  #[yaserde(rename = "RST_FLAG")]
  #[serde(serialize_with = "serialize_to_string")]
  pub rstflag: String, //`xml:"RST_FLAG"`      //结果判定标记 	   胸片项目时必填，否则为空。合格标记为合格时该值只能为0，为不合格时该值只能为1或2。是否缺项为是时只能为3
  #[yaserde(rename = "DATA_VERSION")]
  #[serde(serialize_with = "serialize_to_string")]
  pub dataversion: String, //`xml:"DATA_VERSION"`     //诊断结论 对照字典3.23 判断模式为定量且未缺项时，该值不为空，否则为空
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_MHKRSTS")]
pub struct CheckResultList {
  #[yaserde(rename = "TD_TJ_MHKRST")]
  pub checkresults: Vec<CheckResult>, // TD_TJ_MHKRST `xml:"TD_TJ_MHKRST"`
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_MHKRST")]
pub struct CheckResult {
  #[yaserde(rename = "RID")]
  #[serde(serialize_with = "serialize_to_string")]
  pub rid: String, //`xml:"RID"`
  #[yaserde(rename = "BHKRST_CODE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub bhkrstcode: String, //`xml:"BHKRST_CODE"` //主检结论
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_EXMSDATA")]
pub struct QuestionNaire {
  #[yaserde(rename = "RID")]
  #[serde(serialize_with = "serialize_to_string")]
  pub rid: String, //`xml:"RID"`          //主键
  #[yaserde(rename = "MNRAGE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub mnrage: String, //`xml:"MNRAGE"`       //月经史-初潮年龄
  #[yaserde(rename = "MNS")]
  #[serde(serialize_with = "serialize_to_string")]
  pub mns: String, //`xml:"MNS"`          //月经史-经期
  #[yaserde(rename = "CYC")]
  #[serde(serialize_with = "serialize_to_string")]
  pub cyc: String, //`xml:"CYC"`          //月经史-周期
  #[yaserde(rename = "MNLAGE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub mnlage: String, //`xml:"MNLAGE"`       //月经史-停经年龄
  #[yaserde(rename = "ISXMNS")]
  #[serde(serialize_with = "serialize_to_string")]
  pub isxmns: String, //`xml:"ISXMNS"`       //月经史-是否经期
  #[yaserde(rename = "CHLDQTY")]
  #[serde(serialize_with = "serialize_to_string")]
  pub chldqty: String, //`xml:"CHLDQTY"`      //生育史-子女人数
  #[yaserde(rename = "ABRQTY")]
  #[serde(serialize_with = "serialize_to_string")]
  pub abrqty: String, //`xml:"ABRQTY"`       //生育史-流产次数
  #[yaserde(rename = "SLNKQTY")]
  #[serde(serialize_with = "serialize_to_string")]
  pub slnkqty: String, //`xml:"SLNKQTY"`      //生育史-早产次数
  #[yaserde(rename = "STLQTY")]
  #[serde(serialize_with = "serialize_to_string")]
  pub stlqty: String, //`xml:"STLQTY"`       //生育史-死产次数
  #[yaserde(rename = "TRSQTY")]
  #[serde(serialize_with = "serialize_to_string")]
  pub trsqty: String, //`xml:"TRSQTY"`       //生育史-畸胎次数
  #[yaserde(rename = "CHLDHTHCND")]
  #[serde(serialize_with = "serialize_to_string")]
  pub chldhthcnd: String, //`xml:"CHLDHTHCND"`   //生育史-子女健康状况
  #[yaserde(rename = "MRYDAT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub mrydat: String, //`xml:"MRYDAT"`       //婚姻史-结婚日期,D10(YYYY-MM-DD)
  #[yaserde(rename = "CPLRDTCND")]
  #[serde(serialize_with = "serialize_to_string")]
  pub cplrdtcnd: String, //`xml:"CPLRDTCND"` //配偶接触放射线情况
  #[yaserde(rename = "CPLPRFHTHCND")]
  #[serde(serialize_with = "serialize_to_string")]
  pub cplprfhthcnd: String, //`xml:"CPLPRFHTHCND"` //配偶职业及健康状况
  #[yaserde(rename = "SMKSTA")]
  #[serde(serialize_with = "serialize_to_string")]
  pub smksta: String, //`xml:"SMKSTA"`       //烟酒史-吸烟情况
  #[yaserde(rename = "SMKDAYBLE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub smkdayble: String, //`xml:"SMKDAYBLE"`    //每天吸烟支数
  #[yaserde(rename = "SMKYERQTY")]
  #[serde(serialize_with = "serialize_to_string")]
  pub smkyerqty: String, //`xml:"SMKYERQTY"`    //吸烟年数
  #[yaserde(rename = "SMKMTHQTY")]
  #[serde(serialize_with = "serialize_to_string")]
  pub smkmthqty: String, //`xml:"SMKMTHQTY"`    //吸烟史-月
  #[yaserde(rename = "WINSTA")]
  #[serde(serialize_with = "serialize_to_string")]
  pub winsta: String, //`xml:"WINSTA"`       //烟酒史-饮酒情况
  #[yaserde(rename = "WINDAYMLX")]
  #[serde(serialize_with = "serialize_to_string")]
  pub windaymlx: String, //`xml:"WINDAYMLX"`    //饮酒每天毫升数
  #[yaserde(rename = "WINYERQTY")]
  #[serde(serialize_with = "serialize_to_string")]
  pub winyerqty: String, //`xml:"WINYERQTY"`    //饮酒年数
  #[yaserde(rename = "JZS")]
  #[serde(serialize_with = "serialize_to_string")]
  pub jzs: String, //`xml:"JZS"`          //家族史
  #[yaserde(rename = "GRS")]
  #[serde(serialize_with = "serialize_to_string")]
  pub grs: String, //`xml:"GRS"`          //个人史
  #[yaserde(rename = "OTH")]
  #[serde(serialize_with = "serialize_to_string")]
  pub oth: String, //`xml:"OTH"`          //其他情况
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_EMHISTORYS")]
pub struct OccupationHistoryList {
  #[yaserde(rename = "TD_TJ_EMHISTORY")]
  pub ocuhistory: Vec<OccupationHistory>, // TD_TJ_EMHISTORY `xml:"TD_TJ_EMHISTORY"`
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_EMHISTORY")]
pub struct OccupationHistory {
  #[yaserde(rename = "RID")]
  #[serde(serialize_with = "serialize_to_string")]
  pub rid: String, //`xml:"RID"`           //主键
  #[yaserde(rename = "HIS_TYPE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub histype: String, //`xml:"HIS_TYPE"`      //类型编码
  #[yaserde(rename = "NUM")]
  #[serde(serialize_with = "serialize_to_string")]
  pub num: String, //`xml:"NUM"`           //排序号
  #[yaserde(rename = "STASTP_DATE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub stastpdate: String, //`xml:"STASTP_DATE"`   //起止日期 格式：2001.7-2011.6
  #[yaserde(rename = "UNIT_NAME")]
  #[serde(serialize_with = "serialize_to_string")]
  pub unitname: String, //`xml:"UNIT_NAME"`     //工作单位名称
  #[yaserde(rename = "DEPARTMENT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub department: String, //`xml:"DEPARTMENT"`    //部门车间
  #[yaserde(rename = "WORK_TYPE")]
  #[serde(serialize_with = "serialize_to_string")]
  pub worktype: String, //`xml:"WORK_TYPE"`     //工种
  #[yaserde(rename = "PRFRAYSRT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub prfraysrt: String, //`xml:"PRFRAYSRT"`     //接触有害因素
  #[yaserde(rename = "PRFWRKLOD")]
  #[serde(serialize_with = "serialize_to_string")]
  pub prfwrklod: String, //`xml:"PRFWRKLOD"`     //(放射)每日工作时数或工作量
  #[yaserde(rename = "PRFSHNVLU")]
  #[serde(serialize_with = "serialize_to_string")]
  pub prfshnvlu: String, //`xml:"PRFSHNVLU"`     //(放射)职业史累积受照剂量"
  #[yaserde(rename = "PRFEXCSHN")]
  #[serde(serialize_with = "serialize_to_string")]
  pub prfexcshn: String, //`xml:"PRFEXCSHN"`     //(放射)职业史过量照射史
  #[yaserde(rename = "PRFRAYSRT2")]
  #[serde(serialize_with = "serialize_to_string")]
  pub prfraysrt2: String, //`xml:"PRFRAYSRT2"`    //(放射)职业照射种类
  #[yaserde(rename = "PRFRAYSRTCODS")]
  #[serde(serialize_with = "serialize_to_string")]
  pub prfraysrtcods: String, //`xml:"PRFRAYSRTCODS"` //(放射)职业照射种类代码
  #[yaserde(rename = "FSSZL")]
  #[serde(serialize_with = "serialize_to_string")]
  pub fsszl: String, //`xml:"FSSZL"`         //(放射)放射线种类
  #[yaserde(rename = "DEFEND_STEP")]
  #[serde(serialize_with = "serialize_to_string")]
  pub defendstep: String, //`xml:"DEFEND_STEP"`   //(非放射)防护措施
  #[yaserde(rename = "CHKDAT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub chkdat: String, //`xml:"CHKDAT"`        //检查日期,D10(YYYY-MM-DD)
  #[yaserde(rename = "CHKDOCT")]
  #[serde(serialize_with = "serialize_to_string")]
  pub chkdoct: String, //`xml:"CHKDOCT"`       //检查医生
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_SYMPTOMS")]
pub struct SymptomList {
  #[yaserde(rename = "TD_TJ_SYMPTOM")]
  pub symptoms: Vec<Symptom>, // `xml:"TD_TJ_SYMPTOM"`
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_TJ_SYMPTOM")]
pub struct Symptom {
  #[yaserde(rename = "RID")]
  pub rid: String, //`xml:"RID"`          //主键
  #[yaserde(rename = "SYMPTOM_CODE")]
  pub symptomcode: String, //`xml:"SYMPTOM_CODE"` //症状编码
  #[yaserde(rename = "OTHSYM")]
  pub othsym: String, //`xml:"OTHSYM"`       //其他症状
  #[yaserde(rename = "CHKDAT")]
  pub chkdat: String, //`xml:"CHKDAT"`       //检查日期,D10(YYYY-MM-DD)
  #[yaserde(rename = "CHKDOCT")]
  pub chkdoct: String, //`xml:"CHKDOCT"`      //检查医生
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_BHK_ANAMNESISES")]
pub struct DiseaseHistoryList {
  #[yaserde(rename = "TD_BHK_ANAMNESIS")]
  pub diseasehistories: Vec<DiseaseHistory>, //`xml:"TD_BHK_ANAMNESIS"`
}

#[derive(Debug, Clone, Default, YaDeserialize, YaSerialize, Deserialize, Serialize, PartialEq)]
#[yaserde(rename = "TD_BHK_ANAMNESIS")]
pub struct DiseaseHistory {
  #[yaserde(rename = "RID")]
  pub rid: String, //`xml:"RID"`       //主键
  #[yaserde(rename = "HSTNAM")]
  pub hstnam: String, //`xml:"HSTNAM"`    //疾病名称
  #[yaserde(rename = "HSTDAT")]
  pub hstdat: String, //`xml:"HSTDAT"`    //诊断日期,D10(YYYY-MM-DD)
  #[yaserde(rename = "HSTUNT")]
  pub hstunt: String, //`xml:"HSTUNT"`    //诊断单位
  #[yaserde(rename = "HSTCRUPRC")]
  pub hstcruprc: String, //`xml:"HSTCRUPRC"` //治疗经过
  #[yaserde(rename = "HSTLPS")]
  pub hstlps: String, //`xml:"HSTLPS"`    //转归
}

pub fn serialize_to_string<S>(x: &str, s: S) -> Result<S::Ok, S::Error>
where
  S: Serializer,
{
  s.serialize_str(common::get_base64_decode(&x.to_string()).as_str())
}
