# Report Server - Compilation and Deployment Guide

This document provides comprehensive instructions for compiling and deploying the Report Server REST API.

## Quick Start

### Windows

```cmd
# Run the automated deployment script
deploy.bat
```

### Linux/Mac

```bash
# Make script executable and run
chmod +x deploy.sh
./deploy.sh
```

## Manual Compilation Steps

### 1. Prerequisites

- Python 3.7+ (tested with Python 3.13)
- pip package manager
- Git (for version control)

### 2. Install Dependencies

```bash
cd python
pip install -r requirements.txt
```

### 3. Build Options

#### Option A: Standalone Executable

```bash
# Install PyInstaller
pip install pyinstaller

# Build executable
python build.py --executable

# Create deployment package
python build.py --package zip
```

#### Option B: Python Package

```bash
# Create deployment package without executable
python build.py --package zip
```

#### Option C: Docker Image

```bash
# Build Docker image
python build.py --docker reportserver:latest

# Or use Docker Compose
docker-compose build
```

#### Option D: All-in-One Build

```bash
# Build everything
python build.py --all
```

## Deployment Methods

### Method 1: Direct Python Deployment

**Pros:** Simple, easy to debug
**Cons:** Requires Python on target system

```bash
# Start development server
cd python
python start_server.py

# Start production server with Gunicorn
pip install gunicorn
gunicorn --config gunicorn.conf.py wsgi:app
```

### Method 2: Standalone Executable

**Pros:** No Python required on target system
**Cons:** Larger file size, platform-specific

```bash
# After building executable
cd dist/reportserver_package
./start_server.bat  # Windows
./start_server.sh   # Linux/Mac
```

### Method 3: Docker Deployment

**Pros:** Consistent environment, easy scaling
**Cons:** Requires Docker

```bash
# Using Docker Compose (recommended)
docker-compose up -d

# Or direct Docker
docker build -t reportserver python/
docker run -d -p 8080:8080 \
  -v $(pwd)/config:/app/config:ro \
  -v $(pwd)/fonts:/app/fonts:ro \
  -v $(pwd)/reports:/app/reports \
  reportserver
```

### Method 4: System Service

#### Windows Service (using NSSM)

```cmd
# Download NSSM from https://nssm.cc/
nssm install ReportServer
nssm set ReportServer Application "C:\path\to\reportserver.exe"
nssm set ReportServer AppDirectory "C:\path\to\reportserver"
nssm start ReportServer
```

#### Linux Systemd Service

```bash
# Create service file
sudo nano /etc/systemd/system/reportserver.service

# Enable and start
sudo systemctl enable reportserver
sudo systemctl start reportserver
```

## Configuration

### Environment Variables

```bash
# Server configuration
export REPORT_SERVER_HOST=0.0.0.0
export REPORT_SERVER_PORT=8080
export REPORT_SERVER_DEBUG=false

# Paths
export REPORT_OUTPUT_DIR=./reports
export REPORT_LOG_LEVEL=INFO
```

### Required Files

1. **config/nodipexam.toml** - Database configuration
2. **fonts/simhei.ttf** - Chinese font
3. **fonts/simsun.ttc** - Chinese font

### Directory Structure

```
reportserver/
├── reportserver.exe (or Python files)
├── config/
│   └── nodipexam.toml
├── fonts/
│   ├── simhei.ttf
│   └── simsun.ttc
├── reports/ (created automatically)
├── log/ (created automatically)
└── start_server.* (startup scripts)
```

## Production Deployment

### 1. Reverse Proxy Setup (Nginx)

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /api/ {
        proxy_pass http://127.0.0.1:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_read_timeout 300;
    }

    location /reports/ {
        alias /path/to/reports/;
        autoindex on;
    }
}
```

### 2. SSL/HTTPS Configuration

```bash
# Using Let's Encrypt
certbot --nginx -d your-domain.com
```

### 3. Monitoring and Logging

```bash
# Check service status
systemctl status reportserver

# View logs
tail -f log/reportserver.log
tail -f log/gunicorn_access.log

# Health check
curl http://localhost:8080/api/health
```

## Performance Optimization

### 1. Gunicorn Configuration

```python
# gunicorn.conf.py
workers = multiprocessing.cpu_count() * 2 + 1
timeout = 300  # For long report generation
max_requests = 1000
preload_app = True
```

### 2. Nginx Optimization

```nginx
# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

# Compression
gzip on;
gzip_types application/json text/css application/javascript;

# Caching
location /reports/ {
    expires 1h;
    add_header Cache-Control "public, immutable";
}
```

### 3. Database Optimization

- Use connection pooling
- Optimize database queries
- Regular database maintenance

## Security Considerations

### 1. Network Security

```bash
# Firewall rules
ufw allow 80/tcp
ufw allow 443/tcp
ufw deny 8080/tcp  # Block direct access
```

### 2. File Security

```bash
# Set proper permissions
chmod 755 reportserver
chmod 644 config/nodipexam.toml
chmod 755 reports/
```

### 3. Application Security

- Validate all input parameters
- Sanitize file paths
- Regular security updates
- Monitor access logs

## Troubleshooting

### Common Issues

1. **Font not found errors**

   ```bash
   # Check font files exist
   ls -la fonts/
   # Ensure correct paths in report.py
   ```

2. **Database connection errors**

   ```bash
   # Verify config file
   cat config/nodipexam.toml
   # Test database connectivity
   ```

3. **Permission errors**

   ```bash
   # Fix directory permissions
   chmod 755 reports/ log/
   chown -R reportserver:reportserver /opt/reportserver/
   ```

4. **Port already in use**
   ```bash
   # Find process using port
   netstat -tulpn | grep 8080
   # Kill process or change port
   ```

### Debug Mode

```bash
# Enable debug logging
export REPORT_SERVER_DEBUG=true
export REPORT_LOG_LEVEL=DEBUG
python start_server.py
```

### Log Analysis

```bash
# Monitor logs in real-time
tail -f log/reportserver.log | grep ERROR

# Search for specific errors
grep "Database" log/reportserver.log
```

## Backup and Recovery

### 1. Configuration Backup

```bash
# Backup configuration
tar -czf config-backup-$(date +%Y%m%d).tar.gz config/

# Backup reports
tar -czf reports-backup-$(date +%Y%m%d).tar.gz reports/
```

### 2. Database Backup

```bash
# MySQL backup
mysqldump -u username -p database_name > backup.sql
```

### 3. Automated Backup Script

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "reportserver-backup-$DATE.tar.gz" config/ reports/ log/
```

## Scaling and High Availability

### 1. Load Balancing

```nginx
upstream reportserver {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}
```

### 2. Database Clustering

- Master-slave replication
- Connection pooling
- Read replicas for reporting

### 3. File Storage

- Shared network storage (NFS)
- Object storage (S3, MinIO)
- CDN for report distribution

## API Testing

### 1. Health Check

```bash
curl http://localhost:8080/api/health
```

### 2. Generate Report

```bash
curl -X POST http://localhost:8080/api/reports/generate \
  -H "Content-Type: application/json" \
  -d '{"report_id": 12, "report_format": 2}'
```

### 3. Automated Testing

```bash
# Run test suite
python test_api.py --url http://localhost:8080/api
```

## Support and Maintenance

### 1. Regular Maintenance

- Update dependencies monthly
- Clean old report files
- Monitor disk space
- Review security logs

### 2. Performance Monitoring

- Monitor response times
- Track error rates
- Monitor resource usage
- Set up alerts

### 3. Documentation

- Keep deployment notes
- Document configuration changes
- Maintain runbooks
- Update API documentation

For additional support, check the logs in `log/reportserver.log` and refer to the API documentation in `API_DOCUMENTATION.md`.
