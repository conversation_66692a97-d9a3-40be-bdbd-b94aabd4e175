use anyhow::{anyhow, Result};
use chrono::NaiveDateTime;
use dataservice::entities::prelude::*;
use futures_util::stream::TryStreamExt;
use tiberius::Row;
use tokio::net::TcpStream;
use tokio_util::compat::Compat;
// "server=tcp:localhost,1433;IntegratedSecurity=true;TrustServerCertificate=true".to_owned()

#[derive(Debug)]
pub struct RecvMssql;

impl RecvMssql {
  pub async fn query_from_external_labresult(client: &mut tiberius::Client<Compat<TcpStream>>, tjbh: &str) -> Result<Vec<VTjLisresult>> {
    let sql = format!(
      "select tjbh, brxm, xmxh, xmmc, xmdw, xmjg, gdbj, ckdz, ckgz, ckfw, jyys, bgrq, bgys,sfyc from v_tj_lisresult where tjbh = '{}'",
      tjbh
    );

    // info!("sql string is:{}", &sql);
    let mut results: Vec<VTjLisresult> = vec![];

    let stream = client.query(&sql, &[]).await?;
    let mut stream = stream.into_row_stream();

    while let Some(row) = stream.try_next().await? {
      // info!("row is:{:?}", &row);

      // let tjbh: Option<&str> = row.try_get(0)?; // index by 0-based position
      // let brxm: Option<&str> = row.try_get(1)?; // nullable column must be get as Option<...> to avoid panic.
      // let xmxh: Option<&str> = row.try_get(2)?; //
      // let xmmc: Option<&str> = row.try_get(3)?; //
      // let xmdw: Option<&str> = row.try_get(4)?; //
      // let xmjg: Option<&str> = row.try_get(5)?; //
      // let gdbj: Option<&str> = row.try_get(6)?; //
      // let ckdz: Option<&str> = row.try_get(7)?; //
      // let ckgz: Option<&str> = row.try_get(8)?; //
      // let ckfw: Option<&str> = row.try_get(9)?; //
      // let jyys: Option<&str> = row.try_get(10)?; //
      // let mut bgrq: String = "".to_string();
      // let ret: Result<Option<&str>, tiberius::error::Error> = row.try_get(11); //
      // if ret.is_ok() {
      //   bgrq = ret.unwrap().unwrap_or_default().to_string();
      //   // info!("This is string format,value is:{:?}", &bgrq);
      // } else {
      //   let ret: Result<Option<chrono::NaiveDateTime>, tiberius::error::Error> = row.try_get(11);
      //   if ret.is_ok() {
      //     info!("This is date time format,value is:{:?}", &ret);
      //     if let Some(rq) = ret.unwrap() {
      //       bgrq = rq.format("%Y-%m-%d %H:%M:%S").to_string();
      //     }
      //   } else {
      //     error!("unhandled date format....................");
      //   }
      // }
      // let bgys: Option<&str> = row.try_get(12)?; //
      // let sfyc: Option<i32> = row.try_get(13)?; //

      let tjbh: String = RecvMssql::read_value(&row, 0).unwrap_or_default();
      let brxm: String = RecvMssql::read_value(&row, 1).unwrap_or_default();
      let xmxh: String = RecvMssql::read_value(&row, 2).unwrap_or_default();
      let xmmc: String = RecvMssql::read_value(&row, 3).unwrap_or_default();
      let xmdw: String = RecvMssql::read_value(&row, 4).unwrap_or_default();
      let xmjg: String = RecvMssql::read_value(&row, 5).unwrap_or_default();
      let gdbj: String = RecvMssql::read_value(&row, 6).unwrap_or_default();
      let ckdz: String = RecvMssql::read_value(&row, 7).unwrap_or_default();
      let ckgz: String = RecvMssql::read_value(&row, 8).unwrap_or_default();
      let ckfw: String = RecvMssql::read_value(&row, 9).unwrap_or_default();
      let jyys: String = RecvMssql::read_value(&row, 10).unwrap_or_default();
      let bgrq: String = RecvMssql::read_value(&row, 11).unwrap_or_default();
      let bgys: String = RecvMssql::read_value(&row, 12).unwrap_or_default();
      let sfyc_str: String = RecvMssql::read_value(&row, 13).unwrap_or_default();

      let mut sfyc = 0;
      if !sfyc_str.is_empty() {
        sfyc = sfyc_str.parse().unwrap_or_default();
      }

      let labret = VTjLisresult {
        id: 0,
        tjbh,
        brxm,
        xmxh,
        xmmc,
        xmdw,
        xmjg,
        gdbj,
        ckdz,
        ckgz,
        ckfw,
        jyys,
        bgrq,
        bgys,
        sfyc,
      };
      results.push(labret);
    }
    if results.len() <= 0 {
      return Err(anyhow!("lis系统没有结果数据"));
    }
    // info!("results are:{:#?}", &results);

    Ok(results)
  }

  pub async fn query_from_external_pacsresult(client: &mut tiberius::Client<Compat<TcpStream>>, tjbh: &str, hassfyc: i32, _version: i32) -> Result<Vec<VTjPacsresult>> {
    let mut results: Vec<VTjPacsresult> = vec![];
    let sql: String;
    if hassfyc == 1 {
      sql = format!(
        "select tjbh,brxm,jclx,jcxm,jcmc,imagesight,imagediagnosis,jcys,sxys,bgys,bgrq,sfyc from v_tj_pacsresult where tjbh = '{}'",
        tjbh
      );
    } else {
      sql = format!(
        "select tjbh,brxm,jclx,jcxm,jcmc,imagesight,imagediagnosis,jcys,sxys,bgys,bgrq from v_tj_pacsresult where tjbh = '{}'",
        tjbh
      );
    }
    let stream = client.query(&sql, &[]).await?;
    let mut stream = stream.into_row_stream();
    while let Some(row) = stream.try_next().await? {
      // let tjbh: Option<&str> = row.try_get(0)?; // index by 0-based position
      // let brxm: Option<&str> = row.try_get(1)?; // nullable column must be get as Option<...> to avoid panic.
      // let jclx: Option<&str> = row.try_get(2)?; //
      // let jcxm: Option<&str> = row.try_get(3)?; //
      // let jcmc: Option<&str> = row.try_get(4)?; //
      // let imagesight: Option<&str> = row.try_get(5)?; //
      // let imagediagnosis: Option<&str> = row.try_get(6)?; //
      // let jcys: Option<&str> = row.try_get(7)?; //
      // let sxys: Option<&str> = row.try_get(8)?; //
      // let bgys: Option<&str> = row.try_get(9)?; //
      // let bgrq: Option<&str> = row.try_get(10)?; //
      // let mut sfyc = 0;
      // if hassfyc == 1 {
      //   if version == 1 {
      //     let sfyci: Option<i32> = row.try_get(11)?; //
      //     sfyc = sfyci.unwrap_or_default();
      //   } else {
      //     let sfs: Option<&str> = row.try_get(11)?;
      //     if sfs.is_some() {
      //       sfyc = sfs.unwrap().parse().unwrap_or_default();
      //     }
      //   }
      // }
      let tjbh: String = RecvMssql::read_value(&row, 0).unwrap_or_default();
      let brxm: String = RecvMssql::read_value(&row, 1).unwrap_or_default();
      let jclx: String = RecvMssql::read_value(&row, 2).unwrap_or_default();
      let jcxm: String = RecvMssql::read_value(&row, 3).unwrap_or_default();
      let jcmc: String = RecvMssql::read_value(&row, 4).unwrap_or_default();
      let imagesight: String = RecvMssql::read_value(&row, 5).unwrap_or_default();
      let imagediagnosis: String = RecvMssql::read_value(&row, 6).unwrap_or_default();
      let jcys: String = RecvMssql::read_value(&row, 7).unwrap_or_default();
      let sxys: String = RecvMssql::read_value(&row, 8).unwrap_or_default();
      let bgys: String = RecvMssql::read_value(&row, 9).unwrap_or_default();
      let bgrq: String = RecvMssql::read_value(&row, 10).unwrap_or_default();
      let mut sfyc_str = "".to_string();
      if hassfyc == 1 {
        sfyc_str = RecvMssql::read_value(&row, 11).unwrap_or_default();
      }
      let mut sfyc = 0;
      if !sfyc_str.is_empty() {
        sfyc = sfyc_str.parse().unwrap_or_default();
      }
      let imagepath = Some("".to_string());
      let datas = Some("".to_string());
      let pret: VTjPacsresult = VTjPacsresult {
        id: 0,
        tjbh,
        brxm,
        jclx,
        jcxm,
        jcmc,
        imagesight,
        imagediagnosis,
        jcys,
        sxys,
        bgys,
        bgrq,
        sfyc,
        imagepath,
        datas,
      };
      results.push(pret);
    }
    Ok(results)
  }

  pub async fn query_lis_xmxx(client: &mut tiberius::Client<Compat<TcpStream>>) -> Result<Vec<VLisXmxx>> {
    let sql = "select itemid, chinesename, englishab, unit from v_lis_xmxx";
    let mut results: Vec<VLisXmxx> = vec![];

    let stream = client.query(sql, &[]).await?;
    let mut stream = stream.into_row_stream();

    while let Some(row) = stream.try_next().await? {
      //
      let itemid: Option<&str> = row.try_get(0)?; // index by 0-based position
      let chinesename: Option<&str> = row.try_get(1)?; // nullable column must be get as Option<...> to avoid panic.
      let englishab: Option<&str> = row.try_get(2)?; //
      let unit: Option<&str> = row.try_get(3)?; //

      let labret = VLisXmxx {
        itemid: itemid.unwrap_or_default().to_string(),
        chinesename: chinesename.unwrap_or_default().to_string(),
        englishab: englishab.unwrap_or_default().to_string(),
        unit: unit.unwrap_or_default().to_string(),
      };
      results.push(labret);
    }

    Ok(results)
  }

  pub fn read_value(row: &Row, idx: usize) -> Result<String> {
    if idx >= row.columns().len() {
      return Err(anyhow!("index out size"));
    }
    let ret = match row.columns()[idx].column_type() {
      tiberius::ColumnType::Null => "".to_string(),
      tiberius::ColumnType::Bit
      | tiberius::ColumnType::Int1
      | tiberius::ColumnType::Int2
      | tiberius::ColumnType::Int4
      | tiberius::ColumnType::Int8
      | tiberius::ColumnType::Intn
      | tiberius::ColumnType::Numericn
      | tiberius::ColumnType::Bitn => {
        let ret2: Option<i32> = row.try_get(idx)?;
        if let Some(ret) = ret2 {
          ret.to_string()
        } else {
          "".to_string()
        }
      }
      tiberius::ColumnType::Datetime4
      | tiberius::ColumnType::Datetime
      | tiberius::ColumnType::Datetime2
      | tiberius::ColumnType::Datetimen
      | tiberius::ColumnType::Daten
      | tiberius::ColumnType::Timen
      | tiberius::ColumnType::DatetimeOffsetn => {
        let ret2: Option<NaiveDateTime> = row.try_get(idx)?;
        if let Some(ret) = ret2 {
          ret.format("%Y-%m-%d %H:%M:%S").to_string()
        } else {
          "".to_string()
        }
      }
      tiberius::ColumnType::Float4
      | tiberius::ColumnType::Float8
      | tiberius::ColumnType::Money
      | tiberius::ColumnType::Money4
      | tiberius::ColumnType::Decimaln
      | tiberius::ColumnType::Floatn => {
        let ret2: Option<f64> = row.try_get(idx)?;
        if let Some(ret) = ret2 {
          format!("{}", ret)
        } else {
          "".to_string()
        }
      }

      tiberius::ColumnType::BigVarBin
      | tiberius::ColumnType::BigVarChar
      | tiberius::ColumnType::BigBinary
      | tiberius::ColumnType::BigChar
      | tiberius::ColumnType::Guid
      | tiberius::ColumnType::NVarchar
      | tiberius::ColumnType::NChar
      | tiberius::ColumnType::Xml
      | tiberius::ColumnType::Udt
      | tiberius::ColumnType::Text
      | tiberius::ColumnType::NText
      | tiberius::ColumnType::SSVariant => {
        let ret2: Option<&str> = row.try_get(idx)?;
        if let Some(ret) = ret2 {
          format!("{}", ret)
        } else {
          "".to_string()
        }
      }
      _ => "".to_string(),
    };
    Ok(ret)
  }
}
