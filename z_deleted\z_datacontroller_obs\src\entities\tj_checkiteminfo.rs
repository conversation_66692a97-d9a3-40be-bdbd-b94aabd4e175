use serde::{Deserialize, Serialize};

#[crud_table(id_name:"id"|id_type:"i64"|table_name:"tj_checkiteminfo")]
#[derive(C<PERSON>, Debug, Default, Serialize, Deserialize)]
pub struct TjCheckiteminfo {
    pub id: i64,
    pub tj_testid: String,
    pub tj_itemid: String,
    pub tj_synid: String,
    pub tj_itemname: String,
    pub tj_result: String,
    pub tj_itemrange: String,
    pub tj_itemunit: String,
    pub tj_abnormalflag: i32,
    pub tj_barflag: i32,
    pub tj_abnormalshow: String,
    pub tj_combineflag: i32,
    pub tj_deptorder: i32,
    pub tj_showorder: i32,
    pub tj_combineorder: i32,
    pub tj_deptid: String,
    pub tj_barnum: String,
    pub tj_barcode: String,
    pub tj_checkdate: i64,
    pub tj_checkdoctor: String,
    pub tj_recheckdoctor: String,
    pub tj_recheckdate: i64,
}
