// use anyhow::{anyhow, Result};
use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct VExternalLis {
  pub testid: String,
  pub testername: String,
  pub tid: String,
  pub psex: i32,
  pub birthdate: String,
  // pub phone: String,
  pub age: i32,
  pub itemid: String,
  pub itemname: String,
  pub sampletype: String,
  pub deptid: String,
  pub deptname: String,
  pub requesterid: String,
  pub requestername: String,
  pub requestdate: String,
  pub diag: String,
  pub price: String,
  pub phone: String,
}
crud!(VExternalLis {}, "v_external_lis");
rbatis::impl_select!(VExternalLis{query_many(testid:&str, barcode:&str) =>
  "`where id > 0 `
  if testid != '':
    ` and testid = #{testid} `
  if barcode != '':
    ` and tid = #{barcode} `"});
