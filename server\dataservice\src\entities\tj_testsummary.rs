//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Default, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "tj_testsummary")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tj_testid: String,
  pub tj_deptid: String,
  pub tj_summary: String,
  pub tj_suggestion: String,
  pub tj_isfinished: i32,
  pub tj_doctorid: String,
  pub tj_doctor: String,
  pub tj_date: i64,
  pub tj_forceend: i32,
  pub tj_checkdoctor: String,
  pub tj_checkdate: i64,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
