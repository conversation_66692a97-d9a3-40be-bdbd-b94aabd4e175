use anyhow::{anyhow, Result};
use datacontroller::{datasetup::DbConnection, entities::tj_patient::TjPatient};

use crate::prelude::{DictService, PID};
pub struct PatientSvc;

impl PatientSvc {
  pub async fn check_and_update_patient(ptinfo: &TjPatient, dictsvc: &DictService, db: &DbConnection) -> Result<TjPatient> {
    let mut ptinfo_clone = ptinfo.to_owned();
    let idcard = ptinfo_clone.tj_pidcard.clone();

    if idcard.is_empty() {
      //没有身份证号码，则创建新的病人信息
      let pid = dictsvc.generate_new_id(PID, db).await;
      if pid.as_ref().is_err() {
        return Err(anyhow!("{:?}", pid.err().unwrap().to_string()));
      }
      ptinfo_clone.tj_pid = pid.unwrap();
      ptinfo_clone.id = 0;
      //insert into database
      let ret = TjPatient::save(&ptinfo_clone, db).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{:?}", ret.err().unwrap().to_string()));
      }
      return Ok(ret.unwrap());
    }
    let ret = TjPatient::query_many_by_idcard(&vec![idcard], db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{:?}", ret.err().unwrap().to_string()));
    }
    let ptinfos_new = ret.unwrap();
    if ptinfos_new.len() <= 0 {
      //数据库不存在该身份证号码
      ptinfo_clone.id = 0;
    } else {
      ptinfo_clone.id = ptinfos_new[0].id;
    }
    let ret = TjPatient::save(&ptinfo_clone, db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{:?}", ret.err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
