use crate::{
  client::httpclient::HttpClient,
  constant::API_MEDEXAM_SYNC,
  dataview::{
    medinfoview::MedexaminfosView,
    nxcorpinfo, nxmedexaminfo,
    nxmodel::{self, NxMedreportinfo},
  },
  dto::dto::{IdsDto, MedexamResultDetail, MedresultResponse, ReportResult, SyncQueryResult, TidNumDto},
  prelude::{
    DictService, ExamStatus, TestType, API_CORP_REPORT_UPLOAD, API_CORP_UPDATE, API_MEDEXAM_PHOTO_UPLOAD, API_MEDEXAM_REPORT, API_MEDEXAM_REPORT_UPLOAD, API_MEDEXAM_RESULT,
    API_MEDSTATUS_UPDATE, DICTTYPE,
  },
  response::httpresponse::{ResponseBody, CODE_HTTP_OK},
  service::{audiogramsvc::AudiogramSvc, corpsvc::CorpSvc, medexamsvc},
};
use anyhow::{anyhow, Result};
use datacontroller::{
  datasetup::DbConnection,
  entities::{
    ss_dictionary::SsDictionary, tj_audiogramdetail::TjAudiogramdetail, tj_checkallnew::TjCheckallnew, tj_checkiteminfo::TjCheckiteminfo, tj_corpinfo::TjCorpinfo,
    tj_corpoccureport::TjCorpoccureport, tj_departinfo::TjDepartinfo, tj_medexaminfo::TjMedexaminfo, tj_patient::TjPatient, tj_staffadmin::TjStaffadmin, tj_testsummary::TjTestsummary,
  },
};
use std::collections::HashSet;
use std::path;
use utility::timeutil::current_timestamp;

pub struct MedexamSyncSvc;

impl MedexamSyncSvc {
  ///从polar平台同步预约的体检信息和企业信息
  pub async fn sync_medinfo_and_corpinfos(userinfo: &TjStaffadmin, orgid: &str, server: &str, dictsvc: &DictService, db: &DbConnection) -> Result<Vec<MedexaminfosView>> {
    if server.is_empty() {
      return Ok(vec![]);
    }
    info!("start to sync medinfo and corp infos......");
    let ret = HttpClient::send_request("", orgid, API_MEDEXAM_SYNC, server).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }

    let result_text = ret.unwrap();
    info!("Result from server:{}", &result_text);
    let ret = serde_json::from_str(result_text.as_str());
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let result_response: ResponseBody<SyncQueryResult> = ret.unwrap();
    if result_response.code != CODE_HTTP_OK {
      return Err(anyhow!("{}", result_response.message));
    }
    info!("Sync result:{:#?}", &result_response);
    if result_response.data.corpinfos.len() > 0 {
      //insert new corp infos
      let corpinfos: Vec<TjCorpinfo> = result_response.data.corpinfos.iter().map(|x| nxcorpinfo::convert_to_tj_corpinfo(x)).collect();
      if corpinfos.len() > 0 {
        let ret = CorpSvc::check_corpinfos(&corpinfos, db).await;
        // let ret = TjCorpinfo::insert_many(&corpinfos, db).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        //更新平台的企业状态
        let corpids = corpinfos.iter().map(|x| x.p_cmpid).collect::<Vec<i64>>();
        let ret = serde_json::to_string(&corpids);
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        let parms = ret.unwrap();
        info!("update corpinfo status parms:{}", &parms);
        let ret = HttpClient::send_request(&parms, orgid, API_CORP_UPDATE, server).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        let resp = ret.unwrap();
        info!("response from request:{}", &resp);
        let ret = serde_json::from_str(resp.as_str());
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        let result_response: ResponseBody<String> = ret.unwrap();
        if result_response.code != CODE_HTTP_OK {
          return Err(anyhow!("{}", result_response.message));
        }
      }
    }
    let mut appointed_medinfos: Vec<MedexaminfosView> = vec![];
    info!("start to make appointment......");
    if result_response.data.medinfos.len() > 0 {
      //insert new appointment
      let mut medinfos: Vec<MedexaminfosView> = result_response.data.medinfos.iter().map(|x| nxmedexaminfo::convert_to_medexam_info_view(x)).collect();
      info!("converted medexaminfos:{:#?}", &medinfos);
      if medinfos.len() > 0 {
        let cmpids: Vec<i64> = medinfos.iter().map(|x| x.p_cmpid).collect::<HashSet<_>>().into_iter().collect::<Vec<i64>>();
        info!("platform cmpids:{:?}", &cmpids);
        let ret = TjCorpinfo::query_many_by_cmpids(&cmpids, db).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        let corpinfos = ret.unwrap();
        info!("corpinfos:{:#?}", &corpinfos);
        for val in medinfos.iter_mut() {
          let cmp = corpinfos.iter().find(|xc| xc.p_cmpid == val.p_cmpid);
          if cmp.is_some() {
            info!("find corpinfo by cmpid:{}", val.p_cmpid);
            val.corpnum = cmp.unwrap().id;
          }
        }
        //make appointment
        let ret = medexamsvc::MedexamService::medexam_appoint(userinfo, &mut medinfos, dictsvc, db).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        appointed_medinfos = ret.unwrap();

        //更新平台的体检状态
        let medids: Vec<i64> = medinfos.iter().map(|x| x.p_medid).collect();
        let tids = TidNumDto {
          tid: medids,
          pid: vec![],
          code: ExamStatus::Register as i64,
        };
        let ret = serde_json::to_string(&tids);
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        let parms = ret.unwrap();
        info!("update medexam status parms:{}", &parms);
        let ret = HttpClient::send_request(&parms, orgid, API_MEDSTATUS_UPDATE, server).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        let resp = ret.unwrap();
        info!("response from request:{}", &resp);
        let ret = serde_json::from_str(resp.as_str());
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        let result_response: ResponseBody<String> = ret.unwrap();
        info!("result_response :{:?}", &result_response);
        if result_response.code != CODE_HTTP_OK {
          return Err(anyhow!("{}", result_response.message));
        }
      }
    }

    info!("make appointment ended......");
    Ok(appointed_medinfos)
  }

  pub async fn update_med_status(_userinfo: &TjStaffadmin, dto: &IdsDto, orgid: &str, server: &str, db: &DbConnection) -> Result<()> {
    info!("start to update medexam status......");
    if server.is_empty() {
      return Ok(());
    }
    let ret = TjMedexaminfo::query_many(&dto.ids, &db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }

    let tid: Vec<i64> = ret.unwrap().iter().filter(|&f| f.p_medid > 0).map(|v| v.p_medid).collect();
    let dto: TidNumDto = TidNumDto {
      tid: tid,
      pid: vec![],
      code: dto.code,
    };
    let ret = serde_json::to_string(&dto);
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let parms = ret.unwrap();
    info!("update medexam status parms:{}", &parms);
    let ret = HttpClient::send_request(&parms, orgid, API_MEDSTATUS_UPDATE, server).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }

    Ok(())
  }

  // pub async fn upload_medinfos(_userinfo: &TjStaffadmin, testid: &str, orgid: i64, server: &str, rptfile: &str, db: &DbConnection) -> Result<TjMedexaminfo> {
  //   //从tj_medexaminfo表中获取所有的体检信息
  //   if server.is_empty() {
  //     // return Ok(vec![]);
  //     return Err(anyhow!("未配置上传服务器，不能上传结果"));
  //   }
  //   if testid.is_empty() {
  //     return Err(anyhow!("testid is empty"));
  //   }
  //   info!("start to query medinfos by:{}", &testid);
  //   let ret = TjMedexaminfo::query(testid, db).await;
  //   if ret.as_ref().is_none() {
  //     error!("can't find medinfo by testid:{}", testid);
  //     return Err(anyhow!("can't find medinfo by testid:{}", testid));
  //   }
  //   let mut medinfo = ret.unwrap();
  //   if medinfo.tj_checkstatus < ExamStatus::Allchecked as i32 {
  //     return Err(anyhow!("该体检者未总检，不能上传结果"));
  //   }
  //   info!("start to upload data to saas server......");
  //   //查找体检人员信息
  //   let ret = TjPatient::query(&medinfo.tj_pid, db).await;
  //   if ret.as_ref().is_none() {
  //     error!("can't find ptinfo by testid: {}", &medinfo.tj_testid);
  //     return Err(anyhow!("can't find ptinfo by testid: {}", &medinfo.tj_testid));
  //   }
  //   let mut ptinfo = ret.unwrap();
  //   // let ret = orgid.parse::<i64>();
  //   // if ret.as_ref().is_err() {
  //   //   error!("{}", ret.as_ref().err().unwrap().to_string());
  //   //   return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  //   // }
  //   // let orgid = ret.unwrap();
  //   let ret = MedexamSyncSvc::prepare_upload_medresults(&medinfo, &ptinfo, orgid, rptfile, db).await;
  //   if ret.as_ref().is_err() {
  //     error!("can't find ptinfo by testid: {}", &medinfo.tj_testid);
  //     return Err(anyhow!("can't find ptinfo by testid: {}", &medinfo.tj_testid));
  //   }
  //   let dto = ret.unwrap();
  //   let ret = serde_json::to_string(&dto);
  //   if ret.as_ref().is_err() {
  //     error!("{}", ret.as_ref().err().unwrap().to_string());
  //     return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  //   }
  //   let parm = ret.unwrap();
  //   //上传结果信息
  //   info!("start to send to saas server with api:{}{}", server, API_MEDEXAM_RESULT);
  //   let ret = HttpClient::send_request(&parm, &orgid.to_string(), API_MEDEXAM_RESULT, server).await;
  //   if ret.as_ref().is_err() {
  //     error!("{}", ret.as_ref().err().unwrap().to_string());
  //     return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  //   }
  //   let resp = ret.unwrap();
  //   info!("response from request:{}", &resp);
  //   let ret = serde_json::from_str::<ResponseBody<MedresultResponse>>(resp.as_str());
  //   if ret.as_ref().is_err() {
  //     error!("{}", ret.as_ref().err().unwrap().to_string());
  //     return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  //   }
  //   let result_response = ret.unwrap();
  //   if result_response.code != CODE_HTTP_OK {
  //     return Err(anyhow!("{}", result_response.message));
  //   }
  //   let resp = result_response.data;
  //   if medinfo.p_medid != resp.medid && resp.medid > 0 {
  //     medinfo.p_medid = resp.medid;
  //     let ret = TjMedexaminfo::update(&medinfo, db).await;
  //     if ret.as_ref().is_err() {
  //       error!("update medinfo error:{}", ret.as_ref().unwrap_err().to_string());
  //     }
  //   }
  //   if ptinfo.p_wkid != resp.wkid && resp.wkid > 0 {
  //     ptinfo.p_wkid = resp.wkid;
  //     let ret = TjPatient::update(&ptinfo, db).await;
  //     if ret.as_ref().is_err() {
  //       error!("update medinfo error:{}", ret.as_ref().unwrap_err().to_string());
  //     }
  //   }
  //   Ok(medinfo)
  // }

  pub async fn upload_medresults(_userinfo: &TjStaffadmin, testid: &str, filename: &str, orgid: i64, server: &str, photodir: &str, db: &DbConnection) -> Result<()> {
    //开始上传详细结果......
    info!("report file is uploaded, start to upload medinfos......");

    if server.is_empty() {
      // return Ok(vec![]);
      return Err(anyhow!("未配置上传服务器，不能上传结果"));
    }
    if testid.is_empty() {
      return Err(anyhow!("testid is empty"));
    }

    info!("start to query medinfos by:{}", &testid);
    let ret = TjMedexaminfo::query(testid, db).await;
    if ret.as_ref().is_none() {
      error!("can't find medinfo by testid:{}", testid);
      return Err(anyhow!("can't find medinfo by testid:{}", testid));
    }
    let mut medinfo = ret.unwrap();
    if medinfo.tj_checkstatus < ExamStatus::Allchecked as i32 {
      return Err(anyhow!("该体检者未总检，不能上传结果"));
    }
    info!("medinfos:{:#?}", &medinfo);

    //查找体检人员信息
    let ret = TjPatient::query(&medinfo.tj_pid, db).await;
    if ret.as_ref().is_none() {
      error!("can't find ptinfo by testid: {}", &medinfo.tj_testid);
      return Err(anyhow!("can't find ptinfo by testid: {}", &medinfo.tj_testid));
    }
    let mut ptinfo = ret.unwrap();
    info!("ptinfo:{:#?}", &ptinfo);

    let ret = MedexamSyncSvc::prepare_upload_medresults(&medinfo, &ptinfo, orgid, filename, db).await;
    if ret.as_ref().is_err() {
      error!(" {}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!(" {}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut dto = ret.unwrap();

    //如果是健康证体检，检查照片有没有，有的话，则上传照片
    let mut uploaded_photo = "".to_string();
    if medinfo.tj_testtype == TestType::JKZ as i32 {
      // if jkz_ptids.len() > 0 {
      let photo_name = ptinfo.tj_photo.to_owned();
      let full_photo = format!("{}/{}", photodir, photo_name);
      info!("健康证体检，开始上传照片:{}", &full_photo);
      if !photo_name.is_empty() && std::path::Path::new(&full_photo).exists() {
        //start to upload photo
        // info!("start to upload photo file......,photo name is:{}", &full_photo);
        let ret = HttpClient::send_upload_request(&full_photo, medinfo.p_medid, &orgid.to_string(), API_MEDEXAM_PHOTO_UPLOAD, server).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        let resp = ret.unwrap();
        info!("response from request:{}", &resp);
        let ret = serde_json::from_str(resp.as_str());
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        let result_response: ResponseBody<String> = ret.unwrap();
        info!("photo upload response:{:?}", &result_response);
        if result_response.code != CODE_HTTP_OK {
          return Err(anyhow!("{}", result_response.message));
        }
        uploaded_photo = result_response.data.to_string();
      }
    }
    info!("开始上传报告文件......");
    //上传报告文件
    // let medid = medinfos.get(0).unwrap().p_medid;
    // let medid = medinfo.p_medid;
    let mut report_file_name = "".to_string();
    if !filename.is_empty() {
      //start to upload file
      let path = path::Path::new(&filename);
      if path.exists() {
        //do upload
        info!("start to upload report file......,file name is:{}", filename);
        let ret = HttpClient::send_upload_request(&filename, medinfo.p_medid, &orgid.to_string(), API_MEDEXAM_REPORT_UPLOAD, server).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        let resp = ret.unwrap();
        info!("response from request:{}", &resp);
        let ret = serde_json::from_str(resp.as_str());
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        let result_response: ResponseBody<String> = ret.unwrap();
        if result_response.code != CODE_HTTP_OK {
          return Err(anyhow!("{}", result_response.message));
        }
        report_file_name = result_response.data.to_owned();
        info!("uploaded file name is:{}", report_file_name);
      }
    }

    if !report_file_name.is_empty() {
      dto.medinfo.p_rptfile = report_file_name.clone();
      dto.checkall.p_rptfile = report_file_name;
    }
    if !uploaded_photo.is_empty() {
      dto.wkinfo.wx_photo = uploaded_photo;
    }

    info!("medexaminfo prepared, nxwkinfo is:{:#?}", &dto.wkinfo);
    info!("medexaminfo prepared, nxmedinfo is:{:#?}", &dto.medinfo);
    info!("medexaminfo prepared, nxcheckalls is:{:#?}", &dto.checkall);

    //开始上传结果信息......
    let ret = serde_json::to_string(&dto);
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let parm = ret.unwrap();
    //上传结果信息
    info!("start to send to saas server with api:{}{}", server, API_MEDEXAM_RESULT);
    let ret = HttpClient::send_request(&parm, &orgid.to_string(), API_MEDEXAM_RESULT, server).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let resp = ret.unwrap();
    info!("response from request:{}", &resp);
    let ret = serde_json::from_str::<ResponseBody<MedresultResponse>>(resp.as_str());
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let result_response = ret.unwrap();
    if result_response.code != CODE_HTTP_OK {
      return Err(anyhow!("{}", result_response.message));
    }

    let resp = result_response.data;
    if medinfo.p_medid != resp.medid && resp.medid > 0 {
      medinfo.p_medid = resp.medid;
      medinfo.tj_checkstatus = ExamStatus::Syncd as i32;
      medinfo.tj_uploadtime = current_timestamp();
      let ret = TjMedexaminfo::update(&medinfo, db).await;
      if ret.as_ref().is_err() {
        error!("update medinfo error:{}", ret.as_ref().unwrap_err().to_string());
      }
    }

    if ptinfo.p_wkid != resp.wkid && resp.wkid > 0 {
      ptinfo.p_wkid = resp.wkid;
      let ret = TjPatient::update(&ptinfo, db).await;
      if ret.as_ref().is_err() {
        error!("update medinfo error:{}", ret.as_ref().unwrap_err().to_string());
      }
    }

    // Ok(medinfo)

    Ok(())
  }

  pub async fn upload_corp_report(rptids: &Vec<String>, rptfile: &str, orgid: &str, server: &str, db: &DbConnection) -> Result<()> {
    if server.is_empty() {
      return Ok(());
    }
    if rptids.len() <= 0 {
      return Err(anyhow!("没有需要同步的报告信息"));
    }

    info!("report ids:{:?},report file:{}", &rptids, &rptfile);

    let mut report_file = "".to_string();
    if !rptfile.is_empty() {
      let path = path::Path::new(&rptfile);
      if path.exists() {
        info!("start to upload corp report file:{}", &rptfile);

        let ret = HttpClient::send_upload_request(&rptfile, 0, &orgid.to_string(), API_CORP_REPORT_UPLOAD, server).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        let resp = ret.unwrap();
        info!("response from request:{}", &resp);
        let ret = serde_json::from_str(resp.as_str());
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.err().unwrap().to_string()));
        }
        let result_response: ResponseBody<String> = ret.unwrap();
        if result_response.code != CODE_HTTP_OK {
          return Err(anyhow!("{}", result_response.message));
        }
        report_file = result_response.data;
      }
    }

    let rptids = rptids.iter().map(|e| e.parse::<i64>().unwrap_or(0)).collect::<Vec<i64>>();
    info!("report ids with i64 is:{:?}", &rptids);
    let ret = TjCorpoccureport::query_many_by_dto(&rptids, "", "", "", "", "", "", "", db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let reports = ret.unwrap();
    if reports.len() <= 0 {
      return Err(anyhow!("没有需要同步的报告信息"));
    }
    // info!("reports:{:#?}", &reports);
    let corpids: Vec<i64> = reports.iter().map(|x| x.tj_corpid.parse::<i64>().unwrap_or(0)).collect();
    let ret = TjCorpinfo::query_many(&corpids, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let corpinfos = ret.unwrap();

    // info!("corpinfos:{:#?}", &corpinfos);
    //过滤不是平台的报告
    let nxreports: Vec<NxMedreportinfo> = reports
      .iter()
      .map(|x| {
        let corpinfo = corpinfos.iter().find(|&p| p.id == x.tj_corpid.parse::<i64>().unwrap_or_default());
        nxmodel::convert_from_tj_report(x, corpinfo, orgid.parse::<i64>().unwrap(), &report_file)
      })
      .collect();

    let nxreports: Vec<NxMedreportinfo> = nxreports.iter().filter(|f| f.p_cmpid > 0).map(|v| v.to_owned()).collect();
    if nxreports.len() <= 0 {
      error!("没有需要同步的报告信息");
      return Err(anyhow!("没有需要同步的报告信息,请检查企业信息是否已经与平台同步"));
    }

    //报告人员信息暂时不用，直接用pdf报告
    // let ret = TjCorpoccureportinfo::query_many_by_dto(&rptids, db).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().err().unwrap().to_string());
    //   return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    // }
    // let report_details = ret.unwrap();
    // let testids: Vec<String> = report_details.iter().map(|x| x.tj_test_id.to_owned()).collect::<HashSet<_>>().into_iter().collect();
    // info!("test ids : {:?}", &testids);
    // let ret = TjMedexaminfo::query_many(&testids, db).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().err().unwrap().to_string());
    //   return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    // }
    // let medinfos = ret.unwrap();
    // let nxreport_details: Vec<NxMedReportdetails> = report_details
    //   .iter()
    //   .map(|x| {
    //     let medinfo = medinfos.iter().find(|m| m.tj_testid.eq_ignore_ascii_case(&x.tj_test_id));
    //     nxmodel::convert_from_tj_corpinfo_detail(x, medinfo)
    //   })
    //   .collect();
    // //过滤不是平台预约的体检信息
    // let nxreport_details: Vec<NxMedReportdetails> = nxreport_details.iter().filter(|&f| f.p_medid > 0).map(|v| v.to_owned()).collect();
    // if nxreport_details.len() <= 0 {
    //   error!("没有需要同步的报告信息");
    //   return Err(anyhow!("没有需要同步的报告信息,所有体检结果需要是平台同步的结果信息"));
    // }
    let dto = ReportResult {
      reports: nxreports,
      reportdetails: vec![],
    };
    let ret = serde_json::to_string(&dto);
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let parm = ret.unwrap();
    let ret = HttpClient::send_request(&parm, &orgid.to_string(), API_MEDEXAM_REPORT, server).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let resp = ret.unwrap();
    info!("response from request:{}", &resp);
    let ret = serde_json::from_str(resp.as_str());
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let result_response: ResponseBody<String> = ret.unwrap();
    if result_response.code != CODE_HTTP_OK {
      return Err(anyhow!("{}", result_response.message));
    }

    Ok(())
  }

  async fn prepare_upload_medresults(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, orgid: i64, rptfile: &str, db: &DbConnection) -> Result<MedexamResultDetail> {
    let testid = &medinfo.tj_testid.to_owned();
    let ids: Vec<i64> = vec![];
    let ret = TjStaffadmin::query_many(&ids, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let doctors = ret.unwrap();

    let ret = TjDepartinfo::query_many(&ids, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let deptinfos = ret.unwrap();

    let ret = SsDictionary::query_many(DICTTYPE::DictCheckall as i32, 0, db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let dicts = ret.unwrap();

    //查找企业信息
    let ret = TjCorpinfo::query(medinfo.tj_corpnum, db).await;
    if ret.as_ref().is_none() {
      error!("can't find corpinfo by corpnum: {}", &medinfo.tj_corpnum);
      return Err(anyhow!("can't find corpinfo by corpnum: {}", &medinfo.tj_corpnum));
    }
    let corpinfo = ret.unwrap();
    info!("上传时企业信息:{:#?}", &corpinfo);
    // if corpinfo.p_cmpid <= 0 {
    //   error!("根据企业id:{}查到的企业信息还没有配置", medinfo.tj_corpnum);
    //   return Err(anyhow!("该企业未配置，不能上传结果"));
    // }

    //从tj_checkiteminfo表中获取所有的检查结果信息
    let ret = TjCheckiteminfo::query_many(&vec![testid], db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let checkitems = ret.unwrap();
    //从tj_testsummary表中获取所有的科室小结信息
    let ret = TjTestsummary::query_many(&vec![testid], db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let summaries = ret.unwrap();
    //从tj_checkallnew表中获取所有的总检结果信息
    let ret = TjCheckallnew::query(testid, db).await;
    if ret.as_ref().is_none() {
      error!("can't find ptinfo by testid: {}", &medinfo.tj_testid);
      return Err(anyhow!("can't find ptinfo by testid: {}", &medinfo.tj_testid));
    }
    let checkalls = ret.unwrap();

    let ret = TjAudiogramdetail::query_many(&vec![testid], db).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.err().unwrap().to_string()));
    }
    let audiogramdetails = ret.unwrap();

    let nxwkinfo = nxmodel::convert_from_tj_patientinfo(&ptinfo);
    let nxcheckalls = nxmodel::convert_from_tj_checkallnew(&checkalls, &medinfo, &dicts, &doctors, orgid, rptfile); //

    let nxmedinfo = nxmodel::convert_from_tj_medexaminfo(orgid, &medinfo, &ptinfo, &checkalls, &corpinfo);

    let nxsummaries = summaries
      .iter()
      .filter(|&f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid))
      .map(|x| nxmodel::convert_from_tj_testsummary(x, &medinfo, &deptinfos, orgid))
      .collect();

    let nxcheckresults = checkitems
      .iter()
      .filter(|&f| f.tj_testid.eq_ignore_ascii_case(&medinfo.tj_testid))
      .map(|x| nxmodel::convert_from_tj_checkiteminfo(x, &medinfo, &deptinfos, orgid))
      .collect();

    let audiograms = AudiogramSvc::convert_to_saas_audiogram_details(&audiogramdetails, &medinfo, orgid);
    // }

    info!("medexaminfo prepared, nxwkinfo is:{:#?}", &nxwkinfo);
    info!("medexaminfo prepared, nxmedinfo is:{:#?}", &nxmedinfo);
    info!("medexaminfo prepared, nxcheckalls is:{:#?}", &nxcheckalls);

    let dto = MedexamResultDetail {
      wkinfo: nxwkinfo,
      checkall: nxcheckalls,
      medinfo: nxmedinfo,
      checkresults: nxcheckresults,
      summaries: nxsummaries,
      audiograms,
    };

    Ok(dto)
  }
}
