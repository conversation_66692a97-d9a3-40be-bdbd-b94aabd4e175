use crate::{
  app::{CardlistqueryDto, ParmDto},
  config::settings::Settings,
  service::transcontroller::TransController,
  webserver::httpresponse::HttpCode,
};

use super::httpresponse::{response_error, ResponseBody};
// use anyhow::Result;
use axum::extract::{Extension, Json};
// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
// use nodipservice::common::syscache::SysCache;
// use nodipservice::prelude::*;
// use log::*;
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

pub async fn do_upload_data(
  Extension(db): Extension<Arc<DbConnection>>,
  Extension(config): Extension<Arc<Settings>>,
  // Extension(cache): Extension<Arc<SysCache>>,
  Json(payload): Json<serde_json::Value>,
) -> Json<Value> {
  info!("do_upload_data payload is: {:?}", &payload);
  let up_parm: Result<ParmDto, serde_json::Error> = serde_json::from_value(payload);
  if up_parm.as_ref().is_err() {
    return response_error(&up_parm.as_ref().err().unwrap().to_string());
  }
  let upload_parm = up_parm.unwrap();

  if upload_parm.tid.len() <= 0 {
    return response_error(&"没有需要上报的数据信息".to_string());
  }
  //start to upload
  let trans_ctl = TransController::new(&db, &config);
  let ret = trans_ctl.do_upload(&upload_parm).await;
  if ret.as_ref().is_err() {
    error!("{}", ret.as_ref().err().unwrap().to_string());
    return response_error(&ret.err().unwrap().to_string());
  }

  let res = ResponseBody::new(HttpCode::OK as i32, ret.unwrap().as_str(), upload_parm.tid[0].as_str());
  info!("upload response:{res:?}");
  Json(serde_json::json!(res))
}

pub async fn do_query_refused_profiles(Extension(db): Extension<Arc<DbConnection>>, Extension(config): Extension<Arc<Settings>>, Json(payload): Json<serde_json::Value>) -> Json<Value> {
  info!("do_query_refused_profiles payload is: {:?}", &payload);
  let dto: Result<CardlistqueryDto, serde_json::Error> = serde_json::from_value(payload);
  if dto.as_ref().is_err() {
    error!("do_query_refused_profiles dto error: {:?}", &dto.as_ref().err().unwrap().to_string());
    return response_error(&dto.as_ref().err().unwrap().to_string());
  }
  let query_parm = dto.unwrap();

  let trans_ctl = TransController::new(&db, &config);
  let ret = trans_ctl.do_query_refused_profiles(&query_parm).await;
  if ret.as_ref().is_err() {
    error!("{}", ret.as_ref().err().unwrap().to_string());
    return response_error(&ret.err().unwrap().to_string());
  }
  let ret_data = ret.unwrap();
  info!("do_query_refused_profiles ret is: {:?}", &ret_data);
  Json(serde_json::json!(ret_data))
}
