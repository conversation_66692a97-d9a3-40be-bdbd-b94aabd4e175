use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use tracing::*;

use crate::{common::constant, dto::IteminfoQueryDto, SYSCACHE};
pub struct ItemSvc;

impl ItemSvc {
  //item types
  pub async fn query_itemtypes(typeids: &Vec<i64>, db: &DbConnection) -> Result<Vec<TjItemtype>> {
    let ret = crate::SYSCACHE.get().unwrap().get_itemtypes(typeids, &db).await;
    Ok(ret)
    // let ret = TjItemtype::query_many( typeids, &db.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
  }

  pub async fn save_itemtype(info: &TjItemtype, db: &DbConnection) -> Result<i64> {
    let ret = TjItemtype::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let db_clone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Itemtype as i32, &db_clone).await;
    });
    Ok(ret.unwrap())
  }
  pub async fn delete_itemtype(info: &TjItemtype, db: &DbConnection) -> Result<i64> {
    info!("start to delete itemtype:{info:?}");
    let ret = TjItemtype::delete(&vec![info.id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    // info!("delete result:{:?}", &ret);
    let db_clone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Itemtype as i32, &db_clone).await;
    });
    Ok(1 as i64)
  }
  // combine items
  pub async fn query_combine_items(combids: &Vec<String>, db: &DbConnection) -> Result<Vec<TjCombineinfo>> {
    // let codes: Vec<&str> = combids.iter().map(|v| v.as_str()).collect();
    // let ret = TjCombineinfo::query_many( &codes).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
    let ret = crate::SYSCACHE.get().unwrap().get_combineinfos(&combids, &db).await;
    // info!("获取组合项目结果:{ret:?}");
    Ok(ret)
  }

  pub async fn save_combine_items(info: &TjCombineinfo, db: &DbConnection) -> Result<i64> {
    let ret = TjCombineinfo::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let db_clone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Combine as i32, &db_clone).await;
    });
    Ok(ret.unwrap())
  }

  pub async fn delete_combine_items(info: &TjCombineinfo, db: &DbConnection) -> Result<u64> {
    let ret = TjCombineinfo::delete(&vec![info.id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let db_clone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Combine as i32, &db_clone).await;
    });
    Ok(1 as u64)
  }

  pub async fn query_iteminfos(dto: &IteminfoQueryDto, db: &DbConnection) -> Result<Vec<TjIteminfo>> {
    info!("query iteminfos dto:{:?}", &dto);
    // let itemids: Vec<&str> = dto.itemids.iter().map(|v| v.as_str()).collect();
    // let lisnum: Vec<&str> = dto.lisnum.iter().map(|v| v.as_str()).collect();
    // let ret = TjIteminfo::query_many( &itemids, dto.okflag, dto.combineflag, dto.itemtype, &lisnum).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())

    let ret = crate::SYSCACHE
      .get()
      .unwrap()
      .get_iteminfos(&dto.itemids, dto.okflag, dto.combineflag, dto.itemtype, &dto.lisnum, db)
      .await;
    Ok(ret)
  }
  pub async fn save_iteminfo(info: &TjIteminfo, combines: &Vec<TjCombineinfo>, db: &DbConnection) -> Result<i64> {
    let mut info = info.to_owned();
    let cache_iteminfo = SYSCACHE.get().unwrap().get_iteminfo(&info.tj_itemid, &db).await;
    if info.tj_extcode.is_empty() && !cache_iteminfo.tj_extcode.is_empty() {
      info.tj_extcode = cache_iteminfo.tj_extcode.to_string();
    }
    info!("保存的项目信息:{:?}", &info);
    let ret = TjIteminfo::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut has_combine = false;
    if info.tj_combineflag == constant::YesOrNo::Yes as i32 {
      has_combine = true;
      if combines.len() <= 0 {
        info!("组合项目为空，需要删除全部组合项目信息");
        let ret = TjCombineinfo::delete_by_combid(&info.tj_itemid, &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
        }
      } else {
        let ret = TjCombineinfo::query_many(&vec![info.tj_itemid.to_string()], &db.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().unwrap_err().to_string());
          return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
        }
        let exist_infos = ret.unwrap();
        let insert_infos: Vec<TjCombineinfo> = combines
          .iter()
          .filter(|&f| {
            exist_infos
              .iter()
              .find(|&f2| f2.tj_itemid.eq_ignore_ascii_case(&f.tj_itemid) && f2.tj_combid.eq_ignore_ascii_case(&f.tj_combid))
              .is_none()
          })
          .map(|v| v.to_owned())
          .collect();
        info!("需要插入的组合项目信息：{insert_infos:?}");
        if insert_infos.len() > 0 {
          let ret = TjCombineinfo::save_many(&insert_infos, &db.get_connection()).await;
          if ret.as_ref().is_err() {
            error!("{}", ret.as_ref().unwrap_err().to_string());
            return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
          }
        }

        let delete_infos: Vec<TjCombineinfo> = exist_infos
          .iter()
          .filter(|&f| {
            combines
              .iter()
              .find(|&f2| f2.tj_itemid.eq_ignore_ascii_case(&f.tj_itemid) && f2.tj_combid.eq_ignore_ascii_case(&f.tj_combid))
              .is_none()
          })
          .map(|v| v.to_owned())
          .collect();
        info!("需要删除的组合项目信息：{delete_infos:?}");
        if delete_infos.len() > 0 {
          let del_ids: Vec<i64> = delete_infos.iter().map(|v| v.id).collect();
          let ret = TjCombineinfo::delete(&del_ids, &db.get_connection()).await;
          if ret.as_ref().is_err() {
            error!("{}", ret.as_ref().unwrap_err().to_string());
            return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
          }
        }
      }
    }
    let db_clone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Item as i32, &db_clone).await;
      if has_combine {
        crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Combine as i32, &db_clone).await;
      }
    });
    Ok(ret.unwrap())
  }
  pub async fn delete_iteminfos(iteminfo: &TjIteminfo, db: &DbConnection) -> Result<i64> {
    if iteminfo.id <= 0 || iteminfo.tj_itemid.is_empty() {
      return Err(anyhow!("项目信息为空，不能删除"));
    }
    info!("开始删除项目：{:?}", &iteminfo);
    //如果是组合项目，需要从
    if iteminfo.tj_combineflag == constant::YesOrNo::Yes as i32 {
      //1）从科室开展项目中删除
      info!("组合项目，从科室开展项目中删除......:{}", &iteminfo.tj_itemid);
      let ret = TjDeptitem::delete_by_itemid_deptid(&iteminfo.tj_itemid, &"".to_string(), &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      //2）从条码项目信息中删除
      info!("组合项目，从条码项目中删除......:{}", &iteminfo.tj_itemid);
      let ret = TjBardetail::delete_by_itemid(&iteminfo.tj_itemid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      //3）从指引单中删除
      info!("组合项目，从指引单项目中删除......:{}", &iteminfo.tj_itemid);
      let ret = TjGuideitem::delete_by_itemid(&iteminfo.tj_itemid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      // info!("组合项目，从组合项目中删除......:{}", &iteminfo.tj_itemid);
      // let ret = TjCombineinfo::delete_by_column( "tj_combid", &iteminfo.tj_itemid).await;
      // if ret.as_ref().is_err() {
      //   error!("{}", ret.as_ref().unwrap_err().to_string());
      //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      // }
    } else {
      //如果是非组合项目，则需要从
      //1）从组合项目中删除
      let ret = TjCombineinfo::delete_by_itemid(&iteminfo.tj_itemid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = TjIteminfo::delete(&vec![iteminfo.id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let db_clone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Item as i32, &db_clone).await;
    });
    Ok(1 as i64)
  }

  pub async fn query_item_resultinfos(itemid: &String, db: &DbConnection) -> Result<Vec<TjItemresultinfo>> {
    let ret = crate::SYSCACHE.get().unwrap().get_itemresultinfos(itemid, &db).await;
    Ok(ret)
    // let ret = TjItemresultinfo::query( &itemid, &db.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
  }

  pub async fn save_item_resultinfo(info: &TjItemresultinfo, db: &DbConnection) -> Result<i64> {
    let ret = TjItemresultinfo::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let db_clone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::ItemResult as i32, &db_clone).await;
    });
    Ok(ret.unwrap())
  }
  pub async fn delete_item_resultinfo(info: &TjItemresultinfo, db: &DbConnection) -> Result<i64> {
    let ret = TjItemresultinfo::delete(&vec![info.id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    // crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::ItemResult as i32, &db).await;

    let db_clone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::ItemResult as i32, &db_clone).await;
    });
    Ok(ret.unwrap() as i64)
  }

  pub async fn query_sampletypes(db: &DbConnection) -> Result<Vec<TjSampletype>> {
    let ret = TjSampletype::query_many(&db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn get_item_rangeinfo(itemid: &String, age: i32, sex: i32, db: &DbConnection) -> TjItemrangeinfo {
    let iteminfo: TjIteminfo = crate::SYSCACHE.get().unwrap().get_iteminfo(itemid, &db).await;
    let itemids = vec![iteminfo.tj_itemid.to_string()];
    let itemranges = crate::SYSCACHE.get().unwrap().query_itemrangeinfos(age, sex, &itemids).await;

    let mut result = TjItemrangeinfo {
      id: 0,
      tj_itemid: itemid.to_string(),
      tj_sex: sex,
      tj_startage: 0,
      tj_endage: 0,
      tj_lowvalue: iteminfo.tj_lowvalue.to_string(),
      tj_uppervalue: iteminfo.tj_uppervalue.to_string(),
      tj_displowhigh: "".to_string(),
    };
    if itemranges.len() > 0 {
      result = itemranges[0].to_owned();
    }

    result
  }

  pub async fn query_itemrangeinfos(sex: i32, age: i32, itemids: &Vec<String>, _db: &DbConnection) -> Result<Vec<TjItemrangeinfo>> {
    // let ret = TjItemrangeinfo::query_itemrangeinfos(db.get_connection_ref(), age, sex, itemids).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
    let ret = crate::SYSCACHE.get().unwrap().query_itemrangeinfos(sex, age, &itemids).await;
    Ok(ret)
  }

  pub async fn save_itemrangeinfo(info: &TjItemrangeinfo, db: &DbConnection) -> Result<i64> {
    let ret = TjItemrangeinfo::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let db_clone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::ItemRange as i32, &db_clone).await;
    });

    Ok(ret.unwrap())
  }

  pub async fn delete_itemrangeinfos(ids: &Vec<i64>, db: &DbConnection) -> Result<i64> {
    let ret = TjItemrangeinfo::delete(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let db_clone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::ItemRange as i32, &db_clone).await;
    });
    Ok(ret.unwrap() as i64)
  }

  pub fn find_itemrangeinfo(sex: i32, age: i32, itemid: &String, itemranges: &Vec<TjItemrangeinfo>) -> Option<TjItemrangeinfo> {
    // info!("开始根据性别：{}，年龄：{}，项目编号：{} 查找参考值信息", sex, age, itemid);
    let ret = itemranges
      .iter()
      .find(|&p| {
        let b1 = if p.tj_sex == sex || p.tj_sex == 0 || p.tj_sex == 3 { true } else { false };
        let b2 = if age < 0 { true } else { p.tj_startage <= age && age < p.tj_endage };
        // let b2 = match age {
        //   x if x >= 0 => p.tj_startage >= x && p.tj_endage < x,
        //   _ => true,
        // };
        let b3 = if !itemid.is_empty() { p.tj_itemid.eq_ignore_ascii_case(&itemid) } else { true };
        // info!("B1 is:{}", &b1);
        // info!("B2 is:{}", &b2);
        // info!("B3 is:{}", &b3);
        b1 && b2 && b3
      })
      .map(|v| v.to_owned());

    ret
  }

  pub async fn pair_checkitem(itemid: &String, extcode: &String, db: &DbConnection) -> Result<String> {
    //清空其他的配置项目
    let mut iteminfos = crate::SYSCACHE.get().unwrap().get_iteminfo_by_extcode(extcode).await;
    if iteminfos.len() > 0 {
      iteminfos.iter_mut().for_each(|v| v.tj_extcode = "".to_string());
      let ret = TjIteminfo::save_many(&iteminfos, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }

    let mut iteminfo = crate::SYSCACHE.get().unwrap().get_iteminfo(itemid, &db).await;
    iteminfo.tj_extcode = extcode.to_owned();
    // ItemSvc::save_iteminfo(&iteminfo, &vec![], db).await;
    let ret = TjIteminfo::save(&iteminfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }

    let db_clone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Item as i32, &db_clone).await;
    });
    Ok("Ok".to_string())
  }
  pub async fn update_iteminfo_lisnum(itemid: &String, lisnum: &String, db: &DbConnection) -> Result<i64> {
    let mut iteminfo = crate::SYSCACHE.get().unwrap().get_iteminfo(itemid, &db).await;
    if iteminfo.tj_lisnum.eq_ignore_ascii_case(&lisnum) {
      return Ok(1);
    }
    iteminfo.tj_lisnum = lisnum.to_owned();
    // ItemSvc::save_iteminfo(&iteminfo, &vec![], db).await;
    let ret = TjIteminfo::save(&iteminfo, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let db_clone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Item as i32, &db_clone).await;
    });
    Ok(1)
  }
}
