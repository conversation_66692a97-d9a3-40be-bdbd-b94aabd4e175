[package]
name = "odipservice"
version = "0.2.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
datacontroller = {path = "../datacontroller"}
utility = {path="../utility"}
anyhow = "1"

num_enum = "0.5"
strum = "^0.24"
strum_macros = "^0.24"

log = "0.4"

rust_decimal = "1"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
reqwest = { version = "0.11", default-features = false, features = ["json","stream", "multipart","rustls-tls"] }
mpart-async = "0.6"
rs-snowflake = "0.6"
