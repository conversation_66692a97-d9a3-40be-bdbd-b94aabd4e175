//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_audiogramdetail")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_testid: String,
    pub tj_itemid: String,
    pub tj_freq: i32,
    pub tj_adtype: i32,
    pub tj_ear: i32,
    pub tj_result: i32,
    pub tj_revise: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
