//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug,<PERSON><PERSON>ult, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "s_cdc_audiogramitem")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  #[sea_orm(unique)]
  pub cdc_item_code: Option<String>,
  pub cdc_item_name: Option<String>,
  pub cdc_deptid: Option<i32>,
  pub tj_itemid: Option<String>,
  pub tj_adtype: Option<i32>,
  pub tj_ear: Option<i32>,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
