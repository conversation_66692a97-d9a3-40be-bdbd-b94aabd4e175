use anyhow::Result;
use sea_orm::{ConnectOptions, ConnectionTrait, Database, DatabaseBackend, DatabaseConnection, DbErr, ExecResult, Statement};
// use sqlx::mysql;
// use sqlx::mysql::{MySqlPool, MySqlPoolOptions};
use std::time::Duration;
use tracing::*;

#[derive(Debug, Clone)]
pub struct DbConnection {
  dbconn: DatabaseConnection,
  backend: DatabaseBackend,
}

impl DbConnection {
  pub async fn new(db_url: &str, dbtype: i32) -> Self {
    // tracing_subscriber::fmt().with_max_level(tracing::Level::DEBUG).with_test_writer().init();
    let mut backend = DatabaseBackend::MySql;
    if dbtype == 2 {
      backend = DatabaseBackend::Postgres;
    }

    let mut opt = ConnectOptions::new(db_url.to_owned());
    opt
      .max_connections(15)
      // .min_connections(10)
      .connect_timeout(Duration::from_secs(256))
      .sqlx_logging(true)
      .sqlx_logging_level(log::LevelFilter::Debug)
      // .idle_timeout(Duration::from_secs(60 * 30))
      // .pool_options::<sqlx::MySql>()
      // .test_before_acquire(false)
      ;

    // .acquire_timeout(Duration::from_secs(28800))
    // .idle_timeout(Duration::from_secs(28800))
    // .max_lifetime(Duration::from_secs(28800))
    let ret = Database::connect(opt).await; //.expect("init database connection error");
    if ret.as_ref().is_err() {
      error!("数据库连接错误:{},error:{}", &db_url, &ret.as_ref().unwrap_err().to_string());
    }
    let dbconn = ret.unwrap();
    // let dbconn: DatabaseConnection = Database::connect(db_url).await.expect("connection error");
    let dbconn = DbConnection { dbconn, backend };
    dbconn
  }

  pub fn get_connection(&self) -> &DatabaseConnection {
    &self.dbconn
  }

  pub async fn execute_sql(&self, sqlstr: &str) -> Result<ExecResult, DbErr> {
    // let ret = self.dbconn.execute(Statement::from_string(self.backend, sqlstr.to_owned())).await;
    let ret = self.dbconn.execute(Statement::from_string(self.backend, sqlstr.to_owned())).await;
    info!("execute sql ret:{:?}, sql:{}", ret, sqlstr);

    ret
  }
}
