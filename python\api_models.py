# coding=utf-8

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from datetime import datetime


@dataclass
class ReportGenerationRequest:
    """Request model for report generation"""
    report_id: int
    report_format: int  # 1: DOCX, 2: PDF
    page_style: int = 0  # 0: Portrait, 1: Landscape
    split_in_row: int = 1  # Split rows parameter
    output_directory: Optional[str] = None  # Optional custom output directory
    
    def __post_init__(self):
        """Validate the request parameters"""
        if self.report_id <= 0:
            raise ValueError("report_id must be a positive integer")
        
        if self.report_format not in [1, 2]:
            raise ValueError("report_format must be 1 (DOCX) or 2 (PDF)")
        
        if self.page_style not in [0, 1]:
            raise ValueError("page_style must be 0 (Portrait) or 1 (Landscape)")
        
        if self.split_in_row < 1:
            raise ValueError("split_in_row must be at least 1")


@dataclass
class ReportGenerationResponse:
    """Response model for report generation"""
    success: bool
    message: str
    report_id: int
    output_file: Optional[str] = None
    file_size: Optional[int] = None
    generation_time: Optional[float] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()


@dataclass
class ReportStatusResponse:
    """Response model for report status"""
    report_id: int
    status: str  # "pending", "processing", "completed", "failed", "not_found"
    message: str
    output_file: Optional[str] = None
    created_at: Optional[str] = None
    completed_at: Optional[str] = None
    error_details: Optional[str] = None


@dataclass
class ErrorResponse:
    """Error response model"""
    error: str
    message: str
    status_code: int
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()


@dataclass
class ReportFileInfo:
    """Information about a report file"""
    filename: str
    file_path: str
    file_size: int
    created_at: str
    report_type: Optional[str] = None
    report_format: Optional[str] = None


@dataclass
class ReportListResponse:
    """Response model for listing reports"""
    reports: List[ReportFileInfo]
    total_count: int
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()


@dataclass
class HealthCheckResponse:
    """Health check response model"""
    status: str
    timestamp: str
    version: str
    uptime: Optional[float] = None
    database_status: Optional[str] = None
    disk_space: Optional[Dict[str, Any]] = None


@dataclass
class ReportTypesResponse:
    """Response model for available report types"""
    report_types: List[Dict[str, Any]]
    report_formats: List[Dict[str, Any]]
    page_styles: List[Dict[str, Any]]
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()
