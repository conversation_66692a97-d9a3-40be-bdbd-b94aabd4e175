# coding=utf-8

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class APIConfig:
    """Configuration class for the Report Server API"""

    # Server configuration
    host: str = "0.0.0.0"
    port: int = 8080
    debug: bool = False

    # Report configuration
    output_directory: str = "./reports"
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    allowed_extensions: tuple = ('.pdf', '.docx')

    # Database configuration (loaded from nodipexam.toml)
    database_uri: Optional[str] = None

    # Logging configuration
    log_level: str = "INFO"
    log_file: str = "./log/reportserver.log"

    def __post_init__(self):
        """Load configuration from environment variables and config files"""
        # Load from environment variables
        self.host = os.getenv("REPORT_SERVER_HOST", self.host)
        self.port = int(os.getenv("REPORT_SERVER_PORT", self.port))
        self.debug = os.getenv("REPORT_SERVER_DEBUG", "false").lower() == "true"
        self.output_directory = os.getenv("REPORT_OUTPUT_DIR", self.output_directory)
        self.log_level = os.getenv("REPORT_LOG_LEVEL", self.log_level)

        # Load database configuration from nodipexam.toml
        self._load_database_config()

        # Ensure output directory exists
        os.makedirs(self.output_directory, exist_ok=True)

    def _load_database_config(self):
        """Load database configuration from nodipexam.toml"""
        try:
            # Try to use the robust config reader
            from config_reader import load_database_config
            self.database_uri = load_database_config()
            if self.database_uri:
                return
        except ImportError:
            pass

        # Fallback to direct tomli loading
        try:
            import tomli
            config_paths = [
                "./config/nodipexam.toml",
                "../config/nodipexam.toml",
                "../../config/nodipexam.toml"
            ]

            for config_path in config_paths:
                if os.path.exists(config_path):
                    try:
                        with open(config_path, "rb") as f:
                            config = tomli.load(f)
                            self.database_uri = config.get("database", {}).get("uri")
                            return
                    except Exception as e:
                        print(f"Warning: Could not load database config from {config_path}: {e}")
        except ImportError:
            print("Warning: TOML support not available, trying alternative config formats...")

            # Try JSON fallback
            json_paths = [
                "./config/nodipexam.json",
                "../config/nodipexam.json",
                "../../config/nodipexam.json"
            ]

            for json_path in json_paths:
                if os.path.exists(json_path):
                    try:
                        import json
                        with open(json_path, "r", encoding="utf-8") as f:
                            config = json.load(f)
                            self.database_uri = config.get("database", {}).get("uri")
                            print(f"Loaded database config from JSON: {json_path}")
                            return
                    except Exception as e:
                        print(f"Warning: Could not load database config from {json_path}: {e}")

        print("Warning: Config file nodipexam.toml not found in any expected location")

    def get_database_uri(self) -> Optional[str]:
        """Get the database URI with pymysql driver"""
        if self.database_uri:
            return self.database_uri.replace("mysql:", "mysql+pymysql:")
        return None

    def validate(self) -> bool:
        """Validate the configuration"""
        errors = []

        if self.port < 1 or self.port > 65535:
            errors.append("Port must be between 1 and 65535")

        if not os.path.exists(os.path.dirname(self.output_directory)):
            try:
                os.makedirs(os.path.dirname(self.output_directory), exist_ok=True)
            except Exception as e:
                errors.append(f"Cannot create output directory parent: {e}")

        if self.max_file_size < 1024:  # Minimum 1KB
            errors.append("max_file_size must be at least 1024 bytes")

        if errors:
            print("Configuration validation errors:")
            for error in errors:
                print(f"  - {error}")
            return False

        return True

    def __str__(self) -> str:
        """String representation of the configuration"""
        return f"""APIConfig(
    host={self.host},
    port={self.port},
    debug={self.debug},
    output_directory={self.output_directory},
    max_file_size={self.max_file_size},
    database_uri={'***' if self.database_uri else None},
    log_level={self.log_level}
)"""
