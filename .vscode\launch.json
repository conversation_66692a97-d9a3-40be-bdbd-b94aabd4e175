{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in library 'datacontroller'",
      "cargo": {
        "args": ["test", "--no-run", "--lib", "--package=datacontroller"],
        "filter": {
          "name": "datacontroller",
          "kind": "lib"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in library 'utility'",
      "cargo": {
        "args": ["test", "--no-run", "--lib", "--package=utility"],
        "filter": {
          "name": "utility",
          "kind": "lib"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in library 'odipservice'",
      "cargo": {
        "args": ["test", "--no-run", "--lib", "--package=odipservice"],
        "filter": {
          "name": "odipservice",
          "kind": "lib"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug executable 'cdcuploader'",
      "cargo": {
        "args": ["build", "--bin=cdcuploader", "--package=cdcuploader"],
        "filter": {
          "name": "cdcuploader",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in executable 'cdcuploader'",
      "cargo": {
        "args": [
          "test",
          "--no-run",
          "--bin=cdcuploader",
          "--package=cdcuploader"
        ],
        "filter": {
          "name": "cdcuploader",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug executable 'demo'",
      "cargo": {
        "args": ["build", "--bin=demo", "--package=demo"],
        "filter": {
          "name": "demo",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in executable 'demo'",
      "cargo": {
        "args": ["test", "--no-run", "--bin=demo", "--package=demo"],
        "filter": {
          "name": "demo",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    }
  ]
}
