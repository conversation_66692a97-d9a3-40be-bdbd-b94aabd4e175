use crate::entities::{prelude::*, tj_healthyinfo};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, QueryOrder, Set};
use serde_json::json;

impl TjHealthyinfo {
  pub async fn query(code: &str, db: &DatabaseConnection) -> Result<Option<TjHealthyinfo>> {
    let conditions = Condition::all().add(tj_healthyinfo::Column::TjTestid.eq(code));

    // conditions = conditions.add(tj_healthyinfo::Column::Id.max());
    // if !code.is_empty() {
    //   conditions = conditions.add(tj_healthyinfo::Column::TjTestid.eq(code))
    // }

    let ret = TjHealthyinfoEntity::find().filter(conditions).order_by_desc(tj_healthyinfo::Column::Id).one(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let ret = ret.unwrap();
    // if ret.is_none() {
    //   return Ok(TjHealthyinfo { ..Default::default() });
    // }
    Ok(ret)
  }

  ///query by pid
  pub async fn query_many(code: &str, db: &DatabaseConnection) -> Result<Vec<TjHealthyinfo>> {
    if code.is_empty() {
      return Ok(vec![]);
    }
    let ret = TjHealthyinfoEntity::find().filter(tj_healthyinfo::Column::TjPid.eq(code)).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjHealthyinfo, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_healthyinfo::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    Ok(val_id.unwrap())
  }

  pub async fn delete(testids: &Vec<String>, id: i64, db: &DatabaseConnection) -> Result<u64> {
    if testids.len() <= 0 {
      return Ok(0);
    }
    let mut conditions = Condition::all();
    if testids.len() > 0 {
      conditions = conditions.add(tj_healthyinfo::Column::TjTestid.is_in(testids.to_owned()))
    }
    if id > 0 {
      conditions = conditions.add(tj_healthyinfo::Column::Id.ne(id))
    }

    let ret = TjHealthyinfoEntity::delete_many().filter(conditions).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
}
