from sqlalchemy import (
    Column,
    Integer,
    String,
)
from dataentities.dbconn import Base


class TjPatienthazards(Base):
    # 数据库中存储的表名
    __tablename__ = "tj_patienthazards"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    tj_testid = Column(String(128), nullable=False)
    tj_hid = Column(Integer, nullable=False)
    tj_poisionage = Column(String(256), nullable=False)
    tj_typeid = Column(Integer, nullable=False)
    tj_diseases = Column(String(256), nullable=False)
    tj_recheckitems = Column(String(256), nullable=False)
    tj_olcorpname = Column(String(256), nullable=False)