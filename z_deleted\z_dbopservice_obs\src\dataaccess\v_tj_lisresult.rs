use anyhow::{anyhow, Result};
// use rbatis::crud;
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct VTjLisresult {
  pub id: i64,
  pub tjbh: String,
  pub brxm: String,
  pub xmxh: String,
  pub xmmc: String,
  pub xmdw: String,
  pub xmjg: String,
  pub sfyc: i32,
  pub gdbj: String,
  pub ckdz: String,
  pub ckgz: String,
  pub ckfw: String,
  pub jyys: String,
  pub bgrq: String,
  pub bgys: String,
}

crud!(VTjLisresult {}, "v_tj_lisresult");
rbatis::impl_select!(VTjLisresult{select_all_by_testid(testid:&str) => "`where tjbh = #{testid}`"});
rbatis::impl_select!(VTjLisresult{query_many(testid:&str) => "`where tjbh = #{testid} `"});
rbatis::impl_select!(VTjLisresult{query_many_by_xmxh(testid:&str, xmxh:&[String]) => "`where tjbh = #{testid} and xmxh in ${xmxh.sql()}`"});
rbatis::impl_delete!(VTjLisresult{delete_many_by_ids(ids:&[i64]) => "`where id in ${ids.sql()} `"});
rbatis::impl_delete!(VTjLisresult{delete_many(testid:&str) => "`where tjbh = #{testid} `"});
rbatis::impl_delete!(VTjLisresult{delete_many_by_xmxh(testids:&[String], xmxh:&[String]) => "`where tjbh in ${testids.sql()} and xmxh in ${xmxh.sql()}`"});
rbatis::impl_delete!(VTjLisresult{clear(date:&String) => "`where bgrq < #{date} `"});

impl VTjLisresult {
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<VTjLisresult>) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<VTjLisresult>, Vec<VTjLisresult>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = VTjLisresult::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = VTjLisresult::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }
}

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct VTjLisresultV2 {
  pub id: i64,
  pub tjbh: String,
  pub brxm: String,
  pub xmxh: String,
  pub xmmc: String,
  pub xmdw: String,
  pub xmjg: String,
  pub sfyc: String,
  pub gdbj: String,
  pub ckdz: String,
  pub ckgz: String,
  pub ckfw: String,
  pub jyys: String,
  pub bgrq: String,
  pub bgys: String,
}
crud!(VTjLisresultV2 {}, "v_tj_lisresult");
rbatis::impl_select!(VTjLisresultV2{select_all_by_testid(testid:&str) => "`where tjbh = #{testid}`"});
