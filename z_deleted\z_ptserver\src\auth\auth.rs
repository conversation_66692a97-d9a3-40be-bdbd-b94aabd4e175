use anyhow::{anyhow, Result};
use axum::{
  async_trait,
  extract::FromRequestParts,
  http::{request::Parts, StatusCode},
  response::{IntoResponse, Response},
  Json,
};
use axum_extra::{
  headers::{authorization::Bear<PERSON>, Authorization},
  TypedHeader,
};
// use dbopservice::dataaccess::tj_staffadmin::TjStaffadmin;
// use dataservice::entities::prelude::*;
use jsonwebtoken::{Decoding<PERSON>ey, EncodingKey, Header, Validation};
// use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use serde_json::json;
// use std::fmt::Display;

pub static KEY: [u8; 16] = *include_bytes!("./secret.key");
static ONE_WEEK: u64 = 60 * 60 * 24 * 7;
// static ONE_DAY: u64 = 60 * 60 * 8;

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
  // issued at
  pub iat: String,
  // expiration
  pub exp: i64,
  // data
  pub user: String,
  pub userid: i64,  //       int
  pub isadmin: i32, //      int
  #[serde(rename = "login_session")]
  pub loginsession: String, // string
}

impl Claims {
  pub fn generate_token(/*usercode: &TjStaffadmin*/) -> Result<String> {
    // let mut user_new = "usercode.to_owned()";
    // user_new.tj_password = "".to_string();
    let now = jsonwebtoken::get_current_timestamp();
    let exp = ONE_WEEK;
    let payload = Claims {
      iat: utility::timeutil::current_datetime(),
      exp: (exp + now) as i64,
      user: "usercode.tj_staffno.to_owned()".to_string(),
      userid: 0,
      isadmin: 0,
      loginsession: "".to_string(), //utility::encrypt::aescbc_encrypt(&Uuid::new_v4().to_string()),
    };

    let token = jsonwebtoken::encode(&Header::default(), &payload, &EncodingKey::from_secret(&KEY));
    if token.as_ref().is_err() {
      return Err(anyhow!(token.err().unwrap().to_string()));
    }

    Ok(token.unwrap())
  }

  //   pub fn generate_refresh_token(usercode: &NxUserinfo) -> Result<String> {
  //     // let now = /*PrimitiveDateTime::now()..timestamp(); */Local::now().timestamp();
  //     let mut user_new = usercode.to_owned();
  //     user_new.user_pwd = "".to_string();
  //     let now = jsonwebtoken::get_current_timestamp();
  //     let exp = ONE_WEEK;
  //     let payload = UserToken {
  //       iat: now,
  //       exp: exp + now,
  //       usercode: usercode.user_code.to_owned(),
  //       userinfo: usercode.to_owned(),
  //       login_session: "".to_string(), //utility::encrypt::aescbc_encrypt(&Uuid::new_v4().to_string()),
  //     };
  //     let token = jsonwebtoken::encode(&Header::default(), &payload, &EncodingKey::from_secret(&KEY));
  //     if token.as_ref().is_err() {
  //       return Err(anyhow!(token.err().unwrap().to_string()));
  //     }
  //     Ok(token.unwrap())
  //   }
  pub fn decode_token(token: &str) -> Result<Claims> {
    // jsonwebtoken::decode::<UserToken>(&token, &KEY, &Validation::default())
    info!("start to decode token:{token}");
    let ret = jsonwebtoken::decode::<Claims>(&token, &DecodingKey::from_secret(&KEY), &Validation::default());
    if ret.as_ref().is_err() {
      error!("Decode claim error:{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("decode error:{}", ret.as_ref().err().unwrap().to_string()));
    }

    let token = ret.unwrap();

    Ok(token.claims)
  }

  //   pub async fn verify_token(token_data: &UserToken, Extension(pservice): Extension<Arc<CacheSvc>>, Extension(db): &Extension<Arc<DbConnection>>) -> Result<NxUserinfo> {
  //     let current = jsonwebtoken::get_current_timestamp();
  //     let exp = token_data.exp;
  //     if current > exp {
  //       error!("token is expired......");
  //       return Err(anyhow!("token is expired"));
  //     }
  //     // run the executor
  //     // let ret = async_global_executor::block_on(async {
  //     info!("in async global executor ......");
  //     //从缓存中查找用户信息
  //     let ret = pservice.get_user(&token_data.usercode, &db).await;
  //     info!("get user from cache result:{:?}", &ret);
  //     let userinfo: NxUserinfo;
  //     if ret.as_ref().is_err() {
  //       return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
  //     } else {
  //       userinfo = ret.unwrap().to_owned();
  //     }

  //     Ok(userinfo)
  //     // });
  //     // ret
  //     // Ok(())
  //   }
}

#[async_trait]
impl<S> FromRequestParts<S> for Claims
where
  S: Send + Sync,
{
  type Rejection = AuthError;

  async fn from_request_parts(parts: &mut Parts, state: &S) -> Result<Self, Self::Rejection> {
    // Extract the token from the authorization header
    // let TypedHeader(Authorization(bearer)) = parts.extract::<TypedHeader<Authorization<Bearer>>>().await.map_err(|_| AuthError::InvalidToken)?;
    // info!("bear token is:{:?}", bearer);
    // // Decode the user data
    // let token_data = decode::<Claims>(bearer.token(), &KEYS.decoding, &Validation::default()).map_err(|_| AuthError::InvalidToken)?;
    // info!("token data is:{:?}", &token_data);

    let TypedHeader(Authorization(bearer)) = TypedHeader::<Authorization<Bearer>>::from_request_parts(parts, state)
      .await
      .map_err(|_| AuthError::InvalidToken)?;
    // info!("bear token is:{:?}", &bearer.token());
    // Decode the user data
    let token_data = Claims::decode_token(bearer.token()).map_err(|_| AuthError::InvalidToken)?;
    info!("token data is:{token_data:?}");

    Ok(token_data)
  }
}

impl IntoResponse for AuthError {
  fn into_response(self) -> Response {
    let (status, error_message) = match self {
      // AuthError::WrongCredentials => (StatusCode::UNAUTHORIZED, "Wrong credentials"),
      // AuthError::MissingCredentials => (StatusCode::BAD_REQUEST, "Missing credentials"),
      // AuthError::TokenCreation => (StatusCode::INTERNAL_SERVER_ERROR, "Token creation error"),
      AuthError::InvalidToken => (StatusCode::BAD_REQUEST, "Invalid token"),
    };
    let body = Json(json!({
        "error": error_message,
    }));
    (status, body).into_response()
  }
}

// #[derive(Debug)]
pub enum AuthError {
  // WrongCredentials,
  // MissingCredentials,
  // TokenCreation,
  InvalidToken,
}
