from sqlalchemy import Column, Integer, String, Text
from dataentities.dbconn import Base


class SsArea(Base):
    # 数据库中存储的表名
    __tablename__ = "ss_area"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    area_code = Column(String(512), nullable=False, unique=True)
    area_name = Column(String(512), nullable=False)
    area_fullname = Column(String(512), nullable=False)
    area_level = Column(Integer, nullable=False)
    area_pcode = Column(Integer, nullable=False)
