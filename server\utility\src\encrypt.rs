use aead::generic_array::GenericArray;
use aes::cipher::{block_padding::Pkcs7, BlockDecryptMut, BlockEncryptMut, KeyIvInit};
use aes_gcm::{
  aead::{Aead, KeyInit},
  Aes256Gcm,
  Nonce, // Or `Aes128Gcm`
};
use anyhow::{anyhow, Result};
use base64::{engine::general_purpose, Engine as _};

use hex_literal::hex;
//v6
// use digest::Digest;
// use rsa::{
//   pkcs8::{DecodePrivateKey, DecodePublicKey},
//   PublicKey, RsaPrivateKey, RsaPublicKey,
// };
//end of v6
//v8
use rsa::sha2::Sha256;
use rsa::{
  pkcs1v15::SigningKey,
  pkcs8::{DecodePrivateKey, DecodePublicKey},
  signature::RandomizedSigner,
  Pkcs1v15Encrypt, PublicKey, Rsa<PERSON>ri<PERSON><PERSON><PERSON>, RsaP<PERSON>lic<PERSON><PERSON>,
};
//end of v8
// use sha2::Sha256;

use std::path::Path;
const AESCBC_KEY: [u8; 32] = hex!("8a12de5f1a4a273811123c14051617108a12de5f1d5e973811270c1405161710");
const AESCBC_IV: [u8; 16] = hex!("592dfa83410f93b71f15fe0c533283a7");

const AESGCM_KEY: [u8; 32] = hex!("8a12de5f1a4a273811123c14051617108a12de5f1d6e973811270c1405161710");
const AESGCM_IV: [u8; 12] = hex!("516c33929df5a3284ff463d7");

const KEY_NODIP: [u8; 32] = hex!("8a12de5f1a46273811170c14051617108a12de5f1a46973811270c1405161710");
const IV_NODIP: [u8; 16] = hex!("592dfa83390f93b71f15fe0c53328399");

pub fn md5(plaintext: &String) -> String {
  let digest = md5::compute(plaintext.as_bytes());
  format!("{:x}", digest)
}

pub fn encrypt_nodip(plaintext: &String) -> Result<String> {
  if plaintext.is_empty() {
    return Ok("".to_string());
  }
  type Aes256CbcEnc = cbc::Encryptor<aes::Aes256>;

  // buffer must have enough space for message+padding
  let mut buf = [0u8; 1024];
  // copy message to the buffer
  let pt_len = plaintext.len();
  buf[..pt_len].copy_from_slice(plaintext.as_bytes());
  let cipher = Aes256CbcEnc::new(&KEY_NODIP.into(), &IV_NODIP.into()).encrypt_padded_mut::<Pkcs7>(&mut buf, pt_len);
  if cipher.is_err() {
    return Err(anyhow!("{}", cipher.err().unwrap().to_string()));
  }
  let cipher = cipher.unwrap();

  let base64bytes = general_purpose::STANDARD.encode(cipher);
  Ok(base64bytes)
}

// pub fn aesgcm_encrypt_nodip(plaintext: &str) -> Result<String> {
//   let key = GenericArray::from_slice(&AESGCM_KEY_NODIP);
//   let cipher = Aes256::new(&key);
//   let nonce = Nonce::from_slice(&AESGCM_IV_NODIP); // 96-bits; unique per message
//   let ret = cipher.encrypt(nonce, plaintext.as_bytes());
//   if ret.is_err() {
//     return Err(anyhow!("{}", ret.err().unwrap().to_string()));
//   }
//   let ciphertext = ret.unwrap();
//   //   let ret = base64::encode(&ciphertext);
//   let ret = general_purpose::STANDARD.encode(&ciphertext);
//   return Ok(ret);
// }

pub fn aesgcm_encrypt(plaintext: &str) -> Result<String> {
  let key = GenericArray::from_slice(&AESGCM_KEY);
  let cipher = Aes256Gcm::new(&key);
  let nonce = Nonce::from_slice(&AESGCM_IV); // 96-bits; unique per message
  let ret = cipher.encrypt(nonce, plaintext.as_bytes());
  if ret.is_err() {
    return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  }
  let ciphertext = ret.unwrap();
  //   let ret = base64::encode(&ciphertext);
  let ret = general_purpose::STANDARD.encode(&ciphertext);
  return Ok(ret);
}

pub fn aesgcm_decrypt(ciphertext: &str) -> Result<String> {
  let key = GenericArray::from_slice(&AESGCM_KEY);
  let cipher = Aes256Gcm::new(&key);
  let nonce = Nonce::from_slice(&AESGCM_IV); // 96-bits; unique per message

  let ret = general_purpose::STANDARD.decode(&ciphertext.as_bytes());
  //   let ret = base64::decode(&ciphertext);
  if ret.is_err() {
    return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  }
  let decoded_ciphertext = ret.unwrap();

  let ret = cipher.decrypt(nonce, decoded_ciphertext.as_ref());

  if ret.is_err() {
    return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  }
  let plainbytes = ret.unwrap();
  let ret = std::str::from_utf8(&plainbytes);

  if ret.is_err() {
    return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  }
  let plaintext = ret.unwrap().to_string();
  return Ok(plaintext);
}

pub fn aescbc_encrypt(plaintext: &String) -> Result<String> {
  if plaintext.is_empty() {
    return Ok("".to_string());
  }
  type Aes256CbcEnc = cbc::Encryptor<aes::Aes256>;

  // buffer must have enough space for message+padding
  let mut buf = [0u8; 1024];
  // copy message to the buffer
  let pt_len = plaintext.len();
  buf[..pt_len].copy_from_slice(plaintext.as_bytes());
  let cipher = Aes256CbcEnc::new(&AESCBC_KEY.into(), &AESCBC_IV.into()).encrypt_padded_mut::<Pkcs7>(&mut buf, pt_len);
  if cipher.is_err() {
    return Err(anyhow!("{}", cipher.err().unwrap().to_string()));
  }
  let cipher = cipher.unwrap();

  let base64bytes = general_purpose::STANDARD.encode(cipher);
  Ok(base64bytes)
}

pub fn aescbc_decrypt(enctext: &String) -> Result<String> {
  if enctext.is_empty() {
    return Ok("".to_string());
  }
  type Aes256CbcDec = cbc::Decryptor<aes::Aes256>;

  let ret = general_purpose::STANDARD.decode(&enctext);
  if ret.as_ref().is_err() {
    return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  }
  let mut buf = ret.unwrap();
  let cipher = Aes256CbcDec::new(&AESCBC_KEY.into(), &AESCBC_IV.into()).decrypt_padded_mut::<Pkcs7>(&mut buf);
  if cipher.is_err() {
    return Err(anyhow!("{}", cipher.err().unwrap().to_string()));
  }
  let ret = std::str::from_utf8(cipher.unwrap());
  if ret.as_ref().is_err() {
    return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  }

  return Ok(String::from(ret.unwrap()));
}
//rsa v6
// pub fn rsa_sign(content: &str, key_file: &str) -> Result<String> {
//   let private_key_path = Path::new(key_file);
//   let private_key = RsaPrivateKey::read_pkcs8_pem_file(private_key_path); //.expect("failed to read private key");
//   if private_key.as_ref().is_err() {
//     error!("can't read pkcs8 from {}", &key_file);
//     return Err(anyhow!("read pri key error:{}", private_key.as_ref().err().unwrap().to_string()));
//   }
//   let private_key = private_key.unwrap();
//   // 创建一个Sha256对象
//   let mut hasher = Sha256::new();
//   hasher.update(content.as_bytes());
//   let result = hasher.finalize();
//   // 对摘要进行签名
//   let hash = rsa::Hash::SHA2_256;
//   let sign_result = private_key.sign(rsa::PaddingScheme::PKCS1v15Sign { hash: Option::from(hash) }, &result);
//   if sign_result.as_ref().is_err() {
//     error!("sign error:{}", &sign_result.as_ref().err().unwrap().to_string());
//     return Err(anyhow!("sign error:{}", &sign_result.as_ref().err().unwrap().to_string()));
//   }
//   // 签名结果转化为base64
//   let vec = sign_result.expect("create sign error for base64");
//   let result = general_purpose::STANDARD.encode(vec);
//   Ok(result)
// }

//rsa v8
pub fn rsa_sign(content: &str, key_file: &str) -> Result<String> {
  let private_key_path = Path::new(key_file);
  let mut rng = rand::thread_rng();
  let private_key = RsaPrivateKey::read_pkcs8_pem_file(private_key_path); //.expect("failed to read private key");
  if private_key.as_ref().is_err() {
    // error!("can't read pkcs8 from {}", &key_file);
    return Err(anyhow!("read pri key error:{}", private_key.as_ref().err().unwrap().to_string()));
  }
  let private_key = private_key.unwrap();

  let signing_key = SigningKey::<Sha256>::new_with_prefix(private_key);
  let signature = signing_key.sign_with_rng(&mut rng, &content.as_bytes());

  let base64_ret = general_purpose::STANDARD.encode(&signature);

  Ok(base64_ret)
}

pub fn rsa_encrypt(plaintext: &str, pub_file: &str) -> Result<String> {
  let mut rng = rand::thread_rng();
  let pub_key = RsaPublicKey::read_public_key_pem_file(Path::new(pub_file)); //.expect("read pub.key file error");
  if pub_key.is_err() {
    return Err(anyhow!("{}", pub_key.err().unwrap().to_string()));
  }
  let pub_key = pub_key.unwrap();
  let enc_data = pub_key.encrypt(&mut rng, Pkcs1v15Encrypt, plaintext.as_bytes());
  if enc_data.is_err() {
    return Err(anyhow!("{}", enc_data.err().unwrap().to_string()));
  }
  let enc_data = enc_data.unwrap();
  let ret = general_purpose::STANDARD.encode(enc_data);
  Ok(ret)
  // base64::encode(enc_data)
}

pub fn rsa_decrypt(encdata: &str, priv_file: &str) -> Result<String> {
  // let priv_file = "./config/pri.key";
  let priv_path_file = Path::new(priv_file);
  if !priv_path_file.exists() {
    return Err(anyhow!("key file:{priv_file} does not exist"));
  }
  let priv_key = RsaPrivateKey::read_pkcs8_pem_file(priv_path_file); //.expect("read private key file error");
  if priv_key.is_err() {
    return Err(anyhow!("priv key file error:{}", priv_key.err().unwrap().to_string()));
  }
  let priv_key = priv_key.unwrap();
  let enc_text = general_purpose::STANDARD.decode(encdata.as_bytes()); //.expect("base64 decode error");
  if enc_text.is_err() {
    return Err(anyhow!("base64 decode error: {}", enc_text.err().unwrap().to_string()));
  }
  let enc_text = enc_text.unwrap();
  // info!("enc_text is:{:?}", &enc_text);
  // let enc_text = "s1k4BLuet8CeJI6O/89pkLqNwzWa9c3QHIFxsmfyNmV+BAsACJwHv76OlgjpMDVtyZEoAoiUhkNpH93ktPRhKoz2cATiiq5AG+1chZQBmIiyyrjKqYrpBE4G5YVYbWDCqtNX0vYS3R5+4ivQzKB1k1x9enBPKg8RNmEsywhJOp3MrdEQDZ4KkNh3GrVz8UScWThqBo40ZZPfRqyrN/m0UR46sUDe7e6qQz2OyGaDSPgbEwwDY+0X6l/f2/Yi8g4skFdoeVu6TTLHRKapJJRkQubMzZn9/9ilU0mrN/PKHv94ockPNNcRMtUj0MnM5T8SBEOM2gdUTnnyzCdTW2mWpw==".as_bytes();
  // Decrypt
  // let dec_data = priv_key.decrypt(PaddingScheme::new_pkcs1v15_encrypt(), &enc_text); //.expect("failed to decrypt");
  let dec_data = priv_key.decrypt(Pkcs1v15Encrypt, &enc_text); //.expect("failed to decrypt");
  if dec_data.is_err() {
    return Err(anyhow!("decrypt error:{}", dec_data.err().unwrap().to_string()));
  }
  let ret = String::from_utf8(dec_data.unwrap());
  if ret.is_err() {
    return Err(anyhow!("{}", ret.err().unwrap().to_string()));
  }
  Ok(ret.unwrap())
}
