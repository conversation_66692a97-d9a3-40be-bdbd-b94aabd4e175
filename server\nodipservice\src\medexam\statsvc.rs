use crate::{
  common::constant::{self, Customer, DictType},
  dto::{FeeSummaryDetails, StatSummaryDto, StatSummaryResponse},
};
use anyhow::{anyhow, Result};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use docx_rs::*;
use itertools::Itertools;
use rust_decimal::prelude::*;
use serde::Deserialize;
use std::collections::{BTreeMap, HashMap, HashSet};
use tracing::*;
use utility::timeutil;
#[derive(Debug, Default, Clone, Deserialize)]
pub struct HazardStats {
  pub hname: String,     //显示的名字
  pub hvalues: Vec<i64>, //要统计的危害因素，为空表示其他
  pub htype: i32,        //危害因素类别
}

#[derive(Default, Debug)]
pub struct HazardStat {
  pub corpname: String,
  pub hazardname: String,
  pub totalnum: i32,     //本企业接触职业危害因素劳动者人员
  pub infactnum: i32,    //当年实际接受职业健康检查的人员
  pub oculikenum: i32,   //疑似人员
  pub forbiddennum: i32, //禁忌人员
}

#[derive(Default, Debug)]
pub struct DeptitemStat {
  pub deptname: String,
  pub itemname: String,
  pub totalnum: i32,      //数量
  pub itemprice: String,  //单价
  pub totalvalue: String, //总金额
}

//全四素，砂尘，
pub struct StatSvc;

impl StatSvc {
  pub async fn stat_summaries(dto: &StatSummaryDto, db: &DbConnection) -> Result<Vec<StatSummaryResponse>> {
    let mut enddate = dto.enddate;
    if enddate > 0 {
      enddate = timeutil::timestamp_add_days(enddate, 1);
    }
    // let deptids: Vec<&str> = dto.deptids.iter().map(|v| v.as_str()).collect();
    let ret = TjTestsummary::query_many(&vec![], &dto.deptids, dto.stdate, enddate, dto.status, &dto.staffid, -1, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let summaries = ret.unwrap();
    let mut response: Vec<StatSummaryResponse> = Vec::new();
    for val in dto.deptids.iter() {
      let res = StatSummaryResponse {
        deptid: val.to_string(),
        completed: summaries
          .iter()
          .filter(|f| f.tj_deptid.eq_ignore_ascii_case(&val) && f.tj_isfinished == constant::YesOrNo::Yes as i32)
          .count() as i64,
        uncompleted: summaries
          .iter()
          .filter(|f| f.tj_deptid.eq_ignore_ascii_case(&val) && f.tj_isfinished == constant::YesOrNo::No as i32)
          .count() as i64,
      };
      response.push(res);
    }
    Ok(response)
  }

  pub async fn stat_dept_item(dtstart: i64, dtend: i64, dir: &str, db: &DbConnection) -> Result<String> {
    info!("开始统计开始时间:{}，结束时间:{} 的体检信息", &dtstart, &dtend);
    let enddate = timeutil::timestamp_add_days(dtend, 1);
    //统计该段时间内的所有已经完成的体检号
    let ret = TjMedexaminfo::query_distinct_testids(dtstart, enddate, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("Error:{:?}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }
    let testids = ret.unwrap();
    info!("体检人数:{}", &testids.len());
    let ret = TjCheckiteminfo::query_many(&testids, &vec![], -1, 1, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("Error:{:?}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }
    let checkitems = ret.unwrap();
    info!("总共体检项目数量:{}", checkitems.len());
    let deptids: HashSet<String> = checkitems.iter().map(|v| v.tj_deptid.clone()).collect();
    // let itemids: HashSet<String> = checkitems.iter().map(|v| v.tj_synid.clone()).collect();

    let mut group_checkitems = BTreeMap::<String, Vec<TjCheckiteminfo>>::new();
    for val in checkitems {
      group_checkitems.entry(val.tj_deptid.clone()).or_default().push(val.clone());
    }
    // info!("group checkitems:{:#?}", &group_checkitems);

    let mut stat_result = BTreeMap::<String, Vec<BTreeMap<String, Vec<TjCheckiteminfo>>>>::new();
    for val in deptids {
      let iteminfos = group_checkitems.get(&val);
      if iteminfos.is_none() {
        continue;
      }
      let iteminfos = iteminfos.unwrap().to_owned();
      let mut dept_item: BTreeMap<String, Vec<TjCheckiteminfo>> = BTreeMap::new();
      for (key, chunk) in &iteminfos.into_iter().chunk_by(|k| k.tj_synid.to_string()) {
        dept_item.entry(key).or_default().extend(chunk.collect_vec().into_iter());
      }
      stat_result.entry(val.clone()).or_default().push(dept_item);
    }
    // info!("stat_result:{:#?}", &stat_result);

    let mut final_result: BTreeMap<String, Vec<DeptitemStat>> = BTreeMap::new();
    for val in stat_result.into_iter() {
      // let deptname = val.0.to_string();
      let deptname = crate::SYSCACHE.get().unwrap().get_department(&val.0, &db).await.tj_deptname;
      for items in val.1.into_iter() {
        for item in items.into_iter() {
          let iteminfo = crate::SYSCACHE.get().unwrap().get_iteminfo(&item.0, &db).await;
          let totalnum = item.1.iter().count() as i32;
          let dept_item = DeptitemStat {
            deptname: deptname.clone(),
            itemname: iteminfo.tj_itemname.to_owned(),
            totalnum,
            itemprice: iteminfo.tj_itemprice.to_string(),
            totalvalue: (totalnum as f32 * iteminfo.tj_itemprice).to_string(),
          };
          final_result.entry(val.0.clone()).or_default().push(dept_item);
        }
      }
    }
    // info!("final result:{:#?}", &final_result);

    //开始生成docx文件
    let file_name = format!(
      "./{}/dept_item_{}_to_{}.docx",
      dir,
      timeutil::format_timestamp_date_only(dtstart),
      timeutil::format_timestamp_date_only(dtend)
    );
    let path = std::path::Path::new(&file_name);
    let file = std::fs::File::create(path).unwrap();
    let font_size = 24;
    let big_font_size = 28;
    let col_width = vec![4000, 4000, 2500, 2500, 2500];
    let width_type = WidthType::Dxa;
    let mut rows: Vec<TableRow> = vec![];
    let header_row = TableRow::new(vec![
      TableCell::new()
        .width(col_width[0], width_type)
        .vertical_align(VAlignType::Center)
        .add_paragraph(Paragraph::new().add_run(Run::new().add_text("科室名称").size(font_size))),
      TableCell::new()
        .width(col_width[1], width_type)
        .vertical_align(VAlignType::Center)
        .add_paragraph(Paragraph::new().add_run(Run::new().add_text("项目名称").size(font_size))),
      TableCell::new()
        .width(col_width[2], width_type)
        .vertical_align(VAlignType::Center)
        .add_paragraph(Paragraph::new().add_run(Run::new().add_text("检查数量(人)").size(font_size)).align(AlignmentType::Center)),
      TableCell::new()
        .width(col_width[3], width_type)
        .vertical_align(VAlignType::Center)
        .add_paragraph(Paragraph::new().add_run(Run::new().add_text("项目单价(元)").size(font_size)).align(AlignmentType::Center)),
      TableCell::new()
        .width(col_width[4], width_type)
        .vertical_align(VAlignType::Center)
        .add_paragraph(Paragraph::new().add_run(Run::new().add_text("项目金额(元)").size(font_size)).align(AlignmentType::Center)),
    ]);
    rows.push(header_row);

    for val in final_result {
      //写入详细数据
      let mut idx = 1;
      for val2 in val.1 {
        //
        let cell1 = match idx {
          1 => TableCell::new()
            .add_paragraph(Paragraph::new().add_run(Run::new().add_text(val2.deptname.to_string()).size(font_size)))
            .vertical_align(VAlignType::Center)
            .width(col_width[0], width_type)
            .vertical_merge(VMergeType::Restart),
          _ => TableCell::new().add_paragraph(Paragraph::new()).vertical_merge(VMergeType::Continue),
        };
        let row = TableRow::new(vec![
          cell1,
          TableCell::new()
            .width(col_width[1], width_type)
            .add_paragraph(Paragraph::new().add_run(Run::new().add_text(val2.itemname.to_string()).size(font_size)))
            .vertical_align(VAlignType::Center),
          TableCell::new()
            .width(col_width[2], width_type)
            .add_paragraph(
              Paragraph::new()
                .add_run(Run::new().add_text(val2.totalnum.to_string()).size(font_size))
                .align(AlignmentType::Center),
            )
            .vertical_align(VAlignType::Center),
          TableCell::new()
            .width(col_width[3], width_type)
            .add_paragraph(
              Paragraph::new()
                .add_run(Run::new().add_text(val2.itemprice.to_string()).size(font_size))
                .align(AlignmentType::Center),
            )
            .vertical_align(VAlignType::Center),
          TableCell::new()
            .width(col_width[4], width_type)
            .add_paragraph(
              Paragraph::new()
                .add_run(Run::new().add_text(val2.totalvalue.to_string()).size(font_size))
                .align(AlignmentType::Center),
            )
            .vertical_align(VAlignType::Center),
        ]);
        rows.push(row);
        idx += 1;
      }
    }
    let table = Table::new(rows);
    let p1 = Paragraph::new()
      .add_run(Run::new().add_text("科室检查项目汇总表").size(big_font_size + 4) /*.add_break(BreakType::TextWrapping) */)
      .style("Title")
      .page_break_before(true)
      .align(AlignmentType::Center)
      .size(26);
    let p3 = Paragraph::new()
      .add_run(
        Run::new()
          .add_text(format!(
            "统计时间: {} 到 {}, 总共体检人数:{}人",
            utility::timeutil::format_timestamp_date_only(dtstart),
            utility::timeutil::format_timestamp_date_only(dtend),
            testids.len()
          ))
          .size(font_size),
      )
      .align(AlignmentType::Left);

    Docx::new().add_paragraph(p1).add_table(table).add_paragraph(p3).build().pack(file)?;
    Ok(file_name)
  }

  pub async fn generate_stat_hazardinfos_docx(dtstart: i64, dtend: i64, stathds: &BTreeMap<i32, HazardStats>, dir: &str, db: &DbConnection) -> Result<String> {
    //generate docx report file
    let file_name = format!(
      "./{}/stat_{}_to_{}.docx",
      dir,
      timeutil::format_timestamp_date_only(dtstart),
      timeutil::format_timestamp_date_only(dtend)
    );

    let enddate = timeutil::timestamp_add_days(dtend, 1);

    let path = std::path::Path::new(&file_name);
    let file = std::fs::File::create(path).unwrap();

    let stat = StatSvc::stat_hazardinfos_v2(dtstart, enddate, stathds, db).await;
    if stat.is_err() {
      return Err(anyhow!("Stat error:{}", stat.unwrap_err().to_string()));
    }
    let font_size = 24;
    let big_font_size = 28;
    let stat = stat.unwrap();
    let col_width = vec![3000, 4000, 2500, 2500, 2500, 2500];
    let width_type = WidthType::Auto;
    let mut rows: Vec<TableRow> = vec![];
    let header_row = TableRow::new(vec![
      TableCell::new()
        .width(col_width[0], width_type)
        .vertical_align(VAlignType::Center)
        .add_paragraph(Paragraph::new().add_run(Run::new().add_text("用人单位名称").size(font_size))),
      TableCell::new()
        .width(col_width[1], width_type)
        .vertical_align(VAlignType::Center)
        .add_paragraph(Paragraph::new().add_run(Run::new().add_text("危害因素").size(font_size))),
      TableCell::new().width(col_width[2], width_type).vertical_align(VAlignType::Center).add_paragraph(
        Paragraph::new()
          .add_run(Run::new().add_text("接触职业病危害因素劳动者(人)").size(font_size))
          .align(AlignmentType::Center),
      ),
      TableCell::new().width(col_width[3], width_type).vertical_align(VAlignType::Center).add_paragraph(
        Paragraph::new()
          .add_run(Run::new().add_text("当年实际接受职业健康检查劳动者(人)").size(font_size))
          .align(AlignmentType::Center),
      ),
      TableCell::new()
        .width(col_width[4], width_type)
        .vertical_align(VAlignType::Center)
        .add_paragraph(Paragraph::new().add_run(Run::new().add_text("检出的疑似职业病(人)").size(font_size)).align(AlignmentType::Center)),
      TableCell::new()
        .width(col_width[5], width_type)
        .vertical_align(VAlignType::Center)
        .add_paragraph(Paragraph::new().add_run(Run::new().add_text("职业禁忌证(人)").size(font_size)).align(AlignmentType::Center)),
    ]);
    rows.push(header_row);
    for val in stat {
      let mut idx = 1;
      for val2 in val.1 {
        let cell1 = match idx {
          1 => TableCell::new()
            .add_paragraph(Paragraph::new().add_run(Run::new().add_text(val.0.to_string()).size(font_size)))
            .vertical_align(VAlignType::Center)
            .width(col_width[0], width_type)
            .vertical_merge(VMergeType::Restart),
          _ => TableCell::new().add_paragraph(Paragraph::new()).vertical_merge(VMergeType::Continue),
        };

        let row = TableRow::new(vec![
          cell1,
          TableCell::new()
            .width(col_width[1], width_type)
            .add_paragraph(Paragraph::new().add_run(Run::new().add_text(val2.hazardname.to_string()).size(font_size)))
            .vertical_align(VAlignType::Center),
          TableCell::new()
            .width(col_width[2], width_type)
            .add_paragraph(
              Paragraph::new()
                .add_run(Run::new().add_text(if val2.totalnum < 0 { "".to_string() } else { val2.totalnum.to_string() }).size(font_size))
                .align(AlignmentType::Center),
            )
            .vertical_align(VAlignType::Center),
          TableCell::new()
            .width(col_width[3], width_type)
            .add_paragraph(
              Paragraph::new()
                .add_run(Run::new().add_text(val2.infactnum.to_string()).size(font_size))
                .align(AlignmentType::Center),
            )
            .vertical_align(VAlignType::Center),
          TableCell::new()
            .width(col_width[4], width_type)
            .add_paragraph(
              Paragraph::new()
                .add_run(Run::new().add_text(val2.oculikenum.to_string()).size(font_size))
                .align(AlignmentType::Center),
            )
            .vertical_align(VAlignType::Center),
          TableCell::new()
            .width(col_width[5], width_type)
            .add_paragraph(
              Paragraph::new()
                .add_run(Run::new().add_text(val2.forbiddennum.to_string()).size(font_size))
                .align(AlignmentType::Center),
            )
            .vertical_align(VAlignType::Center),
        ]);

        rows.push(row);
        idx += 1;
      }
    }
    let table = Table::new(rows); //.indent(50);

    let p1 = Paragraph::new()
      .add_run(
        Run::new().add_text("表1-2 职业健康检查机构职业健康指标常规监测汇总表").size(big_font_size + 4), /*.add_break(BreakType::TextWrapping) */
      )
      .style("Title")
      .page_break_before(true)
      .align(AlignmentType::Center)
      .size(26);

    let style1 = Style::new("Run1", StyleType::Character).name("Disarea").bold().size(big_font_size); //.underline("single");

    let org_dict = crate::SYSCACHE.get().unwrap().get_dict(DictType::DictCustomer as i32, Customer::CustomerName as i32, db).await;
    let p2 = Paragraph::new()
      .add_run(
        Run::new()
          .add_text("地区:__________________")
          .add_tab()
          .add_text("职业健康体检机构名称:")
          // .add_break(BreakType::TextWrapping)
          .style("Run1"),
      )
      .add_run(
        Run::new()
          .add_text(format!(" {} ", org_dict.ss_name.to_owned()))
          .underline("single")
          .style("Run1")
          .size(big_font_size),
      )
      .align(AlignmentType::Center);

    let p3 = Paragraph::new()
      .add_run(
        Run::new()
          .add_text(format!(
            "统计时间: {} 到 {}",
            utility::timeutil::format_timestamp_date_only(dtstart),
            utility::timeutil::format_timestamp_date_only(dtend)
          ))
          .size(font_size),
      )
      .align(AlignmentType::Left);
    // let mut img = File::open("./images/yes.png").unwrap();
    // let mut buf = Vec::new();
    // let _ = img.read_to_end(&mut buf).unwrap();
    // let pic = Pic::new(&buf).size(25 * 9525, 25 * 9525);
    // let p4 = Paragraph::new()
    //   .add_run(Run::new().add_image(pic.clone()).add_text(" 上 岗").size(big_font_size))
    //   .indent(Some(1840), Some(SpecialIndentType::FirstLine(1720)), None, None);
    // let p5 = Paragraph::new()
    //   .add_run(Run::new().add_image(pic).add_text(" 在 岗").size(big_font_size + 10))
    //   .indent(Some(1840), Some(SpecialIndentType::FirstLine(1720)), None, None);
    let page_margin = 600;
    Docx::new()
      .add_style(style1)
      .add_paragraph(p1)
      .add_paragraph(p2)
      // .add_paragraph(p4)
      // .add_paragraph(p5)
      .add_table(table)
      .add_paragraph(p3)
      .page_margin(PageMargin {
        top: page_margin,
        left: page_margin,
        bottom: page_margin,
        right: page_margin + 200,
        header: page_margin,
        footer: page_margin,
        gutter: page_margin,
      })
      .build()
      .pack(file)?;

    Ok(file_name)
  }

  pub async fn stat_hazardinfos_v2(dtstart: i64, dtend: i64, stathds: &BTreeMap<i32, HazardStats>, db: &DbConnection) -> Result<HashMap<String, Vec<HazardStat>>> {
    //最终统计结果
    //先获取该时间段内有体检的企业信息
    let ret = TjMedexaminfo::query_distinct_corps(dtstart, dtend, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("Error:{:?}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }
    let corp_query_ret = ret.unwrap();
    info!("exec sql result:{:?}", &corp_query_ret);
    if corp_query_ret.len() <= 0 {
      // return Err(anyhow!("没有体检机构"));
      return Ok(HashMap::new());
    }
    //获取企业信息
    let ret = TjCorpinfo::query_many(&corp_query_ret, "", &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("Error:{:?}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("{}", ret.unwrap_err().to_string()));
    }
    //企业信息
    let corpinfos = ret.unwrap();

    let mut stat_result: HashMap<String, Vec<HazardStat>> = HashMap::new();
    // info!("开始统计危害因素信息......");
    for corp in corpinfos {
      let ret = TjMedexaminfo::query_many(
        dtstart,
        dtend,
        &vec![],
        &vec![],
        corp.id, //225
        0,
        &vec![],
        &vec![2, 3, 4, 5, 6],
        &vec![4, 5, 6, 7],
        1,
        &"".to_string(),
        &db.get_connection(),
      )
      .await;
      if ret.as_ref().is_err() {
        error!("Error:{:?}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      let medinfos = ret.unwrap();
      if medinfos.len() <= 0 {
        // return Ok(stat_result);
        continue;
      }
      let testids = medinfos.iter().map(|v| v.tj_testid.to_string()).collect::<Vec<String>>();
      // info!("总的体检编号数:{:?}", &testids.len());
      // let corpids = medinfos.iter().map(|v| v.tj_corpnum).collect::<Vec<i64>>();

      let ret = TjPatienthazards::query_many(&testids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("Error:{:?}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      //接触的危害因素信息
      let patienthazards = ret.unwrap();
      // info!("总的patient hazards数量：{:?}", &patienthazards.len());
      // let patienthdids = patienthazards.iter().map(|v| v.tj_hid).collect::<Vec<i64>>();
      let ret = TjHazardinfo::query_many(&vec![], &vec![], &vec![], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("Error:{:?}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      //所有的危害因素信息
      let hazardinfos = ret.unwrap();

      // //需要统计的危害因素的信息
      // let stat_hazardinfos = hazardinfos
      //   .iter()
      //   .filter(|&v| hdnames.iter().find(|&f| f.eq_ignore_ascii_case(&v.tj_hname)).is_some())
      //   .map(|mv| mv.clone())
      //   .collect::<Vec<TjHazardinfo>>();

      let ret = TjCheckallnew::query_many(&testids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("Error:{:?}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("{}", ret.unwrap_err().to_string()));
      }
      //所有的总检信息
      let checkallinfos = ret.unwrap();

      //开始根据企业信息进行统计汇总
      // for corp in corpinfos {
      let corp_medinfos = medinfos.iter().filter(|&p| p.tj_corpnum == corp.id).map(|v| v.clone()).collect::<Vec<TjMedexaminfo>>();
      let corp_testids = corp_medinfos.iter().map(|v| v.tj_testid.to_string()).collect::<Vec<String>>();
      let corp_checkalls = checkallinfos
        .iter()
        .filter(|&f| corp_testids.iter().find(|&v| v.eq_ignore_ascii_case(&f.tj_testid)).is_some())
        .map(|m| m.clone())
        .collect::<Vec<TjCheckallnew>>();

      // let infact_nums = corp_testids.iter().count() as i32;
      let mut corp_stat: Vec<HazardStat> = vec![];

      let mut hazard_stat = HazardStat { ..Default::default() };
      //全因素
      hazard_stat.hazardname = "全因素".to_string();
      hazard_stat.totalnum = corp.tj_total;
      hazard_stat.infactnum = corp_testids.iter().count() as i32; //需要统计全部
      hazard_stat.oculikenum = corp_checkalls.iter().filter(|&f| f.tj_typeid == constant::CheckResultType::Oculike as i32).count() as i32; //需要统计疑似
      hazard_stat.forbiddennum = corp_checkalls.iter().filter(|&f| f.tj_typeid == constant::CheckResultType::Forbidden as i32).count() as i32; //禁忌症
      corp_stat.insert(0, hazard_stat);
      //根据配置的危害因素进行统计
      for stathd in stathds.iter() {
        hazard_stat = HazardStat { ..Default::default() };
        //统计具体的危害因素
        if stathd.1.hvalues.len() > 0 {
          //所有接触这些危害因素的体检号
          let testids = patienthazards
            .iter()
            .filter(|&f| stathd.1.hvalues.iter().find(|&&v| v == f.tj_hid).is_some())
            .map(|v| v.tj_testid.to_string())
            .collect::<Vec<String>>();
          // info!("接触:{:?}的体检编号{:?}", &stathd.1.hvalues, &testids);

          hazard_stat.hazardname = stathd.1.hname.to_string();
          hazard_stat.totalnum = corp.tj_total;
          hazard_stat.infactnum = testids.iter().count() as i32;
          hazard_stat.oculikenum = patienthazards
            .iter()
            .filter(|&v| v.tj_typeid == constant::CheckResultType::Oculike as i32 && testids.iter().find(|&f| f.eq_ignore_ascii_case(&v.tj_testid)).is_some())
            .count() as i32;
          hazard_stat.forbiddennum = patienthazards
            .iter()
            .filter(|&v| v.tj_typeid == constant::CheckResultType::Forbidden as i32 && testids.iter().find(|&f| f.eq_ignore_ascii_case(&v.tj_testid)).is_some())
            .count() as i32;
          // info!("统计结果：{:?}", &hazard_stat);
          corp_stat.push(hazard_stat);
        } else {
          //统计其他
          let hdtype = stathd.1.htype; //危害因素的类别

          //1.汇总已经统计过的危害因素
          let stat_hd_ids: Vec<i64> = stathds
            .iter()
            .filter(|&v| v.1.htype == hdtype && v.1.hvalues.len() > 0)
            .map(|m| m.1.hvalues.clone())
            .collect::<Vec<Vec<i64>>>()
            .into_iter()
            .flatten()
            .collect();
          // info!("类别是:{}的，已经统计的危害因素:{:?}", &stathd.1.htype, &stat_hd_ids);
          //2.汇总其他的危害因素
          let other_hds = patienthazards
            .iter()
            .filter(|&f| {
              stat_hd_ids.iter().find(|&&s| s == f.tj_hid).is_none() && corp_testids.iter().find(|&f2| f2.eq_ignore_ascii_case(&f.tj_testid)).is_some() && {
                match hazardinfos.iter().find(|&pf| pf.id == f.tj_hid) {
                  None => false,
                  Some(v) => v.tj_tid == hdtype,
                }
              }
            })
            .map(|v| v.clone())
            .collect::<Vec<TjPatienthazards>>();
          // info!("其他危害因素信息:{:?}", &other_hds);

          hazard_stat.hazardname = stathd.1.hname.to_string();
          hazard_stat.totalnum = corp.tj_total;
          hazard_stat.infactnum = other_hds.iter().count() as i32;
          hazard_stat.oculikenum = other_hds
            .iter()
            .filter(|&v| v.tj_typeid == constant::CheckResultType::Oculike as i32 && testids.iter().find(|&f| f.eq_ignore_ascii_case(&v.tj_testid)).is_some())
            .count() as i32;
          hazard_stat.forbiddennum = other_hds
            .iter()
            .filter(|&v| v.tj_typeid == constant::CheckResultType::Forbidden as i32 && testids.iter().find(|&f| f.eq_ignore_ascii_case(&v.tj_testid)).is_some())
            .count() as i32;
          // info!("统计结果：{:?}", &hazard_stat);

          corp_stat.push(hazard_stat);
        }
      }

      stat_result.insert(corp.tj_corpname.to_string(), corp_stat);
    }
    // info!("Final stat result:{:#?}", &stat_result);
    Ok(stat_result)
  }

  pub async fn export_fee_by_departments(stdate: i64, enddate: i64, deptids: &Vec<String>, dir: &str, db: &DbConnection) -> Result<String> {
    let stdate_str = utility::timeutil::format_timestamp_date_only(stdate);
    let enddate_str = utility::timeutil::format_timestamp_date_only(enddate);
    let filename = format!("{dir}/feesum_{}_{}.xlsx", stdate_str, enddate_str);
    let empty_strings: Vec<String> = vec![];
    let empty_ints: Vec<i32> = vec![];
    let enddate = timeutil::timestamp_add_days(enddate, 1);
    info!(stdate, enddate, stdate_str, enddate_str, "start to query medexaminfos");
    let medinfos = TjMedexaminfo::query_many(
      stdate,
      enddate,
      &empty_strings,
      &empty_strings,
      -1,
      -1,
      &empty_strings,
      &empty_ints,
      &vec![3, 4, 5, 6, 7, 8, 9],
      -1,
      &"".to_string(),
      &db.get_connection(),
    )
    .await;

    if medinfos.is_err() {
      return Err(anyhow!("query medinfos error:{}", medinfos.as_ref().unwrap_err().to_string()));
    }
    let medinfos = medinfos.unwrap();
    let testids = medinfos.iter().map(|v| v.tj_testid.clone()).collect();

    let ret = TjCheckiteminfo::query_many(&testids, deptids, -1, 1, &empty_strings, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let checkiteminfos = ret.unwrap();
    let deptids: HashSet<String> = checkiteminfos.iter().map(|v| v.tj_deptid.clone()).collect();
    let deptids: Vec<String> = deptids.into_iter().collect();
    let ret = TjDepartinfo::query_many(&vec![], &deptids, -1, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut deptinfos = ret.unwrap(); //
                                      // info!("排训前的科室信息:{:?}", deptinfos);
    deptinfos.sort_by(|a, b| a.tj_showorder.cmp(&b.tj_showorder));
    // info!("排训后的科室信息:{:?}", deptinfos);

    //开始整理数据
    let mut feesums: Vec<FeeSummaryDetails> = Vec::new();
    for deptinfo in deptinfos.iter() {
      let dept_iteminfos: Vec<TjCheckiteminfo> = checkiteminfos
        .iter()
        .filter(|&v| v.tj_deptid.eq_ignore_ascii_case(&deptinfo.tj_deptid))
        .map(|m| m.to_owned())
        .collect();
      for di in dept_iteminfos.into_iter() {
        let itmeinfo = crate::SYSCACHE.get().unwrap().get_iteminfo(&di.tj_itemid, db).await;
        let medinfo = medinfos.iter().find(|&f| f.tj_testid.eq_ignore_ascii_case(&di.tj_testid));
        let feeinfo = FeeSummaryDetails {
          deptname: deptinfo.tj_deptname.to_string(),
          itemname: itmeinfo.tj_itemname.to_string(),
          itemprice: itmeinfo.tj_itemprice,
          medtype: match medinfo {
            None => 2,
            Some(v) => {
              if v.tj_corpnum > 1 {
                2
              } else {
                1
              }
            }
          },
        };
        feesums.push(feeinfo);
      }
    }

    //开始写入excel
    let mut workbook = rust_xlsxwriter::Workbook::new();
    // Add a worksheet to the workbook.
    let worksheet = workbook.add_worksheet();

    // Set the column width for clarity.
    worksheet.set_column_width(0, 4)?;
    worksheet.set_column_width(1, 15)?;
    worksheet.set_column_width(2, 22)?;
    worksheet.set_default_row_height(20);

    let merge_format = rust_xlsxwriter::Format::new()
      .set_border(rust_xlsxwriter::FormatBorder::Thin)
      .set_align(rust_xlsxwriter::FormatAlign::Center)
      .set_align(rust_xlsxwriter::FormatAlign::VerticalCenter);

    let border_format = rust_xlsxwriter::Format::new()
      .set_border(rust_xlsxwriter::FormatBorder::Thin)
      .set_align(rust_xlsxwriter::FormatAlign::Center);
    let number_format = rust_xlsxwriter::Format::new()
      .set_border(rust_xlsxwriter::FormatBorder::Thin)
      .set_align(rust_xlsxwriter::FormatAlign::Center)
      .set_num_format("0.00");
    // Write a string without formatting.
    worksheet.write(0, 0, "#")?;
    worksheet.write(0, 1, "科室")?;
    worksheet.write(0, 2, "项目")?;
    worksheet.write_with_format(0, 3, "总量", &border_format)?;
    worksheet.write_with_format(0, 5, "个人", &border_format)?;
    worksheet.write_with_format(0, 7, "团体", &border_format)?;

    worksheet.write_with_format(1, 3, "人数", &border_format)?;
    worksheet.write_with_format(1, 4, "金额(元)", &border_format)?;
    worksheet.write_with_format(1, 5, "人数", &border_format)?;
    worksheet.write_with_format(1, 6, "金额(元)", &border_format)?;
    worksheet.write_with_format(1, 7, "人数", &border_format)?;
    worksheet.write_with_format(1, 8, "金额(元)", &border_format)?;

    // Write some merged cells.
    worksheet.merge_range(0, 0, 1, 0, "#", &merge_format)?;
    worksheet.merge_range(0, 1, 1, 1, "科室", &merge_format)?;
    worksheet.merge_range(0, 2, 1, 2, "项目", &merge_format)?;

    worksheet.merge_range(0, 3, 0, 4, "总量", &merge_format)?;
    worksheet.merge_range(0, 5, 0, 6, "团体", &merge_format)?;
    worksheet.merge_range(0, 7, 0, 8, "个人", &merge_format)?;

    let mut total_num_sum: i32 = 0;
    let mut total_price_sum: Decimal = Decimal::from_f32(0.00).unwrap_or_default();
    let mut corp_num_sum: i32 = 0;
    let mut corp_price_sum: Decimal = Decimal::from_f32(0.00).unwrap_or_default();
    let mut pers_num_sum: i32 = 0;
    let mut pers_price_sum: Decimal = Decimal::from_f32(0.00).unwrap_or_default();
    let mut start_row = 2;
    let mut current_row = start_row;
    for deptinfo in deptinfos.into_iter() {
      let itemnames: HashSet<String> = feesums
        .iter()
        .filter(|&p| p.deptname.eq_ignore_ascii_case(&deptinfo.tj_deptname))
        .map(|v| v.itemname.to_owned())
        .collect();

      let mut idx = 1;
      for itemname in itemnames.into_iter() {
        let fees: Vec<&FeeSummaryDetails> = feesums
          .iter()
          .filter(|&f| f.deptname.eq_ignore_ascii_case(&deptinfo.tj_deptname) && f.itemname.eq_ignore_ascii_case(&itemname))
          .map(|v| v.to_owned())
          .collect();
        if fees.len() > 0 {
          let price = rust_decimal::Decimal::from_f32(fees[0].itemprice).ok_or(anyhow!("Invalid decimal value"))?;
          //total
          let total_nums = rust_decimal::Decimal::from(fees.iter().count());
          let total_price = price.saturating_mul(total_nums);
          //corp
          let corp_nums = Decimal::from(fees.iter().filter(|&p| p.medtype == 2).count());
          let corp_price = price.saturating_mul(corp_nums);
          let pers_nums = Decimal::from(fees.iter().filter(|&p| p.medtype == 1).count());
          let pers_price = price.saturating_mul(pers_nums);
          // for fee in fees {
          worksheet.write_with_format(current_row, 0, format!("{}", idx), &border_format)?;
          worksheet.write_with_format(current_row, 1, format!("{}", deptinfo.tj_deptname), &border_format)?;
          worksheet.write_with_format(current_row, 2, format!("{}", itemname), &border_format)?;
          total_num_sum += total_nums.to_i32().unwrap_or(0);
          total_price_sum += total_price;
          corp_num_sum += corp_nums.to_i32().unwrap_or(0);
          corp_price_sum += corp_price;
          pers_num_sum += pers_nums.to_i32().unwrap_or(0);
          pers_price_sum += pers_price;
          // Create a format for numbers with 2 decimal places

          worksheet.write_with_format(current_row, 3, total_nums.to_i32().unwrap_or_default() /*format!("{}", total_nums)*/, &border_format)?;
          worksheet.write_with_format(current_row, 4, total_price.to_f64().unwrap_or_default(), &number_format)?;
          worksheet.write_with_format(current_row, 5, corp_nums.to_i32().unwrap_or_default() /*format!("{}", corp_nums)*/, &border_format)?;
          worksheet.write_with_format(current_row, 6, corp_price.to_f64().unwrap_or_default(), &number_format)?;
          worksheet.write_with_format(current_row, 7, pers_nums.to_i32().unwrap_or_default() /* format!("{}", pers_nums)*/, &border_format)?;
          worksheet.write_with_format(current_row, 8, pers_price.to_f64().unwrap_or_default(), &number_format)?;
          idx += 1;
          current_row += 1;
        }
      }
      // info!("start row:{}", start_row);
      // info!("current row:{}", current_row);
      if current_row > start_row + 1 {
        // info!("start to merge from {} to {}", start_row, current_row - 1);
        worksheet.merge_range(start_row, 1, current_row - 1, 1, &deptinfo.tj_deptname, &merge_format)?;
      }
      start_row = current_row;
    }
    worksheet.write_with_format(start_row, 2, format!("总计:"), &border_format)?;
    worksheet.write_with_format(start_row, 3, total_num_sum /*format!("{}", total_num_sum)*/, &border_format)?;
    worksheet.write_with_format(start_row, 4, total_price_sum.to_f64().unwrap_or_default(), &number_format)?;
    worksheet.write_with_format(start_row, 5, corp_num_sum /*format!("{}", corp_num_sum)*/, &border_format)?;
    worksheet.write_with_format(start_row, 6, corp_price_sum.to_f64().unwrap_or_default(), &number_format)?;
    worksheet.write_with_format(start_row, 7, pers_num_sum /*format!("{}", pers_num_sum)*/, &border_format)?;
    worksheet.write_with_format(start_row, 8, pers_price_sum.to_f64().unwrap_or_default(), &number_format)?;

    worksheet.write(start_row + 1, 1, format!("统计时间:{}到{}", stdate_str, enddate_str))?;
    let _ = workbook.save(&filename)?;
    Ok(filename)
  }
}
/* 校验sql脚本
select * from tj_corpinfo where tj_corpname = '杭州简并激光科技有限公司';
select * from tj_hazardinfo WHERE id in (
select DISTINCT tj_hid from tj_patienthazards where tj_testid in (
SELECT tj_testid FROM `tj_medexaminfo` WHERE `tj_medexaminfo`.`tj_checkstatus` > -1 AND `tj_medexaminfo`.`tj_testdate` >= ********** AND `tj_medexaminfo`.`tj_testdate` < ********** AND `tj_medexaminfo`.`tj_corpnum` = 209 AND `tj_medexaminfo`.`tj_isrecheck` = 0 AND `tj_medexaminfo`.`tj_completed` > 0 AND `tj_medexaminfo`.`tj_testtype` IN (2, 3, 4, 5, 6) AND `tj_medexaminfo`.`tj_checkstatus` IN (4, 5, 6, 7)
)
);
select * from tj_patienthazards where tj_testid in (
SELECT tj_testid FROM `tj_medexaminfo` WHERE `tj_medexaminfo`.`tj_checkstatus` > -1 AND `tj_medexaminfo`.`tj_testdate` >= ********** AND `tj_medexaminfo`.`tj_testdate` < ********** AND `tj_medexaminfo`.`tj_corpnum` = 209 AND `tj_medexaminfo`.`tj_isrecheck` = 0 AND `tj_medexaminfo`.`tj_completed` > 0 AND `tj_medexaminfo`.`tj_testtype` IN (2, 3, 4, 5, 6) AND `tj_medexaminfo`.`tj_checkstatus` IN (4, 5, 6, 7)
) order by tj_hid
;

SELECT * FROM `tj_medexaminfo` WHERE `tj_medexaminfo`.`tj_checkstatus` > -1 AND `tj_medexaminfo`.`tj_testdate` >= ********** AND `tj_medexaminfo`.`tj_testdate` < ********** AND `tj_medexaminfo`.`tj_corpnum` = 209 AND `tj_medexaminfo`.`tj_isrecheck` = 0 AND `tj_medexaminfo`.`tj_completed` > 0 AND `tj_medexaminfo`.`tj_testtype` IN (2, 3, 4, 5, 6) AND `tj_medexaminfo`.`tj_checkstatus` IN (4, 5, 6, 7)
;
 */
