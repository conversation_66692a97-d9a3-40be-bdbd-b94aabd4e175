use anyhow::{anyhow, Result};
use tracing::*;
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};

use crate::{
  common::constant,
  dto::{KeyIntDto, MultiKeyDto, MultiKeysDto},
};

pub struct OrgSvc;

impl OrgSvc {
  ///department
  pub async fn query_departments(dto: &MultiKeysDto, db: &DbConnection) -> Result<Vec<TjDepartinfo>> {
    let ret = crate::SYSCACHE.get().unwrap().get_departinfos(&dto.keys_int, &dto.keys_str, -1, &db).await;
    Ok(ret)

    // let ret = TjDepartinfo::query_many( &dto.keys_int, &dto.keys_str, -1, &db.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())
  }
  pub async fn save_departments(info: &TjDepartinfo, db: &DbConnection) -> Result<i64> {
    let ret = TjDepartinfo::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Dept as i32, &dbclone).await;
    });
    Ok(ret.unwrap())
  }
  pub async fn delete_departments(info: &TjDepartinfo, db: &DbConnection) -> Result<u64> {
    let ids = vec![info.id];
    let ret = TjDepartinfo::delete(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Dept as i32, &dbclone).await;
    });
    Ok(ids.len() as u64)
  }
  //
  pub async fn query_deptitems(dto: &MultiKeyDto, db: &DbConnection) -> Result<Vec<TjDeptitem>> {
    // let itemids: Vec<&str> = dto.keys_str1.iter().map(|v| v.as_str()).collect();
    // let deptids: Vec<&str> = dto.keys_str2.iter().map(|v| v.as_str()).collect();
    // let ret = TjDeptitem::query_many( &itemids, &deptids).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    let ret = crate::SYSCACHE.get().unwrap().get_deptitems(&dto.keys_str1, &dto.keys_str2, &db).await;
    Ok(ret)
  }
  pub async fn save_deptitems(infos: &Vec<TjDeptitem>, db: &DbConnection) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("infos are empty, not allowed"));
    }
    // let deptid = infos[0].tj_deptid.to_string();
    // let ret = TjDeptitem::select_by_column( "tj_deptid", &deptid).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let exist_infos = ret.unwrap();
    // let delete_infos: Vec<TjDeptitem> = exist_infos
    //   .iter()
    //   .filter(|&f| infos.iter().find(|&f2| f2.tj_deptid.eq_ignore_ascii_case(&f.tj_deptid)).is_none())
    //   .map(|v| v.to_owned())
    //   .collect();
    // let insert_infos: Vec<TjDeptitem> = infos
    //   .iter()
    //   .filter(|&f| exist_infos.iter().find(|&f2| f2.tj_deptid.eq_ignore_ascii_case(&f.tj_deptid)).is_none())
    //   .map(|v| v.to_owned())
    //   .collect();

    // if delete_infos.len() > 0 {
    //   let del_ids: Vec<i64> = delete_infos.iter().map(|v| v.id).collect();
    //   let ret = TjDeptitem::delete_by_column_batch( "id", &del_ids).await;
    //   if ret.as_ref().is_err() {
    //     error!("{}", ret.as_ref().unwrap_err().to_string());
    //     return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    //   }
    // }

    // if insert_infos.len() > 0 {
    let ret = TjDeptitem::save_many(&infos, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    // }
    // let ret = TjDeptitem::save( &info).await;
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Deptitem as i32, &dbclone).await;
    });
    Ok(infos.len() as i64)
  }

  pub async fn delete_deptitem(info: &TjDeptitem, db: &DbConnection) -> Result<u64> {
    let ret = TjDeptitem::delete_by_itemid_deptid(&info.tj_itemid, &info.tj_deptid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Deptitem as i32, &dbclone).await;
    });
    Ok(1 as u64)
  }
  //staff
  pub async fn query_staffs(dto: &MultiKeysDto, db: &DbConnection) -> Result<Vec<TjStaffadmin>> {
    // let codes: Vec<&str> = dto.keys_str.iter().map(|v| v.as_str()).collect();
    // let ret = TjStaffadmin::query_many( &codes, &dto.keys_int).await;
    // if ret.as_ref().is_err() {
    //   error!("{}", ret.as_ref().unwrap_err().to_string());
    //   return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // Ok(ret.unwrap())

    let ret = crate::SYSCACHE.get().unwrap().get_staffs(&dto.keys_str, &dto.keys_int, &db).await;
    Ok(ret)
  }
  pub async fn save_staff(info: &TjStaffadmin, db: &DbConnection) -> Result<i64> {
    let mut info = info.to_owned();
    if info.id <= 0 && info.tj_password.is_empty() {
      info.tj_password = utility::encrypt::encrypt_nodip(&"Abc12345".to_string()).unwrap_or_default();
    }
    let ret = TjStaffadmin::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Staff as i32, &dbclone).await;
    });
    Ok(ret.unwrap())
  }
  pub async fn delete_staff(id: i64, db: &DbConnection) -> Result<i64> {
    let ret = TjStaffadmin::delete(&vec![id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let dbclone = db.clone();
    tokio::spawn(async move {
      crate::SYSCACHE.get().unwrap().update_caches(constant::CacheType::Staff as i32, &dbclone).await;
    });
    Ok(1 as i64)
  }
  pub async fn query_staff_depts(dto: &KeyIntDto, db: &DbConnection) -> Result<Vec<TjStaffdept>> {
    let mut ids: Vec<i64> = vec![];
    if dto.key > 0 {
      ids.push(dto.key);
    }
    let ret = TjStaffdept::query_many(&ids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save_staff_depts(info: &TjStaffdept, db: &DbConnection) -> Result<i64> {
    let ret = TjStaffdept::save(&info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn delete_staff_dept(info: &TjStaffdept, db: &DbConnection) -> Result<u64> {
    let ids: Vec<i64> = vec![info.id];
    let ret = TjStaffdept::delete_many(&ids, &vec![], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ids.len() as u64)
  }
  pub async fn query_staffrights(staffid: i64, db: &DbConnection) -> Result<Vec<TjStaffright>> {
    let ret = TjStaffright::query_many(&vec![staffid], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save_staffrights(staff: &TjStaffadmin, infos: &Vec<TjStaffright>, db: &DbConnection) -> Result<i64> {
    if infos.len() <= 0 {
      let ret = TjStaffright::delete(&vec![staff.id], &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      return Ok(0 as i64);
    }
    let ret = TjStaffright::query_many(&vec![staff.id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let exist_rights = ret.unwrap();
    let delete_rights: Vec<TjStaffright> = exist_rights
      .iter()
      .filter(|&f1| infos.iter().find(|&f2| f2.tj_staffid == f1.tj_staffid && f2.tj_menuid == f1.tj_menuid).is_none())
      .map(|v| v.to_owned())
      .collect();
    if delete_rights.len() > 0 {
      let del_ids: Vec<i64> = delete_rights.into_iter().map(|v| v.id).collect();
      let ret = TjStaffright::delete_by_ids(&del_ids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let insert_rights: Vec<TjStaffright> = infos
      .iter()
      .filter(|&f1| exist_rights.iter().find(|&f2| f2.tj_staffid == f1.tj_staffid && f2.tj_menuid == f1.tj_menuid).is_none())
      .map(|v| {
        let mut newv = v.to_owned();
        newv.id = 0;
        newv
      })
      .collect();
    if insert_rights.len() > 0 {
      let ret = TjStaffright::insert_many(&insert_rights, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    Ok(1 as i64)
  }

  pub async fn query_groupinfos(gid: i64, db: &DbConnection) -> Result<Vec<TjGroupinfo>> {
    let mut gids: Vec<i64> = vec![];
    if gid > 0 {
      gids.push(gid.to_owned());
    }
    let ret = TjGroupinfo::query_many(&gids, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save_groupinfo(info: &TjGroupinfo, db: &DbConnection) -> Result<i64> {
    let ret = TjGroupinfo::save(info, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn delete_groupinfo(info: &TjGroupinfo, db: &DbConnection) -> Result<i64> {
    let sql = format!("update tj_staffadmin set tj_groupid = 0 where tj_groupid = {}", info.id);
    let ret = db.execute_sql(&sql).await;
    // let ret = TjStaffadmin::delete_group(&info.id, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    // let ret = TjStaffadmin::update_by_column( table, column)
    let ret = TjGroupright::delete_by_gid(info.id, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = TjGroupinfo::delete(&vec![info.id], &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(1 as i64)
  }

  pub async fn query_grouprights(gid: i64, db: &DbConnection) -> Result<Vec<TjGroupright>> {
    let ret = TjGroupright::query_many(gid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save_grouprights(gid: i64, infos: &Vec<TjGroupright>, db: &DbConnection) -> Result<()> {
    if infos.len() <= 0 {
      let ret = TjGroupright::delete_by_gid(gid, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
      return Ok(());
    }
    let ret = TjGroupright::query_many(gid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      error!("{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
    }
    let exist_rights = ret.unwrap();
    let delete_rights: Vec<TjGroupright> = exist_rights
      .iter()
      .filter(|&f| infos.iter().find(|&f1| f1.tj_gid == f.tj_gid && f1.tj_menuid == f.tj_menuid).is_none())
      .map(|v| v.to_owned())
      .collect();

    if delete_rights.len() > 0 {
      let delids: Vec<i64> = delete_rights.into_iter().map(|v| v.id).collect();
      let ret = TjGroupright::delete_by_ids(&delids, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }

    let insert_rights: Vec<TjGroupright> = infos
      .iter()
      .filter(|&f| exist_rights.iter().find(|&f1| f1.tj_gid == f.tj_gid && f1.tj_menuid == f.tj_menuid).is_none())
      .map(|v| {
        let mut newv = v.to_owned();
        newv.id = 0;
        newv
      })
      .collect();
    if insert_rights.len() > 0 {
      let ret = TjGroupright::save_many(&insert_rights, &db.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().unwrap_err().to_string());
        return Err(anyhow!("Error:{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    Ok(())
  }
}
