//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_groupinfo")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_gname: String,
  pub tj_gmemo: Option<String>,
  pub tj_goperator: Option<String>,
  pub tj_gmoddate: i64,
  pub tj_status: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
