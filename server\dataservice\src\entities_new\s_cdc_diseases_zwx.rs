//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Co<PERSON>, Clone, Default, Debug, DeriveEntity)]
pub struct Entity;

impl EntityName for Entity {
  fn table_name(&self) -> &str {
    "s_cdc_diseases_zwx"
  }
}

#[derive(<PERSON>lone, Debug, PartialEq, DeriveModel, DeriveActiveModel, Eq, Serialize, Deserialize)]
pub struct Model {
  pub id: i32,
  pub tj_discode: String,
  pub tj_disname: String,
  pub tj_disnum: String,
  pub tj_deptid: i32,
  pub tj_type: String,
  pub tj_content: String,
  pub tj_occudiseaseflag: i32,
  pub tj_startage: i32,
  pub tj_endage: i32,
  pub tj_sex: i32,
  pub tj_career: String,
  pub tj_marriage: i32,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_typeid: i32,
  pub tj_opinion: String,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, <PERSON><PERSON><PERSON><PERSON>, DeriveColumn)]
pub enum Column {
  #[sea_orm(column_name = "ID")]
  Id,
  TjDiscode,
  TjDisname,
  TjDisnum,
  TjDeptid,
  TjType,
  TjContent,
  TjOccudiseaseflag,
  TjStartage,
  TjEndage,
  TjSex,
  TjCareer,
  TjMarriage,
  TjPyjm,
  TjZdym,
  TjTypeid,
  TjOpinion,
}

#[derive(Copy, Clone, Debug, EnumIter, DerivePrimaryKey)]
pub enum PrimaryKey {
  Id,
}

impl PrimaryKeyTrait for PrimaryKey {
  type ValueType = i32;
  fn auto_increment() -> bool {
    true
  }
}

#[derive(Copy, Clone, Debug, EnumIter)]
pub enum Relation {}

impl ColumnTrait for Column {
  type EntityName = Entity;
  fn def(&self) -> ColumnDef {
    match self {
      Self::Id => ColumnType::Integer.def(),
      Self::TjDiscode => ColumnType::String(Some(25u32)).def(),
      Self::TjDisname => ColumnType::String(Some(250u32)).def(),
      Self::TjDisnum => ColumnType::String(Some(20u32)).def(),
      Self::TjDeptid => ColumnType::Integer.def(),
      Self::TjType => ColumnType::String(Some(10u32)).def(),
      Self::TjContent => ColumnType::String(Some(800u32)).def(),
      Self::TjOccudiseaseflag => ColumnType::Integer.def(),
      Self::TjStartage => ColumnType::Integer.def(),
      Self::TjEndage => ColumnType::Integer.def(),
      Self::TjSex => ColumnType::Integer.def(),
      Self::TjCareer => ColumnType::String(Some(50u32)).def(),
      Self::TjMarriage => ColumnType::Integer.def(),
      Self::TjPyjm => ColumnType::String(Some(100u32)).def(),
      Self::TjZdym => ColumnType::String(Some(100u32)).def(),
      Self::TjTypeid => ColumnType::Integer.def(),
      Self::TjOpinion => ColumnType::String(Some(450u32)).def(),
    }
  }
}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
