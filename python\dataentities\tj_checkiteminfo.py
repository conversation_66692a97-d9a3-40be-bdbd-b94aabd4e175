from sqlalchemy import (
    Column,
    Integer,
    String,
)
from dataentities.dbconn import Base


class TjCheckiteminfo(Base):
    # 数据库中存储的表名
    __tablename__ = "tj_checkiteminfo"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    tj_testid = Column(String(128), nullable=False)
    tj_itemid = Column(String(256), nullable=False)
    tj_synid = Column(String(64), nullable=False, unique=True)
    tj_itemname = Column(String(128), nullable=False)
    tj_result = Column(String(128), nullable=False)
    tj_lowvalue = Column(String(128), nullable=False)
    tj_uppervalue = Column(String(128), nullable=False)
    tj_itemrange = Column(String(256), nullable=False)
    tj_itemunit = Column(String(128), nullable=False)
    tj_abnormalflag = Column(Integer, nullable=False)
    tj_barflag = Column(Integer, nullable=False)
    tj_abnormalshow = Column(String(128), nullable=False)
    tj_combineflag = Column(Integer, nullable=False)
    tj_deptorder = Column(Integer, nullable=False)
    tj_showorder = Column(Integer, nullable=False)
    tj_combineorder = Column(Integer, nullable=False)
    tj_deptid = Column(String(128), nullable=False)
    tj_barnum = Column(String(256), nullable=False)
    tj_barcode = Column(Integer, nullable=False)
    tj_checkdate = Column(Integer, nullable=False)
    tj_checkdoctor = Column(String(256), nullable=False)
    tj_recheckdoctor = Column(String(128), nullable=False)
    tj_recheckdate = Column(Integer, nullable=False)