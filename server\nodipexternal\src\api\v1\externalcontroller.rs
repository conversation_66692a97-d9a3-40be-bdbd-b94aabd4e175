use log::*;
use std::sync::Arc;

use axum::{Extension, Json};
// use dbopservice::dbinit::DbConnection;
use dataservice::dbinit::DbConnection;
use nodipservice::dto::ExtTypeDTO;
use serde_json::Value;

use crate::{
  api::httpresponse::{response_json_value_error, response_json_value_ok},
  service::externalservice::{DftMessage, ExternalService},
};

pub async fn external_extend(
  Extension(db): Extension<Arc<DbConnection>>,
  Extension(tx): Extension<Arc<futures::channel::mpsc::UnboundedSender<String>>>,
  Extension(txp03): Extension<Arc<futures::channel::mpsc::UnboundedSender<DftMessage>>>,
  Json(dto): Json<ExtTypeDTO>,
) -> Json<Value> {
  info!("external upload, testid:{}, optype:{}", &dto.testid, &dto.exttype);
  let ret = ExternalService::external_extend(&dto, &db, &tx, &txp03).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), 0);
  }
  // let results = ret.unwrap();
  response_json_value_ok(1, ret.unwrap())
}
