use anyhow::Result;
use quick_xml::events::Event;
use quick_xml::Reader;
use std::collections::HashSet;
// use yaserde::*;
// use chrono::{Duration, Local, Months};
// use rust_xlsxwriter::*;

use crate::{dto::FeeSummaryDetails, external::ZjsrmyyService};

// pub mod medexam;
// pub mod unittest;
#[tokio::test]
pub async fn test_date() {
  println!("start to test");
  let _workguard = utility::loggings::init_tracing("nodipservice", "./log");
  let uri = "mysql://hzone:FKsX3Gde3G4r!@************:5506/nodip_new";
  let dbconn = dataservice::dbinit::DbConnection::new(uri, 1).await;
  let filename = "C:\\Users\\<USER>\\raiernix\\joesong\\nodipservers\\server\\nodipservice\\src\\external\\extcomp\\herenkj\\examples\\yc.xlsx";
  let _ret = ZjsrmyyService::import_lis_results(filename, &dbconn).await;
  // let stdate = 1609459200;
  // let enddate = 1640995200;
  // let ret = DocumentSvc::export_fee_by_departments(stdate, enddate, &vec![], &dbconn).await;

  // let str1 = r"(中文1 中文2，你好ant( 中文3, lion( tiger))、 bird)";
  // let result = utility::string::split_by_regex(str1);
  // println!("split result:{:?}", &result);
  // let local_now = Local::now();
  // println!("local now is:{:?}", &local_now);
  // if let Some(checked_add) = local_now.checked_add_signed(Duration::days(-365)) {
  //   println!("local now is:{:?}", &checked_add);
  //   println!("time stamp is:{}", &checked_add.timestamp());

  // let result = "{\"code\":201,\"message\":\"操作员ID不存在:[admin2]\",\"data\":\"\"}";
  // let ret = serde_json::from_str::<ResponseBody<DataBody<String>>>(&result);
}

#[test]
pub fn test_export_fee() {
  //开始写入excel
  let mut workbook = rust_xlsxwriter::Workbook::new();
  // Add a worksheet to the workbook.
  let worksheet = workbook.add_worksheet();
  let merge_format = rust_xlsxwriter::Format::new()
    .set_border(rust_xlsxwriter::FormatBorder::Thin)
    .set_align(rust_xlsxwriter::FormatAlign::Center)
    .set_align(rust_xlsxwriter::FormatAlign::VerticalCenter);
  // Write a string without formatting.
  worksheet.write(0, 0, "#").unwrap();
  worksheet.write(0, 1, "科室").unwrap();
  worksheet.write(0, 2, "项目").unwrap();
  worksheet.write(0, 3, "总量").unwrap();
  worksheet.write(0, 5, "个人").unwrap();
  worksheet.write(0, 7, "团体").unwrap();

  worksheet.write(1, 3, "人数").unwrap();
  worksheet.write(1, 4, "金额(元)").unwrap();
  worksheet.write(1, 5, "人数").unwrap();
  worksheet.write(1, 6, "金额(元)").unwrap();
  worksheet.write(1, 7, "人数").unwrap();
  worksheet.write(1, 8, "金额(元)").unwrap();

  // Write some merged cells.
  worksheet.merge_range(0, 0, 1, 0, "#", &merge_format).unwrap();
  worksheet.merge_range(0, 1, 1, 1, "科室", &merge_format).unwrap();
  worksheet.merge_range(0, 2, 1, 2, "项目", &merge_format).unwrap();

  worksheet.merge_range(0, 3, 0, 4, "总量", &merge_format).unwrap();
  worksheet.merge_range(0, 5, 0, 6, "团体", &merge_format).unwrap();
  worksheet.merge_range(0, 7, 0, 8, "个人", &merge_format).unwrap();

  let mut datas: Vec<FeeSummaryDetails> = vec![];
  let fee1 = FeeSummaryDetails {
    deptname: "检验科".to_string(),
    itemname: "血常规".to_string(),
    itemprice: 30.0,
    medtype: 1,
  };
  datas.push(fee1);
  let fee1 = FeeSummaryDetails {
    deptname: "检验科".to_string(),
    itemname: "血常规".to_string(),
    itemprice: 30.0,
    medtype: 2,
  };
  datas.push(fee1);
  let fee1 = FeeSummaryDetails {
    deptname: "检验科".to_string(),
    itemname: "尿常规".to_string(),
    itemprice: 20.0,
    medtype: 2,
  };
  datas.push(fee1);
  let fee1 = FeeSummaryDetails {
    deptname: "检验科".to_string(),
    itemname: "生化常规".to_string(),
    itemprice: 35.0,
    medtype: 1,
  };
  datas.push(fee1);
  let fee1 = FeeSummaryDetails {
    deptname: "内科".to_string(),
    itemname: "普通内科".to_string(),
    itemprice: 40.0,
    medtype: 1,
  };
  datas.push(fee1);

  let departs = vec!["检验科", "内科"];
  let mut start_row = 2;
  let mut current_row = start_row;
  // let iteminfos = datas.iter().map(|v|)
  for deptinfo in departs.iter() {
    let itemnames: HashSet<String> = datas.iter().filter(|&p| p.deptname.eq_ignore_ascii_case(&deptinfo)).map(|f| f.itemname.to_owned()).collect();

    let mut idx = 1;
    for itemname in itemnames.iter() {
      let fees: Vec<&FeeSummaryDetails> = datas
        .iter()
        .filter(|&v| v.deptname.eq_ignore_ascii_case(&deptinfo) && v.itemname.eq_ignore_ascii_case(&itemname))
        .map(|m| m.to_owned())
        .collect();
      if fees.len() > 0 {
        let total_nums = fees.iter().count();
        let price = fees[0].itemprice;
        let total_price = total_nums as f32 * price;

        // for fee in fees {
        worksheet.write(current_row, 0, format!("{}", idx)).unwrap();
        worksheet.write(current_row, 1, format!("{}", deptinfo)).unwrap();
        worksheet.write(current_row, 2, format!("{}", itemname)).unwrap();
        worksheet.write(current_row, 3, format!("{}", total_nums)).unwrap();
        worksheet.write(current_row, 4, format!("{}", total_price)).unwrap();
        idx += 1;
        current_row += 1;
      }
    }
    println!("start row:{}", start_row);
    println!("current row:{}", current_row);
    if current_row > start_row + 1 {
      println!("start to merge from {} to {}", start_row, current_row);
      worksheet.merge_range(start_row, 1, current_row - 1, 1, deptinfo, &merge_format).unwrap();
    }
    start_row = current_row;
    //
  }

  // Save the file to disk.
  println!("start to save");
  workbook.save("demo.xlsx").unwrap();
}

#[test]
pub fn test_decode_xml() -> Result<()> {
  let xml = r#"<?xml version="1.0" encoding="UTF-8" ?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV='http://schemas.xmlsoap.org/soap/envelope/' xmlns:xsi='http://wmw.w3.org/2001/xMLschema-instance' xmlns:s='http://www.w3.org/2001/XMLschema'>
<SOAP-ENV:Body><Sample2Response xmlns="http://tempuri.org"><Sample2Result>{"resultcode":"-1","desclessage":"无效的外部体检流水号","result":null}</Sample2Result></Sample2Response></SOAP-ENV:Body>
</SOAP-ENV:Envelope>"#;

  // Create a quick-xml reader
  let mut reader = Reader::from_str(xml);
  reader.config_mut().trim_text(true); // Trim whitespace

  let mut buf = Vec::new();
  let mut json_string = None;

  // Parse the XML
  loop {
    match reader.read_event_into(&mut buf)? {
      Event::Start(e) => {
        // Check for Sample2Result element
        if e.name().as_ref() == b"Sample2Result" {
          // Read the text content of the element
          if let Event::Text(text) = reader.read_event_into(&mut buf)? {
            json_string = Some(text.unescape()?.into_owned());
          }
        }
      }
      Event::Eof => break, // End of XML
      _ => (),             // Ignore other events
    }
    buf.clear(); // Clear buffer after each event
  }
  println!("json string:{:?}", json_string);
  Ok(())
}
