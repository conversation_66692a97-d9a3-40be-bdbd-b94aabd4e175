use std::{env, path::PathBuf};
use tracing::*;

use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::tj_corpoccureport::TjCorpoccureport, dbinit::DbConnection};
use crate::{
  client::httpclient::HttpClient,
  common::constant::{self, ReportFormat},
  dto::CorpReportDTO,
};
use dataservice::{dbinit::DbConnection, entities::prelude::*};

use tokio::process::Command;
// use utility::*;

// use rust_xlsxwriter::*;

pub struct DocumentSvc;

impl DocumentSvc {
  pub async fn generate_corp_occu_report(rptid: i64, rpttype: i32, oritype: i32, splitinrow: i32, rptdir: &str, egouri: &str, commander: &str, db: &DbConnection) -> Result<String> {
    if rptid <= 0 {
      return Err(anyhow!("report id is empty, not allowed"));
    }
    let ret = TjCorpoccureport::query(rptid, &db.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let rptinfo = ret.unwrap();
    if rptinfo.is_none() {
      return Err(anyhow!("报告编号:{rptid}的报告信息不存在"));
    }
    let rptinfo = rptinfo.unwrap();

    if rpttype == ReportFormat::DOCX as i32 && rptinfo.tj_reporttype == constant::ReportType::Normal as i32 {
      let dto = CorpReportDTO {
        rptid,
        pagestyle: oritype,
        outdir: rptdir.to_string(),
      };
      let ego_url = format!("{}/api/docs/ocuv2", egouri);
      let ret: Result<String> = HttpClient::send_http_post_request("", &ego_url, &Some(dto)).await;
      info!("http get result is:{:?}", &ret);
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let rptfile = ret.unwrap();
      return Ok(rptfile);
    }
    if commander.is_empty() {
      return Err(anyhow!("暂不支持该格式，请选用其他格式"));
    }
    let mut fileext = "pdf";
    if rpttype == ReportFormat::DOCX as i32 {
      fileext = "docx";
    }
    // if rpttype == ReportFormat::PDF as i32 && !commander.is_empty() {
    let report_type_str = match rptinfo.tj_reporttype {
      0 => "职业",
      1 => "普通",
      2 => "放射",
      _ => "职业",
    };
    let c_dict = crate::SYSCACHE.get().unwrap().get_dict(constant::DictType::DictTesttype as i32, rptinfo.tj_testtype, &db).await;
    let rptfile = format!("{}-{}-{}-{}.{}", rptinfo.tj_corpname, report_type_str, c_dict.ss_name, rptinfo.tj_reportnumint, fileext);
    let ret = DocumentSvc::generate_report(rptid, rpttype, oritype, splitinrow, rptdir, commander, &rptfile).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    // rptfile = ret.unwrap();
    info!("final report file is:{rptfile}");
    return Ok(rptfile);
    // }
    // Err(anyhow!("Not supported report type or empty commander"))
  }

  pub async fn generate_report(rptid: i64, rpttype: i32, oritype: i32, splitinrow: i32, rptdir: &str, commander: &str, rptfile: &str) -> anyhow::Result<String> {
    let current_dir = env::current_dir();
    if current_dir.is_err() {
      return Err(anyhow::anyhow!("{}", current_dir.as_ref().unwrap_err().to_string()));
    }
    let current_dir = current_dir.unwrap();
    info!("Current directory is:{}", &current_dir.display());

    // let exec_command = current_dir.join("office").join(commander);
    let exec_command = PathBuf::from(commander);
    info!("exec command is:{}", &exec_command.display());
    if !exec_command.exists() {
      return Err(anyhow::anyhow!("没有用于生成报告的命令"));
    }

    // let output_dir = current_dir.join(rptdir);
    // info!("output dir is:{}", &output_dir.display());

    let mut command = Command::new(exec_command);
    command.arg(&rptid.to_string());
    command.arg(&rpttype.to_string());
    command.arg(&oritype.to_string());
    command.arg(&splitinrow.to_string());
    command.arg(rptdir);
    let output_file = current_dir.join(rptdir).join(rptfile);
    if output_file.exists() {
      if let Err(e) = std::fs::remove_file(&output_file) {
        error!("remove file error:{:?}", e);
      }
    }
    info!("output file is:{}", &rptfile);
    info!("command is:{:?}", &command);
    let ret = command.output().await;
    if ret.as_ref().is_err() {
      info!("exec command error:{}", ret.as_ref().unwrap_err().to_string());
      return Err(anyhow::anyhow!("{}", ret.err().unwrap().to_string()));
    }
    // let output_ret = ret.unwrap();
    // let result = String::from_utf8(output_ret.stdout.to_vec());
    // info!("exec command output is:{:?}", result);

    //等待文件生成
    let mut nums = 1;
    loop {
      if nums >= 1000 {
        return Err(anyhow!("不能生成pdfv2版本的报告，请到报告管理选择pdfv1版本再次尝试查看报告"));
      }
      if output_file.exists() {
        break;
      } else {
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
        nums += 1;
        continue;
      }
    }
    Ok(rptfile.to_string())
  }
}

// use docx_rs::*;
// pub fn main() -> Result<(), DocxError> {
//   let path = std::path::Path::new("./output/table_border.docx");
//   let file = std::fs::File::create(&path).unwrap();
//   let style1 = Style::new("Heading1", StyleType::Paragraph).name("Heading 1").align(AlignmentType::Center);
//   let style2 = Style::new("Run1", StyleType::Character).name("Run test").bold();
//   let style3 = Style::new("Table1", StyleType::Table)
//     .name("Table test")
//     .size(30)
//     .margins(TableCellMargins::new().margin_top(200, WidthType::Dxa))
//     .table_align(TableAlignmentType::Center);
//   let p1 = Paragraph::new()
//         // .add_run(Run::new().add_text("Hello").style("Run1"))
//         .add_run(Run::new().add_text("职业健康检查报告书").size(48).bold())
//         .add_run(Run::new().add_break(BreakType::TextWrapping))
//         .add_run(Run::new().add_break(BreakType::TextWrapping))
//         // .style("style1")
//         .align(AlignmentType::Center)
//         // .page_break_before(true)
//         ;
//   let table = Table::new(vec![
//         TableRow::new(vec![
//             TableCell::new()
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb")))
//                 .grid_span(2)
//                 // .clear_border(TableCellBorderPosition::Left)
//                 // .clear_border(TableCellBorderPosition::Bottom)
//                 // .clear_border(TableCellBorderPosition::Right)
//                 ,
//             TableCell::new()
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb")))
//                 .vertical_align(VAlignType::Center)
//                 .vertical_merge(VMergeType::Restart),
//         ]),
//         TableRow::new(vec![
//             TableCell::new()
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb")))
//                 .vertical_merge(VMergeType::Restart)
//                 // .clear_all_border()
//                 ,
//             TableCell::new().add_paragraph(Paragraph::new().add_run(Run::new().add_text("精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb"))),
//             TableCell::new()
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb")))
//                 .vertical_merge(VMergeType::Continue),
//         ]),
//         TableRow::new(vec![
//             TableCell::new()
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb")))
//                 .vertical_merge(VMergeType::Continue),
//             TableCell::new().add_paragraph(Paragraph::new().add_run(Run::new().add_text("精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb"))),
//             TableCell::new()
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb精简版是基于 vue-pure-admin (opens new window)提炼出的架子，包含主体功能，更适合实际项目开发，打包后的大小在全局引入 element-plus (opens new window)的情况下仍然低于 2.3MB，并且会永久同步完整版的代码。开启 brotli 压缩和 cdn 替换本地库模式后，打包大小低于 350kb")))
//                 .vertical_merge(VMergeType::Continue),
//         ]),
//     ])
//     .set_grid(vec![2000, 2000, 6000])
//     // .indent(1000)
//     ;
//   let table2 = Table::new(vec![
//         TableRow::new(vec![
//             TableCell::new()
//                 .add_paragraph(
//                     Paragraph::new()
//                         .add_run(Run::new().add_text("用人单位："))
//                         .align(AlignmentType::Right)
//                         .align(AlignmentType::End),
//                 ).vertical_align(VAlignType::Center)
//                 .clear_all_border(),
//             TableCell::new()
//                 .add_paragraph(Paragraph::new().add_run(
//                     Run::new().add_text("精简版是基于提炼出的架子精简dsafasdfasdf版于架子"),
//                 ))
//                 .clear_border(TableCellBorderPosition::Left)
//                 .clear_border(TableCellBorderPosition::Right)
//                 .clear_border(TableCellBorderPosition::Top),
//         ]),
//         TableRow::new(vec![
//             TableCell::new()
//                 .add_paragraph(
//                     Paragraph::new()
//                         .add_run(Run::new().add_text("地    址："))
//                         .align(AlignmentType::Right),
//                 )
//                 .clear_all_border(),
//             TableCell::new()
//                 .add_paragraph(Paragraph::new().add_run(
//                     Run::new().add_text("精简版是基于提炼出的架子精简版是基于提炼出的架子"),
//                 ))
//                 .clear_border(TableCellBorderPosition::Left)
//                 .clear_border(TableCellBorderPosition::Right)
//                 .clear_border(TableCellBorderPosition::Top),
//         ]),
//         TableRow::new(vec![
//             TableCell::new()
//                 .add_paragraph(
//                     Paragraph::new()
//                         .add_run(Run::new().add_text("联系电话："))
//                         .align(AlignmentType::Right),
//                 )
//                 .clear_all_border(),
//             TableCell::new()
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("13456789098")))
//                 .clear_border(TableCellBorderPosition::Left)
//                 .clear_border(TableCellBorderPosition::Right)
//                 .clear_border(TableCellBorderPosition::Top),
//         ]),
//         TableRow::new(vec![
//             TableCell::new()
//                 .add_paragraph(
//                     Paragraph::new()
//                         .add_run(Run::new().add_text(""))
//                         .align(AlignmentType::Right),
//                 )
//                 .clear_all_border(),
//             TableCell::new()
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("")))
//                 .clear_all_border(),
//         ]),
//         TableRow::new(vec![
//             TableCell::new()
//                 .add_paragraph(
//                     Paragraph::new()
//                         .add_run(Run::new().add_text(""))
//                         .align(AlignmentType::Right),
//                 )
//                 .clear_all_border(),
//             TableCell::new()
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("")))
//                 .clear_all_border(),
//         ]),
//         TableRow::new(vec![
//             TableCell::new()
//                 .add_paragraph(
//                     Paragraph::new()
//                         .add_run(Run::new().add_text("体检类型："))
//                         .align(AlignmentType::Right),
//                 )
//                 .clear_all_border(),
//             TableCell::new()
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("上岗 ✔")).line_spacing(LineSpacing::new().after_lines(20)))
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("在岗")).line_spacing(LineSpacing::new().after_lines(20)))
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("离岗")).line_spacing(LineSpacing::new().after_lines(20)))
//                 .add_paragraph(Paragraph::new().add_run(Run::new().add_text("应急")).line_spacing(LineSpacing::new().after_lines(20)))
//                 .clear_all_border(),
//         ]),
//     ])
//     .set_grid(vec![2000, 6000])
//     .indent(1000)
//     .style("Table1")
//     // .margins(TableCellMargins::new().margin_top(200, WidthType::Pct))
//     ;
//   Docx::new()
//     .page_margin(PageMargin::new().left(800).right(800).top(1200).bottom(1000))
//     .add_style(style1)
//     .add_style(style2)
//     .add_style(style3)
//     .add_paragraph(p1)
//     .add_table(table2)
//     .add_paragraph(Paragraph::new())
//     .add_table(table)
//     .build()
//     .pack(file)?;
//   Ok(())
// }
