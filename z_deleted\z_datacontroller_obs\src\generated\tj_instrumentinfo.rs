//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_instrumentinfo")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    #[sea_orm(unique)]
    pub tj_struid: String,
    pub tj_struname: String,
    pub tj_typename: String,
    pub tj_deptid: String,
    pub tj_memo: String,
    pub tj_com: String,
    pub tj_lowvalue: f32,
    pub tj_upvalue: f32,
    pub tj_vlimited: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
