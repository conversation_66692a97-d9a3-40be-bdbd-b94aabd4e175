use crate::{datasetup::DbConnection, entities::prelude::*};
use anyhow::{anyhow, Result};
use rbatis::crud::CRUD;

impl ScdcItemunit {
    pub fn new() -> Self {
        ScdcItemunit { ..Default::default() }
    }

    pub async fn query(itemcode: &String, db: &DbConnection) -> Option<ScdcItemunit> {
        if itemcode.is_empty() {
            return None;
        }
        let w = db.get_connection().new_wrapper().eq("item_code", itemcode); //

        let query_ret = db.get_connection().fetch_by_wrapper(w).await;

        match query_ret {
            Ok(v) => v,
            Err(e) => {
                error!("query error:{}", e);
                None
            }
        }
    }

    pub async fn query_many(ids: &Vec<String>, db: &DbConnection) -> Result<Vec<ScdcItemunit>> {
        // if testids.len() <= 0 {
        //     return Err(anyhow!("ids is empty"));
        // }
        let w = db.get_connection().new_wrapper().do_if(ids.len() > 0, |w| w.r#in("item_code", &ids));

        let query_ret = db.get_connection().fetch_list_by_wrapper(w).await;

        match query_ret {
            Ok(v) => Ok(v),
            Err(e) => {
                error!("query error:{}", &e);
                Err(anyhow!("query many error:{:?}", &e))
            }
        }
    }
}
