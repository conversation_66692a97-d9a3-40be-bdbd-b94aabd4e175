use crate::entities::{prelude::*, tj_pacsresult};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter};
use serde_json::json;

impl TjPacsresult {
  pub async fn query_many(code: &str, db: &DatabaseConnection) -> Result<Vec<TjPacsresult>> {
    let ret = TjPacsresultEntity::find().filter(tj_pacsresult::Column::TjTestid.eq(code)).all(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn insert_many(info: &Vec<TjPacsresult>, db: &DatabaseConnection) -> Result<i64> {
    if info.len() <= 0 {
      return Err(anyhow!("empty data"));
    }

    let mut active_values: Vec<tj_pacsresult::ActiveModel> = Vec::new();
    for val in info {
      let ret = tj_pacsresult::ActiveModel::from_json(json!(val));
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      active_values.push(ret.unwrap());
    }
    let ret = TjPacsresultEntity::insert_many(active_values).exec(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().last_insert_id)
  }
  pub async fn delete(testids: &Vec<String>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjPacsresultEntity::delete_many()
      .filter(Condition::all().add(tj_pacsresult::Column::TjTestid.is_in(testids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
  pub async fn clear(date: i64, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjPacsresultEntity::delete_many()
      .filter(Condition::all().add(tj_pacsresult::Column::TjImportdate.lt(date)))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
