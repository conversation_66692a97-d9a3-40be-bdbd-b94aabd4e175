use crate::entities::{prelude::*, tj_corpinfo};
use anyhow::{anyhow, Result};
use sea_orm::{sea_query::Expr, ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjCorpinfo {
  pub async fn query(code: i64, db: &DatabaseConnection) -> Result<Option<TjCorpinfo>> {
    let ret = TjCorpinfoEntity::find().filter(tj_corpinfo::Column::Id.eq(code)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(corpids: &Vec<i64>, corpname: &str, orgcode: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjCorpinfo>> {
    let mut conditions = Condition::all().add(tj_corpinfo::Column::TjStatus.gte(0));

    if corpids.len() > 0 {
      conditions = conditions.add(tj_corpinfo::Column::Id.is_in(corpids.to_owned()));
    }
    if !corpname.is_empty() {
      conditions = conditions.add(tj_corpinfo::Column::TjCorpname.contains(corpname));
    }
    if orgcode.len() > 0 {
      conditions = conditions.add(tj_corpinfo::Column::TjOrgcode.is_in(orgcode.to_owned()));
    }

    let ret = TjCorpinfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let results = ret.unwrap();
    // info!("corp query completed, total corpinfos:{}", &results.len());
    Ok(results)
  }

  pub async fn save(info: &TjCorpinfo, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_corpinfo::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    // info!("new val is:{:#?}", &newval);
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjCorpinfoEntity::update_many()
      .col_expr(tj_corpinfo::Column::TjStatus, Expr::value(-1))
      .filter(tj_corpinfo::Column::Id.is_in(ids.to_owned()))
      .exec(db)
      .await;

    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let update_ret = ret.unwrap();
    Ok(update_ret.rows_affected)
  }
}
