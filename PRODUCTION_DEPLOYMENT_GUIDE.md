# Production Deployment Guide for Report Server

## ⚠️ **Werkzeug Warning Solution**

If you see this warning:

```
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
```

This is expected when using the Flask development server. Here are the solutions:

## 🚀 **Production Deployment Options**

### **Option 1: Automatic Production Setup (Recommended)**

#### **Windows Batch Script**

```cmd
cd python
start_production.bat
```

#### **PowerShell Script**

```powershell
cd python
.\start_production.ps1
```

#### **Python Script (Cross-platform)**

```bash
cd python
python start_production.py
```

**Features:**

- ✅ Automatically detects available WSGI servers
- ✅ Installs missing servers if needed
- ✅ Uses optimal configuration for each server
- ✅ Provides clear status messages

### **Option 2: Manual Production Setup**

#### **Gunicorn (Linux/Mac - Recommended)**

```bash
# Install Gunicorn
pip install gunicorn

# Start with provided configuration
gunicorn --config gunicorn.conf.py wsgi:app

# Or with custom settings
gunicorn --bind 0.0.0.0:8080 --workers 4 --timeout 300 wsgi:app
```

#### **Waitress (Windows - Recommended)**

```bash
# Install Waitress
pip install waitress

# Start server
waitress-serve --host=0.0.0.0 --port=8080 wsgi:app
```

#### **uWSGI (Advanced)**

```bash
# Install uWSGI
pip install uwsgi

# Start server
uwsgi --http 0.0.0.0:8080 --module wsgi:app --processes 4
```

### **Option 3: Development Server (Testing Only)**

If you want to continue using the development server:

```bash
cd python
python start_server.py
```

**Note:** The warning is now suppressed, but this is still not recommended for production.

## 📋 **Production Server Comparison**

| Server        | Platform  | Performance | Ease of Use | Recommended For         |
| ------------- | --------- | ----------- | ----------- | ----------------------- |
| **Gunicorn**  | Linux/Mac | Excellent   | Easy        | Production Linux/Mac    |
| **Waitress**  | All       | Good        | Very Easy   | Production Windows      |
| **uWSGI**     | All       | Excellent   | Complex     | High-performance setups |
| **Flask Dev** | All       | Poor        | Very Easy   | Development only        |

## 🔧 **Production Configuration**

### **Gunicorn Configuration**

The provided `gunicorn.conf.py` includes:

- **Workers:** CPU count × 2 + 1
- **Timeout:** 300 seconds (for report generation)
- **Logging:** Access and error logs
- **Process management:** Auto-restart workers
- **Performance tuning:** Preload app, keepalive

### **Environment Variables**

```bash
# Server configuration
export REPORT_SERVER_HOST=0.0.0.0
export REPORT_SERVER_PORT=8080
export REPORT_SERVER_DEBUG=false

# Paths
export REPORT_OUTPUT_DIR=./reports
export REPORT_LOG_LEVEL=INFO
```

### **Reverse Proxy Setup (Nginx)**

For production, use Nginx as a reverse proxy:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /api/ {
        proxy_pass http://127.0.0.1:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_read_timeout 300;  # For long report generation
    }

    location /reports/ {
        alias /path/to/reports/;
        autoindex on;
    }
}
```

## 🛠 **Windows Service Installation**

### **Using NSSM (Non-Sucking Service Manager)**

1. **Download NSSM:** https://nssm.cc/download

2. **Install Service:**

```cmd
nssm install ReportServer
nssm set ReportServer Application "C:\Python\python.exe"
nssm set ReportServer AppParameters "C:\path\to\start_production.py --server waitress"
nssm set ReportServer AppDirectory "C:\path\to\python"
nssm set ReportServer DisplayName "Report Server API"
nssm set ReportServer Description "REST API for Report Generation"
nssm start ReportServer
```

### **Using Windows Task Scheduler**

1. Create a batch file:

```cmd
@echo off
cd /d "C:\path\to\python"
python start_production.py --server waitress
```

2. Schedule it to run at startup with highest privileges.

## 🐧 **Linux Systemd Service**

Create `/etc/systemd/system/reportserver.service`:

```ini
[Unit]
Description=Report Server API
After=network.target

[Service]
Type=simple
User=reportserver
WorkingDirectory=/opt/reportserver/python
Environment=PATH=/opt/reportserver/venv/bin
ExecStart=/opt/reportserver/venv/bin/python start_production.py --server gunicorn
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start:

```bash
sudo systemctl daemon-reload
sudo systemctl enable reportserver
sudo systemctl start reportserver
sudo systemctl status reportserver
```

## 🐳 **Docker Deployment**

### **Using Docker Compose**

```bash
docker-compose up -d
```

### **Manual Docker**

```bash
# Build image
docker build -t reportserver python/

# Run container
docker run -d \
  --name reportserver \
  -p 8080:8080 \
  -v $(pwd)/config:/app/config:ro \
  -v $(pwd)/fonts:/app/fonts:ro \
  -v $(pwd)/reports:/app/reports \
  reportserver
```

## 📊 **Performance Tuning**

### **Gunicorn Optimization**

```bash
# Calculate optimal workers
python -c "import multiprocessing; print(multiprocessing.cpu_count() * 2 + 1)"

# Start with optimized settings
gunicorn \
  --bind 0.0.0.0:8080 \
  --workers 9 \
  --worker-class sync \
  --timeout 300 \
  --keepalive 2 \
  --max-requests 1000 \
  --max-requests-jitter 100 \
  --preload-app \
  wsgi:app
```

### **System Optimization**

```bash
# Increase file descriptor limits
ulimit -n 65536

# Optimize TCP settings
echo 'net.core.somaxconn = 65536' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65536' >> /etc/sysctl.conf
sysctl -p
```

## 🔍 **Monitoring and Logging**

### **Log Files**

- **Application:** `log/reportserver.log`
- **Gunicorn Access:** `log/gunicorn_access.log`
- **Gunicorn Error:** `log/gunicorn_error.log`
- **Waitress:** Console output (redirect to file)

### **Health Monitoring**

```bash
# Health check script
#!/bin/bash
HEALTH_URL="http://localhost:8080/api/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "$(date): Service is healthy"
else
    echo "$(date): Service is down (HTTP $RESPONSE)"
    # Restart service or send alert
fi
```

### **Process Monitoring**

```bash
# Check if server is running
ps aux | grep -E "(gunicorn|waitress|uwsgi)" | grep -v grep

# Monitor resource usage
top -p $(pgrep -f "gunicorn|waitress|uwsgi")
```

## 🚨 **Troubleshooting**

### **Common Issues**

1. **Port already in use**

   ```bash
   # Find process using port
   netstat -tulpn | grep 8080
   # Kill process or change port
   ```

2. **Permission denied**

   ```bash
   # Fix permissions
   chmod 755 reports/ log/
   chown -R reportserver:reportserver /opt/reportserver/
   ```

3. **Database connection errors**
   ```bash
   # Check config file
   cat config/nodipexam.toml
   # Test database connectivity
   ```

### **Performance Issues**

1. **Slow response times**

   - Increase worker processes
   - Check database performance
   - Monitor system resources

2. **Memory usage**
   - Set max-requests to restart workers
   - Monitor for memory leaks
   - Use worker recycling

## 📈 **Scaling Options**

### **Horizontal Scaling**

```nginx
upstream reportserver {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}
```

### **Load Balancing**

- Use Nginx upstream
- HAProxy for advanced load balancing
- Cloud load balancers (AWS ALB, etc.)

### **Database Scaling**

- Read replicas for reporting
- Connection pooling
- Database clustering

## ✅ **Quick Start Summary**

1. **For Development:**

   ```bash
   python start_server.py
   ```

2. **For Production (Auto):**

   ```bash
   python start_production.py --install
   ```

3. **For Production (Manual):**

   ```bash
   pip install gunicorn  # or waitress
   gunicorn --config gunicorn.conf.py wsgi:app
   ```

4. **Access API:**
   ```
   http://localhost:8080/api/health
   ```

The production deployment eliminates the Werkzeug warning and provides a robust, scalable solution for the Report Server API.
