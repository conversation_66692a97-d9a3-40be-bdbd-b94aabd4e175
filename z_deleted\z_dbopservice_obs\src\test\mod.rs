// use rbatis::dark_std::sync::vec;

use crate::{dataaccess::prelude::TjTestsummary, dbinit};

#[tokio::test]
async fn test_1() {
  let dburl = "mysql://joedev:JoeSong0406!@*************:8306/nodip_new";
  let db = dbinit::DbConnection::new(&dburl, 1).await;
  let deptid = vec!["0001".to_string(), "0010".to_string()];
  let testid = vec!["10009400".to_string()];
  let ret = TjTestsummary::query_by_testids_deptids(&mut db.get_connection(), &testid, &deptid).await;
  println!("Result is:{:#?}", &ret);
}
