#[allow(unused)]
use crate::rusthl7::{Field, Hl7ParseError, Message, Separators};
use anyhow::Result;
use std::fmt::Display;

#[derive(Debug, PartialEq)]
pub struct MsaSegment<'a> {
  pub source: &'a str,
  pub msg_encoding_characters: Separators,
  pub msa_1_acknowledge_code: Field<'a>,
  pub msa_2_message_control_id: Field<'a>,
  pub msa_3_text_message: Option<Field<'a>>,
  pub msa_4_expected_sequence_number: Option<Field<'a>>,
  pub msa_5_delayed_acknowledge_type: Option<Field<'a>>,
  pub msa_6_error_condition: Option<Field<'a>>,
}

impl<'a> MsaSegment<'a> {
  pub fn parse<S: Into<&'a str>>(input: S, delims: &Separators) -> Result<MsaSegment<'a>, Hl7ParseError> {
    let input = input.into();

    let mut fields = input.split(delims.field);

    assert!(fields.next().unwrap() == "MSA");

    let msh = MsaSegment {
      source: input,
      msg_encoding_characters: delims.to_owned(),
      msa_1_acknowledge_code: Field::parse_mandatory(fields.next(), delims)?,
      msa_2_message_control_id: Field::parse_mandatory(fields.next(), delims)?,
      msa_3_text_message: Field::parse_optional(fields.next(), delims)?,
      msa_4_expected_sequence_number: Field::parse_optional(fields.next(), delims)?,
      msa_5_delayed_acknowledge_type: Field::parse_optional(fields.next(), delims)?,
      msa_6_error_condition: Field::parse_optional(fields.next(), delims)?,
    };

    Ok(msh)
  }
}
/// Common formatter trait implementation for the strongly-typed segment
impl<'a> Display for MsaSegment<'a> {
  /// Required for to_string() and other formatter consumers
  fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
    write!(f, "{}", self.source)
  }
}
/// Common clone trait implementation for the strongly-typed segment
impl<'a> Clone for MsaSegment<'a> {
  /// Creates a new Message object using _the same source_ slice as the original.
  fn clone(&self) -> Self {
    let delims = self.msg_encoding_characters;
    MsaSegment::parse(self.source, &delims).unwrap()
  }
}

/// Extracts header element for external use
pub fn _msa<'a>(msg: &Message<'a>) -> Result<MsaSegment<'a>, Hl7ParseError> {
  let seg = msg.segments_by_identifier("MSA").unwrap()[0];
  let segment = MsaSegment::parse(seg.source, &msg.get_separators()).expect("Failed to parse MSA segment");
  Ok(segment)
}
