//! SeaORM Entity. Generated by sea-orm-codegen 0.9.2

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "v_tj_pacsresult")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i64,
  pub tjbh: String,
  pub brxm: String,
  pub jclx: String,
  pub jcxm: String,
  pub jcmc: String,
  pub imagesight: String,
  pub imagediagnosis: String,
  pub jcys: String,
  pub sxys: String,
  pub bgys: String,
  pub bgrq: String,
  // pub organizationname: String,
  // pub resultstatus: String,
  pub sfyc: i32,
  pub imagepath: Option<String>,
  pub datas: Option<String>,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
  fn def(&self) -> RelationDef {
    panic!("No RelationDef")
  }
}

impl ActiveModelBehavior for ActiveModel {}
