
use anyhow::{anyhow, Result};
use rbatis::{crud,py_sql,sql, rbdc};
use serde::{Deserialize, Serialize};
use rbatis::rbatis_codegen::IntoSql;

#[derive(<PERSON><PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjMedexaminfo {
  pub id: i64,
  pub tj_testid: String,
  pub tj_pid: String,
  pub tj_age: i32,
  pub tj_testcat: i32,
  pub tj_testsource: i32,
  pub tj_testtype: i32,
  pub tj_corpnum: i64,
  pub tj_empid: String,
  pub tj_workage: String,
  pub tj_wtcode: String,
  pub tj_worktype: String,
  pub tj_workshop:String,
  pub tj_monitortype: String,
  pub tj_poisionfactor: String,
  pub tj_poisionage: String,
  pub tj_radiationtype:String,
  pub tj_recorddate: i64,
  pub tj_recorder: String,
  pub tj_testdate: i64,
  pub tj_expdate: i64,
  pub tj_subdate: i64,
  pub tj_completed: i32,
  pub tj_total: i32,
  pub tj_checkstatus: i32,
  pub tj_printflag: i32,
  pub tj_printtimes: i32,
  pub tj_rptnum: String,
  pub tj_peid: i32,
  pub tj_isrecheck: i32,
  pub tj_oldtestid: String,
  pub tj_rechecktimes: i32,
  pub tj_push: i32,
  pub tj_num: i64,
  pub tj_pushstatus: i32,
  pub tj_upload: i32,
  pub tj_uploadtime: i64,
  pub tj_syncstatus: i32,
  pub tj_paymethod: i32,
  pub tj_packageid: i64,
  pub tj_packagename: String,
  pub tj_additional: String,
  pub p_medid: i64,
}
crud!(TjMedexaminfo {}, "tj_medexaminfo");
rbatis::impl_select!(TjMedexaminfo{query(testid:&str) -> Option => "`where tj_testid = #{testid} `"});
rbatis::impl_select!(TjMedexaminfo{query_many(dtstart:i64, dtend:i64, testids:&[&str], pids:&[&str],corpid:i64, isrecheck:i32, oldtestids:&[&str], testtypes:&[i32],status:&[i32],filter:i32, pname:&str) => 
  "`where id > 0 and tj_checkstatus >= 0`
  if dtstart > 0:
    ` and tj_testdate >= #{dtstart} `
  if dtend > 0:
    ` and tj_testdate < #{dtend} `
  if !testids.is_empty():
    ` and tj_testid in ${testids.sql()} `
  if !pids.is_empty():
    ` and tj_pid in ${pids.sql()} `
  if corpid == 2:
    ` and tj_corpnum > 2 `
  if corpid == 1:  
    ` and tj_corpnum = 1 `
  if corpid > 2:
    ` and tj_corpnum = #{corpid} `
  if isrecheck > 0:  
    ` and tj_isrecheck = #{isrecheck} `
  if !oldtestids.is_empty():
    ` and tj_oldtestid in ${oldtestids.sql()} `    
  if !testtypes.is_empty():
    ` and tj_testtype in ${testtypes.sql()} `   
  if !status.is_empty():
    ` and tj_checkstatus in ${status.sql()} `   
  if filter == 1:  
    ` and tj_completed >= 1 ` 
  if pname != '':  
    ` and tj_pid in (select tj_pid from tj_patient where tj_pname like #{'%'+pname+'%'}) `
  "});

rbatis::impl_select!(TjMedexaminfo{query_many_by_idcard_testtype(idcard:&str,testtype:i32, status:i32) => 
  "`where id > 0 `
  if testtype > 0:
    ` and tj_testtype = #{testtype} `
  if status > 0:
    ` and tj_checkstatus = #{status} `
  if idcard != '':  
    ` and tj_pid in (select tj_pid from tj_patient where tj_pidcard = #{idcard}) `
  "});

rbatis::impl_select!(TjMedexaminfo{query_empty_factors() => 
  "`where id > 0 and tj_poisionfactor='' and tj_testtype >=3 and tj_testtype <= 6 `
  "});



impl TjMedexaminfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjMedexaminfo ) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjMedexaminfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjMedexaminfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
  #[py_sql("update tj_medexaminfo set tj_rptnum = #{rptnum},tj_checkstatus=6 where tj_testid in ${testids.sql()} ")]
  pub async fn update_report_numbers(rb: &mut rbatis::RBatis, testids: &[String], rptnum: &str) -> rbatis::Result<rbdc::db::ExecResult> {
    impled!()
  }

  #[sql("update tj_medexaminfo set tj_checkstatus = ?,tj_completed=?,tj_total=?,tj_testdate=? where tj_testid = ? ")]
  pub async fn update_status(rb: &mut rbatis::RBatis, status: i32, completed: i32, total: i32, testdate: i64, testid: &str ) -> rbatis::Result<rbdc::db::ExecResult> {
    impled!()
  }
  #[py_sql("update tj_medexaminfo set tj_checkstatus = -10 where tj_testid in ${testids.sql()} ")]
  pub async fn delete(rb: &mut rbatis::RBatis,  testids: &[&str] ) -> rbatis::Result<rbdc::db::ExecResult> {
    impled!()
  }
  #[py_sql("update tj_medexaminfo set tj_upload = #{upstatus},tj_uploadtime=#{uptime},tj_checkstatus=#{checkstatus} where tj_testid in ${testids.sql()} ")]
  pub async fn update_medinfo_upload_status(rb: &mut rbatis::RBatis, upstatus:i32, uptime:i64,checkstatus:i32, testids: &[&str] ) -> rbatis::Result<rbdc::db::ExecResult> {
    impled!()
  }
  #[py_sql("update tj_medexaminfo set tj_poisionfactor = #{factors} where tj_testid in ${testids.sql()} ")]
  pub async fn update_medinfo_poisionfactors(rb: &mut rbatis::RBatis, factors:&str, testids: &[String] ) -> rbatis::Result<rbdc::db::ExecResult> {
    impled!()
  }
  #[py_sql("update tj_medexaminfo set tj_printflag = 1,tj_printtimes = tj_printtimes + 1 where tj_testid in ${testids.sql()} ")]
  pub async fn update_medexam_print_status(rb: &mut rbatis::RBatis, testids: &[String]) -> rbatis::Result<rbdc::db::ExecResult> {
    impled!()
  }
}
