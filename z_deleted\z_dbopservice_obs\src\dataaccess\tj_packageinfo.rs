use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(C<PERSON>, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjPackageinfo {
  pub id: i64,
  pub tj_pnum: String,
  pub tj_pname: String,
  pub tj_price: String,
  pub tj_type: i32,
  pub tj_memo: String,
  pub tj_showorder: i32,
  pub tj_fitrange: i32,
  pub tj_pyjm: String,
  pub tj_zdym: String,
  pub tj_flag: i32,
  pub tj_operator: i32,
  pub tj_moddate: i64,
}
crud!(TjPackageinfo {}, "tj_packageinfo");
rbatis::impl_select!(TjPackageinfo{query(id:i64) -> Option => "`where id = #{id} limit 1 `"});
rbatis::impl_delete!(TjPackageinfo{delete(ids:&[i64]) => "`where id in ${ids.sql()} `"});

impl TjPackageinfo {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjPackageinfo) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjPackageinfo::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjPackageinfo::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
}
