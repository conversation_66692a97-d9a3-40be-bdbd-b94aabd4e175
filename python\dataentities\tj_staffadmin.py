from sqlalchemy import Column, Integer, String
from dataentities.dbconn import Base


class TjStaffadmin(Base):
    # 数据库中存储的表名
    __tablename__ = "tj_staffadmin"
    # 对于必须插入的字段，采用nullable=False进行约束，它相当于NOT NULL
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    tj_staffno = Column(String(128), nullable=False)
    tj_sex = Column(Integer, nullable=False)
    tj_staffname = Column(String(512), nullable=False, unique=True)
    tj_deptid = Column(Integer, nullable=False)
    tj_groupid = Column(Integer, nullable=False)
    tj_password = Column(Integer, nullable=False)
    tj_role = Column(Integer, nullable=False)
    tj_checkallflag = Column(Integer, nullable=False)
    tj_title = Column(String(512), nullable=False)
    tj_status = Column(Integer, nullable=False)
    tj_operator = Column(String(512), nullable=False)
    tj_moddate = Column(Integer, nullable=False)
    tj_memo = Column(String(512), nullable=False)
    tj_isadmin = Column(Integer, nullable=False)
    login_session = Column(String(512), nullable=False)
    tj_esign = Column(String(512), nullable=False)
