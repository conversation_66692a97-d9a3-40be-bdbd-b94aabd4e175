# coding=utf-8

"""
Database Connection Manager

This module provides a centralized way to manage database connections
for the Report Server application.
"""

import logging
from typing import Optional
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.engine import Engine

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Singleton database connection manager"""
    
    _instance = None
    _engine: Optional[Engine] = None
    _session_factory = None
    _current_session: Optional[Session] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseManager, cls).__new__(cls)
        return cls._instance
    
    def initialize(self, database_uri: str) -> bool:
        """Initialize the database connection"""
        try:
            logger.info("Initializing database connection...")
            
            # Create engine with connection pooling
            self._engine = create_engine(
                database_uri,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,  # Verify connections before use
                pool_recycle=3600,   # Recycle connections every hour
                echo=False  # Set to True for SQL debugging
            )
            
            # Test the connection
            with self._engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            
            # Create session factory
            self._session_factory = sessionmaker(bind=self._engine)
            
            # Create a global session
            self._current_session = self._session_factory()
            
            logger.info("✓ Database connection established successfully")
            logger.info(f"Database engine: {self._engine.url}")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ Failed to initialize database connection: {e}")
            return False
    
    def get_session(self) -> Optional[Session]:
        """Get the current database session"""
        if self._current_session is None:
            logger.warning("Database session not initialized")
            return None
        
        # Check if session is still valid
        try:
            self._current_session.execute(text("SELECT 1"))
            return self._current_session
        except Exception as e:
            logger.warning(f"Database session invalid, creating new one: {e}")
            self._current_session = self._session_factory()
            return self._current_session
    
    def get_new_session(self) -> Optional[Session]:
        """Get a new database session (for concurrent operations)"""
        if self._session_factory is None:
            logger.warning("Database not initialized")
            return None
        
        return self._session_factory()
    
    def get_engine(self) -> Optional[Engine]:
        """Get the database engine"""
        return self._engine
    
    def close_session(self):
        """Close the current session"""
        if self._current_session:
            try:
                self._current_session.close()
                logger.info("Database session closed")
            except Exception as e:
                logger.error(f"Error closing database session: {e}")
            finally:
                self._current_session = None
    
    def close_all(self):
        """Close all database connections"""
        try:
            self.close_session()
            
            if self._engine:
                self._engine.dispose()
                logger.info("Database engine disposed")
                self._engine = None
                self._session_factory = None
                
        except Exception as e:
            logger.error(f"Error closing database connections: {e}")
    
    def health_check(self) -> dict:
        """Perform a database health check"""
        health_status = {
            "status": "unknown",
            "engine": "not_initialized",
            "session": "not_initialized",
            "connection_test": "not_performed"
        }
        
        try:
            if self._engine is None:
                health_status["status"] = "not_initialized"
                return health_status
            
            health_status["engine"] = "initialized"
            
            # Test engine connection
            with self._engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                if result.fetchone():
                    health_status["connection_test"] = "passed"
                else:
                    health_status["connection_test"] = "failed"
                    health_status["status"] = "error"
                    return health_status
            
            # Test session
            session = self.get_session()
            if session:
                health_status["session"] = "available"
                health_status["status"] = "healthy"
            else:
                health_status["session"] = "unavailable"
                health_status["status"] = "degraded"
            
        except Exception as e:
            health_status["status"] = "error"
            health_status["error"] = str(e)
            logger.error(f"Database health check failed: {e}")
        
        return health_status
    
    def get_connection_info(self) -> dict:
        """Get database connection information"""
        info = {
            "engine_initialized": self._engine is not None,
            "session_initialized": self._current_session is not None,
            "session_factory_initialized": self._session_factory is not None
        }
        
        if self._engine:
            info["database_url"] = str(self._engine.url)
            info["pool_size"] = self._engine.pool.size()
            info["checked_in_connections"] = self._engine.pool.checkedin()
            info["checked_out_connections"] = self._engine.pool.checkedout()
            info["overflow_connections"] = self._engine.pool.overflow()
        
        return info


# Global database manager instance
db_manager = DatabaseManager()


def initialize_database(database_uri: str) -> bool:
    """Initialize the global database connection"""
    return db_manager.initialize(database_uri)


def get_db_session() -> Optional[Session]:
    """Get the global database session"""
    return db_manager.get_session()


def get_new_db_session() -> Optional[Session]:
    """Get a new database session"""
    return db_manager.get_new_session()


def get_db_engine() -> Optional[Engine]:
    """Get the database engine"""
    return db_manager.get_engine()


def close_database():
    """Close all database connections"""
    db_manager.close_all()


def database_health_check() -> dict:
    """Perform a database health check"""
    return db_manager.health_check()


def get_database_info() -> dict:
    """Get database connection information"""
    return db_manager.get_connection_info()


# Backward compatibility functions
def create_global_session():
    """Create a global session (for backward compatibility)"""
    return get_db_session()


def get_global_session():
    """Get the global session (for backward compatibility)"""
    return get_db_session()
