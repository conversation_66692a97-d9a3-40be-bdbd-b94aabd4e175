use serde::{Deserialize, Serialize};
use utility::serde::deserialize_string_to_i64;

//体检信息视图，体检系统使用
#[derive(Debug, Default, Clone, Serialize, Deserialize)]
pub struct MedexaminfosView {
  pub id: i64,
  pub testid: String, //体检编号
  pub age: i32,
  pub testcatid: i32,
  pub testcatname: String, //体检类别：个人，单位
  pub testtypeid: i32,
  pub testtypename: String, //体检类型
  pub worktypeid: String,
  pub worktypename: String,  //工种
  pub worktypecode: String,  //工种代码
  pub workage: String,       //工龄
  pub poisionfactor: String, //毒害因素
  pub poisionage: String,    //接害工龄
  pub recorddate: i64,       //登记日期
  pub printflagid: i32,
  pub printflagname: String, //打印标志，0未打印 1已打印
  pub printtimes: i32,       //打印次数
  pub testdate: i64,         //体检日期
  pub reportnum: String,     //报告编号
  pub preportnum: String,    //个人报告编号
  pub statusid: i32,         //
  pub statusname: String,    //体检状态
  pub peid: i32,             //所属单位体检编号
  pub empid: String,         //工号
  pub paymethod: i32,        //支付方式 1：个人支付 2：企业支付
  pub packagename: String,   //套餐名称
  pub additional: String,    //额外信息
  //个人信息
  pub pname: String, //名字
  pub pid: String,   //档案编号
  pub sexid: i32,
  pub sexname: String, //性别
  pub pidcard: String, //身份证号
  pub pmarriageid: i32,
  pub nation: String,        //民族
  pub pmarriagename: String, //婚姻状况
  pub pcareer: String,       //职业
  pub paddress: String,      //地址
  pub pmobile: String,       //号码
  pub pbirthday: String,     //出生日期
  pub photo: String,         //照片

  //企业信息
  pub corpnum: i64,     //公司ID
  pub corpname: String, //所在公司

  //总检信息
  pub checkresultesid: String,   //总检结果id
  pub checkresultsstr: String,   //总检结果对应的名字， 正常......
  pub checkresultesname: String, //总检结果
  pub checkstatusid: i32,        //总检状态 1:已经总检
  pub checkstatusname: String,   // 总检状态中文显示
  pub checkalldate: i64,         //总检时间
  ////职业禁忌与职业病
  pub forbidden: String,
  pub targetdis: String,

  //操作人员信息
  pub staffid: i64, //操作人员编码
  pub staffname: String,

  pub inspecteddepart: String,   //已检科室
  pub notexamineddepart: String, //未检科室
  pub rechecktimes: i32,         //复查次数
  pub isrecheck: i32,            //是否复查
  // pub oldtestid: String,         //原体检号
  pub upload: i32,     //0未上传 1已上传 2无需上报
  pub syncstatus: i32, //数据同步状态 0：未同步，1：预约 2：登记 3：正在体检 4：体检结束 5: 以总检 6：已报告
  pub idx: i64,        //排序用

  pub p_cmpid: i64,
  pub p_wkid: i64,
  pub p_medid: i64,
}
