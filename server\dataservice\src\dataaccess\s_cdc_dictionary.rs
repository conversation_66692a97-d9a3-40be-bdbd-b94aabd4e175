use crate::entities::{prelude::*, s_cdc_dictionary};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter};

impl SCdcDictionary {
  pub async fn query(tid: i64, pid: &String, db: &DatabaseConnection) -> Result<Option<SCdcDictionary>> {
    if tid <= 0 && pid.is_empty() {
      return Err(anyhow!("tid and pid empty"));
    }
    let conditons = Condition::all()
      .add(s_cdc_dictionary::Column::Cdctypenum.eq(tid))
      .add(s_cdc_dictionary::Column::Cdcdictnum.eq(pid));
    let ret = SCdcDictionaryEntity::find().filter(conditons).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(tids: &Vec<i64>, db: &DatabaseConnection) -> Result<Vec<SCdcDictionary>> {
    let mut condition = Condition::all();
    if tids.len() > 0 {
      condition = condition.add(s_cdc_dictionary::Column::Cdctypenum.is_in(tids.to_owned()));
    }

    let ret = SCdcDictionaryEntity::find().filter(condition).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
