use crate::entities::{prelude::*, ss_dictionary};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl SsDictionary {
  pub async fn query(tid: i64, pid: i64, db: &DatabaseConnection) -> Result<Option<SsDictionary>> {
    // let ret = NxJobinfoEntity::find_by_id(id).one(db).await;
    if tid <= 0 && pid <= 0 {
      return Err(anyhow!("tid and pid empty"));
    }
    let conditons = Condition::all().add(ss_dictionary::Column::SsTypeid.eq(tid)).add(ss_dictionary::Column::SsPid.eq(pid));
    let ret = SsDictionaryEntity::find().filter(conditons).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(tid: i32, pid: i32, db: &DatabaseConnection) -> Result<Vec<SsDictionary>> {
    let mut condition = Condition::all();
    if tid > 0 {
      condition = condition.add(ss_dictionary::Column::SsTypeid.eq(tid));
    }
    if pid > 0 {
      condition = condition.add(ss_dictionary::Column::SsPid.eq(pid));
    }
    let ret = SsDictionaryEntity::find().filter(condition).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
  pub async fn save(info: &SsDictionary, db: &DatabaseConnection) -> Result<i64> {
    let ret = ss_dictionary::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }
}
