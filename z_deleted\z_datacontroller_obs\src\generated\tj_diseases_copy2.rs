//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_diseases_copy2")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_discode: String,
    pub tj_disname: String,
    pub tj_disnum: String,
    pub tj_deptid: i32,
    pub tj_type: String,
    pub tj_content: String,
    pub tj_occudiseaseflag: i32,
    pub tj_startage: i32,
    pub tj_endage: i32,
    pub tj_sex: i32,
    pub tj_career: String,
    pub tj_marriage: i32,
    pub tj_pyjm: String,
    pub tj_zdym: String,
    pub tj_typeid: i32,
    pub tj_opinion: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
