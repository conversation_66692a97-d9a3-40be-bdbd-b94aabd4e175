// use crate::app;
// use anyhow::{anyhow, Result};
// use datacontroller::entities::{prelude::TjAudiogramdetail, tj_checkiteminfo::TjCheckiteminfo, tj_iteminfo::TjIteminfo};
use md5::{Digest, Md5};
// use odipservice::{dataservice::itemservice::ItemService, typedefine::*};

// pub const HEALTHYEVENTID: &str = "A57";
// pub const CORPYEVENTID: &str = "E10";
// pub const FENCHEN: &str = "11";

pub fn get_body_wrapper_nb(data: &String) -> String {
    let bodywrap = format!(
        r#"<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.domain.jkjczx.ohms.zjcdjk.cn/"><soapenv:Header/><soapenv:Body><ser:putData><!--Optional:--><arg0><![CDATA[{}]]></arg0></ser:putData></soapenv:Body></soapenv:Envelope>"#,
        data
    );
    bodywrap
}

//headSign=MD5（eventId＋requestTime+userId+password）；
pub fn get_header_sign(eventid: &String, requesttime: &String, userid: &String, password: &String) -> String {
    let head_sign_str = format!("{}{}{}{}", eventid, requesttime, userid, password);
    // create a Md5 hasher instance
    let mut hasher = Md5::new();
    // process input message
    hasher.update(head_sign_str.as_bytes());
    // acquire hash digest in the form of GenericArray,
    // which in this case is equivalent to [u8; 16]
    let result = hasher.finalize();

    let ret = format!("{:x}", result);
    ret.to_lowercase()
}
