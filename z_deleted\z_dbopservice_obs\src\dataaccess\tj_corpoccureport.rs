use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjCorpoccureport {
  pub id: i64,
  pub tj_corpid: String,
  pub tj_corpname: String,
  pub tj_wtcorpname: String,
  pub tj_starttime: i64,
  pub tj_endtime: i64,
  pub tj_testyear: i32,
  pub tj_testtype: i32,
  pub tj_typename: String,
  pub tj_poisions: String,
  pub tj_testdate: String,
  pub tj_testaddress: String,
  pub tj_peoplenum: i32,
  pub tj_apeoplenum: i32,
  pub tj_testitems: String,
  pub tj_evalaw: String,
  pub tj_testlaw: String,
  pub tj_result: String,
  pub tj_createdate: i64,
  pub tj_moddate: i64,
  pub tj_creator: String,
  pub tj_modifier: String,
  pub tj_reportnum: String,
  pub tj_status: i32,
  pub tj_peid: i32,
  pub tj_pages: i32,
  pub tj_isrecheck: i32,
  pub tj_orptid: i32,
  pub tj_reportnumint: String,
  pub tj_pyjm: String,
  pub tj_reporttype: i32,
  pub tj_syncflag: i32,
  pub tj_memo: String,
}
crud!(TjCorpoccureport {}, "tj_corpoccureport");
rbatis::impl_select!(TjCorpoccureport{query(id:&i64) ->Option => "`where id = #{id} limit 1 `"});
rbatis::impl_select!(TjCorpoccureport{query_by_rptnum(rptnum:&str) ->Option => "`where tj_reportnumint = #{rptnum} limit 1 `"});
// rbatis::impl_select!(TjCorpoccureport{query_many(testis:&[&str]) => "`where tj_testid in ${testids.sql()} `"});
rbatis::impl_delete!(TjCorpoccureport{delete(ids:&[i64]) => "`where id in ${ids.sql()} `"});
rbatis::impl_select!(TjCorpoccureport{query_many(dtstart:i64, dtend:i64, reportids:&[i64],corpid:i64, testtype:i32, reportnum:&str, testid:&str, testids:&[&str]) => 
  "`where id > 0 `
  if dtstart > 0:
    ` and tj_createdate >= #{dtstart} `
  if dtend > 0:
    ` and tj_createdate <= #{dtend} `
  if !reportids.is_empty():
    ` and id in ${reportids.sql()} `
  if corpid > 0 && corpid != 2:
    ` and tj_corpid = #{corpid} `
  if corpid > 0 && corpid == 2:
    ` and tj_corpid > 2 `
  if testtype > 0:  
    ` and tj_testtype = #{testtype} `
  if reportnum != '':
    ` and tj_reportnumint like #{'%'+reportnum+'%'} `   
  if !testids.is_empty():
    ` and id in (select tj_report_id from tj_corpoccureport_info where tj_test_id in ${testids.sql()}) `
  if testid != '':  
    ` and id in (select tj_report_id from tj_corpoccureport_info where tj_test_id = #{testid}) `
  "});

impl TjCorpoccureport {
  pub async fn save(rb: &mut rbatis::RBatis, info: &TjCorpoccureport) -> Result<i64> {
    // let mut rb = db.get_connection_clone();
    let retid; // = 0 as i64;
    if info.id > 0 {
      let ret = TjCorpoccureport::update_by_column(rb, &info, "id").await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = info.id; //ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    } else {
      let ret = TjCorpoccureport::insert(rb, &info).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
      }
      retid = ret.unwrap().last_insert_id.as_i64().unwrap_or_default();
    }
    Ok(retid)
  }
  // #[sql("update tj_corpoccureport set tj_status = -1 where id = ? ")]
  // pub async fn delete(rb: &mut rbatis::RBatis, id: &i64) -> rbatis::Result<i64> {
  //   impled!()
  // }
}
