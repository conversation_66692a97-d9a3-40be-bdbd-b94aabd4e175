//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_diseaseinfo")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_testid: String,
    pub tj_disid: i32,
    pub tj_diseasenum: String,
    pub tj_diseasename: String,
    pub tj_suggestion: String,
    pub tj_deptid: String,
    pub tj_isdisease: i32,
    pub tj_isoccu: i32,
    pub tj_typeid: i32,
    pub tj_opinion: String,
    pub tj_showorder: i32,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::tj_diseases::Entity",
        from = "Column::TjDisid",
        to = "super::tj_diseases::Column::Id",
        on_update = "NoAction",
        on_delete = "NoAction"
    )]
    TjDiseases,
}

impl Related<super::tj_diseases::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TjDiseases.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
