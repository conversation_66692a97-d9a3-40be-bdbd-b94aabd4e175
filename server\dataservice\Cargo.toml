[package]
name = "dataservice"
version = "0.1.0"
edition = "2021"
resolver = "2"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { path = "../utility" }

anyhow = { workspace = true }     #"1"
serde = { workspace = true }      #{ version = "1.0", features = ["derive"] }
serde_json = { workspace = true } #{ version = "1" }
tracing = { workspace = true }    #"0.1"
log = { workspace = true }        #"0.4"
chrono = { workspace = true }     #"0.4"

# [target.'cfg(target_os = "linux")'.dependencies]
# [target.'cfg(not(target_os = "windows"))'.dependencies]
# sea-orm = { version = "1.1.0", features = [
#     "default",
#     "sqlx-mysql",
#     # "runtime-tokio-rustls",
#     "runtime-tokio",
# ] }

# [target.'cfg(target_os = "windows")'.dependencies]
sea-orm = { version = "1.1.0", features = [
    "default",
    "sqlx-mysql",
    # "runtime-tokio-native-tls",
    "runtime-tokio",
] }

# sqlx = {version = "0.6",features = ["mysql","runtime-tokio-rustls"]}
