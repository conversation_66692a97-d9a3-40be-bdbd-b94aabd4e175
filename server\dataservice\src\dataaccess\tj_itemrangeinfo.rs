use crate::entities::{prelude::*, tj_itemrangeinfo};
use anyhow::{anyhow, Result};
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, Condition, DatabaseConnection, EntityTrait, QueryFilter, Set};
use serde_json::json;

impl TjItemrangeinfo {
  pub async fn query(id: i64, db: &DatabaseConnection) -> Result<Option<TjItemrangeinfo>> {
    if id <= 0 {
      return Err(anyhow!("id is <0, not allowed"));
    }
    let ret = TjItemrangeinfoEntity::find().filter(tj_itemrangeinfo::Column::Id.eq(id)).one(db).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
  }

  pub async fn query_many(age: i32, sex: i32, itemids: &Vec<String>, db: &DatabaseConnection) -> Result<Vec<TjItemrangeinfo>> {
    let mut conditions = Condition::all();
    if itemids.len() > 0 {
      conditions = conditions.add(tj_itemrangeinfo::Column::TjItemid.is_in(itemids.to_owned()));
    }
    if sex > 0 {
      conditions = conditions.add(tj_itemrangeinfo::Column::TjSex.eq(sex));
    }
    if age > 0 {
      conditions = conditions.add(tj_itemrangeinfo::Column::TjStartage.lte(age)).add(tj_itemrangeinfo::Column::TjEndage.gte(age));
    }
    let ret = TjItemrangeinfoEntity::find().filter(conditions).all(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }

  pub async fn save(info: &TjItemrangeinfo, db: &DatabaseConnection) -> Result<i64> {
    let ret = tj_itemrangeinfo::ActiveModel::from_json(json!(info));
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut newval = ret.unwrap();
    if info.id <= 0 {
      newval.id = NotSet;
    } else {
      newval.id = Set(info.id);
    }
    let ret = newval.save(db).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let val_id: Option<i64> = ret.unwrap().id.into_value().unwrap().unwrap();
    if val_id.is_none() {
      return Err(anyhow!("insert result error"));
    }
    // let
    Ok(val_id.unwrap())
  }

  pub async fn delete(ids: &Vec<i64>, db: &DatabaseConnection) -> Result<u64> {
    let ret = TjItemrangeinfoEntity::delete_many()
      .filter(Condition::all().add(tj_itemrangeinfo::Column::Id.is_in(ids.to_owned())))
      .exec(db)
      .await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }

    Ok(ret.unwrap().rows_affected)
  }
}
