use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub struct SsInfoconfig {
  pub id: i32,
  pub ss_code: String,
  pub ss_name: String,
  pub ss_pyjm: String,
  pub ss_type: i32,
  pub ss_parent: String,
  pub ss_monitor: String,
}
crud!(SsInfoconfig {}, "ss_infoconfig");
rbatis::impl_select!(SsInfoconfig{query_many(typeid:i32) => 
  "`where id > 0 `
  if typeid > 0:
    ` and ss_type = #{typeid} `"});
