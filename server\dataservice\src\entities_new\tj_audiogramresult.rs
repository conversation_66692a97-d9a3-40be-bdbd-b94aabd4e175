//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_audiogramresult")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_testid: String,
  #[sea_orm(column_type = "Float")]
  pub tj_avgsgp: f32,
  #[sea_orm(column_type = "Float")]
  pub tj_avgyyp: f32,
  #[sea_orm(column_type = "Float")]
  pub tj_avgzyp: f32,
  #[sea_orm(column_type = "Float")]
  pub tj_avgsyp: f32,
  #[sea_orm(column_type = "Float")]
  pub tj_avgygp: f32,
  #[sea_orm(column_type = "Float")]
  pub tj_avgzgp: f32,
  #[sea_orm(column_type = "Float")]
  pub tj_avgyty: f32,
  #[sea_orm(column_type = "Float")]
  pub tj_avgzty: f32,
  #[sea_orm(column_type = "Float")]
  pub tj_avgsty: f32,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
