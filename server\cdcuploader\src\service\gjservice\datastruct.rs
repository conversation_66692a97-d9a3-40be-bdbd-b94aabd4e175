use serde::*;
use yaserde::*;
// -------------------通用结构体-------------------
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "Header")]
pub struct Header {
  #[yaserde(rename = "DocumentId")]
  pub document_id: String, // <!-- -文档标识ID- 机构编码-业务类别代码-当前时间（yyyyMMddHHmmssSSS）-5位随机码-->
  #[yaserde(rename = "OperateType")]
  pub operate_type: String, // <!-- 数据操作类型 Add,Update,Delete -->
  #[yaserde(rename = "BusinessActivityIdentification")]
  pub business_activity_identification: String, // <!-- 业务活动标识 职业病系统 -->
  #[yaserde(rename = "ReportOrgCode")]
  pub report_org_code: String, // <!-- 上报机构代码 -->
  #[yaserde(rename = "License")]
  pub license: String, // <!-- 上报机构授权码 -->
  #[yaserde(rename = "ReportZoneCode")]
  pub report_zone_code: String, //  <!--上报地区代码-->
}

//DataExchange
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "DataExchange")]
pub struct CorpDataExchange {
  #[yaserde(rename = "Header")]
  pub header: Header, //头部
  #[yaserde(rename = "EventBody")]
  pub event_body: CorpEventBody, //主体
}

//DataExchange
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "DataExchange")]
pub struct ExamDataExchange {
  #[yaserde(rename = "Header")]
  pub header: Header, //头部
  #[yaserde(rename = "EventBody")]
  pub event_body: ExamEventBody, //主体
}

#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "AUDIT_INFO")]
pub struct AuditInfo {
  #[yaserde(rename = "AUDITSTATUS")]
  pub auditstatus: String, // <!-- 审核状态代码--> <!-- 体检信息： 审核状态代码（当上传方式为通过系统xml上传时，审核状态不能为07或09；当上传方式为通过国家数据交换平台上传时，审核状态只能为07）-->
  #[yaserde(rename = "AUDITINFO")]
  pub auditinfo: String, // <!-- 审核意见 -->
  #[yaserde(rename = "AUDITDATE")]
  pub auditdate: String, // <!-- 审核时间 -->
  #[yaserde(rename = "AUDITOR_NAME")]
  pub auditor_name: String, // <!-- 审核人姓名 -->
  #[yaserde(rename = "AUDIT_ORG")]
  pub audit_org: String, // <!-- 审核机构 -->
}

// -----------------------用人单位信息------------------------------------------------
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "ENTERPRISE_INFO")]
pub struct EnterpriseInfo {
  #[yaserde(rename = "ENTERPRISE_NAME")]
  pub enterprise_name: String, //用人单位名称
  #[yaserde(rename = "CREDIT_CODE")]
  pub credit_code: String, //统一社会信用代码(18位)
  #[yaserde(rename = "ADDRESS_CODE")]
  pub address_code: String, //-所属地区代码
  #[yaserde(rename = "ECONOMIC_TYPE_CODE")]
  pub economic_type_code: String, //企业类型代码
  #[yaserde(rename = "INDUSTRY_CATEGORY_CODE")]
  pub industry_category_code: String, //-行业类别代码
  #[yaserde(rename = "BUSINESS_SCALE_CODE")]
  pub business_scale_code: String, //企业规模代码
  #[yaserde(rename = "ADDRESS_DETAIL")]
  pub address_detail: String, //-用人单位详细地址
  #[yaserde(rename = "ADDRESS_ZIP_CODE")]
  pub address_zip_code: String, //用人单位地址邮政代码-
  #[yaserde(rename = "ENTERPRISE_CONTACT")]
  pub enterprise_contact: String, //用人单位联系人名称
  #[yaserde(rename = "CONTACT_TELPHONE")]
  pub contact_telphone: String, //用人单位联系人电话
  #[yaserde(rename = "ISSUBSIDIARY")]
  pub issubsidiary: String, //是否为子公司，0表示否，1表示是
  #[yaserde(rename = "TWOLEVELCODE")]
  pub twolevelcode: String, //-子公司二级代码（当为子公司需填写）
  #[yaserde(rename = "AREA_CODE")]
  pub area_code: String, //创建地区代码
  #[yaserde(rename = "ORG_CODE")]
  pub org_code: String, //创建机构代码
  #[yaserde(rename = "WRITE_UNIT")]
  pub write_unit: String, // <!--填表单位名称-->
  #[yaserde(rename = "WRITE_PERSON")]
  pub write_person: String, // <!--填表人姓名-->
  #[yaserde(rename = "WRITE_PERSON_TEL")]
  pub write_person_tel: String, // <!--填表人联系电话-->
  #[yaserde(rename = "WRITE_DATE")]
  pub write_date: String, //  <!--填表日期-->
  #[yaserde(rename = "REPORT_UNIT")]
  pub report_unit: String, // <!--报告单位名称-->
  #[yaserde(rename = "REPORT_PERSON")]
  pub report_person: String, // <!--报告人姓名-->
  #[yaserde(rename = "REPORT_PERSON_TEL")]
  pub report_person_tel: String, // <!--报告人电话-->
  #[yaserde(rename = "REPORT_DATE")]
  pub report_date: String, //  <!--报告日期-->
}

//ENTERPRISE_INFO_LIST
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "ENTERPRISE_INFO_LIST")]
pub struct EnterpriseInfoList {
  #[yaserde(rename = "ENTERPRISE_INFO")]
  pub enterpriseinfo: Vec<EnterpriseInfo>,
}

//ENTERPRISE_INFO_LIST
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "EventBody")]
pub struct CorpEventBody {
  #[yaserde(rename = "ENTERPRISE_INFO_LIST")]
  pub enterprises: EnterpriseInfoList,
}

// ----------------------------- 健康档案信息 --------------------------------
//ENTERPRISE_INFO_LIST
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "EventBody")]
pub struct ExamEventBody {
  #[yaserde(rename = "HEALTH_EXAM_RECORD_LIST")]
  pub health_exam_record_list: Vec<HealthyExamRecord>,
}

//-用人单位信息
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "ENTERPRISE_INFO")]
pub struct MedEnterpriseInfo {
  #[yaserde(rename = "ENTERPRISE_NAME")]
  pub enterprise_name: String, //用人单位名称
  #[yaserde(rename = "CREDIT_CODE")]
  pub credit_code: String, //统一社会信用代码(18位)
  #[yaserde(rename = "ECONOMIC_TYPE_CODE")]
  pub economic_type_code: String, //企业类型代码
  #[yaserde(rename = "INDUSTRY_CATEGORY_CODE")]
  pub industry_category_code: String, //-行业类别代码
  #[yaserde(rename = "BUSINESS_SCALE_CODE")]
  pub business_scale_code: String, //企业规模代码
  #[yaserde(rename = "ADDRESS_CODE")]
  pub address_code: String, //-所属地区代码
  #[yaserde(rename = "ADDRESS_DETAIL")]
  pub address_detail: String, //-用人单位详细地址
  #[yaserde(rename = "ADDRESS_ZIP_CODE")]
  pub address_zip_code: String, //用人单位地址邮政代码-
  #[yaserde(rename = "ENTERPRISE_CONTACT")]
  pub enterprise_contact: String, //用人单位联系人名称
  #[yaserde(rename = "CONTACT_TELPHONE")]
  pub contact_telphone: String, //用人单位联系人电话
  #[yaserde(rename = "ALL_NAME")]
  pub all_name: String, //用人单位所在区全名称（如：北京市市辖区海淀区）
}
//-用工单位信息
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "ENTERPRISE_INFO_EMPLOYER")]
pub struct MedEnterpriseInfoEmployer {
  #[yaserde(rename = "ENTERPRISE_NAME_EMPLOYER")]
  pub enterprise_name_employer: String, //用人单位名称
  #[yaserde(rename = "CREDIT_CODE_EMPLOYER")]
  pub credit_code_employer: String, //统一社会信用代码(18位)
  #[yaserde(rename = "ECONOMIC_TYPE_CODE_EMPLOYER")]
  pub economic_type_code_employer: String, //企业类型代码
  #[yaserde(rename = "INDUSTRY_CATEGORY_CODE_EMPLOYER")]
  pub industry_category_code_employer: String, //-行业类别代码
  #[yaserde(rename = "BUSINESS_SCALE_CODE_EMPLOYER")]
  pub business_scale_code_employer: String, //企业规模代码
  #[yaserde(rename = "ADDRESS_CODE_EMPLOYER")]
  pub address_code_employer: String, //-所属地区代码
  #[yaserde(rename = "ALL_NAME_EMPLOYER")]
  pub all_name_employer: String, //-用人单位详细地址
}

//-劳动者信息
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "WORKER_INFO")]
pub struct WorkerInfo {
  #[yaserde(rename = "WORKER_NAME")]
  pub worker_name: String, //姓名
  #[yaserde(rename = "ID_CARD_TYPE_CODE")]
  pub id_card_type_code: String, //身份证件类型代码
  #[yaserde(rename = "ID_CARD")]
  pub id_card: String, //身份证件号码
  #[yaserde(rename = "GENDER_CODE")]
  pub gender_code: String, //- 性别代码
  #[yaserde(rename = "BIRTH_DATE")]
  pub birth_date: String, // 出生日期
  #[yaserde(rename = "WORKER_TELPHONE")]
  pub worker_telphone: String, //-联系电话
}

//--个人生活史
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "WORKER_INFO")]
pub struct PersonalHistory {
  #[yaserde(rename = "SMOKING_STATUS")]
  pub smoking_status: String, //目前吸烟情况
  #[yaserde(rename = "PERSONAL_HISTORY_YEAR")]
  pub personal_history_year: String, //吸烟史-年
  #[yaserde(rename = "PERSONAL_HISTORY_MONTH")]
  pub personal_history_month: String, //-吸烟史-月
  #[yaserde(rename = "DAILY_SMOKING_VOLUME")]
  pub daily_smoking_volume: String, //- 平均每天吸烟量
}

//EXAM_CONCLUSION
//每个危害因素对应一个结论，各类粉尘只需填写一个大类粉尘的结论即可
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "EXAM_CONCLUSION")]
pub struct ExamConclusion {
  #[yaserde(rename = "ITAM_CODE")]
  pub itam_code: String, //职业病危害因素代码
  #[yaserde(rename = "ITAM_NAME")]
  pub itam_name: String, //危害因素名称
  #[yaserde(rename = "EXAM_CONCLUSION_CODE")]
  pub exam_conclusion_code: String, //体检结论代码
  #[yaserde(rename = "YSZYB_CODE")]
  pub yszyb_code: String, //- -疑似职业病代码（见字典职业病类型，当体检结论位疑似职业病时，需填写该字段）
  #[yaserde(rename = "ENTERPRISE_NAME")]
  pub enterprise_name: String, //-接触相应职业病危害因素的用人单位名称（当体检类型是上岗前，体检结论是疑似职业病时，需填写该字段）
  #[yaserde(rename = "ZYJJZ_NAME")]
  pub zyjjz_name: String, //职业禁忌证名称（当体检结论是职业禁忌证时，需填写该字段）
  #[yaserde(rename = "QTJB_NAME")]
  pub qtjb_name: String, //其他疾病或异常描述（当体检结论是其他疾病或异常时，需填写该字段）
  #[yaserde(rename = "HARM_START_DATE")]
  pub harm_start_date: String, //开始接害日期（当体检类型是上岗前时非必填；当体检的危害因素是混合接触时，开始接害日期和接害工龄要分别按危害因素填写，各类粉尘只需填写一次）
  #[yaserde(rename = "HARM_AGE_YEAR")]
  pub harm_age_year: String, //实际接害工龄-年（同上）
  #[yaserde(rename = "HARM_AGE_MONTH")]
  pub harm_age_month: String, //实际接害工龄-月（同上）
}
//
//-检查项目信息
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "EXAM_ITEM_RESULT")]
pub struct ExamItemResult {
  #[yaserde(rename = "EXAM_ITEM_PNAME")]
  pub exam_item_pname: String, //检查项目父级名称（检查指标名称一级节点名称）
  #[yaserde(rename = "EXAM_ITEM_NAME")]
  pub exam_item_name: String, //-检查项目名称
  #[yaserde(rename = "EXAM_ITEM_CODE")]
  pub exam_item_code: String, //检查项目代码
  #[yaserde(rename = "EXAM_RESULT_TYPE")]
  pub exam_result_type: String, //-检查结果类型代码
  #[yaserde(rename = "DATA_VERSION")]
  pub data_version: String, // —符号代码（小于或者等于）
  #[yaserde(rename = "EXAM_RESULT")]
  pub exam_result: String, //-检查结果
  #[yaserde(rename = "EXAM_ITEM_UNIT_CODE")]
  pub exam_item_unit_code: String, //-检查项目计量单位及参考值范围（只有检查结果为数字类型的需要填写，没有时可不填写-）
  #[yaserde(rename = "REFERENCE_RANGE_MIN")]
  pub reference_range_min: String, //-参考值范围小值（只有检查结果为数字类型的需要填写，没有时可不填写-）
  #[yaserde(rename = "REFERENCE_RANGE_MAX")]
  pub reference_range_max: String, //--参考值范围大值（只有检查结果为数字类型的需要填写，没有时可不填写-）
  #[yaserde(rename = "ABNORMAL")]
  pub abnormal: String, //-是否异常（0正常、1异常、3未检查；当检查项目是胸片时，见胸片检查结果代码表）
}

//HEALTH_EXAM_RECORD
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, Serialize, Deserialize, PartialEq)]
#[yaserde(rename = "HEALTH_EXAM_RECORD")]
pub struct HealthyExamRecord {
  #[yaserde(rename = "ENTERPRISE_INFO")]
  pub enterpriseinfo: MedEnterpriseInfo, //用人单位信息
  #[yaserde(rename = "ENTERPRISE_INFO_EMPLOYER")]
  pub enterpriseemployerinfo: MedEnterpriseInfoEmployer, //用工单位信息
  #[yaserde(rename = "WORKER_INFO")]
  pub workerinfo: WorkerInfo, //劳动者信息
  #[yaserde(rename = "PERSONAL_HISTORY")]
  pub personalhistory: PersonalHistory, //- 个人生活史
  #[yaserde(rename = "JC_TYPE")]
  pub jc_type: String, //-监测类型代码
  #[yaserde(rename = "EXAM_TYPE_CODE")]
  pub exam_type_code: String, //-体检类型代码
  #[yaserde(rename = "EXAM_DATE")]
  pub exam_date: String, //-体检日期-
  #[yaserde(rename = "CONTACT_FACTOR_CODE")]
  pub contact_factor_code: String, //--接触的职业病危害因素代码 >1001,3008
  #[yaserde(rename = "CONTACT_FACTOR_OTHER")]
  pub contact_factor_other: String, //--接触的其他职业病危害因素
  #[yaserde(rename = "FACTOR_CODE")]
  pub factor_code: String, //-体检危害因素代码
  #[yaserde(rename = "FACTOR_OTHER")]
  pub factor_other: String, //-其他危害因素具体名称（如果危害因素选择其他，需要在这里填写具体的危害因素名称）
  #[yaserde(rename = "WORK_TYPE_CODE")]
  pub work_type_code: String, //-工种代码
  #[yaserde(rename = "OTHER_WORK_TYPE")]
  pub other_work_type: String, //-其他工种名称（工种选择其他时，需要在这里保存具体工种的名字）
  #[yaserde(rename = "IS_REVIEW")]
  pub is_review: String, //-是否复查（0否、1是）
  #[yaserde(rename = "PROTECTIVE_EQUIPMENT_CODE")]
  pub protective_equipment_code: String, //--防护用品佩戴情况代码
  #[yaserde(rename = "AREA_CODE")]
  pub area_code: String, //--创建地区代码
  #[yaserde(rename = "ORG_CODE")]
  pub org_code: String, //-创建机构代码
  #[yaserde(rename = "EXAM_CONCLUSION_LIST")]
  pub exam_conclusion: Vec<ExamConclusion>, //-体检结论信息
  #[yaserde(rename = "EXAM_ITEM_RESULT_LIST")]
  pub exam_item_result_list: Vec<ExamItemResult>, //-检查项目信息
  #[yaserde(rename = "WRITE_PERSON")]
  pub write_person: String, //-填表人姓名-
  #[yaserde(rename = "WRITE_PERSON_TELPHONE")]
  pub write_person_telphone: String, //-填表人电话
  #[yaserde(rename = "WRITE_DATE")]
  pub write_date: String, //-报告出具日期
  #[yaserde(rename = "REPORT_ORGAN_CREDIT_CODE")]
  pub report_organ_credit_code: String, //-—检查单位名称-
  #[yaserde(rename = "REPORT_PERSON")]
  pub report_person: String, //--报告人姓名
  #[yaserde(rename = "REPORT_PERSON_TEL")]
  pub report_person_tel: String, //--报告人联系电话
  #[yaserde(rename = "REPORT_DATE")]
  pub report_date: String, //--报告日期
  #[yaserde(rename = "REPORT_UNIT")]
  pub report_unit: String, //--报告单位名称
  #[yaserde(rename = "REMARK")]
  pub remark: String, //-备注
  #[yaserde(rename = "FACTOR_CODE")]
  pub audit_info: AuditInfo, //- 审核信息  <!-- 审核状态代码（当上传方式为通过系统xml上传时，审核状态不能为07或09；当上传方式为通过国家数据交换平台上传时，审核状态只能为07）-->
}
