// use serde::{Deserialize, Serialize};
use anyhow::{anyhow, Result};
use quick_xml::events::Event;
use quick_xml::Reader;
use yaserde::*;
// use log::*;
use tracing::*;

use crate::app;
use crate::config::settings::Settings;
// use yaserde::{YaDeserialize, YaSerialize};
pub struct ZjResponse {}
impl ZjResponse {
  pub fn decode_response(response: &str, setting: &Settings) -> Result<String> {
    // info!("response is:{}", &response);
    let mut reader = Reader::from_str(response);
    reader.config_mut().trim_text(true);

    let mut txt = Vec::new();
    let mut buf = Vec::new();
    // let mut txt_str = Cow::from("");
    loop {
      // NOTE: this is the generic case when we don't know about the input BufRead.
      // when the input is a &str or a &[u8], we don't actually need to use another
      // buffer, we could directly call `reader.read_event_unbuffered()`
      match reader.read_event_into(&mut buf) {
        // match reader.read_event() {
        // Ok(Event::Text(e)) => txt.push(e.unescape_and_decode(&reader).unwrap()),
        Ok(Event::Text(e)) => txt.push(e.unescape().unwrap().into_owned()),
        // Ok(Event::Start(start)) => {
        //   // read_text_into for buffered readers not implemented
        //   let end = start.to_end().into_owned();
        //   let txt_str = reader.read_text(end.name()).expect("Cannot decode text value");
        // }
        Ok(Event::Eof) => break, // exits the loop when reaching end of file
        Err(e) => error!("Error at position {}: {:?}", reader.buffer_position(), e),
        _ => (), // There are several other `Event`s we do not consider here
      }
    }
    // if we don't keep a borrow elsewhere, we can clear the buffer to keep memory usage low
    buf.clear();
    info!("Text result:{:?}", &txt);
    let txt_str = txt.join("");
    // info!("返回的字符串结果:{:?}", txt_str);
    // let responseinfo;
    if setting.system.platform == app::Platform::Zhejiang.to_string() {
      let de_ret: Result<ResponseData, String> = yaserde::de::from_str(&txt_str);
      //   info!("Result:{:#?}", &de_ret);
      if de_ret.as_ref().is_err() {
        return Err(anyhow!("{}", de_ret.as_ref().err().unwrap().to_string()));
      }
      let responseinfo = de_ret.unwrap();
      if responseinfo.returncode.eq_ignore_ascii_case(RESPONSE_OK) {
        return Ok("".to_string());
      } else {
        let error_msg: String;
        if responseinfo.errorlist.is_some() {
          error_msg = responseinfo
            .errorlist
            .unwrap_or_default()
            .errors
            .iter()
            .map(|x| x.message.clone())
            .collect::<Vec<String>>()
            .join("");
        } else {
          error_msg = "".to_string();
        }
        if error_msg.is_empty() {
          return Err(anyhow!("{}", responseinfo.message.unwrap_or_default()));
        } else {
          return Err(anyhow!("{} => {}", responseinfo.message.unwrap_or_default(), error_msg));
        }
      }
    } else if setting.system.platform == app::Platform::Ningbo.to_string() {
      let de_ret: Result<ResponseDataNb, String> = yaserde::de::from_str(&txt_str);
      // info!("Result:{:#?}", &de_ret);
      if de_ret.as_ref().is_err() {
        return Err(anyhow!("{}", de_ret.as_ref().err().unwrap().to_string()));
      }
      let responseinfo = de_ret.unwrap();
      if responseinfo.returncode.eq_ignore_ascii_case(RESPONSE_OK) {
        return Ok("".to_string());
      } else {
        let error_msg = responseinfo
          .errorlist
          .unwrap_or_default()
          .errors
          .errordatas
          .iter()
          .filter(|&f| !f.retcode.eq_ignore_ascii_case("0"))
          .map(|x| x.message.clone())
          .collect::<Vec<String>>()
          .join("");
        return Err(anyhow!("{} => {}", responseinfo.message.unwrap_or_default(), error_msg));
      }
    } else {
      let de_ret: Result<ResponseData, String> = yaserde::de::from_str(txt_str.as_str());
      // info!("Result:{:#?}", &de_ret);
      if de_ret.as_ref().is_err() {
        return Err(anyhow!("{}", de_ret.as_ref().err().unwrap().to_string()));
      }
      let responseinfo = de_ret.unwrap();
      if responseinfo.returncode.eq_ignore_ascii_case(RESPONSE_OK) {
        return Ok("".to_string());
      } else {
        let error_msg: String;
        if responseinfo.errorlist.is_some() {
          error_msg = responseinfo
            .errorlist
            .unwrap_or_default()
            .errors
            .iter()
            .map(|x| x.message.clone())
            .collect::<Vec<String>>()
            .join("");
        } else {
          error_msg = "".to_string();
        }
        // return Err(anyhow!("{} => {}", responseinfo.message, error_msg));
        if error_msg.is_empty() {
          return Err(anyhow!("{}", responseinfo.message.unwrap_or_default()));
        } else {
          return Err(anyhow!("{} => {}", responseinfo.message.unwrap_or_default(), error_msg));
        }
      }
    }

    // Ok("".to_string())
  }

  pub fn decode_reportcard_response(response: &str, setting: &Settings) -> Result<Option<Vec<Refusedreportcard>>> {
    // info!("response is:{}", &response);
    let mut reader = Reader::from_str(response);
    reader.config_mut().trim_text(true);

    let mut txt = Vec::new();
    let mut buf = Vec::new();
    // let mut txt_str = Cow::from("");
    loop {
      // NOTE: this is the generic case when we don't know about the input BufRead.
      // when the input is a &str or a &[u8], we don't actually need to use another
      // buffer, we could directly call `reader.read_event_unbuffered()`
      match reader.read_event_into(&mut buf) {
        // match reader.read_event() {
        // Ok(Event::Text(e)) => txt.push(e.unescape_and_decode(&reader).unwrap()),
        Ok(Event::Text(e)) => txt.push(e.unescape().unwrap().into_owned()),
        // Ok(Event::Start(start)) => {
        //   // read_text_into for buffered readers not implemented
        //   let end = start.to_end().into_owned();
        //   let txt_str = reader.read_text(end.name()).expect("Cannot decode text value");
        // }
        Ok(Event::Eof) => break, // exits the loop when reaching end of file
        Err(e) => error!("Error at position {}: {:?}", reader.buffer_position(), e),
        _ => (), // There are several other `Event`s we do not consider here
      }
    }
    // if we don't keep a borrow elsewhere, we can clear the buffer to keep memory usage low
    buf.clear();
    info!("Text result:{:?}", &txt);
    let txt_str = txt.join("");
    // info!("返回的字符串结果:{:?}", txt_str);
    // let responseinfo;
    if setting.system.platform == app::Platform::Zhejiang.to_string() {
      let de_ret: Result<ResponseData, String> = yaserde::de::from_str(&txt_str);
      info!("Result:{:#?}", &de_ret);
      if de_ret.as_ref().is_err() {
        return Err(anyhow!("{}", de_ret.as_ref().err().unwrap().to_string()));
      }
      let responseinfo = de_ret.unwrap();
      if responseinfo.returncode.eq_ignore_ascii_case(RESPONSE_OK) {
        // return Ok("".to_string());
        if responseinfo.reportcardlist.is_some() {
          return Ok(Some(responseinfo.reportcardlist.unwrap().reportcards));
        } else {
          return Ok(None);
        }
      } else {
        if responseinfo.message.is_some() {
          return Err(anyhow!("error message: {}", responseinfo.message.unwrap()));
        } else {
          return Err(anyhow!("return code: {}", responseinfo.returncode));
        }
      }
    } else {
      return Err(anyhow!("platform not supported"));
    }
    // } else if setting.system.platform == app::Platform::Ningbo.to_string() {
    //   let de_ret: Result<ResponseDataNb, String> = yaserde::de::from_str(&txt_str);
    //   // info!("Result:{:#?}", &de_ret);
    //   if de_ret.as_ref().is_err() {
    //     return Err(anyhow!("{}", de_ret.as_ref().err().unwrap().to_string()));
    //   }
    //   let responseinfo = de_ret.unwrap();
    //   if responseinfo.returncode.eq_ignore_ascii_case(RESPONSE_OK) {
    //     return Ok("".to_string());
    //   } else {
    //     let error_msg = responseinfo
    //       .errorlist
    //       .unwrap_or_default()
    //       .errors
    //       .errordatas
    //       .iter()
    //       .filter(|&f| !f.retcode.eq_ignore_ascii_case("0"))
    //       .map(|x| x.message.clone())
    //       .collect::<Vec<String>>()
    //       .join("");
    //     return Err(anyhow!("{} => {}", responseinfo.message.unwrap_or_default(), error_msg));
    //   }
    // } else {
    //   let de_ret: Result<ResponseData, String> = yaserde::de::from_str(txt_str.as_str());
    //   // info!("Result:{:#?}", &de_ret);
    //   if de_ret.as_ref().is_err() {
    //     return Err(anyhow!("{}", de_ret.as_ref().err().unwrap().to_string()));
    //   }
    //   let responseinfo = de_ret.unwrap();
    //   if responseinfo.returncode.eq_ignore_ascii_case(RESPONSE_OK) {
    //     return Ok("".to_string());
    //   } else {
    //     let error_msg: String;
    //     if responseinfo.errorlist.is_some() {
    //       error_msg = responseinfo
    //         .errorlist
    //         .unwrap_or_default()
    //         .errors
    //         .iter()
    //         .map(|x| x.message.clone())
    //         .collect::<Vec<String>>()
    //         .join("");
    //     } else {
    //       error_msg = "".to_string();
    //     }
    //     // return Err(anyhow!("{} => {}", responseinfo.message, error_msg));
    //     if error_msg.is_empty() {
    //       return Err(anyhow!("{}", responseinfo.message.unwrap_or_default()));
    //     } else {
    //       return Err(anyhow!("{} => {}", responseinfo.message.unwrap_or_default(), error_msg));
    //     }
    //   }
    // }

    // Ok("".to_string())
  }
}

pub const RESPONSE_OK: &str = "0";

#[derive(YaDeserialize, Default, Debug, PartialEq)]
#[yaserde(rename = "data")]
pub struct ResponseData {
  #[yaserde(rename = "returnCode")]
  pub returncode: String,
  #[yaserde(rename = "message")]
  pub message: Option<String>,
  #[yaserde(rename = "errorList")]
  pub errorlist: Option<ErrorList>,
  #[yaserde(rename = "reportCardList")]
  pub reportcardlist: Option<ReportCardList>,
}

#[derive(YaDeserialize, Default, Debug, PartialEq)]
// #[yaserde(rename = "errorList")]
pub struct ErrorList {
  #[yaserde(rename = "error")]
  pub errors: Vec<ErrorData>,
}

#[derive(YaDeserialize, Default, Debug, PartialEq)]
// #[yaserde(rename = "error")]
pub struct ErrorData {
  #[yaserde(rename = "code")]
  pub code: String,
  #[yaserde(rename = "orgCode")]
  pub orgcode: String,
  #[yaserde(rename = "message")]
  pub message: String,
}

#[derive(YaDeserialize, Default, Debug, PartialEq)]
// #[yaserde(rename = "errorList")]
pub struct ReportCardList {
  #[yaserde(rename = "reportCard")]
  pub reportcards: Vec<Refusedreportcard>,
}
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, PartialEq)]
// #[yaserde(rename = "reportCard")]
pub struct Refusedreportcard {
  #[yaserde(rename = "reportCode")]
  pub reportcode: String, //`xml:"diseaseName"`       //
  #[yaserde(rename = "code")]
  pub code: String, //`xml:"diagnosisDate"`     //
  #[yaserde(rename = "auditLevel")]
  pub auditlevel: String, //`xml:"diagnosisOrg"`      //
  #[yaserde(rename = "auditorName")]
  pub auditorname: String, //`xml:"treatmentProcess"`  //
  #[yaserde(rename = "auditDate")]
  pub auditdate: String, //`xml:"dieaseOutcomeCode"` //转归编码 对照字典26
  #[yaserde(rename = "auditDesc")] //审核不通过原因
  pub auditdesc: String, //`xml:"dieaseOutcomeCode"` //转归编码 对照字典26
}

/* 宁波平台上报返回的错误信息 */

//ResponseData response data
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, PartialEq)]
#[yaserde(rename = "data")]
pub struct ResponseDataNb {
  #[yaserde(rename = "returnCode")]
  pub returncode: String, //`xml:"returnCode"`
  #[yaserde(rename = "message")]
  pub message: Option<String>, //`xml:"message"`
  #[yaserde(rename = "errorDatas")]
  pub errorlist: Option<RErrorListNb>, //`xml:"-"`
}

//RErrorListNb errorDatas
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, PartialEq)]
// #[yaserde(rename = "DATAS")]
pub struct RErrorListNb {
  #[yaserde(rename = "errorReportCards")]
  pub errors: RErrorsNb, //`xml:"errorReportCards"`
}

//RErrorsNb errorDatas
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, PartialEq)]
// #[yaserde(rename = "DATAS")]
pub struct RErrorsNb {
  #[yaserde(rename = "errorData")]
  pub errordatas: Vec<RErrorDataNb>, //`xml:"errorData"`
}

//RErrorDataNb error
#[derive(Debug, Clone, Default, YaSerialize, YaDeserialize, PartialEq)]
// #[yaserde(rename = "DATAS")]
pub struct RErrorDataNb {
  #[yaserde(rename = "cardId")]
  pub code: String, //`xml:"cardId"`
  #[yaserde(rename = "errorMessage")]
  pub message: String, //`xml:"errorMessage"`
  #[yaserde(rename = "returnCode")]
  pub retcode: String, //`xml:"returnCode"`
}
