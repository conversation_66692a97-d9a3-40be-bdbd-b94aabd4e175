use crate::{
  api::httpresponse::{response_json_error, response_json_ok, response_json_value_error, response_json_value_ok, DataBody, ResponseBody}, auth::auth::Claims, common::constant::{self}
};
use axum::{Extension, Json};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::{
  basic::dictsvc::DictSvc,
  dto::{KeyDto, KeyIntDto},
};
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

pub async fn get_id_by_testtype(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntDto>) -> Json<Value> {
  let mut testtype = dto.key;
  if testtype <= 0 {
    testtype = 1;
  }
  info!("start to generate new id by test type: {}", testtype);
  let ret = DictSvc::get_id_by_testtype(testtype as i32, &db).await;
  if ret.as_ref().is_err() {
    return response_json_error("不能获取数据信息");
    // // return response_json_error(&ret.as_ref().unwrap_err().to_string().as_str());
    // return response_json_value_error(&ret.as_ref().unwrap_err().to_string().as_str(), "");
  }

  let results = ret.unwrap();
  info!("new testid is:{}", &results);
  response_json_ok(results.as_str())
  // response_json_value_ok(total, data)
  // let databody = DataBody::new(results.len() as u64, results);
  // Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn update_sys_dicts(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<SsDictionary>) -> Json<Value> {
  let ret = DictSvc::update_dictionary(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), "");
  }
  response_json_value_ok(1, "")
}

// pub async fn query_sys_dicts(Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
//   let ret = DictSvc::query_sys_dicts(&db).await;
//   if ret.as_ref().is_err() {
//     return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<SsDictionary>::new());
//   }
//   let results = ret.unwrap();
//   let databody = DataBody::new(results.len() as u64, results);
//   Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
// }

pub async fn query_sys_dicts_v2(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyIntDto>) -> Json<Value> {
  info!("start to query sysdiction by dto:{dto:?}");
  let ret = DictSvc::query_sys_dicts_v2(&dto, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<SsDictionary>::new());
  }
  let results = ret.unwrap();
  let databody = DataBody::new(results.len() as u64, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

pub async fn query_sys_identity(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>, Json(dto): Json<KeyDto>) -> Json<Value> {
  let code = dto.key.to_owned();
  if code.is_empty() {
    return response_json_value_error("code为空", SsIdentity { ..Default::default() });
  }
  let ret = DictSvc::query_sys_identity(&code, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), SsIdentity { ..Default::default() });
  }
  let results = ret.unwrap();
  let databody = DataBody::new(1, results);
  Json(serde_json::json!(ResponseBody::new(constant::HttpCode::OK as i32, constant::MESSAGE_OK, databody)))
}

//query_info_configs
pub async fn query_info_configs(_claims: Claims, Extension(db): Extension<Arc<DbConnection>>) -> Json<Value> {
  let ret = DictSvc::query_sys_infoconfigs(0, &db).await;
  if ret.as_ref().is_err() {
    return response_json_value_error(ret.as_ref().unwrap_err().to_string().as_str(), Vec::<SsInfoconfig>::new());
  }
  let results = ret.unwrap();
  response_json_value_ok(results.len() as u64, results)
}
