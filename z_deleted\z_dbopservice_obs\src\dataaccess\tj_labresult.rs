use anyhow::{anyhow, Result};
use rbatis::crud;
use rbatis::rbatis_codegen::IntoSql;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct TjLabresult {
  pub id: i64,
  pub tj_clinicid: String,
  pub tj_testid: String,
  pub tj_patientname: String,
  pub tj_sex: String,
  pub tj_origrec: String,
  pub tj_itemid: String,
  pub tj_analyte: String,
  pub tj_shortname: String,
  pub tj_units: String,
  pub tj_final: String,
  pub tj_rn10: String,
  pub tj_ckfw_l: String,
  pub tj_ckfw_h: String,
  pub tj_ckfw: String,
  pub tj_abnormalflag: i32,
  pub tj_displowhigh: String,
  pub tj_senddate: i64,
  pub tj_ordno: String,
  pub tj_testgroup: String,
  pub tj_checkdoctor: String,
  pub tj_recheckdoctor: String,
  pub tj_importer: String,
  pub tj_importdate: i64,
  pub tj_isreceived: i32,
  pub tj_receivdate: i64,
  pub tj_checkdate: String,
  pub tj_recheckdate: String,
}
crud!(TjLabresult {}, "tj_labresult");
rbatis::impl_select!(TjLabresult{query_many(testid:&str) => "`where tj_testid = #{testid} `"});
rbatis::impl_delete!(TjLabresult{delete(testids:&[String]) => "`where tj_testid in ${testids.sql()} `"});
rbatis::impl_delete!(TjLabresult{delete_by_itemids(testids:&[String],itemids:&[String]) => "`where tj_testid in ${testids.sql()} and tj_itemid in ${itemids.sql()} `"});
rbatis::impl_delete!(TjLabresult{delete_by_analytes(testids:&[String],analytes:&[String]) => "`where tj_testid in ${testids.sql()} and tj_analyte in ${analytes.sql()} `"});
rbatis::impl_delete!(TjLabresult{clear(date:i64) => "`where tj_importdate < #{date} `"});

impl TjLabresult {
  pub async fn save_many(rb: &mut rbatis::RBatis, infos: &Vec<TjLabresult>) -> Result<i64> {
    if infos.len() <= 0 {
      return Err(anyhow!("empty data"));
    }
    let total = infos.len() as i64;
    let (insert_vals, update_vals): (Vec<TjLabresult>, Vec<TjLabresult>) = infos.to_owned().into_iter().partition(|f| f.id == 0);

    // let rb = db.get_connection_clone();
    let mut tx = rb.acquire_begin().await.unwrap();

    if insert_vals.len() > 0 {
      let ret = TjLabresult::insert_batch(&mut tx, &infos, infos.len() as u64).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    if update_vals.len() > 0 {
      let ret = TjLabresult::update_by_column_batch(&mut tx, &update_vals, "id", crate::BATCH_SIZE).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    Ok(total)
  }
}
