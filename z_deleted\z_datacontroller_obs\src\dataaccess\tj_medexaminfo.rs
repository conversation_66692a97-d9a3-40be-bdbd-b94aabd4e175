use crate::{
  datasetup::DbConnection,
  // dto::dto::{MedinfoDTO, PatientsDTO},
  entities::prelude::*,
};
use anyhow::{anyhow, Result};
use rbatis::crud::{Skip, CRUD};
use rbson::<PERSON>son;

impl TjMedexaminfo {
  pub async fn query(testid: &str, db: &DbConnection) -> Option<TjMedexaminfo> {
    let w = db.get_connection().new_wrapper().eq("tj_testid", testid); //

    let query_ret = db.get_connection().fetch_by_wrapper(w).await;

    match query_ret {
      Ok(v) => v,
      Err(e) => {
        error!("query error:{}", e);
        None
      }
    }
  }

  pub async fn query_many(testids: &Vec<String>, db: &DbConnection) -> Result<Vec<TjMedexaminfo>> {
    if testids.len() <= 0 {
      return Err(anyhow!("query many parameter error: ids is empty"));
    }
    let w = db.get_connection().new_wrapper().do_if(testids.len() > 0, |w| w.r#in("tj_testid", &testids));

    let query_ret = db.get_connection().fetch_list_by_wrapper(w).await;

    match query_ret {
      Ok(v) => Ok(v),
      Err(e) => Err(anyhow!("query many error:{:?}", &e)),
    }
  }

  pub async fn query_many_by_dto(
    dtstart: i64,
    dtend: i64,
    testids: &Vec<String>,
    corptype: i64,
    corpids: &Vec<i64>,
    testtype: i32,
    statuslow: i32,
    statushigh: i32,
    pid: &Vec<String>,
    pname: &str,
    isrecheck: i32,
    rptnum: &str,
    db: &DbConnection,
  ) -> Result<Vec<TjMedexaminfo>> {
    info!(
      "query medexaminfo parameters, dtstart:{},dtend:{},testids:{:?},corptype:{},corpids:{:?},testtype:{},statuslow:{},statushight:{},pid:{:?},pname:{},isrecheck:{},rptnum:{}",
      dtstart,
      dtend,
      testids,
      corptype,
      corpids,
      testtype,
      statuslow,
      statushigh,
      &pid,
      &pname,
      isrecheck,
      &rptnum
    );
    let mut pids: Vec<String> = Vec::new();
    if pname.len() > 0 {
      //query patient by name
      // let ptdto = PatientsDTO {
      let pid: Vec<&str> = Vec::new(); //,
                                       // let pname  pname.to_owned(),
      let idcard = ""; //idcard: "".to_string(),
                       // };
      let ret = TjPatient::query_many_by_dto(&pid, pname, idcard, &db).await;
      if ret.as_ref().is_err() {
        error!("query patient error:{:?}", ret.as_ref().err());
        return Err(anyhow!("query many error:{:?}", ret.as_ref().err()));
      }
      pids = ret.unwrap().into_iter().map(|x| x.tj_pid).collect();
      // info!("pids:{:?}", &pids);
    }
    let w = db
      .get_connection()
      .new_wrapper()
      .do_if(dtstart > 0, |w| w.gt("tj_testdate", dtstart.to_owned()))
      .do_if(dtend > 0, |w| w.lt("tj_testdate", dtend.to_owned()))
      .do_if(testids.len() > 0, |w| w.r#in("tj_testid", testids.to_owned().as_slice()))
      .do_if(corptype > 0, |w| {
        if corptype == 1 {
          w.eq("tj_corpnum", 1)
        } else if corptype == 2 {
          w.ge("tj_corpnum", 2)
        } else {
          w.eq("tj_corpnum", corptype)
        }
      })
      .do_if(testtype > 0, |w| w.eq("tj_testtype", testtype))
      .do_if(statuslow >= 0, |w| w.ge("tj_checkstatus", statuslow))
      .do_if(statushigh > 0, |w| w.le("tj_checkstatus", statushigh))
      .do_if(pid.len() > 0, |w| w.r#in("tj_pid", pid.to_owned().as_slice()))
      .do_if(corpids.len() > 0, |w| w.r#in("tj_corpnum", &corpids))
      .do_if(pname.len() > 0, |w| w.r#in("tj_pid", pids.as_slice()))
      // .do_if(!pname.is_empty(), |w| {
      //     w.push_sql(format!(" and tj_pid in (select tj_pid from tj_patient where tj_pname like '%{}%' )", pname.to_owned()).as_str())
      // })
      .do_if(isrecheck > 0, |w| w.gt("tj_isrecheck", 0))
      .do_if(!rptnum.is_empty(), |w| w.like("tj_rptnum", rptnum.to_owned()));

    let query_ret = db.get_connection().fetch_list_by_wrapper(w).await;

    match query_ret {
      Ok(v) => Ok(v),
      Err(e) => {
        error!("query error:{}", &e);
        Err(anyhow!("query many error:{:?}", &e))
      }
    }
  }

  pub async fn save(info: &TjMedexaminfo, db: &DbConnection) -> Result<TjMedexaminfo> {
    if info.id == 0 {
      let ret = TjMedexaminfo::insert(info, db).await;
      if ret.as_ref().is_err() {
        error!("error:{:?}", ret.as_ref().err().unwrap());
        return Err(anyhow!("error:{:?}", ret.as_ref().err().unwrap()));
      }
      let id = ret.unwrap();
      let mut ret_val = info.clone();
      ret_val.id = id;
      return Ok(ret_val);
    } else {
      let ret = TjMedexaminfo::update(info, db).await;
      if ret.as_ref().is_err() {
        error!("error:{:?}", ret.as_ref().err().unwrap());
        return Err(anyhow!("error:{:?}", ret.as_ref().err().unwrap()));
      }
      Ok(info.clone())
    }
  }
  pub async fn save_many(infos: &Vec<TjMedexaminfo>, db: &DbConnection) -> Result<()> {
    let (insert_vals, update_vals): (Vec<TjMedexaminfo>, Vec<TjMedexaminfo>) = infos.to_owned().into_iter().partition(|f| f.id == 0);
    if insert_vals.len() > 0 {
      let ret = TjMedexaminfo::insert_many(&insert_vals, &db).await;
      if ret.as_ref().is_err() {
        error!("insrt many error: {:?}", ret.as_ref().err());
        return Err(anyhow::anyhow!("insert many error:{}", ret.err().unwrap().to_string()));
      }
    }

    if update_vals.len() > 0 {
      for val in infos.iter() {
        let ret = db.get_connection().update_by_column("id", val).await;
        if ret.is_err() {
          error!("update  error: {:?}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow::anyhow!("update error:{}", ret.err().unwrap().to_string()));
        }
      }
    }

    Ok(())
  }

  pub async fn insert(info: &TjMedexaminfo, db: &DbConnection) -> Result<i64> {
    let ret = db.get_connection().save(info, &[]).await;
    if ret.as_ref().is_err() {
      error!("保存出错，错误信息：{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("保存出错，错误信息：{}", ret.as_ref().err().unwrap().to_string()));
    }
    // info!("result:{:?}", &ret);
    let id = ret.as_ref().unwrap().last_insert_id.map_or(0, |v| v);
    Ok(id)
  }

  pub async fn insert_many(infos: &Vec<TjMedexaminfo>, db: &DbConnection) -> Result<()> {
    let ret = db.get_connection().save_batch(&infos, &[]).await;
    if ret.as_ref().is_err() {
      error!("保存信息失败:{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("保存信息失败:{}", ret.as_ref().err().unwrap().to_string()));
    }

    Ok(())
  }

  pub async fn update(info: &TjMedexaminfo, db: &DbConnection) -> Result<()> {
    let ret = db.get_connection().update_by_column("id", info).await;
    match ret {
      Ok(_v) => Ok(()),
      Err(e) => Err(anyhow!("update error: {}", e)),
    }
  }

  pub async fn update_may(vals: &Vec<TjMedexaminfo>, conn: &DbConnection) -> Result<()> {
    //方法1： 采用wrapper的方法更新
    for val in vals {
      let w = conn.get_connection().new_wrapper().eq("id", val.id);
      let ret = conn.get_connection().update_by_wrapper(val, w, &[Skip::Value(Bson::Null), Skip::Column("id")]).await;
      if ret.as_ref().is_err() {
        error!("更新错误:{},信息:{:#?}", ret.as_ref().err().unwrap().to_string(), &val);
        // return Err(anyhow!("update user assets info error"));
        continue;
      }

      let updated_rows = ret.unwrap();
      if updated_rows <= 0 {
        let insert_ret = conn.get_connection().save(&val, &[]).await;
        if insert_ret.as_ref().is_err() {
          error!("插入错误:{},信息:{:#?}", insert_ret.as_ref().err().unwrap().to_string(), &val);
          continue;
        }
      }
    }
    Ok(())
  }
}
