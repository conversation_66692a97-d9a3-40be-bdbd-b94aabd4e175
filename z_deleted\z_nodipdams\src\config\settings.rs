use std::collections::{BTreeMap, HashMap};

use config::{Config, ConfigError, File};
use serde::{Deserialize, Serialize};

#[derive(Debug, Default, <PERSON>lone, Deserialize)]
pub struct Database {
  pub uri: String,
}

#[derive(Debug, <PERSON><PERSON>ult, <PERSON>lone, Deserialize)]
pub struct Application {
  pub apphost: String,
  pub appport: i32,
  pub reportdir: String,
  pub filedir: String,
  pub photodir: String,
  pub server: String,
  pub proxyserver: String,
  pub appkey: String,
  pub appsecret: String,
  pub staffid: i32,
  pub reportcommand: String,
  pub corpreportcmd: String,
  pub msorder: String,
  pub agdburi: String,
  pub stdays: i32,
}

#[derive(Debug, De<PERSON>ult, Clone, Deserialize)]
pub struct Medpackages {
  pub packageid: i32,
}

#[derive(Debug, Default, Clone, Deserialize)]
pub struct Settings {
  pub database: Database,
  pub application: Application,
  pub medpackages: HashMap<i32, Medpackages>,
}

impl Settings {
  pub fn init(cfgfile: &str) -> Result<Self, ConfigError> {
    let s = Config::builder()
      // Start off by merging in the "default" configuration file
      .add_source(File::with_name(cfgfile))
      // .add_source(File::with_name("config/stats.toml").required(false))
      // // Add in the current environment file
      // // Default to 'development' env
      // // Note that this file is _optional_
      // .add_source(File::with_name(&format!("examples/hierarchical-env/config/{}", run_mode)).required(false))
      // // Add in a local configuration file
      // // This file shouldn't be checked in to git
      // .add_source(File::with_name("examples/hierarchical-env/config/local").required(false))
      // // Add in settings from the environment (with a prefix of APP)
      // // Eg.. `APP_DEBUG=1 ./target/app` would set the `debug` key
      // .add_source(Environment::with_prefix("app"))
      // // You may also programmatically change settings
      // .set_override("database.url", "postgres://")?
      .build()
      .expect("build config file error");

    s.try_deserialize()
  }
}
