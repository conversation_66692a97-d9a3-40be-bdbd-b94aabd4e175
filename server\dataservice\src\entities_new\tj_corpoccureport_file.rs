//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_corpoccureport_file")]
pub struct Model {
  #[sea_orm(column_name = "ID", primary_key)]
  pub id: i64,
  pub report_id: i32,
  pub create_date: i64,
  pub report_type: i8,
  pub file_url: String,
  pub down_load: i8,
  pub file_type: i8,
  pub report_no: String,
  pub pub_file_url: String,
  pub modify_date: i64,
  pub is_push: i8,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
