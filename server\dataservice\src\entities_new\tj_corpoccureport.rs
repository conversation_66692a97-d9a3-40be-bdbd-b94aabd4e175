//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Default)]
#[sea_orm(table_name = "tj_corpoccureport")]
pub struct Model {
  #[sea_orm(primary_key)]
  pub id: i32,
  pub tj_corpid: String,
  pub tj_corpname: String,
  pub tj_wtcorpname: String,
  pub tj_starttime: i64,
  pub tj_endtime: i64,
  pub tj_testyear: i32,
  pub tj_testtype: Option<i32>,
  pub tj_typename: Option<String>,
  #[sea_orm(column_type = "Text")]
  pub tj_poisions: String,
  pub tj_testdate: Option<String>,
  pub tj_testaddress: Option<String>,
  pub tj_peoplenum: Option<i32>,
  pub tj_apeoplenum: Option<i32>,
  pub tj_testitems: Option<String>,
  #[sea_orm(column_type = "Text", nullable)]
  pub tj_evalaw: Option<String>,
  #[sea_orm(column_type = "Text", nullable)]
  pub tj_testlaw: Option<String>,
  #[sea_orm(column_type = "Text", nullable)]
  pub tj_result: Option<String>,
  pub tj_createdate: i64,
  pub tj_moddate: Option<i64>,
  pub tj_creator: Option<String>,
  pub tj_modifier: Option<String>,
  pub tj_reportnum: Option<String>,
  pub tj_status: Option<i32>,
  pub tj_peid: Option<i32>,
  pub tj_pages: Option<i32>,
  pub tj_isrecheck: Option<i32>,
  pub tj_orptid: i32,
  pub tj_reportnumint: Option<String>,
  pub tj_pyjm: Option<String>,
  pub tj_reporttype: i32,
  pub tj_syncflag: i32,
  pub tj_memo: String,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
