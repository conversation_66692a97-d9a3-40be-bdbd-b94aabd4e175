// use std::collections::HashMap;
// use strum_macros::EnumString;
use num_enum::TryFromPrimitive;
use strum::{AsRefStr, Display, EnumString, IntoStaticStr};
use strum_macros::EnumIter;
// use std::string::ToString;

pub const SUMMARY_ITEMCODE: &str = "000001";
pub const CODE_HTTP_OK: i32 = 200;
pub const CODE_HTTP_ERROR: i32 = 201;
pub const AUDIOGRAM_DEPTID: &str = "0109";
pub const EMPTY_AUDIOGRAM_VALUE: f32 = -200.00;
pub const OTHER_WORKTYPE: &str = "99-9999";
pub const AUDIO_HAZARD_ID: i64 = 68;
// pub const BATCH_SIZE: u64 = 50;

//1:oracle 2:mssql 3:mysql 4:postgresql
#[derive(PartialEq)]
pub enum DatabaseType {
  Oracle = 1,
  MsSql = 2,
  MySql = 3,
  _Postgress = 4,
}

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(u8)]
pub enum YesOrNo {
  No = 0,
  Yes = 1,
}

#[derive(Debug, Clone)]
pub enum InfoType {
  EconomicType = 1,
  WorkType = 2,
  AreaCode = 3,
}

#[derive(Debug, Clone)]
pub enum InfoConfigType {
  EconomicType = 1,
  IndustryType = 2,
  WorkType = 3,
}

#[derive(Debug, Clone)]
pub enum TestType {
  PT = 1,
  TZ = 2,
  SG = 3,
  ZG = 4,
  LG = 5,
  YJ = 6,
  JKZ = 7,
}

//体检状态
#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(i32)]
pub enum ExamStatus {
  Noprogress = 0,  //体检状态0
  Appoint = 1,     //预约 1
  Register = 2,    //登记 2
  Examining = 3,   //正在体检 3
  Examined = 4,    //体检结束 4
  Allchecked = 5,  //已总检 5
  Reported = 6,    //已报告 6
  CDCUploaded = 7, //CDC已上报 7
  Syncd = 8,       //已同步 8
  Printed = 9,     //已打印 9
  MaxStatus = 100, //最大值
}

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(i32)]
pub enum PayMethod {
  Personal = 1, //个人
  Company = 2,  //企业
}

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(i32)]
pub enum Sex {
  Unknown = 0,
  Male = 1,
  Femal = 2,
  All = 3,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CheckResultType {
  Normal = 0,    //正常
  Recheck = 1,   //复查
  Forbidden = 2, //禁忌
  Oculike = 3,   //疑似
  Other = 4,     //其他疾病或异常
  Addional = 5,  //需补检
}

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(i32)]
pub enum DictType {
  DictAll = 0,
  DictSex = 1,
  DictMarriage = 2,
  DictCategory = 3,
  DictSource = 4,
  DictTesttype = 5,
  DictItemtype = 6,
  DictCheckall = 8, //总检结果
  DictCustomer = 10,
  DictCPMEStatus = 11,
  DictStatus = 12,
  DictYesORNo = 13,
  DicttAskType = 14,
  DictGuiMo = 17,
  DictGuide = 18,
  DictSysparm = 19,
  DictDisRet = 20, //疾病转归代码
}

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(i32)]
pub enum Customer {
  CustomerAll = 0,
  CustomerName = 1,
  CustomerPhone = 2,
  CustomerConsultPhone = 3,
  CustomerUrl = 4,
  CustomerAddress = 5,
  CustomerBus = 6,
  CustomerBead = 7,
  CustomerReportNum = 8,
  CustomerContactor = 9,
  CustomerEmail = 10,
  CustomerPostcode = 11,
  CustomerShowLog = 12,
  CustomerLogo = 13,
  CustomerFax = 14,
  CustomerDJH = 15,
  CustomerTJYJ = 16,
  CustomerNreportnum = 17,
  CustomerTip = 18,
  CustomerDptname = 19,
  CustomerRecheckdays = 20,
  CustomerRadioreport = 21,
  CustomerRadioeva = 22,
  CustomerWriter = 23,
  CustomerReporter = 24,
  CustomerMax = 100,
}

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(i32)]
pub enum SysParm {
  SysInfo = 0,
  PacsResut = 1,
  PacsItem = 2,
  LisMethod = 3,
  PacsMethod = 4,
  TestidPrecode = 5,
  SSO = 6,
  LisCodeZh = 7,
  FromPacs = 8,        /* 8 pacs对接 0：否 1：是*/
  PacsNorm = 9,        /* 9 pacs小结正常值，用逗号分割 */
  ShenHeYS = 10,       /*10 个人报告里是否需要审核医师 0：不需要 1：需要*/
  SHYiSheng = 11,      /*11 审核医生的电子签名，为空则没有*/
  ZJeSign = 12,        /*12 主检医生的电子签名 为空则没有*/
  ReportIntro = 13,    /*13 企业报告说明书是否增加额外内容 0：不启用 1：启用*/
  NormalRet = 14,      /*14 普通体检的默认正常结果*/
  OcuRet = 15,         /*15 职业健康体检正常的默认结果*/
  CustomizeRpt = 16,   /*16 是否支持个性化项目选择 0：不支持 1：支持*/
  ExternalUpload = 17, /*17 是否外部对接 0：否 1：是*/
  ShowPaymethod = 18,  /*18 是否显示支付方式 0：否 1：是*/
  ReportUpload = 19,   /*19 是否启用报告上报 0：否 1：是*/
  ExternalName = 20,   /*20 对接得上手*/
  PZeSign = 21,        /*21 批准医生的电子签名 为空则没有*/
  ZYGR = 22,           /*22 个人职业报告*/
  PTGR = 23,           /*23 个人普通报告*/
  ZYHZ = 24,           /*24 职业汇总表*/
  PTHZ = 25,           /*25 普通汇总表*/
  PTesign = 26,        /*26 普通体检的主检医生签名*/
  Platform = 27,       /*27 是否开启平台服务*/
  Customer = 28,       /*28 医院个性化内容 AS:安舒,XSLJ:萧山邻家*/
  BCRule = 29,         /*29 条码规则 1：桐乡三院 其他：默认*/
  BCPrecode = 30,      /*30 条码前置 ss_short:前缀 ss_name:总长度*/
  PacsCode = 31,       /*31 pacs获取依据 1：jclx，2：jcxm*/
  UpdateTestDate = 32, /*32 0：不更新 1：第一次体检 2：最后一次*/
  Stample = 33,        /*33 */
  NormalStample = 34,  /*34 普通报告电子章*/
  Symptom = 35,        /*35 报告里是否合并症状 0：不合并 1：合并*/
  EZJeSign = 36,       /*36 企业总表的主检医师电子签名，为空或者ss_short为0，无，1：有 2：打印名字*/
  Advanced = 37,       /*37 显示高级版本*/
  Photo = 38,          /*38 报告是否打印照片*/
  Audio = 39,          /*39 电测听异常是否默认为职业异常 0：否 1：是*/
  ESHYiSheng = 40,     /*40 企业总表审核医生的电子签名，为空则没有 为空或者ss_short为0，无，1：有 2：打印名字*/
  PacsUseDept = 41,    /*41 影像类用科室结论汇总异常 0：否 1：是*/
  Orientation = 42,    /*是否显示报告排版方式*/
  SgLaw = 43,          /*上岗是否显示评价依据*/
  GuideMulti = 44,     /*指引单是否一行打印2个项目*/
  GuideItemDept = 45,  /*指引单打印是否显示科室*/
  ZBesign = 46,        /*制表人*/
  RptSummary = 47,     /*企业总报告是否添加人数统计 ss_short 0：否 1：是*/
  PacsAbkey = 48,      /*pacs项目判断异常的关键字*/
  CheckallMethod = 49, /*总检判断异常的方法  0 or 1：本地总检 2:服务端总检*/
  ExtDeptCode = 50,    /*外部对接的科室代码*/
  ExtDeptName = 51,    /*外部对接的科室名称*/
  SysparmMax = 99,
}

#[derive(PartialEq, Clone, Debug)]
pub enum PacsType {
  JCLX = 1,
  JCXM = 2, //最后一次
}

#[derive(PartialEq, Clone, Debug)]
pub enum UpdateTestDateType {
  No = 0,
  First = 1,
  Last = 2, //最后一次
}

#[derive(Debug, Eq, Display, PartialEq, EnumString, AsRefStr, IntoStaticStr)]
#[repr(i32)]
pub enum ValueType {
  /// Random Docs
  #[strum(to_string = "1")]
  DingXing,
  #[strum(to_string = "2")]
  DingLiang,
  #[strum(to_string = "3")]
  MiaoShu,
  #[strum(to_string = "4")]
  BanDingLiang,
}

#[derive(Debug, Eq, Display, PartialEq, EnumString, AsRefStr, IntoStaticStr)]
pub enum IdentityCode {
  #[strum(to_string = "pid")]
  Pid,
  #[strum(to_string = "tjbh")]
  Testid,
  #[strum(to_string = "barcode")]
  Barcode,
  #[strum(to_string = "reportnum")]
  Reportnum,
}

#[derive(PartialEq, Clone, Debug)]
pub enum TransType {
  Air = 0,
  Bone = 1,
}

#[derive(PartialEq, Clone, Debug)]
pub enum TransEar {
  Right = 0,
  Left = 1,
}
#[derive(PartialEq, Clone, Debug)]
pub enum Status {
  DEL = 0,
  OK = 1,
}

#[derive(PartialEq, Clone, Debug)]
pub enum ExtOpType {
  DEL = -1,
  ADD = 1,
  EDIT = 2,
}

#[derive(Debug, Eq, Display, PartialEq, EnumString, AsRefStr, IntoStaticStr)]
#[repr(i32)]
pub enum BarcodeType {
  /// Random Docs
  #[strum(to_string = "0")] //0表示只生成lis的条码
  LisOnly,
  #[strum(to_string = "1")] //同时生成检验和pacs的条码 需要在ss_identity表中，设置 关键字位bcrule的ID_OVALUE = 1
  LisAndPacs,
  #[strum(to_string = "2")] //否则全部都要生成新条码
  All,
}

#[derive(PartialEq, Clone, Debug)]
#[repr(i32)]
pub enum DeptType {
  AllDept = 0,
  Check = 1,    //检查科室
  Lab = 2,      //检验科室
  Function = 3, //功能科室
}

#[derive(Debug, Eq, Display, PartialEq, EnumString, AsRefStr, IntoStaticStr)]
pub enum PolarApi {
  #[strum(to_string = "/")]
  Api,
  #[strum(to_string = "/v1/sync/appoint")]
  ApiSyncAppoint,
  #[strum(to_string = "/v1/sync/rpt")]
  ApiSyncReport,
  #[strum(to_string = "/v1/sync/photo")]
  ApiSyncPhoto,
  #[strum(to_string = "/v1/sync/result")]
  ApiSyncResult,
  #[strum(to_string = "/v1/sync/corpuser")]
  ApiSyncCorpuser,
}

#[derive(PartialEq, Clone, Debug)]
pub enum RecvResultCode {
  Ok = 0,
  PartialOK = 1,      //检查科室
  NoExternalData = 2, //无外部数据
  Failure = 3,        //失败，没有配置对接代码
}

#[derive(PartialEq, Clone, Debug)]
pub enum ReportFormat {
  DOCX = 1,
  PDF = 2,
}

#[derive(Debug, PartialEq, EnumIter)]
pub enum CacheType {
  Dict = 1,
  Infoconfig = 2,
  Area = 3,
  AutoDiag = 4,
  Combine = 5,
  Item = 6,
  ItemResult = 7,
  Diseases = 8,
  Dept = 9,
  Staff = 10,
  // OccuConditions = 11,
  Corpinfo = 12,
  Hazardinfo = 13,
  Hazardtype = 14,
  Deptitem = 15,
  Guideinfo = 16,
  GuideItem = 17,
  Baritem = 18,
  Bardetails = 19,
  Barname = 20,
  Itemtype = 21,
  ItemRange = 22,
  EvalLaw = 23,
  Occucond = 24,
}

#[derive(Debug, Eq, Display, PartialEq, EnumString, AsRefStr, IntoStaticStr)]
pub enum MonitorType {
  #[strum(to_string = "01")]
  Changgui,
  #[strum(to_string = "02")]
  Zhudong,
}

#[derive(Debug, Clone, Copy, TryFromPrimitive, PartialEq, Eq)]
#[repr(i32)]
pub enum ReportType {
  Occu = 0,   //职业
  Normal = 1, //普通
  Radio = 2,  //放射
}
