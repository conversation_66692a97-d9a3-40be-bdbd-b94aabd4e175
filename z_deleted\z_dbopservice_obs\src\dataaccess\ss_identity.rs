use anyhow::{anyhow, Result};
use chrono::Datelike;
use rbatis::crud;
use serde::{Deserialize, Serialize};
#[derive(<PERSON><PERSON>, Debug, <PERSON><PERSON>ult, PartialEq, Serialize, Deserialize)]
pub struct SsIdentity {
  pub id: i32,
  pub id_name: String,
  pub id_cvalue: i32,
  pub id_ovalue: i32,
  pub id_incvalue: i32,
}
crud!(SsIdentity {}, "ss_identity"); //crud = insert+select_by_column+update_by_column+delete_by_column
rbatis::impl_select!(SsIdentity{query_by_code(code:&str) ->Option => "`where id_name = #{code} limit 1 `"});

impl SsIdentity {
  pub async fn query(rb: &mut rbatis::RBatis, code: &str) -> Result<Option<SsIdentity>> {
    //
    // let rb = db.get_connection_clone();
    let ret = rb.acquire_begin().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let mut tx = ret.unwrap();

    let ret = SsIdentity::query_by_code(&mut tx, code).await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let ret_data = ret.unwrap();
    if ret_data.is_none() {
      return Err(anyhow!("cant find ss_identity by {}", code));
    }
    let mut idty = ret_data.unwrap();
    idty.id_cvalue = idty.id_cvalue + idty.id_incvalue;
    if code.eq_ignore_ascii_case("reportnum")
      || code.eq_ignore_ascii_case("nreportnum")
      || code.eq_ignore_ascii_case("olreport")
      || code.eq_ignore_ascii_case("olnotice")
      || code.eq_ignore_ascii_case("ofreport")
      || code.eq_ignore_ascii_case("transnum")
    {
      //check years
      let local: chrono::DateTime<chrono::Local> = chrono::Local::now();
      let year = local.year();
      if year != idty.id_ovalue {
        idty.id_cvalue = 1;
        idty.id_ovalue = year;
      }
    }
    let current_year = utility::timeutil::get_current_year_month_day().0;
    if code.eq_ignore_ascii_case("barcode") && (idty.id_ovalue - current_year).abs() == 1 {
      //txsy 年份变化
      if current_year != idty.id_ovalue {
        idty.id_ovalue = current_year;
        idty.id_cvalue = 8000000;
      }
    }

    info!("start to update ss_identity:{:?}", &idty);
    let ret = SsIdentity::update_by_column(&mut tx, &idty, "id").await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    let ret = tx.commit().await;
    if ret.as_ref().is_err() {
      let _ = tx.rollback().await;
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    info!("index value:{:?}", &idty);
    Ok(Some(idty))
  }

  pub async fn query_many(rb: &mut rbatis::RBatis) -> Result<Vec<SsIdentity>> {
    // let mut rb = db.get_connection_clone();
    let ret = SsIdentity::select_all(rb).await;
    if ret.as_ref().is_err() {
      // error!("query error:{}", ret.err().unwrap().to_string());
      return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    }
    Ok(ret.unwrap())
  }
}
