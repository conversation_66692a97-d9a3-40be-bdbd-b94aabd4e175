[package]
name = "nodipexam"
version = "0.106.0"
edition = "2021"
description = "体检中心后台服务 build time: 2025-06-16"
resolver = "2"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { path = "../utility" }
dataservice = { path = "../dataservice" }
nodipservice = { path = "../nodipservice" }
# dbopservice = { path = "../dbopservice" }

serde = { workspace = true }      #{ version = "1", features = ["derive"] }
serde_json = { workspace = true } #"1"

axum = { workspace = true }       #{ version = "0.8.1", features = ["multipart", "tokio", "original-uri"] }
axum-extra = { workspace = true } #{ version = "0.10.0", features = ["typed-header"] }

tower = { workspace = true }          #{ version = "0.5.1", features = ["util", "filter"] }
hyper = { workspace = true }          #{ version = "1.4", features = ["full"] }
hyper-util = { workspace = true }     #{ version = "0.1", features = ["client-legacy"] }
tower-http = { workspace = true }     #{ version = "0.6.1", features = ["trace", "fs", "cors"] }
headers = { workspace = true }        #"0.4"
http-body-util = { workspace = true } #"0.1"
futures = { workspace = true }        #"0.3"

anyhow = { workspace = true }    #"1.0"
thiserror = { workspace = true } #"2.0.3"
config = { workspace = true }    #"0.15.4"
glob = { workspace = true }      #"0.3.1"

# log = { workspace = true } #"0.4"
# log4rs = { workspace = true }

tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = [
    "env-filter",
    "fmt",
    "time",
] }
tracing-error = { workspace = true }
tracing-appender = { workspace = true }
file-rotate = { workspace = true }

jsonwebtoken = { workspace = true } #"9"
mime_guess = { workspace = true }   #"2"
itertools = { workspace = true }    #"0.14.0"
reqwest = { workspace = true }      #
# { version = "0.12", default-features = false, features = [
#     "json",
#     "stream",
#     "multipart",
#     "rustls-tls",
# ] }

uuid = { workspace = true }         #"1"
rust_decimal = { workspace = true } #"1"

chrono = { workspace = true } #{ version = "0.4" }
strum = { workspace = true }  #{ version = "0.26", features = ["derive"] }

tokio = { workspace = true }
tokio-util = { workspace = true }
