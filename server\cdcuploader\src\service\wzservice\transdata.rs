use super::{
  common::{self},
  datastructs::*,
};
use crate::{
  app::{self, DataType, ParmDto},
  config::settings::Settings,
};
use crate::{
  common::utils,
  service::general::zjcommon::{self, AudioDetail},
};
use anyhow::{anyhow, Result};
// use dbopservice::{dataaccess::prelude::*, dbinit::DbConnection};
use dataservice::{dbinit::DbConnection, entities::prelude::*};
use nodipservice::common::{
  constant::{self, *},
  syscache::SysCache,
};
use nodipservice::medexam::audiogramsvc::AudiogramSvc;
use yaserde::ser::to_string_with_config;

#[derive(Debug)]
pub struct WzService {}

impl WzService {
  pub async fn trans_data(dto: &ParmDto, setting: &Settings, dbconn: &DbConnection) -> Result<String> {
    // info!("trans corpinfo now, parmeters:{:?}", &data);
    let seconfig = yaserde::ser::Config {
      perform_indent: false,
      write_document_declaration: false,
      indent_string: None,
    };
    let upload_content: String;
    if dto.datatype == DataType::CORPINFO as i32 {
      //处理datatype
      let corpids: Vec<i64> = dto.tid.iter().map(|x| x.parse::<i64>().unwrap_or_default()).collect();
      let ret = TjCorpinfo::query_many(&corpids, "", &vec![], &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let corpinfo = ret.unwrap();
      let body_str: String;
      if dto.optype != app::OperationType::DELETE as i32 {
        let ret = WzService::trans_employerlist(&corpinfo, setting, &dbconn).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        let employerlist = ret.unwrap();
        // info!("trans ret:{:?}", &employerlist);
        let body = EmployerBody { employerlist };
        //convert xml to string
        let ret = to_string_with_config(&body, &seconfig);
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        body_str = ret.unwrap();
      } else {
        let creditcodesets: Vec<String> = corpinfo.iter().map(|x| x.tj_orgcode.clone()).collect();
        let employerdeletelist = EmployerDeleteList { creditcodesets };
        let body = EmployerDeleteBody {
          org_code: setting.organization.org_code.clone(),
          employerdeletelist,
        };
        let ret = to_string_with_config(&body, &seconfig);
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        body_str = ret.unwrap();
      }
      // info!("**********bodystr:{}", &body_str);
      let ret = utility::encrypt::rsa_sign(&body_str, setting.organization.key_file.as_str());
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let body_sign = ret.unwrap();
      // info!("********bodysign:{}", &body_sign);

      let eventid = zjcommon::CORPYEVENTID.to_string();

      let ret;
      let requesttime = utility::timeutil::current_naive_time_iso_with_seperator(false);
      let head_sign = common::get_header_sign(&requesttime, &eventid, &setting.organization.org_code, &setting.organization.password);

      let header = Header {
        eventid,
        userid: setting.organization.org_code.to_owned(),
        operationtype: zjcommon::get_operation_type(app::OperationType::try_from(dto.optype as u8).unwrap()),
        requesttime: requesttime,
        headsign: head_sign,
        bodysign: body_sign,
      };

      ret = to_string_with_config(&header, &seconfig);
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let header_str = ret.unwrap();
      // info!("headerstring:{}", &header_str);

      // upload_content = "".to_string();
      upload_content = format!("<data>{}{}</data>", header_str, body_str);
    } else if dto.datatype == DataType::HEALTHY as i32 {
      let body_str: String;

      if dto.optype != app::OperationType::DELETE as i32 {
        //处理healthy data
        let ret = WzService::trans_reportcardlist(dto, setting, dbconn).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        let reportcardlist = ret.unwrap();

        let body = HealthyBody { reportcardlist };
        let ret = to_string_with_config(&body, &seconfig);
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        let ret_data = ret.unwrap().replace("\r", "");
        body_str = ret_data.replace("\n", "");
        // let ret_data = ret_data.replace(" ", "");
        // info!("BODY:{:?}", &body_str);
      } else {
        let code: Vec<String> = dto.tid.clone();
        let carddeletelist = ReportCardDeleteList { code };
        let body = ReportCardDeleteBody {
          org_code: setting.organization.org_code.clone(),
          carddeletelist,
        };
        let ret = to_string_with_config(&body, &seconfig);
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        body_str = ret.unwrap();
        info!("body str:{}", &body_str);
      }

      let ret = utility::encrypt::rsa_sign(&body_str, setting.organization.key_file.as_str());
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }

      let body_sign = ret.unwrap();
      // info!("bodysign:{}", &body_sign);

      let eventid = zjcommon::HEALTHYEVENTID.to_string();

      let requesttime = utility::timeutil::current_naive_time_iso_with_seperator(false);
      let head_sign = common::get_header_sign(&requesttime, &eventid, &setting.organization.org_code, &setting.organization.password);

      let header = Header {
        eventid,
        userid: setting.organization.org_code.to_owned(),
        operationtype: zjcommon::get_operation_type(app::OperationType::try_from(dto.optype as u8).unwrap()),
        requesttime: requesttime,
        headsign: head_sign,
        bodysign: body_sign,
      };

      let ret = to_string_with_config(&header, &seconfig);

      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let header_str = ret.unwrap();
      // info!("header string:{}", &header_str);
      // let data_str = format!("{}{}", header_str, body_str);
      upload_content = format!("<data>{}{}</data>", header_str, body_str);

      // let healthy_data = HealthyData { header, body };
      // let ret = to_string_with_config(&healthy_data, &seconfig);
      // if ret.as_ref().is_err() {
      //     error!("{}", ret.as_ref().err().unwrap().to_string());
      //     return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      // }
      // upload_content = ret.unwrap();
    } else {
      //handle all
      // let ret = WzService::trans_all(data, setting);
      // if ret.as_ref().is_err() {
      //     return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      // }
      // let ret_data = ret.unwrap();
      // let ret = to_string(&ret_data);
      // if ret.as_ref().is_err() {
      //     return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      // }
      // upload_content = ret.unwrap();
      upload_content = "".to_string();
    }
    // let path = format!("{}/{}.xml", app::FILE_DIR, utility::timeutil::current_timestamp());
    let path = format!("{}/{}.xml", &setting.system.file_path, &dto.tid[0]);
    let ret = std::fs::write(path, &upload_content);
    if ret.as_ref().is_err() {
      return Err(anyhow!("write to file error:{}", ret.as_ref().err().unwrap().to_string()));
    }

    // let final_upload_content = common::get_body_wrapper(&upload_content);

    // info!("AFTERE:{}", &upload_content);
    Ok(upload_content)
  }

  // fn trans_all(data: &UploadData, setting: &Settings) -> Result<TotalBody> {
  //     info!("trans corpinfo now, parmeters:{:?}", &data);
  //     let totalbody: TotalBody = TotalBody { ..Default::default() };
  //     // let corpdata = TbTjCrpt {
  //     //     Rid: "akdkdk".to_string(),
  //     //     CrptName: "name".to_string(),
  //     //     InstitutionCode: "code".to_string(),
  //     //     ..Default::default()
  //     // };
  //     // let str_ret = to_string(&corpdata);
  //     // info!("serialize result:{:?}", &str_ret.unwrap());
  //     Ok(totalbody)
  // }

  async fn trans_employerlist(data: &Vec<TjCorpinfo>, setting: &Settings, db: &DbConnection) -> Result<EmployerList> {
    // info!("trans corpinfo now, parmeters:{:?}", &data);
    if data.len() <= 0 {
      return Err(anyhow!("corpinfo length 0"));
    }
    let mut corpinfos_vec: Vec<Employer> = Vec::new();
    for val in data.iter() {
      let staff = nodipservice::SYSCACHE.get().unwrap().get_staff(val.tj_operator, db).await;
      let mut dict = SsDictionary { ..Default::default() };
      if let Some(ret) = nodipservice::SYSCACHE.get().unwrap().get_dict(DictType::DictCustomer as i32, Customer::CustomerPhone as i32) {
        dict = ret.to_owned();
      }
      let corpinfo = Employer {
        creditcode: val.tj_orgcode.to_owned(),
        employername: val.tj_corpname.to_owned(),
        orgcode: setting.organization.org_code.to_owned(),
        orgname: setting.organization.org_name.to_owned(),
        areacode: val.tj_areacode.to_owned(),
        economictypecode: val.tj_economic2.to_owned(),
        industrycategorycode: val.tj_industry2.to_owned(),
        enterprisesizecode: zjcommon::get_corp_scale(val.tj_corpscale),
        address: val.tj_address.to_owned(),
        addresszipcode: val.tj_postcode.to_owned(),
        contactperson: val.tj_contactor.to_owned(),
        employerphone: val.tj_phone.to_owned(),
        // contactphone: val.tj_phone.to_owned(),
        issubsidiary: false,
        secondemployercode: "".to_owned(),
        createareacode: val.tj_areacode.to_owned(),
        writeunit: setting.organization.org_name.to_owned(), //填表单位名称
        writeperson: staff.tj_staffname.to_owned(),          //填表人
        writepersontel: dict.ss_name.to_owned(),             //用医院的联系电话
        writedate: utility::timeutil::current_date(),        //current d8
        reportunit: setting.organization.org_name.to_owned(),
        reportperson: staff.tj_staffname.to_owned(),
        reportpersontel: dict.ss_name.to_owned(),
        // auditstatus: "".to_string(),
        // auditdesc: "".to_string(),
        // auditdate: current_date(),
        // auditorname: val.tj_orgcode.to_owned(),
        ..Default::default()
      };
      corpinfos_vec.push(corpinfo);
    }
    let employer_list = EmployerList { employers: corpinfos_vec };

    Ok(employer_list)
  }

  async fn trans_reportcardlist(dto: &ParmDto, setting: &Settings, dbconn: &DbConnection) -> Result<ReportCardList> {
    // info!("start to trans report card:{:?}", &dto.tid);
    let testids: Vec<&str> = dto.tid.iter().map(|v| v.as_str()).collect();
    let ret = TjMedexaminfo::query_many(0, 0, &testids, &vec![], 0, -1, &vec![], &vec![], &vec![], 0, "", &dbconn.get_connection()).await;
    if ret.is_err() {
      error!("{}", ret.as_ref().err().unwrap().to_string());
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let mut medinfos = ret.unwrap();
    if medinfos.len() <= 0 {
      return Err(anyhow!("不能找到id为:{:?}的体检信息", dto.tid));
    }
    let mut reportcards: Vec<ReportCard> = Vec::new();

    for val in medinfos.iter_mut() {
      if val.tj_upload == YesOrNo::Yes as i32 && dto.force == YesOrNo::No as i32 {
        return Err(anyhow!("该体检者已经上报..."));
      }
      if val.tj_checkstatus < ExamStatus::Allchecked as i32 {
        return Err(anyhow!("体检者:{} 未出报告，不能上报", &val.tj_testid));
      }
      if val.tj_testtype <= TestType::PT as i32 {
        return Err(anyhow!("体检者:{} 为普通体检，不需要上报", &val.tj_testid));
      }
      //query patient info
      let ret = TjPatient::query(&val.tj_pid, &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let ret = ret.unwrap();
      if ret.as_ref().is_none() {
        return Err(anyhow!("没有找到编号为:{}的体检人员信息", &val.tj_pid));
      }
      let ptinfo = ret.unwrap();
      //query check all
      let ret = TjCheckallnew::query(&val.tj_testid, &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let ret = ret.unwrap();
      if ret.is_none() {
        return Err(anyhow!("不能找到体检号{}的总检信息", &val.tj_testid));
      }
      let checkall = ret.unwrap();

      let ret = TjPatienthazards::query(&val.tj_testid, &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let pthds = ret.unwrap();
      if pthds.len() <= 0 {
        return Err(anyhow!("该体检者无接触的毒害因素"));
      }
      let hdids: Vec<i64> = pthds.iter().map(|v| v.tj_hid).collect();
      let ret = TjHazardinfo::query_many(&vec![], &hdids, &vec![], &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
      }
      let hdinfos = ret.unwrap();
      for hd in pthds.iter() {
        let hdinfo = hdinfos.iter().find(|v| v.id == hd.tj_hid);
        if hdinfo.is_none() {
          continue;
          // return Err(anyhow!("毒害因素:{}不存在", hd.tj_hid));
        }
        let hdinfo = hdinfo.unwrap();
        if hdinfo.tj_extcode.is_empty() {
          return Err(anyhow!("毒害因素:{}代码未匹配，请先匹配", hdinfo.tj_hname));
        }
      }

      let mut original_items: Vec<TjCheckiteminfo> = Vec::new();
      let mut original_summaries: Vec<TjTestsummary> = Vec::new();
      if val.tj_isrecheck == YesOrNo::Yes as i32 {
        //query checkiteminfos from tj_checkiteminfo
        // let mut old_testid = val.tj_oldtestid.clone(); //ret.unwrap().tj_old_testid;
        if val.tj_oldtestid.is_empty() {
          let ret = TjCorpoccureportFc::query(&val.tj_testid, &dbconn.get_connection()).await;
          if ret.as_ref().is_err() {
            return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
          }
          let ret = ret.unwrap();
          if ret.is_none() {
            return Err(anyhow!("该体检者是复查，但是找不到原来的体检信息"));
          }
          val.tj_oldtestid = ret.unwrap().tj_old_testid;
        }
        for _i in 1..100 {
          //多次复查
          let ret = TjMedexaminfo::query(&val.tj_oldtestid, &dbconn.get_connection()).await;
          if ret.as_ref().is_err() {
            return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
          }
          let ret = ret.unwrap();
          if ret.is_none() {
            return Err(anyhow!("不能找到初检号为{}的体检信息", &val.tj_oldtestid));
          }
          let mi = ret.unwrap();
          info!("查找的体检信息：{:?}", &mi);
          if mi.tj_isrecheck == YesOrNo::Yes as i32 {
            val.tj_oldtestid = mi.tj_oldtestid;
            continue;
          } else {
            val.tj_oldtestid = mi.tj_testid;
            break;
          }
        }

        let ret = TjCheckiteminfo::query(&val.tj_oldtestid, &dbconn.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        original_items = ret.unwrap();
        if original_items.len() <= 0 {
          return Err(anyhow!("该体检者是复查，但是找不到初始的检查信息"));
        }
        let ret = TjTestsummary::query(&val.tj_oldtestid, &vec![], &dbconn.get_connection()).await;
        if ret.as_ref().is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        original_summaries = ret.unwrap();
        if original_summaries.len() <= 0 {
          return Err(anyhow!("该体检者是复查，但是找不到初始的检查小结信息"));
        }
      }

      let ret = WzService::trans_userinfo(&val, &ptinfo, dbconn).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let userinfo = ret.unwrap();

      //empinfo and employerinfo
      let ret = TjCorpinfo::query(val.tj_corpnum, &dbconn.get_connection()).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let ret = ret.unwrap();
      if ret.as_ref().is_none() {
        return Err(anyhow!("找不到编号为{}的企业信息", val.tj_corpnum));
      }
      let corpinfo = ret.unwrap();
      if corpinfo.tj_areacode.is_empty() {
        return Err(anyhow!("该企业的地区代码为空"));
      }
      if corpinfo.tj_orgcode.len() != 18 {
        return Err(anyhow!("组织机构代码长度不准确(只能是18位)"));
      }
      let ret = WzService::trans_empinfo(&corpinfo, &dbconn).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let empinfo = ret.unwrap();
      let mut empcorpinfo = corpinfo.clone();
      if val.tj_empcorp != 0 && val.tj_empcorp != val.tj_corpnum {
        let ret = TjCorpinfo::query(val.tj_empcorp, &dbconn.get_connection()).await;
        if ret.is_err() {
          error!("{}", ret.as_ref().err().unwrap().to_string());
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        let ret = ret.unwrap();
        if ret.as_ref().is_none() {
          return Err(anyhow!("找不到编号为{}的企业信息", val.tj_corpnum));
        }
        empcorpinfo = ret.unwrap();
      }
      let ret = WzService::trans_empinfo_employer(&empcorpinfo, &dbconn).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let empinfoemployer = ret.unwrap();
      //end of empinfo and employer info

      let ret = WzService::trans_orginfo(&setting).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let orginfo = ret.unwrap();
      let ret = WzService::trans_cardinfo(&val, &checkall, &corpinfo, &setting, &dbconn).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let cardinfo = ret.unwrap();
      let ret = WzService::trans_contact_hazard_factor_list(&pthds, &hdinfos, &setting).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let contacthazardfactorlist = ret.unwrap();

      let ret = WzService::trans_hazard_factor_list(&val, &pthds, &hdinfos, &setting).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let hazardfactorlist = ret.unwrap();

      // trans item list
      let ret = TjTestsummary::query(&val.tj_testid, &vec![], &dbconn.get_connection()).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let mut summaries = ret.unwrap();
      for sum in original_summaries.iter_mut() {
        if summaries.iter().find(|&x| x.tj_deptid.eq_ignore_ascii_case(&sum.tj_deptid)).is_some() {
          continue;
        }
        sum.tj_testid = val.tj_testid.clone();
        summaries.push(sum.to_owned());
      }
      let ret = TjCheckiteminfo::query(&val.tj_testid, &dbconn.get_connection()).await;
      if ret.as_ref().is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      //新的体检项目
      let mut checkitems: Vec<TjCheckiteminfo> = ret.unwrap();
      //整合新的体检项目和老的体检项目
      // if original_items.len() > 0 {}
      for oitem in original_items {
        if checkitems.iter().find(|&x| x.tj_itemid == oitem.tj_itemid).is_some() {
          continue;
        }
        checkitems.push(oitem);
      }

      // let itemids: Vec<&str> = checkitems.iter().map(|x| x.tj_itemid.as_str()).collect();
      // let ret = TjIteminfo::query_many( &itemids, -1, -1, &vec![],&dbconn.get_connection()).await;
      // if ret.as_ref().is_err() {
      //   error!("{}", ret.as_ref().err().unwrap().to_string());
      //   return Err(anyhow!("trans {}", ret.as_ref().err().unwrap().to_string()));
      // }
      // let iteminfos = ret.unwrap();
      let iteminfos = nodipservice::SYSCACHE.get().unwrap().get_iteminfos(&vec![], &dbconn).await;
      let ret = WzService::trans_itemlist(&val, &ptinfo, &checkitems, &summaries, &iteminfos, &setting, dbconn).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let itemlist = ret.unwrap();
      // end
      let ret = WzService::trans_diagnosis(&val, &ptinfo, &checkall, &pthds, &hdinfos, &iteminfos, &setting, dbconn).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let diagnosislist = ret.unwrap();

      let ret = WzService::trans_auditinfo().await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let auditinfo = ret.unwrap();

      let ret = WzService::trans_healthy_survey(&val, dbconn).await;
      if ret.is_err() {
        error!("{}", ret.as_ref().err().unwrap().to_string());
        return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
      }
      let healthsurvey = ret.unwrap();
      let card = ReportCard {
        userinfo,
        empinfo,
        empinfoemployer,
        orginfo,
        cardinfo,
        contacthazardfactorlist,
        hazardfactorlist,
        itemlist,
        diagnosislist,
        auditinfo,
        healthsurvey,
      };
      reportcards.push(card);
    }

    let reportcardlist: ReportCardList = ReportCardList { reportcards };

    Ok(reportcardlist)
  }

  async fn trans_userinfo(medinfo: &TjMedexaminfo, ptinfo: &TjPatient, _dbconn: &DbConnection) -> Result<UserInfo> {
    let userinfo: UserInfo = UserInfo {
      name: ptinfo.tj_pname.to_owned(),
      idcardtype: "1".to_string(),
      idcardcode: ptinfo.tj_pidcard.to_owned(),
      sexcode: ptinfo.tj_psex,
      birthday: utility::timeutil::get_date_only(&ptinfo.tj_pbirthday),
      telphone: ptinfo.tj_pmobile.to_owned(),
      emergencycontactperson: "".to_string(),
      mmergencycontactphone: "".to_string(),
      workshop: "".to_string(),
      jobcode: medinfo.tj_wtcode.to_owned(),
      otherjobname: match medinfo.tj_wtcode.starts_with("99") {
        true => medinfo.tj_worktype.to_owned(),
        false => "".to_string(),
      }, //medinfo.tj_worktype.to_owned()
      radiationtype: "".to_string(),
      maritalstatuscode: zjcommon::get_marriage_status(ptinfo.tj_pmarriage),
      jobnumber: medinfo.tj_empid.to_owned(),
    };
    Ok(userinfo)
  }
  async fn trans_empinfo(corpinfo: &TjCorpinfo, db: &DbConnection) -> Result<EmpInfo> {
    let info_config = nodipservice::SYSCACHE.get().unwrap().get_area(&corpinfo.tj_areacode, &db).await;
    if info_config.area_name.is_empty() {
      return Err(anyhow!("地区代码:{}不存在", &corpinfo.tj_areacode));
    }
    let info = EmpInfo {
      creditcode: corpinfo.tj_orgcode.to_owned(),
      employername: corpinfo.tj_corpname.to_owned(),
      economictypecode: corpinfo.tj_economic2.to_owned(),
      industrycategorycode: corpinfo.tj_industry2.to_owned(),
      enterprisesizecode: zjcommon::get_corp_scale(corpinfo.tj_corpscale),
      areacode: corpinfo.tj_areacode.to_owned(),
      areaname: info_config.area_name,
      address: corpinfo.tj_address.to_owned(),
      addresszipcode: corpinfo.tj_postcode.to_owned(),
      contactperson: corpinfo.tj_contactor.to_owned(),
      employerphone: corpinfo.tj_phone.to_owned(),
    };
    Ok(info)
  }

  async fn trans_empinfo_employer(corpinfo: &TjCorpinfo, dbconn: &DbConnection) -> Result<EmpInfoEmployer> {
    let info_config = nodipservice::SYSCACHE.get().unwrap().get_area(&corpinfo.tj_areacode, &dbconn).await;
    if info_config.area_name.is_empty() {
      return Err(anyhow!("地区代码:{}不存在", &corpinfo.tj_areacode));
    }
    let info = EmpInfoEmployer {
      creditcodeemployer: corpinfo.tj_orgcode.to_owned(),
      employernameemployer: corpinfo.tj_corpname.to_owned(),
      economictypecodeemployer: corpinfo.tj_economic2.to_owned(),
      industrycategorycodeemployer: corpinfo.tj_industry2.to_owned(),
      enterprisesizecodeemployer: zjcommon::get_corp_scale(corpinfo.tj_corpscale),
      areacodeemployer: corpinfo.tj_areacode.to_owned(),
      areanameemployer: info_config.area_name,
    };
    Ok(info)
  }

  async fn trans_orginfo(settings: &Settings) -> Result<OrgInfo> {
    let info = OrgInfo {
      orgcode: settings.organization.org_code.clone(),
      orgname: settings.organization.org_name.clone(),
    };
    Ok(info)
  }

  async fn trans_cardinfo(medinfo: &TjMedexaminfo, checkall: &TjCheckallnew, corpinfo: &TjCorpinfo, settings: &Settings, dbconn: &DbConnection) -> Result<CardInfo> {
    let ret = utils::get_year_and_month(medinfo.tj_testdate, medinfo.tj_workage.as_str());
    if ret.as_ref().is_err() {
      return Err(anyhow!("工龄信息错误，请检查工龄是否准确"));
    }
    let total_work_time = ret.unwrap();
    let ret = utils::get_year_and_month(medinfo.tj_testdate, medinfo.tj_poisionage.as_str());
    if ret.as_ref().is_err() {
      return Err(anyhow!("接害工龄信息错误，请检查工接害龄是否准确"));
    }
    let hazard_work_time = ret.unwrap();
    // let hazards = medinfo.tj_poisionfactor.replace("、", ",");
    let mut old_testid: String = String::from("");
    if medinfo.tj_isrecheck == 1 {
      //get last checkid
      old_testid = medinfo.tj_oldtestid.clone();
      // let fc_info = TjCorpoccureportFc::query(&medinfo.tj_testid, dbconn).await;
      // if fc_info.is_some() {
      //   old_testid = fc_info.unwrap().tj_old_testid;
      // }
    }
    // let staff = StaffService::find_staff(medinfo.tj_recorder, &dictdata.staffs);
    let mut dict = SsDictionary { ..Default::default() };
    if let Some(ret) = nodipservice::SYSCACHE.get().unwrap().get_dict(DictType::DictCustomer as i32, Customer::CustomerPhone as i32) {
      dict = ret.to_owned();
    }
    // let corp_report: TjCorpoccureport;
    // let mut report_ret: Option<TjCorpoccureport> = None;
    // if !medinfo.tj_rptnum.is_empty() {
    //   info!("query report info by {}", &medinfo.tj_rptnum);
    //   report_ret = TjCorpoccureport::query(&medinfo.tj_rptnum, dbconn).await;
    // }
    // if report_ret.is_none() {
    let ret = TjCorpoccureportInfo::query(&medinfo.tj_testid, &dbconn.get_connection()).await;
    if ret.is_err() {
      return Err(anyhow!("体检编号:{}报告不存在", &medinfo.tj_testid));
    }
    let rptinfo = ret.unwrap();
    if rptinfo.is_none() {
      return Err(anyhow!("体检编号:{}报告不存在，请先出企业汇总报告后再上报", &medinfo.tj_testid));
    }
    let rptinfo = rptinfo.unwrap();
    let ret = TjCorpoccureport::query(&rptinfo.tj_report_id, &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let report_info = ret.unwrap();
    if report_info.is_none() {
      return Err(anyhow!("找不到体检编号{}所在报告编号为{}的报告信息", &medinfo.tj_testid, rptinfo.tj_report_id));
    }
    let corp_report = report_info.unwrap();
    // } else {
    //   corp_report = report_ret.unwrap();
    // }
    let mut suggest = "".to_string();
    if !checkall.tj_ocusuggestion.is_empty() {
      suggest = checkall.tj_ocusuggestion.to_owned();
    }
    if suggest.is_empty() {
      suggest = checkall.tj_othsuggestion.to_owned();
    }
    if suggest.is_empty() {
      suggest = "无".to_string();
    }
    let mut checkresultcode; //= "".to_string();
    if checkall.tj_typeid > CheckResultType::Normal as i32 && checkall.tj_typeid < CheckResultType::Other as i32 {
      checkresultcode = checkall.tj_ocuconclusion.to_owned();
    } else {
      checkresultcode = checkall.tj_othconclusion.to_owned();
    }
    if checkresultcode.is_empty() {
      checkresultcode = "未见异常".to_string();
    }
    let mut monitor_code = "01".to_string();
    if !corpinfo.tj_monitortype.is_empty() {
      monitor_code = corpinfo.tj_monitortype.clone();
    }
    let mut protective_code = "".to_string();
    if medinfo.tj_monitortype.eq_ignore_ascii_case("02") {
      //主動監測
      if !medinfo.tj_protective.is_empty() {
        protective_code = medinfo.tj_protective.to_string();
      } else {
        protective_code = "4".to_string();
      }
    }
    let info = CardInfo {
      code: medinfo.tj_testid.to_owned(),
      cardtype: "101".to_string(),
      seniorityyear: total_work_time.0,
      senioritymonth: total_work_time.1,
      exposureyear: hazard_work_time.0,
      exposuremonth: hazard_work_time.1,
      checktype: zjcommon::get_check_type(medinfo.tj_isrecheck),
      bodychecktype: zjcommon::get_testtype_status(medinfo.tj_testtype),
      protectiveequipmentcode: protective_code,
      previouscardid: old_testid,
      checktime: utils::timestamp_to_local_date_without_seperator(medinfo.tj_testdate),
      writeperson: nodipservice::SYSCACHE
        .get()
        .unwrap()
        .get_staff(medinfo.tj_recorder.parse::<i64>().unwrap_or_default(), &dbconn)
        .await
        .tj_staffname
        .to_string(), //StaffService::find_staff(medinfo.tj_recorder.parse::<i64>().unwrap_or_default(), &dictdata.staffs).tj_staffname,
      writepersontel: dict.ss_name.clone(),
      checkorgname: settings.organization.org_name.to_owned(),
      writedate: utils::timestamp_to_local_date_without_seperator(corp_report.tj_createdate),
      checkresultcode: checkresultcode, //zjcommon::get_check_result(checkall.tj_typeid),
      suggest: suggest,
      checkdoctor: nodipservice::SYSCACHE.get().unwrap().get_staff(checkall.tj_staffid, &dbconn).await.tj_staffname.to_string(), //StaffService::find_staff(checkall.tj_staffid, &dictdata.staffs).tj_staffname,
      monitortypecode: monitor_code,
      reportunit: settings.organization.org_name.to_owned(),
      reportperson: nodipservice::SYSCACHE
        .get()
        .unwrap()
        .get_staff(medinfo.tj_recorder.parse::<i64>().unwrap_or_default(), &dbconn)
        .await
        .tj_staffname
        .to_string(), //StaffService::find_staff(medinfo.tj_recorder.parse::<i64>().unwrap_or_default(), &dictdata.staffs).tj_staffname,
      reportpersontel: dict.ss_name.clone(),
      remark: "".to_string(),
    };
    Ok(info)
  }

  async fn trans_contact_hazard_factor_list(pthds: &Vec<TjPatienthazards>, hdinfos: &Vec<TjHazardinfo>, setting: &Settings) -> Result<ContactHazardFactorList> {
    let otherhazards: Vec<String> = setting.system.other_hdcode.split(",").map(|x| x.to_string()).collect();
    let mut contacthazardfactors: Vec<ContactHazardFactor> = Vec::new();
    for hd in pthds.iter() {
      let hdinfo = hdinfos.iter().find(|v| v.id == hd.tj_hid);
      if hdinfo.is_none() {
        continue;
        //return Err(anyhow!("毒害因素:{}不存在", hd.tj_hid));
      }
      let hdinfo = hdinfo.unwrap();
      let otherhd = otherhazards.iter().find(|&x| x == &hdinfo.tj_extcode);
      let mut otherhdname = "".to_string();
      if otherhd.is_some() {
        otherhdname = hdinfo.tj_hname.to_string();
      }
      let cthd = ContactHazardFactor {
        hazardcode: hdinfo.tj_extcode.to_owned(),
        otherhazardname: otherhdname,
      };
      contacthazardfactors.push(cthd);
    }
    let info = ContactHazardFactorList { contacthazardfactors };
    Ok(info)
  }

  async fn trans_hazard_factor_list(medinfo: &TjMedexaminfo, pthds: &Vec<TjPatienthazards>, hdinfos: &Vec<TjHazardinfo>, setting: &Settings) -> Result<HazardFactorList> {
    let mut hazardfactors: Vec<HazardFactor> = Vec::new();
    let otherhazards: Vec<String> = setting.system.other_hdcode.split(",").map(|x| x.to_string()).collect();
    for val in pthds.iter() {
      let hdinfo = hdinfos.iter().find(|v| v.id == val.tj_hid);
      if hdinfo.is_none() {
        continue;
        // return Err(anyhow!("毒害因素:{}不存在", val.tj_hid));
      }
      let hdinfo = hdinfo.unwrap();
      let otherhd = otherhazards.iter().find(|&x| x == &hdinfo.tj_extcode);
      let mut otherhdname = "".to_string();

      if otherhd.is_some() {
        otherhdname = hdinfo.tj_hname.to_string();
      }
      let ret = utils::get_year_and_month(medinfo.tj_testdate, &medinfo.tj_poisionage);
      if ret.as_ref().is_err() {
        return Err(anyhow!("接害工龄信息错误，请检查工接害龄是否准确"));
      }
      let hazard_time = ret.unwrap();
      let mut hazardstartdate = hazard_time.2.replace("-", "");
      if medinfo.tj_testtype == TestType::SG as i32 {
        hazardstartdate = "".to_string();
      }
      let hazardfactor = HazardFactor {
        hazardcode: hdinfo.tj_extcode.to_owned(),
        otherhazardname: otherhdname,
        hazardstartdate: hazardstartdate,
        hazardyear: hazard_time.0,
        hazardmonth: hazard_time.1,
      };
      hazardfactors.push(hazardfactor);
    }
    let info = HazardFactorList { hazardfactors };
    Ok(info)
  }

  async fn trans_itemlist(
    medinfo: &TjMedexaminfo,
    patient: &TjPatient,
    checkitems: &Vec<TjCheckiteminfo>,
    summaries: &Vec<TjTestsummary>,
    iteminfos: &Vec<TjIteminfo>,
    setting: &Settings,
    dbconn: &DbConnection,
  ) -> Result<ItemList> {
    let mut itemlist: Vec<Item> = Vec::new();
    let mut has_audiogram = false;
    let otheritems: Vec<String> = setting.system.other_items.split(",").map(|x| x.to_string()).collect();
    // let mut deptname = "";
    let combine_items: Vec<&TjCheckiteminfo> = checkitems.iter().filter(|&x| x.tj_combineflag == 1).collect();
    // info!("++++++++++{:?}", &combine_items);
    // if combine_item.is_some() {
    //     group_name = combine_item.unwrap().tj_itemname.as_str();
    // }
    for val in checkitems.iter() {
      if val.tj_combineflag == YesOrNo::Yes as i32 {
        continue;
      }
      if val.tj_result.eq_ignore_ascii_case("未检") {
        error!("项目:{}未检,不上报", val.tj_itemname);
        continue;
      }
      if val.tj_deptid.as_str() == app::AUDIOGRAM_DEPT_ID {
        // info!("有电测听项目......");
        has_audiogram = true;
        continue;
      }
      if val.tj_result.is_empty() && val.tj_deptid != app::AUDIOGRAM_DEPT_ID {
        error!("项目:{}的检查结果为空，忽略.", val.tj_itemname);
        continue;
      }
      let mut checkdoctor = val.tj_checkdoctor.to_owned();
      if checkdoctor.is_empty() {
        checkdoctor = val.tj_recheckdoctor.to_owned();
      }
      if checkdoctor.is_empty() {
        let sum = summaries.iter().find(|&x| x.tj_deptid.eq_ignore_ascii_case(&val.tj_deptid));
        if sum.is_some() {
          let sum = sum.unwrap();
          checkdoctor = sum.tj_checkdoctor.to_owned();
        }
      }
      if checkdoctor.is_empty() {
        return Err(anyhow!("项目:{}的检查医生为空，请检查项目输入", val.tj_itemname));
      }
      let mut checkdate = val.tj_checkdate;
      if checkdate <= 0 {
        let sum = summaries.iter().find(|&x| x.tj_deptid.eq_ignore_ascii_case(&val.tj_deptid));
        if sum.is_some() {
          let sum = sum.unwrap();
          checkdate = sum.tj_checkdate;
        }
      }
      if checkdate <= 0 {
        return Err(anyhow!("项目:{}的检查日期不对，请检查项目输入", val.tj_itemname));
      }
      let iteminfo = iteminfos.iter().find(|&x| x.tj_itemid == val.tj_itemid);
      if iteminfo.is_none() {
        continue;
      }
      let iteminfo = iteminfo.unwrap();
      if iteminfo.tj_extcode.is_empty() {
        continue;
      }
      // let dept_ret = dict.depts.iter().find(|&x| x.tj_deptid == val.tj_deptid);
      // if dept_ret.is_some() {
      //   deptname = dept_ret.unwrap().tj_deptname.as_str();
      // }
      let deptname = nodipservice::SYSCACHE.get().unwrap().get_department(&val.tj_deptid, &dbconn).await.tj_deptname;

      let mut otheritemname = "".to_string();
      let otheritem = otheritems.iter().find(|&x| x == &iteminfo.tj_extcode);
      if otheritem.is_some() {
        otheritemname = iteminfo.tj_itemname.clone();
      }
      let mut group_name = "未知".to_string();
      let group_item = combine_items.iter().find(|&x| x.tj_itemid.eq_ignore_ascii_case(val.tj_synid.as_str()));
      if group_item.is_some() {
        group_name = group_item.unwrap().tj_itemname.clone();
      }
      let mut itemunit = "无".to_string();
      if !iteminfo.tj_itemunit.is_empty() {
        itemunit = iteminfo.tj_itemunit.to_owned();
      }
      let mut maxvalue = "无".to_string();
      if !iteminfo.tj_uppervalue.is_empty() {
        maxvalue = iteminfo.tj_uppervalue.to_owned();
      }
      if setting.system.platform.eq_ignore_ascii_case("wz") && iteminfo.tj_itemid.eq_ignore_ascii_case("010010") {
        maxvalue = "140".to_string();
      }

      // let mut minvalue = "无".to_string();
      // if !iteminfo.tj_lowvalue.is_empty() {
      //     minvalue = iteminfo.tj_lowvalue.to_owned();
      // }

      let ext_item_id = iteminfo.tj_extcode.to_owned();
      let item = Item {
        itemid: ext_item_id.clone(),
        otheritemname: otheritemname,
        itemgroupname: group_name,
        department: deptname.to_string(),
        result: val.tj_result.to_owned(),
        itemtype: zjcommon::get_item_type(iteminfo.tj_valuetype.as_str()),
        unit: itemunit,
        max: maxvalue,
        min: if iteminfo.tj_lowvalue.is_empty() {
          "无".to_string()
        } else {
          iteminfo.tj_lowvalue.to_owned()
        }, //minvalue,
        charactercode: "".to_string(),
        checkresult: "".to_string(),
        mark: zjcommon::get_qualified_mark(val.tj_abnormalflag, &ext_item_id, &setting.system.dr_deptid).to_string(),
        checkdate: utils::timestamp_to_local_date_without_seperator(checkdate),
        checkdoctor: checkdoctor,
      };
      itemlist.push(item);
    }
    if has_audiogram {
      info!("开始整理电测听结果");
      let summary = summaries.iter().find(|&x| x.tj_testid == medinfo.tj_testid && x.tj_deptid == app::AUDIOGRAM_DEPT_ID.to_string());
      if summary.is_none() {
        return Err(anyhow!("不能找到电测听得科室小结"));
      }
      let aditems: Vec<TjCheckiteminfo> = checkitems
        .iter()
        .filter(|&x| x.tj_testid == medinfo.tj_testid && x.tj_deptid == app::AUDIOGRAM_DEPT_ID.to_string())
        .map(|x| x.clone())
        .collect();
      if aditems.len() <= 0 {
        return Err(anyhow!("不能找到电测听得科室检查项目"));
      }
      let ret = WzService::trans_audiogram_result(medinfo, &patient, &aditems, &summary.unwrap(), dbconn).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("上报电测听错误:{}", ret.as_ref().err().unwrap().to_string()));
      }
      let audiograms = ret.unwrap();
      for val in audiograms.itemlist.into_iter() {
        itemlist.push(val);
      }
    }
    if setting.system.additems.len() > 0 {
      for item in setting.system.additems.iter() {
        if itemlist.iter().find(|&f| f.itemid.eq_ignore_ascii_case(&item)).is_none() {
          let additem = Item {
            itemid: item.clone(),
            otheritemname: "".to_string(),
            itemgroupname: "内科常规检查".to_string(),
            department: "内科".to_string(),
            result: "999".to_string(),
            itemtype: 2,
            unit: "无".to_string(),
            max: "无".to_string(),
            min: "无".to_string(), //minvalue,
            charactercode: "".to_string(),
            checkresult: "".to_string(),
            mark: "1".to_string(),
            checkdate: utils::timestamp_to_local_date_without_seperator(utility::timeutil::current_timestamp()),
            checkdoctor: "无".to_string(),
          };
          itemlist.push(additem);
        }
      }
    }
    let info = ItemList { itemlist };
    Ok(info)
  }

  async fn trans_diagnosis(
    _medinfo: &TjMedexaminfo,
    patient: &TjPatient,
    checkall: &TjCheckallnew,
    pthds: &Vec<TjPatienthazards>,
    hdinfos: &Vec<TjHazardinfo>,
    iteminfos: &Vec<TjIteminfo>,
    setting: &Settings,
    dbconn: &DbConnection,
  ) -> Result<DiagnosisList> {
    let mut diagnosises: Vec<Diagnosis> = Vec::new();

    let mut pthds = pthds.to_owned();
    // let mut result_type = checkall.tj_typeid;
    info!("病人的毒害因素:{:?}", &pthds);
    let ret = TjDiseaseinfo::query(&checkall.tj_testid).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("query disease {}", ret.as_ref().err().unwrap().to_string()));
    }
    let disease_infos = ret.unwrap();
    let disids: Vec<i64> = disease_infos.iter().map(|v| v.tj_disid).collect();
    // let is_multi = false;
    let ismulti = pthds.iter().find(|&p| p.tj_typeid == checkall.tj_typeid).is_some();
    if !ismulti {
      info!("该体检者没有采用多因素结论，需要进行调整......");
      //set to multi
      if checkall.tj_typeid == CheckResultType::Forbidden as i32 || checkall.tj_typeid == CheckResultType::Oculike as i32 {
        if checkall.tj_hazardcode.is_empty() {
          return Err(anyhow!("该体检者禁忌或者疑似，但没有选择相应的毒害因素"));
        }
        let hdids: Vec<i64> = checkall.tj_hazardcode.split(",").map(|x| x.parse::<i64>().unwrap_or_default()).collect();

        let discodes: Vec<i64> = checkall.tj_discode.split(",").map(|x| x.parse::<i64>().unwrap_or_default()).collect();
        let mut disids_n: Vec<i64> = Vec::new();
        for val in discodes.into_iter() {
          //
          if val <= 3000 {
            //认为采用的是ID
            disids_n.push(val);
          } else {
            //用的是代码，需要调整为ID
            let disinfo = nodipservice::SYSCACHE.get().unwrap().find_diseases_by_code(&val.to_string(), &dbconn).await;
            if disinfo.is_none() {
              return Err(anyhow!("不能根据疾病代码：{}找到疾病信息", val));
            }
            disids_n.push(disinfo.unwrap().id);
          }
        }

        for v in pthds.iter_mut() {
          if hdids.iter().find(|&&f| f == v.tj_hid).is_some() {
            v.tj_typeid = checkall.tj_typeid;
            v.tj_diseases = disids_n.iter().map(|f| f.to_string()).collect::<Vec<String>>().join(","); //checkall.tj_discode.to_string();
            v.tj_recheckitems = checkall.tj_itemcode.to_string();
          }
        }
      }
      if checkall.tj_typeid == CheckResultType::Recheck as i32 {
        //需复查
        if checkall.tj_itemcode.is_empty() {
          return Err(anyhow!("该体检者需复查，但没有选择相应的复查项目"));
        }
        // let itemids: Vec<&str> = checkall.tj_itemcode.split(",").map(|x| x).collect();
        if checkall.tj_hazardcode.is_empty() {
          return Err(anyhow!("该体检者需复查，但没有选择相应的毒害因素"));
        }
        let hdids: Vec<i64> = checkall.tj_hazardcode.split(",").map(|x| x.parse::<i64>().unwrap_or_default()).collect();
        let discodes: Vec<i64> = checkall.tj_discode.split(",").map(|x| x.parse::<i64>().unwrap_or_default()).collect();
        let mut disids_n: Vec<i64> = Vec::new();
        for val in discodes.into_iter() {
          //
          if val <= 3000 {
            //认为采用的是discode
            disids_n.push(val);
          } else {
            //用的是代码，需要调整为ID
            let disinfo = nodipservice::SYSCACHE.get().unwrap().find_diseases_by_code(&val.to_string(), &dbconn).await;
            if disinfo.is_none() {
              return Err(anyhow!("不能根据疾病代码：{}找到疾病信息", val));
            }
            disids_n.push(disinfo.unwrap().id);
          }
        }
        for v in pthds.iter_mut() {
          if hdids.iter().find(|&&f| f == v.tj_hid).is_some() {
            v.tj_typeid = checkall.tj_typeid;
            v.tj_diseases = disids_n.iter().map(|f| f.to_string()).collect::<Vec<String>>().join(","); //checkall.tj_discode.to_string();
            v.tj_recheckitems = checkall.tj_itemcode.to_string();
          }
        }
      }

      if disids.len() > 0 {
        //其他疾病或异常
        for v in pthds.iter_mut() {
          if v.tj_typeid == CheckResultType::Normal as i32 || v.tj_typeid == CheckResultType::Other as i32 {
            v.tj_typeid = CheckResultType::Other as i32;
            v.tj_diseases = disids.iter().map(|v| v.to_string()).collect::<Vec<String>>().join(",");
            v.tj_recheckitems = "".to_string();
          }
        }
      }
      let ret = TjPatienthazards::save_many(&pthds).await;
      if ret.as_ref().is_err() {
        error!("update patient hazards error:{:?}", ret.as_ref().unwrap_err().to_string());
      }
    } else {
      info!("不需要调整多因素结果......");
    }
    info!("调整后的毒害因素信息：{:?}", &pthds);

    let other_disease: Vec<String> = disease_infos.iter().filter(|&p| p.tj_isoccu == YesOrNo::No as i32).map(|v| v.tj_disid.to_string()).collect();
    if other_disease.len() > 0 {
      for v in pthds.iter_mut() {
        if v.tj_typeid == CheckResultType::Normal as i32 {
          v.tj_typeid = CheckResultType::Other as i32;
          v.tj_diseases = other_disease.join(",");
        }
      }
    }

    for pthd in pthds.iter() {
      match pthd.tj_typeid {
        x if x == CheckResultType::Recheck as i32 => {
          if pthd.tj_diseases.is_empty() {
            return Err(anyhow!("需复查，需要选择相应的疾病信息(详细结果里处理)"));
          };
          if pthd.tj_recheckitems.is_empty() {
            return Err(anyhow!("需复查，需要选择相应的复查项目(详细结果里处理)"));
          }
        }
        x if x == CheckResultType::Forbidden as i32 => {
          if pthd.tj_diseases.is_empty() {
            return Err(anyhow!("职业禁忌证，需要选择相应的疾病信息(详细结果里处理)"));
          }
          // if checkall.tj_discode.is_empty() {
          //   return Err(anyhow!("职业禁忌证，需要选择相应的疾病信息"));
          // }
        }
        x if x == CheckResultType::Oculike as i32 => {
          // if checkall.tj_hazardcode.is_empty() {
          //   return Err(anyhow!("疑似职业病，需要选择相应的毒害因素"));
          // }
          if pthd.tj_diseases.is_empty() {
            return Err(anyhow!("疑似职业病，需要选择相应的疾病信息(详细结果里处理)"));
          }
        }
        // x if x == CheckResultType::Other as i32 => {
        //   if checkall.tj_hazardcode.is_empty() {
        //     return Err(anyhow!("其他疾病或异常，需要选择相应的毒害因素"));
        //   }
        //   if checkall.tj_discode.is_empty() {
        //     return Err(anyhow!("其他疾病或异常，需要选择相应的疾病信息"));
        //   }
        // }
        _ => {}
      }
    }
    let other_items: Vec<String> = setting.system.other_items.split(",").map(|x| x.to_string()).collect();

    for val in pthds.into_iter() {
      // info!("开始处理毒害因素：{:#?}", &val);
      let hdinfo = hdinfos.iter().find(|&p| p.id == val.tj_hid);
      if hdinfo.is_none() {
        continue;
      }
      let hdinfo = hdinfo.unwrap();
      if hdinfo.tj_extcode.is_empty() {
        return Err(anyhow!("毒害因素:{}代码未匹配，请先匹配", hdinfo.tj_hname));
      }
      let mut extcode = hdinfo.tj_extcode.to_string();
      if extcode.starts_with(zjcommon::FENCHEN) {
        extcode = zjcommon::FENCHEN.to_string();
      }
      let is_exist = diagnosises.iter().find(|&x| x.hazardcode.eq_ignore_ascii_case(extcode.as_str())).is_some();
      if is_exist {
        continue;
      }
      match val.tj_typeid {
        x if x == CheckResultType::Forbidden as i32 => {
          info!("处理禁忌的毒害因素：{:#?}", &val);
          let mut cdts: Vec<Cdt> = Vec::new(); //禁忌
          let disids: Vec<&str> = val.tj_diseases.split(",").collect();
          for did in disids.into_iter() {
            let mut discode = did.to_string();
            if !discode.starts_with("0") {
              let did = discode.parse::<i64>().unwrap_or_default();
              if did <= 3000 {
                let ret = nodipservice::SYSCACHE.get().unwrap().find_diseases_by_id(did, &dbconn).await;
                if ret.is_some() {
                  discode = ret.unwrap().tj_discode.to_owned();
                }
              }
            }
            let cdt = Cdt { cdtid: discode };
            cdts.push(cdt);
          }
          let cdtlist = CdtList { cdts };
          // if diagnosises.iter().find(|&x| x.hazardcode.eq_ignore_ascii_case(&extcode)).is_none() {
          let diagnosis = Diagnosis {
            hazardcode: extcode.clone(),
            conclusion: zjcommon::get_check_result(CheckResultType::Forbidden as i32),
            cdtlist,
            ..Default::default()
          };
          diagnosises.push(diagnosis);
          // }
        }
        x if x == CheckResultType::Oculike as i32 => {
          //疑似
          info!("处理疑似的毒害因素：{:#?}", &val);
          let mut spts: Vec<Spt> = Vec::new(); //疑似
                                               // let disids: Vec<i64> = val.tj_diseases.split(",").map(|v| v.parse::<i64>().unwrap_or_default()).collect();
          let disids: Vec<&str> = val.tj_diseases.split(",").collect();
          for did in disids.into_iter() {
            let mut discode = did.to_string();
            if !discode.starts_with("0") {
              let did = discode.parse::<i64>().unwrap_or_default();
              if did <= 3000 {
                let ret = nodipservice::SYSCACHE.get().unwrap().find_diseases_by_id(did, &dbconn).await;
                if ret.is_some() {
                  discode = ret.unwrap().tj_discode.to_owned();
                }
              }
            }
            let cdt = Spt {
              sptid: discode,
              empname: val.tj_olcorpname.clone(),
            };
            spts.push(cdt);
          }
          let sptlist = SptList { spts };

          // if diagnosises.iter().find(|&x| x.hazardcode.eq_ignore_ascii_case(&extcode)).is_none() {
          let diagnosis = Diagnosis {
            hazardcode: extcode.to_string(),
            conclusion: zjcommon::get_check_result(CheckResultType::Oculike as i32),
            sptlist,
            ..Default::default()
          };
          diagnosises.push(diagnosis);
          // }
        }
        x if x == CheckResultType::Recheck as i32 => {
          //复查
          info!("处理需复查的毒害因素：{:#?}", &val);
          let mut repeatitems: Vec<RepeatItem> = Vec::new(); //复查
          let recheckitems: Vec<&str> = val.tj_recheckitems.split(",").collect();
          info!("需复查项目：{:?}", &recheckitems);
          for info in recheckitems.into_iter() {
            if info.eq_ignore_ascii_case(app::AUDIOGRAM_ITEMID) {
              let audio_items = zjcommon::init_audio_details(patient.tj_psex);
              for va in audio_items.iter() {
                let ret = RepeatItem {
                  repeatitemid: va.extcode.to_owned(),
                  otheritemname: "".to_string(),
                };
                repeatitems.push(ret);
              }
              continue;
            }

            let iteminfo = iteminfos.iter().find(|&x| x.tj_itemid.eq_ignore_ascii_case(info));
            if iteminfo.is_none() {
              error!("找不到项目编号:{:?}的项目信息", &info);
              return Err(anyhow!("找不到项目编号:{}的项目信息", &info));
            }
            let iteminfo = iteminfo.unwrap();
            //一般的复查项目都是组合项目，需要把该组合项目的全部项目都上报
            // if iteminfo.tj_combineflag == YesOrNo::Yes as i32 {
            //组合项目
            let combitems = nodipservice::SYSCACHE.get().unwrap().get_combineinfos(&vec![iteminfo.tj_itemid.to_string()], &dbconn).await;
            if combitems.len() <= 0 {
              return Err(anyhow!("项目代码:{},名称：{}的组合项目没有项目明细", &iteminfo.tj_itemid, &iteminfo.tj_itemname));
            }

            let recheck_itemids: Vec<String> = combitems.into_iter().map(|v| v.tj_itemid.to_string()).collect();
            let recheck_iteminfos = nodipservice::SYSCACHE.get().unwrap().get_iteminfos(&recheck_itemids, &dbconn).await;

            for val in recheck_iteminfos.into_iter() {
              if val.tj_extcode.is_empty() {
                error!("复查项目代码：{}，名称：{} 的上报代码未匹配,请先匹配", &val.tj_itemid, &val.tj_itemname);
                continue;
              }
              let is_other = other_items.iter().find(|&x| x.eq_ignore_ascii_case(&val.tj_itemid));
              let mut other_item_name = "";
              if is_other.is_some() {
                other_item_name = is_other.unwrap().as_str();
              }
              let ret = RepeatItem {
                repeatitemid: val.tj_extcode.to_owned(),
                otheritemname: other_item_name.to_string(),
              };
              info!("复查项目:{:?}", &ret);
              repeatitems.push(ret);
            }
          }
          let repeatitemlist = RepeatItemList { repeatitems };
          // if diagnosises.iter().find(|&x| x.hazardcode.eq_ignore_ascii_case(&extcode)).is_none() {
          let diagnosis = Diagnosis {
            hazardcode: extcode.to_string(),
            conclusion: zjcommon::get_check_result(CheckResultType::Recheck as i32),
            repeatitemlist,
            ..Default::default()
          };
          diagnosises.push(diagnosis);
          // }
        }
        x if x == CheckResultType::Other as i32 => {
          info!("处理其他疾病或异常的毒害因素：{:#?}", &val);
          let mut otherdiseases: Vec<String> = Vec::new();
          let disids: Vec<i64> = val.tj_diseases.split(",").map(|v| v.parse::<i64>().unwrap_or_default()).collect();
          for did in disids.into_iter() {
            let disinfo = disease_infos.iter().find(|&f| f.tj_disid == did);
            if disinfo.is_none() {
              continue;
            }
            let disinfo = disinfo.unwrap();
            if disinfo.tj_isoccu == YesOrNo::Yes as i32 {
              continue;
            }
            otherdiseases.push(disinfo.tj_diseasename.to_owned());
          }
          let otherlist = OtherList { otherdiseases };
          // if diagnosises.iter().find(|&x| x.hazardcode.eq_ignore_ascii_case(&extcode)).is_none() {
          let diagnosis = Diagnosis {
            hazardcode: extcode.to_string(),
            conclusion: zjcommon::get_check_result(CheckResultType::Other as i32),
            otherlist,
            ..Default::default()
          };
          diagnosises.push(diagnosis);
          // }
        }
        _ => {
          info!("处理正常的毒害因素：{:#?}", &val);
          let diagnosis = Diagnosis {
            hazardcode: extcode,
            conclusion: zjcommon::get_check_result(val.tj_typeid),
            ..Default::default()
          };
          diagnosises.push(diagnosis);
        }
      }
    }

    let info = DiagnosisList { diagnosises };
    Ok(info)
  }

  async fn trans_auditinfo() -> Result<AuditInfo> {
    let info = AuditInfo { ..Default::default() };
    Ok(info)
  }

  async fn trans_healthy_survey(medinfo: &TjMedexaminfo, dbconn: &DbConnection) -> Result<HealthSurvey> {
    let mut testid = medinfo.tj_testid.to_string();
    let mut medinfo = medinfo.to_owned();
    if medinfo.tj_isrecheck == constant::YesOrNo::Yes as i32 {
      loop {
        //是复查
        if medinfo.tj_oldtestid.is_empty() {
          return Err(anyhow!("该体检信息是复查体检，但是没有原体检号"));
        }
        let ret = TjMedexaminfo::query(&medinfo.tj_oldtestid, &dbconn.get_connection()).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        let ret = ret.unwrap();
        if ret.is_none() {
          return Err(anyhow!("不能找到原体检号为:{}的体检信息", &medinfo.tj_oldtestid));
        }
        medinfo = ret.unwrap();
        if medinfo.tj_isrecheck == constant::YesOrNo::Yes as i32 {
          continue;
        } else {
          testid = medinfo.tj_testid.to_owned();
          break;
        }
      }
    }
    let ret = TjHealthyinfo::query(&testid, &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    }
    let ret = ret.unwrap();
    if ret.as_ref().is_none() {
      let querstionnair = QuestionNaire { ..Default::default() };
      let info = HealthSurvey { querstionnair };
      return Ok(info);
    }
    let healthyinfo = ret.unwrap();

    let ret = WzService::get_occucpation_history(&testid, dbconn).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let occupationhistorylist = ret.unwrap();
    let ret = WzService::get_disease_history(&testid, dbconn).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let diseasehistorylist = ret.unwrap();

    let ret = WzService::get_marriage_history(&testid, dbconn).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let marriagehistorylist = ret.unwrap();
    let ret = WzService::get_sympotoms(&testid, dbconn).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let symptomlist = ret.unwrap();

    let querstionnair = QuestionNaire {
      id: testid.clone(),
      surveydate: "".to_string(), //:"".to_string(),
      menarcheage: healthyinfo.tj_menarcheage.clone(),
      period: healthyinfo.tj_period.clone(),
      cycle: healthyinfo.tj_cycle.clone(),
      menopauseage: healthyinfo.tj_menopauseage.clone(),
      childrennum: healthyinfo.tj_childrennum.clone(),
      abortionnum: healthyinfo.tj_abortionnum.clone(),
      prematurebirthnum: healthyinfo.tj_prematurenum.clone(),
      stillbirthnum: healthyinfo.tj_stillbirthnum.clone(),
      abnormalfetalnum: healthyinfo.tj_abnormalnum.clone(),
      childrencondition: healthyinfo.tj_childrenhealthy.clone(),
      smksituation: healthyinfo.tj_smoke,
      smkyear: healthyinfo.tj_smokeyear.clone(),
      smkyear: healthyinfo.tj_smokemonth.clone(),
      smknum: healthyinfo.tj_smokenum.clone(),
      drksituation: healthyinfo.tj_drink,
      drknum: healthyinfo.tj_drinknum.clone(),
      drkyear: healthyinfo.tj_drinkyear.clone(),
      occupationhistorylist,
      diseasehistorylist: diseasehistorylist.0,
      occupationdieasehistorylist: diseasehistorylist.1,
      marriagehistorylist,
      symptomlist,
    };

    let info = HealthSurvey { querstionnair };
    Ok(info)
  }

  async fn trans_audiogram_result(medinfo: &TjMedexaminfo, patient: &TjPatient, checkitems: &Vec<TjCheckiteminfo>, summary: &TjTestsummary, dbconn: &DbConnection) -> Result<ItemList> {
    let ret = TjAudiogramdetail::query(&medinfo.tj_testid, &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("获取电测听结果错我：{}", ret.as_ref().unwrap_err().to_string()));
    }
    let mut audio_detail = ret.unwrap();
    if audio_detail.len() <= 0 {
      if medinfo.tj_isrecheck == constant::YesOrNo::Yes as i32 {
        if medinfo.tj_oldtestid.is_empty() {
          return Err(anyhow!("整理电测听时，复查，原体检号为空"));
        }
        let ret = TjAudiogramdetail::query(&medinfo.tj_oldtestid, &dbconn.get_connection()).await;
        if ret.as_ref().is_err() {
          return Err(anyhow!("电测听结果为空"));
        }
        audio_detail = ret.unwrap();
      }
    }
    if audio_detail.len() <= 0 {
      return Err(anyhow!("电测听结果为空"));
    }
    let audio_result = AudiogramSvc::compute_audiogram_result(&medinfo.tj_testid, &audio_detail);
    info!("计算的电测听结果:{:?}", &audio_result);
    // let audio_result: TjAudiogramresult; // = TjAudiogramresult { ..Default::default() };
    // let ret = TjAudiogramresult::query( &medinfo.tj_testid,&dbconn.get_connection()).await;
    // if ret.as_ref().is_err() {
    //   return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    // }
    // let ret = ret.unwrap();
    // if ret.as_ref().is_none() {
    //   if medinfo.tj_isrecheck == constant::YesOrNo::Yes as i32 {
    //     if medinfo.tj_oldtestid.is_empty() {
    //       return Err(anyhow!("整理电测听时，复查，原体检号为空"));
    //     }
    //     let ret = TjAudiogramresult::query( &medinfo.tj_oldtestid,&dbconn.get_connection()).await;
    //     if ret.as_ref().is_err() {
    //       return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
    //     }
    //     let ret = ret.unwrap();
    //     if ret.as_ref().is_none() {
    //       return Err(anyhow!("电测听结果为空"));
    //     }
    //     audio_result = ret.unwrap();
    //   } else {
    //     return Err(anyhow!("电测听结果为空"));
    //   }
    // } else {
    //   audio_result = ret.unwrap();
    // }
    // let audio_summary = TjAudiogramsummary::query(&medinfo.tj_testid, dbconn).await;
    // if audio_summary.as_ref().is_none() {
    //     return Err(anyhow!("没有电测听小结"));
    // }
    // let audio_summary = audio_summary.unwrap();
    let mut doctor = checkitems[0].tj_checkdoctor.trim().to_owned();
    if doctor.is_empty() {
      doctor = summary.tj_checkdoctor.trim().to_owned();
    }
    let mut chkdate = checkitems[0].tj_checkdate;
    if chkdate <= 0 {
      chkdate = summary.tj_checkdate;
    }
    if chkdate <= 0 {
      return Err(anyhow!("项目:电测听的检查检查日期有误，请检查项目输入"));
    }
    let mut itemlist: Vec<Item> = Vec::new();
    for val in audio_detail.into_iter() {
      if val.tj_result <= -100 {
        continue;
      }
      let addetail = zjcommon::get_audio_detail(&val, patient.tj_psex);
      if addetail.is_none() {
        error!("不能找到频率:{},耳朵:{},传导方式:{}的电测听配置结果", val.tj_freq, val.tj_ear, val.tj_adtype);
        continue;
      }
      let addetail = addetail.unwrap();

      let item = WzService::get_audio_gram_item(medinfo, val.tj_revise as f32, &doctor, chkdate, &summary.tj_suggestion, &addetail);
      itemlist.push(item);
    }

    {
      let syp = audio_result.tj_avgsyp;
      if syp > -100 as f32 {
        //do
        let retinfo = zjcommon::get_audio_result(zjcommon::AdResultType::SYP as i32, patient.tj_psex);
        if retinfo.is_some() {
          let item = WzService::get_audio_gram_item(medinfo, syp, &doctor, chkdate, &summary.tj_suggestion, &retinfo.unwrap());
          itemlist.push(item);
        }
      }
      let zyp = audio_result.tj_avgzyp;
      if zyp > -100 as f32 {
        //do
        let retinfo = zjcommon::get_audio_result(zjcommon::AdResultType::ZYP as i32, patient.tj_psex);
        if retinfo.is_some() {
          let item = WzService::get_audio_gram_item(medinfo, zyp, &doctor, chkdate, &summary.tj_suggestion, &retinfo.unwrap());
          itemlist.push(item);
        }
      }
      let yyp = audio_result.tj_avgyyp;
      if yyp > -100 as f32 {
        //do
        let retinfo = zjcommon::get_audio_result(zjcommon::AdResultType::YYP as i32, patient.tj_psex);
        if retinfo.is_some() {
          let item = WzService::get_audio_gram_item(medinfo, yyp, &doctor, chkdate, &summary.tj_suggestion, &retinfo.unwrap());
          itemlist.push(item);
        }
      }
      let sgp = audio_result.tj_avgsgp;
      if sgp > -100 as f32 {
        //do
        let retinfo = zjcommon::get_audio_result(zjcommon::AdResultType::SGP as i32, patient.tj_psex);
        if retinfo.is_some() {
          let item = WzService::get_audio_gram_item(medinfo, sgp, &doctor, chkdate, &summary.tj_suggestion, &retinfo.unwrap());
          itemlist.push(item);
        }
      }

      let ret = audio_result.tj_avgyty;
      if ret > -100 as f32 {
        //do
        let retinfo = zjcommon::get_audio_result(zjcommon::AdResultType::YJQ as i32, patient.tj_psex);
        if retinfo.is_some() {
          let item = WzService::get_audio_gram_item(medinfo, ret, &doctor, chkdate, &summary.tj_suggestion, &retinfo.unwrap());
          itemlist.push(item);
        }
      }

      let ret = audio_result.tj_avgzty;
      if ret > -100 as f32 {
        //do
        let retinfo = zjcommon::get_audio_result(zjcommon::AdResultType::ZJQ as i32, patient.tj_psex);
        if retinfo.is_some() {
          let item = WzService::get_audio_gram_item(medinfo, ret, &doctor, chkdate, &summary.tj_suggestion, &retinfo.unwrap());
          itemlist.push(item);
        }
      }
    }

    let info = ItemList { itemlist };
    Ok(info)
  }
  fn get_audio_gram_item(_medinfo: &TjMedexaminfo, chkresult: f32, doctor: &String, chkdate: i64, _suggestion: &String, addetail: &AudioDetail) -> Item {
    let mut abnormalflag = 1;
    // let mut uplow = 0;
    if chkresult >= addetail.highvalue as f32 {
      // uplow = 2;
      abnormalflag = 0;
    } else if chkresult <= addetail.lowvalue as f32 {
      // uplow = 1;
      abnormalflag = 0;
    }
    Item {
      itemid: addetail.extcode.to_owned(),
      otheritemname: "".to_string(),
      itemgroupname: "电测听".to_string(),
      result: format!("{}", chkresult),
      min: format!("{}", addetail.lowvalue),
      max: format!("{}", addetail.highvalue),
      department: "纯音听力".to_string(),
      itemtype: 2,
      unit: "dB".to_string(),
      charactercode: "".to_string(),
      checkresult: "".to_string(),
      mark: abnormalflag.to_string(),
      checkdate: utils::timestamp_to_local_date_without_seperator(chkdate),
      checkdoctor: doctor.to_owned(),
    }
  }

  async fn get_occucpation_history(testid: &String, dbconn: &DbConnection) -> Result<OccupationHistoryList> {
    let mut ocuhistories: Vec<OccupationHistory> = Vec::new();
    let ret = TjOccupationhistory::query(testid, &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let ocuhises = ret.unwrap();
    if ocuhises.len() <= 0 {
      let ocuhis = OccupationHistory { ..Default::default() };
      ocuhistories.push(ocuhis);
    } else {
      for val in ocuhises.iter() {
        let ouchis = OccupationHistory {
          startdate: val.tj_startdate.to_string(),
          enddate: val.tj_enddate.to_string(),
          depworkshop: val.tj_workshop.to_owned(),
          jobcode: val.tj_worktype.to_owned(),
          otherjobname: match val.tj_worktype.starts_with("99") {
            true => val.tj_worktypename.to_owned(),
            false => "".to_string(),
          },
          harmful: val.tj_harmful.to_owned(),
          protectivemeasures: val.tj_protective.to_owned(),
          daynum: val.tj_raddaynum.to_string(),
          totalnum: val.tj_radtotalnum.to_string(),
          overdose: val.tj_radoverdose.to_owned(),
          occuexposure: val.tj_radexposure.to_owned(),
          occuexposurecode: val.tj_radcode.to_owned(),
          radiationtype: val.tj_radtype.to_owned(),
          radprotectivemeasures: val.tj_radprotective.to_owned(),
        };
        ocuhistories.push(ouchis);
      }
    }
    let ocuhistorlist = OccupationHistoryList { ocuhistories };
    Ok(ocuhistorlist)
  }

  async fn get_disease_history(testid: &String, dbconn: &DbConnection) -> Result<(DiseaseHistoryList, OccupationDieaseHistoryList)> {
    let mut diseasehistories: Vec<DiseaseHistory> = Vec::new();
    let mut occupationdieasehistory: Vec<OccupationDieaseHistory> = Vec::new();

    let ret = TjDiseasehistory::query(&testid, &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let diseases = ret.unwrap();
    for val in diseases.iter() {
      if val.tj_isoccu == YesOrNo::Yes as i32 {
        let dishis = OccupationDieaseHistory {
          diseasename: val.tj_disname.to_owned(),
          diagnosisdate: val.tj_date.to_string(),
          diagnosisorg: val.tj_orgname.to_owned(),
          isrecovery: val.tj_isrecover,
        };
        occupationdieasehistory.push(dishis);
      } else {
        let dishis = DiseaseHistory {
          diseasename: val.tj_disname.to_owned(),
          diagnosisdate: val.tj_date.to_string(),
          diagnosisorg: val.tj_orgname.to_owned(),
          treatmentprocess: val.tj_curve.to_owned(),
          dieaseoutcomecode: val.tj_finalcode.to_owned(),
        };
        diseasehistories.push(dishis);
      }
    }
    let diseasehistorylist = DiseaseHistoryList { diseasehistories };
    let occupationdieasehistorylist = OccupationDieaseHistoryList { occupationdieasehistory };
    Ok((diseasehistorylist, occupationdieasehistorylist))
  }

  async fn get_marriage_history(testid: &String, dbconn: &DbConnection) -> Result<MarriageHistoryList> {
    let mut marriagehistories: Vec<MarriageHistory> = Vec::new();

    // let testids: Vec<String> = vec![testid.clone()];
    let ret = TjMarriagehistory::query(&testid, &dbconn.get_connection()).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
    }
    let marriagehises = ret.unwrap();
    for val in marriagehises.iter() {
      if !val.tj_testid.eq_ignore_ascii_case(testid.as_str()) {
        continue;
      }
      let marriage = MarriageHistory {
        marriagedate: val.tj_date.to_string(),
        spouseradiation: val.tj_spouseradiation.to_owned(),
        spouseocpt: val.tj_spouseoccu.to_owned(),
      };
      marriagehistories.push(marriage);
    }

    let marriagehistorylist = MarriageHistoryList { marriagehistories };
    Ok(marriagehistorylist)
  }

  async fn get_sympotoms(_testid: &String, _dbconn: &DbConnection) -> Result<SymptomList> {
    let mut symptoms: Vec<Symptom> = Vec::new();
    let symptom = Symptom { ..Default::default() };
    symptoms.push(symptom);
    let symptomlist = SymptomList { symptoms };
    Ok(symptomlist)
  }
}
