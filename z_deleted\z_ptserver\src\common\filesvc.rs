// use std::{fs::File, io::Write, sync::Arc};

use std::{ffi::OsStr, path::Path};

use anyhow::{anyhow, Result};
use axum::{body::Bytes, extract::Multipart};
// use dbopservice::dbinit::DbConnection;
// use dataservice::dbinit::DbConnection;
// use futures::{Stream, TryStreamExt};
use headers::{HeaderMap, HeaderValue};
// use http_body_util::StreamBody;
use hyper::header;
// use nodipservice::{common::uidservice::UidgenService, medexam::patientsvc::PatientSvc};
use std::io::Read;
use tokio::{fs::File, io::AsyncWriteExt};
// use tokio_util::io::ReaderStream;

// use crate::config::settings::Settings;
pub struct FileSvc;

impl FileSvc {
  pub async fn do_upload(directory: &str, mut multipart: Multipart) -> Result<String> {
    let mut filename = "".to_string();
    while let Some(field) = multipart.next_field().await.unwrap() {
      // let fname = field.name();
      let file_name = field.file_name();

      info!("file_name:{:?}", &file_name);

      if file_name.is_none() {
        return Err(anyhow!("file name is empty"));
      }

      filename = file_name.unwrap().to_string();

      let ret = field.bytes().await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      }
      let data = ret.unwrap();
      // let new_filename = format!("{}.{}", newname, extension);
      let ret = FileSvc::save_file(&directory, &filename, &data).await;
      if ret.as_ref().is_err() {
        return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
      }
    }

    Ok(filename)
  }

  // pub async fn do_download(filename: &str) -> Result<(HeaderMap, StreamBody<ReaderStream<File>>)> {
  pub async fn do_download(filename: &str) -> Result<(HeaderMap, Vec<u8>)> {
    // let file = match tokio::fs::File::open(&filename).await {
    //   Ok(file) => file,
    //   Err(err) => {
    //     error!("{} not found with error:{}", &filename, err.to_string());
    //     return Err(anyhow!("{} not found", filename));
    //   } // Err(err) => return response_json_error("can't find file specified"),
    // };

    // // convert the `AsyncRead` into a `Stream`
    // let stream = ReaderStream::new(file);
    // // convert the `Stream` into an `axum::body::HttpBody`
    // let body = StreamBody::new(stream);

    // let path = Path::new(filename);
    let mut file = match std::fs::File::open(filename) {
      Ok(f) => f,
      Err(err) => {
        error!("{} not found with error:{}", &filename, err.to_string());
        return Err(anyhow!("{} not found", filename));
      }
    };
    let mut body = vec![];
    file.read_to_end(&mut body)?;

    let mime_type = mime_guess::from_path(filename).first_or_text_plain();
    info!("mime type:{:?}", &mime_type);

    let mut headers = HeaderMap::new();
    // headers.insert("Content-Type", "image/jpeg".parse().unwrap());
    headers.insert(header::CONTENT_TYPE, HeaderValue::from_str(mime_type.as_ref()).unwrap());
    let file_onely_name = filename.split('/').last();
    // info!("file only name is:{:?}", &file_onely_name);
    let mut file_only_name = "";
    if file_onely_name.is_some() {
      file_only_name = file_onely_name.unwrap();
    }
    headers.insert(header::CONTENT_DISPOSITION, format!("attachment; filename=\"{}\"", file_only_name).parse().unwrap());
    // headers.insert(header::CONTENT_DISPOSITION, "attachment;filename=\"report.pdf\"".parse().unwrap());

    Ok((headers, body))
  }

  pub async fn save_file(directory: &str, filename: &str, data: &Bytes) -> Result<String> {
    let new_filename = format!("{}/{}", directory, filename);
    info!("new file name is:{}", &new_filename);
    let ret = File::create(&new_filename).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
    }
    let mut f = ret.unwrap();
    let ret = f.write_all(&data).await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
    }
    let ret = f.sync_all().await;
    if ret.as_ref().is_err() {
      return Err(anyhow!("{}", &ret.err().unwrap().to_string()));
    }
    Ok(new_filename)
  }
}
