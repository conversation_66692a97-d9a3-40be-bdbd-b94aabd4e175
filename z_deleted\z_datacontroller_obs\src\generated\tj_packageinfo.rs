//! SeaORM Entity. Generated by sea-orm-codegen 0.7.0

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "tj_packageinfo")]
pub struct Model {
    #[sea_orm(column_name = "ID", primary_key)]
    pub id: i32,
    pub tj_pnum: String,
    pub tj_pname: String,
    pub tj_price: String,
    pub tj_type: i32,
    pub tj_memo: String,
    pub tj_showorder: i32,
    pub tj_fitrange: i32,
    pub tj_pyjm: String,
    pub tj_zdym: String,
    pub tj_flag: i32,
    pub tj_operator: i32,
    pub tj_moddate: i64,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter)]
pub enum Relation {}

impl RelationTrait for Relation {
    fn def(&self) -> RelationDef {
        panic!("No RelationDef")
    }
}

impl ActiveModelBehavior for ActiveModel {}
